const express = require('express');
const router = express.Router();
const youtubeController = require('../controllers/youtubeController');
const authenticate = require('../middleware/authenticate');

// 公开API - 获取所有视频
router.get('/videos', youtubeController.getAllVideos);
router.get('/videos/category/:category', youtubeController.getVideosByCategory);

// 需要管理员权限的API
router.get('/videos/admin', authenticate, youtubeController.isAdmin, youtubeController.getAllVideosAdmin);
router.post('/videos', authenticate, youtubeController.isAdmin, youtubeController.addVideo);
router.put('/videos/:id', authenticate, youtubeController.isAdmin, youtubeController.updateVideo);
router.delete('/videos/:id', authenticate, youtubeController.isAdmin, youtubeController.deleteVideo);

module.exports = router; 