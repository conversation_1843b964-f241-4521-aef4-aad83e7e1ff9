# Referral System Documentation

## Overview

The referral system allows existing members to invite new users to join MMC Wellness. When a new user registers using a valid referral code, the referring member earns reward points that can be used for membership discounts or other benefits.

## Database Structure

The referral system uses the following tables:

### `referral_codes`
- Stores unique referral codes for each user
- Fields:
  - `id`: Auto-increment primary key
  - `demographic_no`: The demographic number of the referrer (foreign key to `demographic` table)
  - `referral_code`: A unique 6-character code
  - `created_at`: When the code was generated
  - `is_active`: Whether the code is still valid

### `referrals`
- Tracks each successful referral
- Fields:
  - `id`: Auto-increment primary key
  - `referrer_demographic_no`: The demographic number of the member who referred
  - `referred_demographic_no`: The demographic number of the new user who registered
  - `referral_date`: When the referral was made
  - `status`: Current status ('pending', 'completed', 'rewarded')
  - `reward_points`: How many points were awarded for this referral

### `reward_points`
- Records all reward point transactions
- Fields:
  - `id`: Auto-increment primary key
  - `demographic_no`: The demographic number of the member
  - `points`: Number of points (positive for earned, negative for spent)
  - `description`: Description of the transaction
  - `created_at`: When the transaction occurred

## Backend Implementation

### Services

- `ReferralService`: Core service for referral operations
  - `generateReferralCode`: Creates a new referral code for a user
  - `validateReferralCode`: Validates if a referral code is valid
  - `recordReferral`: Records a new referral when a user registers
  - `addRewardPoints`: Adds reward points to a user's account
  - `getTotalRewardPoints`: Gets the total reward points for a user
  - `getUserReferrals`: Gets all referrals made by a user
  - `getReferralCode`: Gets or creates a referral code for a user

### Controllers

- `ReferralController`: Handles API endpoints for referral operations
  - `getReferralCode`: Gets the referral code for a user
  - `getUserReferrals`: Gets all referrals for a user
  - `validateReferralCode`: Validates a referral code

### Routes

- `GET /api/referrals/code`: Get current user's referral code
- `GET /api/referrals/code/:demographicNo`: Get a specific user's referral code (admin only)
- `GET /api/referrals/list`: Get current user's referrals
- `GET /api/referrals/list/:demographicNo`: Get a specific user's referrals (admin only)
- `GET /api/referrals/validate/:code`: Validate a referral code (public endpoint)

## Frontend Implementation

### Membership Component Updates

The Membership component has been updated to:
- Display the user's referral code
- Show a copy button for easy sharing
- Display a list of users the member has referred
- Show total reward points earned

### Registration Updates

The Registration component now:
- Requires a valid referral code to register
- Validates the referral code in real-time
- Shows clear feedback on code validity

## Reward System

Current rewards:
- 100 points for each successful referral

Future potential uses of reward points:
- Discount on membership renewal
- Free or discounted services
- Priority bookings

## Setup Instructions

1. Run the migration script to create the necessary tables:
   ```
   node src/scripts/apply-referral-migration.js
   ```

2. Restart the server to load the new routes and controllers

3. Existing users can now see their referral codes in the Membership section

## Technical Notes

- Referral codes are 6-character alphanumeric strings (uppercase)
- Codes are validated in real-time during registration
- Each user can only be referred once
- Points are awarded when the referral is recorded during registration
- Points history is maintained for future reporting and features 