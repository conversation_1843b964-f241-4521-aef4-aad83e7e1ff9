-- 创建处方解释表（如果不存在）
CREATE TABLE IF NOT EXISTS prescription_explanations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    script_no INT(10) NOT NULL,
    explanation TEXT,
    language VARCHAR(10) DEFAULT 'zh',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_prescription (script_no, language)
) ENGINE=Aria DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci PAGE_CHECKSUM=1;

-- 添加索引
CREATE INDEX idx_script_no ON prescription_explanations(script_no);
CREATE INDEX idx_language ON prescription_explanations(language); 