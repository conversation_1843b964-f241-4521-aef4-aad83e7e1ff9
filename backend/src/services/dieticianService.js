const pool = require('../config/database');
const aiService = require('../utils/aiService');
const MedicalPromptTemplates = require('../prompts/medicalPrompts');

/**
 * 使用Gemini AI API总结营养师评论 - 重构版本
 * @param {Object} commentData - 包含营养师评论信息的对象
 * @param {String} language - 用户偏好的语言 ('en' 或 'zh')
 * @returns {Object} 包含AI总结的对象
 */
exports.summarizeCommentWithAI = async (commentData, language = 'en') => {
    try {
        console.log(`[AI Service] Starting dietician comment summary for comment ID: ${commentData.dietician_comment_id}`);

        // 构建提示数据
        const promptData = {
            entry_date: commentData.entry_date,
            demographic_no: commentData.demographic_no,
            comments: commentData.comments
        };

        // 使用模板构建提示词
        const prompt = MedicalPromptTemplates.buildPrompt('dietician', language, promptData);

        // 调用统一AI服务（包含自动重试逻辑）
        const result = await aiService.generateDieticianSummary(prompt, commentData.dietician_comment_id, language);

        if (result.success) {
            console.log(`[AI Service] Successfully generated summary for comment ID: ${commentData.dietician_comment_id}`);
            return { success: true, summary: result.content };
        } else {
            console.error(`[AI Service] Failed to generate summary for comment ID: ${commentData.dietician_comment_id}:`, result.error);
            return { success: false, message: result.error };
        }

    } catch (error) {
        console.error(`[AI Service] Error in summarizeCommentWithAI for comment ID: ${commentData.dietician_comment_id}:`, error);
        return { 
            success: false, 
            message: 'Failed to generate AI summary',
            error: error.message 
        };
    }
}; 