import { VIDEO_CONFERENCE_CONFIG } from '../config/videoConference';

const { JIGASI_SERVER } = VIDEO_CONFERENCE_CONFIG;

// Common fetch options for handling self-signed certificates
const fetchOptions = {
    mode: 'cors',
    credentials: 'include',
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    }
};

class VideoConferenceService {
    async createConference(settings) {
        const token = localStorage.getItem('token');
        if (!token) {
            throw new Error('Authentication required');
        }

        const response = await fetch(`${JIGASI_SERVER}/create-conference`, {
            ...fetchOptions,
            method: 'POST',
            headers: {
                ...fetchOptions.headers,
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({ settings })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || 'Failed to create conference');
        }

        return await response.json();
    }

    async joinConference(conferenceId, settings) {
        const token = localStorage.getItem('token');
        if (!token) {
            throw new Error('Authentication required');
        }

        const response = await fetch(`${JIGASI_SERVER}/validate-conference/${conferenceId}`, {
            ...fetchOptions,
            method: 'POST',
            headers: {
                ...fetchOptions.headers,
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({ settings })
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || 'Failed to join conference');
        }

        return await response.json();
    }

    async endConference(conferenceId) {
        const token = localStorage.getItem('token');
        if (!token) {
            throw new Error('Authentication required');
        }

        const response = await fetch(`${JIGASI_SERVER}/end-conference/${conferenceId}`, {
            ...fetchOptions,
            method: 'POST',
            headers: {
                ...fetchOptions.headers,
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || 'Failed to end conference');
        }

        return await response.json();
    }

    getConferenceUrl(conferenceId, settings) {
        const settingsParam = encodeURIComponent(JSON.stringify(settings));
        return `${JIGASI_SERVER}/conference/${conferenceId}?settings=${settingsParam}`;
    }
}

export default new VideoConferenceService();
