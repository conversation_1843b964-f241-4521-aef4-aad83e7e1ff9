const pool = require('../config/database');
const ImmunizationExplanationService = require('../services/immunizationExplanationService');
const { createLogger } = require('../utils/logger');
const { ValidationError, AuthorizationError } = require('../utils/errors');

const logger = createLogger('ImmunizationController');

/**
 * Get immunizations/preventions for a specific patient
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} JSON response with immunizations list
 */
exports.getPatientImmunizations = async (req, res) => {
    try {
        const { demographicNo } = req.params;
        const loggedInUser = req.user; // Assuming auth middleware adds user info

        // --- Permission Check (Essential!) ---
        if (!loggedInUser || !loggedInUser.demographic_no) {
            return res.status(403).json({ success: false, message: 'Authentication required or user not linked.' });
        }

        const targetDemoNo = parseInt(demographicNo, 10);
        if (isNaN(targetDemoNo)) {
            return res.status(400).json({ success: false, message: 'Invalid demographic number provided.' });
        }

        // Admin check - admins can view any patient's immunizations
        let isAllowed = false;
        if (loggedInUser.role === 'admin') {
            logger.info(`Admin user ${loggedInUser.id} granted access to view immunizations for demographic ${targetDemoNo}`);
            isAllowed = true;
        } else {
            const loggedInDemoNo = loggedInUser.demographic_no;
            isAllowed = targetDemoNo === loggedInDemoNo;
            if (!isAllowed) {
                const [familyCheck] = await pool.query(
                    `SELECT 1 FROM relationships
                     WHERE ((demographic_no = ? AND relation_demographic_no = ?) OR (demographic_no = ? AND relation_demographic_no = ?))
                     AND deleted != '1' LIMIT 1`,
                    [loggedInDemoNo, targetDemoNo, targetDemoNo, loggedInDemoNo]
                );
                if (familyCheck.length > 0) { isAllowed = true; }
            }
        }

        if (!isAllowed) {
            return res.status(403).json({ success: false, message: 'Permission denied to view these immunizations.' });
        }
        // --- End Permission Check ---

        // Fetch non-deleted, non-refused preventions from the database
        const query = `
            SELECT
                id,
                prevention_date,
                provider_no,
                provider_name,
                prevention_type,
                next_date
            FROM preventions
            WHERE demographic_no = ?
            AND (deleted IS NULL OR deleted != '1')
            AND (refused IS NULL OR refused != '1')
            ORDER BY prevention_date DESC;
        `;

        const [immunizations] = await pool.query(query, [targetDemoNo]);

        // Format the response
        const formattedImmunizations = immunizations.map(imm => ({
            id: imm.id,
            immunizationDate: imm.prevention_date,
            providerNo: imm.provider_no,
            providerName: imm.provider_name,
            type: imm.prevention_type,
            nextDueDate: imm.next_date
        }));

        res.json({
            success: true,
            immunizations: formattedImmunizations
        });

    } catch (error) {
        logger.error('Error fetching immunizations:', error);
        res.status(500).json({ success: false, message: 'Server error fetching immunizations' });
    }
};

/**
 * Get AI explanation for a specific immunization
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} JSON response with immunization explanation
 */
exports.getImmunizationExplanation = async (req, res) => {
    try {
        const { immunizationId } = req.params;
        const { language = 'zh' } = req.query;
        const immId = parseInt(immunizationId, 10);

        if (isNaN(immId)) {
            throw new ValidationError('Invalid immunization ID provided');
        }

        // 获取当前用户的所有疫苗接种记录
        const [immunizations] = await pool.query(
            `SELECT 
                id,
                prevention_date AS immunizationDate,
                provider_no AS providerNo,
                provider_name AS providerName,
                prevention_type AS type,
                next_date AS nextDueDate
            FROM preventions
            WHERE demographic_no = ?
            AND (deleted IS NULL OR deleted != '1')
            AND (refused IS NULL OR refused != '1')`,
            [req.user.demographic_no]
        );

        // 查找请求的疫苗接种记录
        const immunization = immunizations.find(imm => imm.id === immId);

        if (!immunization) {
            throw new ValidationError('Immunization not found or no access');
        }

        // 获取或生成疫苗接种解释
        const explanationResult = await ImmunizationExplanationService.getOrCreateExplanation(immunization, language);

        if (!explanationResult.success) {
            throw new Error(explanationResult.message);
        }

        res.json({
            success: true,
            immunizationId: immId,
            explanation: explanationResult.explanation
        });

    } catch (error) {
        logger.error('Error in getImmunizationExplanation controller:', {
            message: error.message,
            stack: error.stack,
            immunizationId: req.params.immunizationId,
            userId: req.user?.id
        });

        if (error instanceof ValidationError) {
            return res.status(400).json({
                success: false,
                message: error.message
            });
        }

        if (error instanceof AuthorizationError) {
            return res.status(403).json({
                success: false,
                message: error.message
            });
        }

        res.status(500).json({
            success: false,
            message: 'Server error fetching immunization explanation'
        });
    }
}; 