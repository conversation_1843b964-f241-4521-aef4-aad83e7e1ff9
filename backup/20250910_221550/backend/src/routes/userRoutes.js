const express = require('express');
const router = express.Router();
const fileController = require('../controllers/fileController'); // Assuming fileController now has uploadUserAvatar
const authMiddleware = require('../middleware/auth'); // Assuming this is your auth middleware path
const multer = require('multer');

// Configure multer for memory storage for avatar uploads
const uploadAvatarToMemory = multer({
    storage: multer.memoryStorage(),
    fileFilter: (req, file, cb) => {
        if (file.mimetype.startsWith('image/')) {
            cb(null, true);
        } else {
            cb(new Error('Not an image! Please upload an image file for the avatar (jpeg, png, gif).'), false);
        }
    },
    limits: { fileSize: 5 * 1024 * 1024 } // 5MB limit for avatars
});

// Route for uploading/updating user avatar
// The field name in uploadAvatarToMemory.single('avatarImage') must match the name attribute of your file input in the frontend form
router.post('/avatar', authMiddleware, uploadAvatarToMemory.single('avatarImage'), fileController.uploadUserAvatar);

// You can add other user-specific routes here, for example:
// router.get('/profile', authMiddleware, userController.getProfile);
// router.put('/profile', authMiddleware, userController.updateProfile);

module.exports = router; 