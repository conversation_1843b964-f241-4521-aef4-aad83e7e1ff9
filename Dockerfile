# Use Node.js as base image with Python support
FROM node:20-alpine

# Install Python, pip, curl and other dependencies
RUN apk add --no-cache curl python3 py3-pip

# Create symbolic link for python
RUN ln -sf python3 /usr/bin/python

# Install PyMuPDF4LLM
RUN pip3 install --break-system-packages pymupdf4llm

# Set working directory
WORKDIR /app

# Copy package.json files
COPY backend/package*.json ./

# Install dependencies
RUN npm install

# Install health tips system dependencies
RUN npm install axios dotenv cron

# Copy backend source code
COPY backend/src ./src

# Create documents and logs directories, and uploads directory for booking images
RUN mkdir -p documents logs uploads/booking_images

# Add execution permission
RUN chmod +x src/scripts/setupDailyTips.sh

# List contents of /app/src for debugging
RUN ls -la /app/src

# Expose port
EXPOSE 3000

# Start application
CMD ["node", "src/index.js"] 