import React, { createContext, useState, useContext, useEffect } from 'react';

// Translation data
const translations = {
    en: {
        // Navigation groups
        'basic_info': 'Basic Information',
        'appointment_mgmt': 'Appointment Management',
        'health_records': 'Health Records',
        'system_settings': 'System Settings',

        // Navigation items
        'profile': 'Personal Profile',
        'membership': 'Membership Info',
        'appointments': 'Appointments',
        'booking': 'Book Appointment',
        'lab_reports': 'Lab Reports',
        'prescriptions': 'Prescriptions',
        'immunizations': 'Immunizations',
        'settings': 'Settings',

        // User menu
        'logout': 'Logout',
        'return_to_profile': 'Return to Personal Profile',
        'viewing_family_member': 'You are viewing family member information',
        'preferences': 'PREFERENCES',
        'language': 'Language',
        'theme': 'Theme',
        'switch_to_chinese': 'Switch to Chinese',
        'switch_to_english': 'Switch to English',
        'light_mode': 'Light Mode',
        'dark_mode': 'Dark Mode',

        // Common buttons and labels
        'save': 'Save',
        'cancel': 'Cancel',
        'loading': 'Loading...',
        'error': 'Error',
        'success': 'Success',
        'no_data': 'No data available',
        'confirm': 'Confirm',
        'delete': 'Delete',
        'edit': 'Edit',
        'view': 'View',
        'user': 'User',

        // App title
        'app_title': 'MMC WELLNESS',
        'app_subtitle': 'Professional Health Management Platform',

        // Profile page
        'personal_information': 'Personal Information',
        'full_name': 'Full Name',
        'address': 'Address',
        'phone': 'Phone',
        'email': 'Email',
        'care_card': 'Care Card No.',
        'sex': 'Sex',
        'family_members': 'Family Members',
        'request_access': 'Request Access',
        'access_active': 'Access Active',
        'no_membership': 'No Membership',
        'child': 'Child',
        'spouse': 'Spouse',
        'mr': 'MR',
        'mrs': 'MRS',
        'ms': 'MS',

        // Appointment page
        'your_appointments': 'Your Appointments',
        'upcoming_appointments': 'Upcoming Appointments',
        'past_appointments': 'Past Appointments',
        'latest_appointments': 'Latest Appointments',
        'date': 'Date',
        'time': 'Time',
        'doctor': 'Doctor',
        'location': 'Location',
        'notes': 'Notes',
        'reason': 'Reason',
        'status': 'Status',
        'confirmed': 'Confirmed',
        'completed': 'Completed',
        'cancelled': 'Cancelled',
        'pending': 'Pending',
        'reschedule': 'Reschedule',

        // Booking page
        'book_appointment': 'Schedule Appointment',
        'select_provider': 'Select Provider',
        'select_date_time': 'Select Date & Time',
        'confirm_booking': 'Confirm Booking',
        'provider': 'Provider',
        'next': 'Continue',
        'previous': 'Previous',
        'appointment_details': 'Appointment Details',
        'appointment_confirmation': 'Appointment Confirmation',

        // Lab reports page
        'your_medical_documents': 'Your Medical Documents',
        'family_medical_documents': 'Family Member Medical Documents',
        'documents': 'Documents',
        'test_values': 'Test Values',
        'document': 'Document',
        'document_viewer': 'Document Viewer',
        'no_documents': 'No documents found in your records.',
        'no_measurements': 'No measurement data found in your records.',
        'viewing_family_lab_reports': 'You are currently viewing lab reports for a family member.',
        'invalid_response': 'Invalid response format. Expected JSON, got: ',
        'lab_document': 'Lab Document',
        'consult_document': 'Consult Document',
        'lab': 'lab',
        'consult': 'consult',
        'download': 'Download',
        'view_document': 'View Document',

        // Settings page
        'security': 'Security',
        'notifications': 'Notifications',
        'general_preferences': 'General Preferences',
        'app_experience_settings': 'These settings control your application experience',
        'change_password': 'Change Password',
        'current_password': 'Current Password',
        'new_password': 'New Password',
        'confirm_new_password': 'Confirm New Password',
        'update_password': 'Update Password',
        'passwords_do_not_match': 'New passwords do not match',
        'password_too_short': 'Password must be at least 8 characters',
        'settings_updated': 'Settings updated successfully!',
        'notification_preferences': 'Notification Preferences',
        'email_notifications': 'Email Notifications',
        'receive_general_notifications': 'Receive general notifications via email',
        'appointment_reminders': 'Appointment Reminders',
        'receive_appointment_reminders': 'Receive reminders before your scheduled appointments',
        'document_notifications': 'Document Notifications',
        'receive_document_notifications': 'Receive notifications when new documents are available',
        'marketing_communications': 'Marketing Communications',
        'receive_marketing_emails': 'Receive promotional emails about services and events',
        'use_24hour_format': 'Use 24-hour time format',

        // General for medical documents
        'unknown_date': 'Unknown date',
        'not_available': 'N/A',
        'not_authenticated': 'Not authenticated. Please log in.',
        'fetch_failed': 'Failed to fetch data.',
        'fetch_error': 'An error occurred while fetching data.',
        'api_error': 'API returned error.',
        'switch_back': 'Switch Back to My Profile',

        // Prescriptions
        'prescribed_by': 'Prescribed By',
        'details': 'Details',
        'comments': 'Comments',
        'no_prescriptions': 'No prescriptions found.',
        'viewing_family_prescriptions': 'You are currently viewing prescriptions for a family member.',

        // Immunizations
        'vaccine_type': 'Vaccine / Prevention Type',
        'date_administered': 'Date Administered',
        'administered_by': 'Administered By',
        'next_due_date': 'Next Due Date',
        'no_immunizations': 'No immunization records found.',
        'viewing_family_immunizations': 'You are currently viewing immunizations for a family member.',

        // Additional appointment page translations
        'network_error': 'Network Connection Issue',
        'network_error_message': 'Unable to connect to the server. Please check your network connection and refresh the page.',
        'refresh': 'Refresh Page',
        'viewing_family_appointments': 'You are currently viewing appointments for a family member.',
        'family_appointments': 'Family Member Appointments',
        'error_loading_data': 'Error loading data',
        'upcoming_short': 'Upcoming',
        'past_short': 'Past',
        'past_appointments_latest': 'Past Appointments (Latest 10)',
        'loading_appointments': 'Loading appointments...',
        'no_appointments_found': 'No appointments found',
        'no_upcoming_appointments': 'There are no upcoming appointments',
        'no_past_appointments': 'There are no past appointments',
        'invalid_patient_id': 'Invalid patient ID. Please log in again or contact customer service.',
        'no_permission_appointments': 'You do not have permission to view this patient\'s appointments.',
        'fetch_appointments_failed': 'Failed to retrieve appointment information',

        // Additional profile page translations
        'request_access_title': 'Confirm Authorization Request',
        'request_access_dear_user': 'Dear User,',
        'request_access_intro': 'You are requesting temporary access to view sensitive health information for {name}. Please be advised of the following:',
        'request_access_point1_title': 'Verification Required:',
        'request_access_point1_text': 'Access requires verification via an email sent to the registered address of {name}. This is a security measure to protect patient privacy.',
        'request_access_point2_title': 'Temporary Access:',
        'request_access_point2_text': 'If verification is successful, access granted is temporary and may be subject to time limits or other restrictions as defined by MMC Wellness policy.',
        'request_access_point3_title': 'Confidentiality:',
        'request_access_point3_text': 'All health information accessed must be treated with the strictest confidentiality in accordance with privacy laws and regulations. Unauthorized disclosure is prohibited.',
        'request_access_point4_title': 'Disclaimer:',
        'request_access_point4_text': 'MMC Wellness provides this access mechanism as a convenience. While we strive for accuracy, MMC Wellness is not liable for any decisions made based on the information viewed or for any misuse of this access feature.',
        'request_access_confirmation': 'By clicking "Confirm", you acknowledge that you understand these terms and wish to proceed with the verification process to request access to {name}\'s records.',
        'request_access_sincerely': 'Sincerely,',
        'request_access_team': 'The MMC Wellness Team',
        'verification_title': 'Enter Verification Code',
        'verification_message': 'Please enter the verification code sent to {email}',
        'verification_code': 'Verification Code',
        'verify': 'Verify & View Profile',
        'no_family_members': 'No family members are currently linked to your account.',
        'no_profile_data': 'No profile data available.',
        'verification_error': 'Verification failed. Please try again or contact support.',
        'verification_success': 'Verification successful! Redirecting...',
        'profile_access_granted': 'Access granted. You can now view this profile.',
        'profile_access_denied': 'Access denied. Please contact support if you believe this is an error.',
        'profile_access_expired': 'Access has expired. Please request access again.',
        'profile_loading': 'Loading profile information...',
        'request_verification': 'Request Verification Code',
        'resend_code': 'Resend Code',

        // Membership page
        'membership_info': 'Membership Information',
        'family_members_status': 'Family Members Status',
        'your_membership': 'Your Membership',
        'self': 'Self',
        'family_member': 'Family Member',
        'no_membership': 'No Membership',
        'purchase_membership': 'Purchase Membership',
        'expired': 'Expired',
        'active': 'Active',
        'start_date': 'Start Date',
        'end_date': 'End Date',
        'membership_type': 'Membership Type',
        'payment_amount': 'Payment Amount',
        'renew_membership': 'Renew Membership',
        'renew_early': 'Renew Early',
        'hide_history': 'Hide History',
        'view_history': 'View History',
        'membership_history': 'Membership History',
        'date': 'Date',
        'type': 'Type',
        'amount': 'Amount',
        'status': 'Status',
        'completed': 'Completed',
        'loading_membership': 'Loading membership information...',
        'no_membership_records': 'No membership records found',
        'membership_details': 'Membership Details',
        'available_membership_types': 'Available Membership Types',
        'price': 'Price',
        'no_membership_types': 'No membership types available',
        'membership_benefits_note': 'Note: Membership fees are non-refundable. Please contact our office for any questions regarding membership.',
        'select_membership_type': 'Please select the membership type you wish to purchase',
        'proceed_to_payment': 'Proceed to Payment',
        'renewal_success': 'Membership renewed successfully!',
        'payment_process_note': 'Note: This will be processed as a new membership purchase. Your membership will be valid for one year from today.',
        'authentication_failed': 'Authentication failed. Please login again.',
        'server_invalid_format': 'Server returned invalid format.',
        'server_invalid_structure': 'Server returned invalid data structure.',
        'fetch_membership_failed': 'Failed to fetch membership information',
        'fetch_membership_failed_retry': 'Failed to fetch membership information. Please try again later.',
        'fetch_membership_types_failed': 'Failed to fetch membership types.',
        'request_timeout': 'Request timeout. Please check your network connection and try again.',
        'status_code': 'Status Code',
        'loading': 'Loading',
        // Chatbot
        'chatbot': 'Health Assistant',
        'chat_window_title': 'Health Assistant',
        'chat_bot_name': 'MMC Health Assistant',
        'chat_greeting': 'Hello! I\'m the MMC Health Assistant. How can I help you with your health management questions today?',
        'type_message': 'Type your message...',

        // Appointment Cancellation
        'cancel_appointment_confirmation': 'Confirm Appointment Cancellation',
        'cancel_appointment_warning': 'Please note that appointments must be cancelled at least 2 days in advance. A notification will be sent to the clinic about this cancellation.',
        'cancel_policy': 'Cancellation Policy: Appointments can only be cancelled at least 2 days in advance. Late cancellations may incur a fee.',
        'cancellation_lead_time': 'Cancellations require 2 business days notice',
        'cancellation_email_notice': 'An email notification will be sent to the clinic (<EMAIL>) about your cancellation.',
        'confirm_cancel': 'Confirm Cancel',
        'cancelling': 'Cancelling...',
        'appointment_cancelled_successfully': 'Appointment cancelled successfully',
        'back': 'Back',
        'close': 'Close',
        'cannot_cancel_late': 'This appointment cannot be cancelled online as it is less than 2 days away. Please call the clinic directly to discuss cancellation options.',
        'auth_required': 'Authentication required. Please log in again.',
        'cancel_failed': 'Failed to cancel appointment. Please try again or contact the clinic directly.',

        // Booking page
        'booking_intro': 'Book your appointment online with just a few steps. Choose your preferred consultation type or doctor.',
        'book_by_service': 'By Service Type',
        'book_by_doctor': 'By Doctor',
        'select_service_type': 'Select Service',
        'select_date_time_doctor': 'Select Date & Doctor',
        'service_type_description': 'Please select the type of service you need. Different services may be provided by different specialists.',
        'service_no_description': 'Specialized medical service provided by our professional team',

        // Service types
        'service_types': {
            '健康管理咨询': {
                en: 'Health Management Consultation',
                zh: '健康管理咨询',
                description: {
                    en: 'Comprehensive health assessment and personalized management plan',
                    zh: '全面评估您的健康状况，制定个性化健康管理方案，预防疾病发生'
                }
            },
            '健康管理（电话）': {
                en: 'Telehealth Consultation',
                zh: '远程健康咨询',
                description: {
                    en: 'Convenient phone consultation with our healthcare professionals',
                    zh: '便捷的电话咨询服务，无需亲临医院，即可获得专业医疗建议'
                }
            },
            '功能医学咨询': {
                en: 'Functional Medicine',
                zh: '功能医学评估',
                description: {
                    en: 'Address root causes of health issues with personalized functional approach',
                    zh: '通过功能医学方法找出健康问题根源，制定个性化治疗方案'
                }
            }
        },

        // Date selection
        'date_selection_description': 'Please select your preferred date, then choose from available time slots and doctors below.',
        'select_date': 'Choose Date',
        'appointment_date': 'Appointment Date',
        'date_notice': 'Note: Appointments must be scheduled at least 2 days in advance',
        'loading_available_dates': 'Checking available dates...',
        'no_available_dates': 'No available dates for this service type. Please try another service.',
        'available_dates_info': 'Available dates found for your selected service',

        // Navigation buttons
        'previous': 'Previous',
        'next': 'Continue',

        // 预约系统相关翻译
        'book_appointment': 'Schedule Appointment',
        'booking_intro': 'Book your appointment online with just a few steps. Choose your preferred consultation type or doctor.',
        // 按医生预约相关
        'doctor_selection_description': 'Please select a doctor you prefer. You can then view their available appointment times.',
        'doctor_selection_prompt': 'Select your preferred physician',
        'available_times': 'Available Times',
        'no_slots_available': 'No available slots for this date',
        'select_date_first': 'Please select a date first',
        'selected_service': 'Selected Service',
        'selected_doctor': 'Selected Doctor',
        'selected_date': 'Selected Date',
        'selected_time': 'Selected Time',
        'available_slots': 'Available Time Slots',

        // Chatbot related translations
        'mmc_wellness_chat': 'MMC Wellness Chat',
        'chat_history': 'Chat History',
        'conversations': 'Conversations',
        'new_chat': 'New Chat',
        'delete_conversation': 'Delete Conversation',
        'no_conversations': 'No conversations yet',
        'welcome_to_mmc_chat': 'Welcome to MMC Wellness Chat',
        'chat_intro_expanded': 'I can provide information about common health conditions, seasonal health reminders, and MMC Wellness services. How can I help you today?',
        'type_message': 'Type your message here...',
        'send': 'Send',
        'sending': 'Sending...',
        'close': 'Close',
        'you': 'You',
        'more_options': 'More Options',
        'delete': 'Delete',
        'chat_greeting': 'Hello! How can I assist you with your health-related questions today?',
        'cannot_load_chat_history': 'Could not load chat history. Please try again later.',
        'conversation_deleted': 'Conversation deleted successfully',
        'failed_to_delete': 'Failed to delete conversation',
        'error_loading_conversation': 'Error loading conversation',
        'search_chats': 'Search conversations',
    },
    zh: {
        // Navigation groups
        'basic_info': '基本信息',
        'appointment_mgmt': '预约管理',
        'health_records': '健康记录',
        'system_settings': '系统设置',

        // Navigation items
        'profile': '个人资料',
        'membership': '会员信息',
        'appointments': '预约记录',
        'booking': '预约挂号',
        'lab_reports': '检验报告',
        'prescriptions': '处方记录',
        'immunizations': '疫苗接种',
        'settings': '设置',

        // User menu
        'logout': '退出登录',
        'return_to_profile': '返回个人页面',
        'viewing_family_member': '您正在查看家庭成员信息',
        'preferences': '偏好设置',
        'language': '语言',
        'theme': '主题',
        'switch_to_chinese': '切换到中文',
        'switch_to_english': 'Switch to English',
        'light_mode': '浅色模式',
        'dark_mode': '深色模式',

        // Common buttons and labels
        'save': '保存',
        'cancel': '取消',
        'loading': '加载中...',
        'error': '错误',
        'success': '成功',
        'no_data': '暂无数据',
        'confirm': '确认',
        'delete': '删除',
        'edit': '编辑',
        'view': '查看',
        'user': '用户',

        // App title
        'app_title': 'MMC健康管理',
        'app_subtitle': '专业健康管理平台',

        // Profile page
        'personal_information': '个人信息',
        'full_name': '姓名',
        'address': '地址',
        'phone': '电话',
        'email': '邮箱',
        'care_card': '医保卡号',
        'sex': '性别',
        'family_members': '家庭成员',
        'request_access': '请求访问权限',
        'access_active': '访问权限有效',
        'no_membership': '无会员资格',
        'child': '子女',
        'spouse': '配偶',
        'mr': '先生',
        'mrs': '女士',
        'ms': '女士',

        // Appointment page
        'your_appointments': '您的预约',
        'upcoming_appointments': '即将到来的预约',
        'past_appointments': '历史预约',
        'latest_appointments': '最近预约',
        'date': '日期',
        'time': '时间',
        'doctor': '医生',
        'location': '地点',
        'notes': '备注',
        'reason': '原因',
        'status': '状态',
        'confirmed': '已确认',
        'completed': '已完成',
        'cancelled': '已取消',
        'pending': '待确认',
        'reschedule': '重新安排',

        // Booking page
        'book_appointment': '预约就诊服务',
        'select_provider': '选择医生',
        'select_date_time': '选择日期和时间',
        'confirm_booking': '确认预约信息',
        'provider': '医生',
        'next': '继续预约',
        'previous': '返回上一步',
        'appointment_details': '预约详情',
        'appointment_confirmation': '预约确认',

        // Lab reports page
        'your_medical_documents': '您的医疗文档',
        'family_medical_documents': '家庭成员医疗文档',
        'documents': '文档',
        'test_values': '检验值',
        'document': '文档',
        'document_viewer': '文档查看器',
        'no_documents': '在您的记录中没有找到文档。',
        'no_measurements': '在您的记录中没有找到测量数据。',
        'viewing_family_lab_reports': '您正在查看家庭成员的检查报告。',
        'invalid_response': '无效的响应格式。预期为JSON，收到: ',
        'lab_document': '检验报告',
        'consult_document': '会诊记录',
        'lab': '检验',
        'consult': '会诊',
        'download': '下载',
        'view_document': '查看文档',

        // Settings page
        'security': '安全',
        'notifications': '通知',
        'general_preferences': '通用设置',
        'app_experience_settings': '这些设置控制您的应用体验',
        'change_password': '修改密码',
        'current_password': '当前密码',
        'new_password': '新密码',
        'confirm_new_password': '确认新密码',
        'update_password': '更新密码',
        'passwords_do_not_match': '新密码不匹配',
        'password_too_short': '密码必须至少8个字符',
        'settings_updated': '设置更新成功！',
        'notification_preferences': '通知偏好',
        'email_notifications': '电子邮件通知',
        'receive_general_notifications': '接收常规通知邮件',
        'appointment_reminders': '预约提醒',
        'receive_appointment_reminders': '在预约前接收提醒',
        'document_notifications': '文档通知',
        'receive_document_notifications': '当有新文档可用时接收通知',
        'marketing_communications': '营销通讯',
        'receive_marketing_emails': '接收关于服务和活动的促销邮件',
        'use_24hour_format': '使用24小时时间格式',

        // General for medical documents
        'unknown_date': '未知日期',
        'not_available': '暂无',
        'not_authenticated': '未认证。请登录。',
        'fetch_failed': '获取数据失败。',
        'fetch_error': '获取数据时发生错误。',
        'api_error': 'API返回错误。',
        'switch_back': '切换回我的个人资料',

        // Prescriptions
        'prescribed_by': '处方医生',
        'details': '详情',
        'comments': '备注',
        'no_prescriptions': '没有找到处方记录。',
        'viewing_family_prescriptions': '您正在查看家庭成员的处方记录。',

        // Immunizations
        'vaccine_type': '疫苗/预防类型',
        'date_administered': '接种日期',
        'administered_by': '接种医生',
        'next_due_date': '下次接种日期',
        'no_immunizations': '没有找到疫苗记录。',
        'viewing_family_immunizations': '您正在查看家庭成员的疫苗记录。',

        // Additional appointment page translations
        'network_error': '网络连接问题',
        'network_error_message': '无法连接到服务器，请检查您的网络连接并刷新页面。',
        'refresh': '刷新页面',
        'viewing_family_appointments': '您正在查看家庭成员的预约记录。',
        'family_appointments': '家庭成员预约记录',
        'error_loading_data': '加载数据错误',
        'upcoming_short': '即将到来',
        'past_short': '历史',
        'past_appointments_latest': '历史预约（最近10条）',
        'loading_appointments': '加载预约记录中...',
        'no_appointments_found': '没有找到预约记录',
        'no_upcoming_appointments': '没有即将到来的预约',
        'no_past_appointments': '没有历史预约记录',
        'invalid_patient_id': '无效的患者ID。请重新登录或联系客服。',
        'no_permission_appointments': '您没有权限查看此患者的预约信息。',
        'fetch_appointments_failed': '获取预约信息失败',

        // Additional profile page translations
        'request_access_title': '确认授权请求',
        'request_access_dear_user': '尊敬的用户：',
        'request_access_intro': '您正在请求临时访问 {name} 的敏感健康信息。请注意以下事项：',
        'request_access_point1_title': '需要验证：',
        'request_access_point1_text': '访问权限需要通过发送到 {name} 注册邮箱地址的电子邮件进行验证。这是一项保护患者隐私的安全措施。',
        'request_access_point2_title': '临时访问：',
        'request_access_point2_text': '如果验证成功，授予的访问权限是临时的，并可能受到 MMC 健康管理中心政策规定的时间限制或其他限制。',
        'request_access_point3_title': '保密责任：',
        'request_access_point3_text': '所有被访问的健康信息必须根据隐私法律法规以最严格的保密方式处理。禁止未经授权的披露。',
        'request_access_point4_title': '免责声明：',
        'request_access_point4_text': 'MMC 健康管理中心提供此访问机制是为了方便用户。尽管我们力求信息准确，但对于基于所查看信息做出的任何决定或任何滥用此访问功能的行为，MMC 健康管理中心不承担任何责任。',
        'request_access_confirmation': '点击"确认"，即表示您已理解这些条款，并希望继续进行验证过程以请求访问 {name} 的记录。',
        'request_access_sincerely': '此致，',
        'request_access_team': 'MMC 健康管理中心团队',
        'verification_title': '输入验证码',
        'verification_message': '请输入发送到{email}的验证码',
        'verification_code': '验证码',
        'verify': '验证并查看资料',
        'no_family_members': '目前没有家庭成员与您的账户关联。',
        'no_profile_data': '没有可用的个人资料数据。',
        'verification_error': '验证失败。请重试或联系客服。',
        'verification_success': '验证成功！正在重定向...',
        'profile_access_granted': '访问已授权。您现在可以查看此个人资料。',
        'profile_access_denied': '访问被拒绝。如果您认为这是错误，请联系客服。',
        'profile_access_expired': '访问已过期。请再次请求访问。',
        'profile_loading': '正在加载个人资料信息...',
        'request_verification': '请求验证码',
        'resend_code': '重新发送验证码',

        // Membership page
        'membership_info': '会员信息',
        'family_members_status': '家庭成员状态',
        'your_membership': '您的会员',
        'self': '本人',
        'family_member': '家庭成员',
        'no_membership': '无会员信息',
        'purchase_membership': '购买会员',
        'expired': '已过期',
        'active': '有效',
        'start_date': '开始日期',
        'end_date': '到期日期',
        'membership_type': '会员类型',
        'payment_amount': '支付金额',
        'renew_membership': '续订会员',
        'renew_early': '提前续订',
        'hide_history': '隐藏历史记录',
        'view_history': '查看历史记录',
        'membership_history': '会员历史记录',
        'date': '日期',
        'type': '类型',
        'amount': '金额',
        'status': '状态',
        'completed': '已完成',
        'loading_membership': '加载会员信息中...',
        'no_membership_records': '没有找到会员记录',
        'membership_details': '会员说明',
        'available_membership_types': '可选会员类型',
        'price': '价格',
        'no_membership_types': '暂无可用会员类型',
        'membership_benefits_note': '注意: 会员费不予退还。如对会员有任何疑问，请联系我们的办公室。',
        'select_membership_type': '请选择您想要购买的会员类型',
        'proceed_to_payment': '前往支付',
        'renewal_success': '会员续订成功！',
        'payment_process_note': '注意: 这将作为新会员购买处理。会员资格将从今天起一年内有效。',
        'authentication_failed': '认证失败，请重新登录',
        'server_invalid_format': '服务器返回格式错误',
        'server_invalid_structure': '服务器返回无效的数据结构',
        'fetch_membership_failed': '获取会员信息失败',
        'fetch_membership_failed_retry': '获取会员信息失败，请稍后再试',
        'fetch_membership_types_failed': '获取会员类型信息失败',
        'request_timeout': '请求超时，请检查您的网络连接后重试',
        'status_code': '状态码',
        'loading': '加载中',
        // Chatbot
        'chatbot': '健康助手',
        'chat_window_title': '健康助手',
        'chat_bot_name': 'MMC健康助手',
        'chat_greeting': '您好！我是MMC健康助手。今天我能帮您解答哪些健康管理问题？',
        'type_message': '请输入您的问题...',

        // Appointment Cancellation
        'cancel_appointment_confirmation': '确认取消预约',
        'cancel_appointment_warning': '请注意，预约必须提前至少2天取消。取消信息将通知诊所。',
        'cancel_policy': '取消政策：预约只能提前至少2天取消。逾期取消可能会产生费用。',
        'cancellation_lead_time': '取消预约需要提前2个工作日通知',
        'cancellation_email_notice': '关于您的取消预约，系统将自动发送通知邮件至诊所(<EMAIL>)。',
        'confirm_cancel': '确认取消',
        'cancelling': '取消中...',
        'appointment_cancelled_successfully': '预约已成功取消',
        'back': '返回',
        'close': '关闭',
        'cannot_cancel_late': '由于距离预约时间不足2天，此预约无法在线取消。请直接致电诊所讨论取消选项。',
        'auth_required': '需要身份验证。请重新登录。',
        'cancel_failed': '取消预约失败。请重试或直接联系诊所。',

        // Booking page
        'booking_intro': '便捷在线预约，轻松完成就诊安排。您可以选择服务类型或指定医生进行预约。',
        'book_by_service': '按服务类型预约',
        'book_by_doctor': '按医生预约',
        'select_service_type': '选择服务类型',
        'select_date_time_doctor': '选择日期与医生',
        'service_type_description': '请选择您需要的医疗服务类型。我们提供多种专业服务，由经验丰富的专科医生提供。',
        'service_no_description': '由我院专业医疗团队提供的专科医疗服务',

        // Service types
        'service_types': {
            '健康管理咨询': {
                en: 'Health Management Consultation',
                zh: '健康管理咨询',
                description: {
                    en: 'Comprehensive health assessment and personalized management plan',
                    zh: '全面评估您的健康状况，制定个性化健康管理方案，预防疾病发生'
                }
            },
            '健康管理（电话）': {
                en: 'Telehealth Consultation',
                zh: '远程健康咨询',
                description: {
                    en: 'Convenient phone consultation with our healthcare professionals',
                    zh: '便捷的电话咨询服务，无需亲临医院，即可获得专业医疗建议'
                }
            },
            '功能医学咨询': {
                en: 'Functional Medicine',
                zh: '功能医学评估',
                description: {
                    en: 'Address root causes of health issues with personalized functional approach',
                    zh: '通过功能医学方法找出健康问题根源，制定个性化治疗方案'
                }
            }
        },

        // Date selection
        'date_selection_description': '请选择您希望就诊的日期，然后从下方选择可用的时间段和医生。',
        'select_date': '选择就诊日期',
        'appointment_date': '预约日期',
        'date_notice': '注意：预约需至少提前2天进行',
        'loading_available_dates': '正在查询可预约日期...',
        'no_available_dates': '当前服务暂无可预约日期，请尝试其他服务类型',
        'available_dates_info': '已找到可预约日期，请选择合适的时间',

        // Navigation buttons
        'previous': '返回上一步',
        'next': '继续预约',

        // 预约系统相关翻译
        'book_appointment': '预约就诊服务',
        'booking_intro': '便捷在线预约，轻松完成就诊安排。您可以选择服务类型或指定医生进行预约。',
        // 按医生预约相关
        'doctor_selection_description': '请选择您偏好的医生，然后查看该医生的可预约时间。',
        'doctor_selection_prompt': '选择您的首选医生',
        'available_times': '可预约时间',
        'no_slots_available': '当前日期无可预约时段',
        'select_date_first': '请先选择日期',
        'selected_service': '所选服务',
        'selected_doctor': '所选医生',
        'selected_date': '所选日期',
        'selected_time': '所选时间',
        'available_slots': '可预约时段',

        // Chatbot related translations
        'mmc_wellness_chat': 'MMC健康助手',
        'chat_history': '聊天历史',
        'conversations': '对话记录',
        'new_chat': '新对话',
        'delete_conversation': '删除对话',
        'no_conversations': '暂无对话记录',
        'welcome_to_mmc_chat': '欢迎使用MMC健康助手',
        'chat_intro_expanded': '我可以提供有关常见健康问题、季节性健康提醒和MMC诊所服务的信息。有什么可以帮到您的吗？',
        'type_message': '请输入您的问题...',
        'send': '发送',
        'sending': '发送中...',
        'close': '关闭',
        'you': '您',
        'more_options': '更多选项',
        'delete': '删除',
        'chat_greeting': '您好！有什么健康相关的问题需要帮助吗？',
        'cannot_load_chat_history': '无法加载聊天历史。请稍后再试。',
        'conversation_deleted': '对话已成功删除',
        'failed_to_delete': '删除对话失败',
        'error_loading_conversation': '加载对话内容错误',
        'search_chats': '搜索对话',
    }
};

// Create the language context
const LanguageContext = createContext();

export const LanguageProvider = ({ children }) => {
    // Try to get language from localStorage, default to Chinese
    const [language, setLanguage] = useState(() => {
        const savedLanguage = localStorage.getItem('language');
        return savedLanguage || 'zh';
    });

    // Save language preference to localStorage when it changes
    useEffect(() => {
        localStorage.setItem('language', language);
    }, [language]);

    // Function to toggle language
    const toggleLanguage = () => {
        setLanguage(prevLang => prevLang === 'en' ? 'zh' : 'en');
    };

    // Translate function - UPDATED to handle placeholder replacement
    const t = (key, options = {}) => { // Accept options object
        const currentTranslations = translations[language];
        let translation = currentTranslations[key] || key; // Get base translation or key itself

        // Replace placeholders like {key} with values from options object
        if (typeof translation === 'string' && options) {
            Object.keys(options).forEach((optionKey) => {
                // Use a regex to replace all occurrences of {optionKey}
                const regex = new RegExp(`\{${optionKey}\}`, 'g');
                translation = translation.replace(regex, options[optionKey]);
            });
        }

        return translation;
    };

    // Context value
    const contextValue = {
        language,
        setLanguage,
        toggleLanguage,
        t
    };

    return (
        <LanguageContext.Provider value={contextValue}>
            {children}
        </LanguageContext.Provider>
    );
};

// Custom hook to use the language context
export const useLanguage = () => {
    const context = useContext(LanguageContext);
    if (!context) {
        throw new Error('useLanguage must be used within a LanguageProvider');
    }
    return context;
}; 