{"name": "chatbot-service", "version": "1.0.0", "description": "Backend service to handle chatbot interactions via OpenRouter API", "main": "server.js", "scripts": {"start": "node server.js", "crawler": "node health-news-crawler.js"}, "dependencies": {"express": "^4.19.2", "node-fetch": "^2.6.7", "dotenv": "^16.4.5", "cors": "^2.8.5", "mysql2": "^3.6.5", "axios": "^1.6.2", "cheerio": "^1.0.0-rc.12", "node-cron": "^3.0.2"}, "author": "", "license": "ISC"}