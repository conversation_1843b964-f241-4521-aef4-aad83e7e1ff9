const mysql = require('mysql2/promise');

async function checkAIStatsTable() {
  const pool = mysql.createPool({
    host: process.env.MAIN_DB_HOST || 'localhost',
    port: process.env.MAIN_DB_PORT || 3306,
    user: process.env.MAIN_DB_USER || 'root',
    password: process.env.MAIN_DB_PASSWORD || '',
    database: process.env.MAIN_DB_NAME || 'oscar'
  });
  
  try {
    console.log('=== 检查AI使用统计表 ===');
    
    // 检查表是否存在
    const [tables] = await pool.execute('SHOW TABLES LIKE "ai_usage_stats"');
    console.log('ai_usage_stats表存在:', tables.length > 0);
    
    if (tables.length > 0) {
      // 检查记录总数
      const [countResult] = await pool.execute('SELECT COUNT(*) as count FROM ai_usage_stats');
      console.log('记录总数:', countResult[0].count);
      
      // 检查最近的记录
      const [recentRecords] = await pool.execute('SELECT * FROM ai_usage_stats ORDER BY created_at DESC LIMIT 5');
      console.log('\n最近5条记录:');
      recentRecords.forEach((record, index) => {
        console.log(`${index + 1}. ${record.provider}/${record.service_type}/${record.model_name} - 调用:${record.total_calls} 成功:${record.successful_calls} 日期:${record.date_recorded}`);
      });
      
      // 按服务类型统计
      const [serviceStats] = await pool.execute(`
        SELECT 
          service_type, 
          COUNT(*) as record_count,
          SUM(total_calls) as total_calls,
          SUM(successful_calls) as successful_calls
        FROM ai_usage_stats 
        GROUP BY service_type
      `);
      
      console.log('\n按服务类型统计:');
      serviceStats.forEach(stat => {
        console.log(`${stat.service_type}: ${stat.record_count}条记录, ${stat.total_calls}次调用, ${stat.successful_calls}次成功`);
      });
      
      // 检查今天的数据
      const today = new Date().toISOString().split('T')[0];
      const [todayStats] = await pool.execute('SELECT * FROM ai_usage_stats WHERE date_recorded = ?', [today]);
      console.log(`\n今天(${today})的记录数:`, todayStats.length);
      
      if (todayStats.length > 0) {
        console.log('今天的记录:');
        todayStats.forEach((record, index) => {
          console.log(`${index + 1}. ${record.provider}/${record.service_type} - 调用:${record.total_calls} 成功:${record.successful_calls}`);
        });
      }
    }
    
    // 检查错误日志表
    const [errorTables] = await pool.execute('SHOW TABLES LIKE "ai_error_logs"');
    console.log('\nai_error_logs表存在:', errorTables.length > 0);
    
    if (errorTables.length > 0) {
      const [errorCount] = await pool.execute('SELECT COUNT(*) as count FROM ai_error_logs');
      console.log('错误记录总数:', errorCount[0].count);
    }
    
  } catch (error) {
    console.error('检查数据库时出错:', error.message);
  } finally {
    await pool.end();
  }
}

checkAIStatsTable().catch(console.error); 