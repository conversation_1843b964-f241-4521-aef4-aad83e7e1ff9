-- 创建患者健康摘要表
CREATE TABLE IF NOT EXISTS `patient_summaries` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `demographic_no` int(10) NOT NULL,
  `language` varchar(10) NOT NULL DEFAULT 'en',
  `summary_type` varchar(50) NOT NULL,
  `generated_summary` text NOT NULL,
  `last_appointment_date_covered` date DEFAULT NULL,
  `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_patient_summary` (`demographic_no`, `language`, `summary_type`),
  KEY `demographic_no_index` (`demographic_no`),
  KEY `summary_type_index` (`summary_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 添加注释
ALTER TABLE `patient_summaries` 
  COMMENT = '存储患者健康摘要，包括历史摘要和半年度摘要';
