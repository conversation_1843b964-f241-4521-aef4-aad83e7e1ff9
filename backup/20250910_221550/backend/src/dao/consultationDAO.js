console.log('>>> Loading backend/src/dao/consultationDAO.js...'); // <<< DEBUG LOG
const pool = require('../config/database');
const { createLogger } = require('../utils/logger');

const logger = createLogger('ConsultationDAO');

class ConsultationDAO {
    static async getByDemographicNo(demographicNo) {
        const query = `
            SELECT
                cr.requestId,
                cr.referalDate,
                cr.appointmentDate,
                cr.appointmentTime,
                cr.reason,
                cr.clinicalInfo,
                cr.status,
                cr.statusText,
                cr.urgency,
                cr.lastUpdateDate,
                ps.fName AS specialistFName,
                ps.lName AS specialistLName,
                ps.specType AS specialistType,
                cs.serviceDesc AS serviceDescription
            FROM consultationRequests cr
            LEFT JOIN professionalSpecialists ps ON cr.specId = ps.specId
            LEFT JOIN consultationServices cs ON cr.serviceId = cs.serviceId
            WHERE cr.demographicNo = ? 
              AND (ps.deleted IS NULL OR ps.deleted != 1) -- Ensure specialist is not deleted
            ORDER BY cr.referalDate DESC, cr.lastUpdateDate DESC;
        `;

        try {
            const startTime = Date.now();
            const [results] = await pool.query(query, [demographicNo]);
            const queryTime = Date.now() - startTime;

            if (queryTime > 1000) { // Log queries slower than 1 second
                logger.warn('Slow query detected: getByDemographicNo', {
                    durationMs: queryTime,
                    demographicNo
                });
            }

            return results;
        } catch (error) {
            logger.error('Error in ConsultationDAO.getByDemographicNo:', {
                errorMessage: error.message,
                stack: error.stack,
                demographicNo
            });
            // Re-throw the error to be handled by the service layer
            throw error;
        }
    }

    // Reuse or adapt the relationship check if needed for consultations
    // For now, assuming the service layer handles access validation logic
    static async checkFamilyRelationship(demoNo1, demoNo2) {
        const query = ` 
            SELECT 1 FROM relationships
            WHERE ((demographic_no = ? AND relation_demographic_no = ?)
                OR (demographic_no = ? AND relation_demographic_no = ?))
            AND deleted != '1' LIMIT 1
        `;

        try {
            const [results] = await pool.query(query, [demoNo1, demoNo2, demoNo2, demoNo1]);
            return results.length > 0;
        } catch (error) {
            logger.error('Error in ConsultationDAO.checkFamilyRelationship:', {
                errorMessage: error.message,
                stack: error.stack,
                demoNo1,
                demoNo2
            });
            throw error;
        }
    }
}

module.exports = ConsultationDAO; 