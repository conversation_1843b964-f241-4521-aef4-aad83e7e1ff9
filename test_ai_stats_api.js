const fetch = require('node-fetch');

async function testAIStatsAPI() {
    try {
        console.log('=== 测试AI使用统计API ===');
        
        // 测试 /api/admin/ai-usage-stats
        const usageResponse = await fetch('http://localhost:3000/api/admin/ai-usage-stats');
        const usageData = await usageResponse.json();
        
        console.log('AI使用统计响应:', {
            success: usageData.success,
            hasData: !!usageData.data,
            timelineLength: usageData.data?.timeline?.length || 0,
            summaryLength: usageData.data?.summary?.length || 0
        });
        
        if (usageData.data?.timeline) {
            console.log('\n=== Timeline数据示例 ===');
            usageData.data.timeline.slice(0, 3).forEach((item, index) => {
                console.log(`${index + 1}. ${item.provider}/${item.service_type} - 调用:${item.total_calls} 成功:${item.successful_calls}`);
            });
        }
        
        if (usageData.data?.summary) {
            console.log('\n=== Summary数据示例 ===');
            usageData.data.summary.slice(0, 3).forEach((item, index) => {
                console.log(`${index + 1}. ${item.provider}/${item.model_name} - 调用:${item.total_calls} 成功:${item.successful_calls}`);
            });
        }
        
        // 检查chatbot相关数据
        const chatbotTimelineData = usageData.data?.timeline?.filter(d => 
            d.service_type && (
                d.service_type.includes('chatbot') || 
                d.service_type === 'chatbot' ||
                d.service_type.startsWith('chatbot_') ||
                d.service_type === 'content_generation' // 也包含当前的分类
            )
        ) || [];
        
        console.log('\n=== Chatbot相关数据 ===');
        console.log(`找到 ${chatbotTimelineData.length} 条chatbot相关记录`);
        chatbotTimelineData.forEach((item, index) => {
            console.log(`${index + 1}. ${item.provider}/${item.service_type} - 调用:${item.total_calls} 成功:${item.successful_calls} 时间:${item.period}`);
        });
        
        // 测试 /api/admin/ai-dashboard
        console.log('\n=== 测试AI Dashboard API ===');
        const dashboardResponse = await fetch('http://localhost:3000/api/admin/ai-dashboard');
        const dashboardData = await dashboardResponse.json();
        
        console.log('AI Dashboard响应:', {
            success: dashboardData.success,
            todayTotalCalls: dashboardData.data?.today?.summary?.totalCalls || 0,
            todaySuccessfulCalls: dashboardData.data?.today?.summary?.successfulCalls || 0,
            todayProvidersCount: dashboardData.data?.today?.byProvider?.length || 0
        });
        
        if (dashboardData.data?.today?.byProvider) {
            console.log('\n=== Today Providers ===');
            dashboardData.data.today.byProvider.forEach((provider, index) => {
                console.log(`${index + 1}. ${provider.provider}/${provider.model_name} - 调用:${provider.total_calls} 成功:${provider.successful_calls}`);
            });
        }
        
    } catch (error) {
        console.error('测试API时出错:', error.message);
    }
}

testAIStatsAPI().catch(console.error); 