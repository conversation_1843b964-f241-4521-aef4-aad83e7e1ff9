const mysql = require('mysql2/promise');

// Create database connection pool - connecting to main server database
const pool = mysql.createPool({
    host: process.env.MAIN_DB_HOST || '**************',
    port: process.env.MAIN_DB_PORT || 3306,
    user: process.env.MAIN_DB_USER || 'root',
    password: process.env.MAIN_DB_PASSWORD || 'Z2Rh6VGr7DE=',
    database: process.env.MAIN_DB_NAME || 'oscar',
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
});

// Utility function to record AI usage
async function recordUsage(usageData) {
    try {
        const {
            provider,
            serviceType,
            endpoint,
            modelName,
            success,
            responseTimeMs,
            cacheHit,
            estimatedCostUsd = 0,
            tokenUsage = null
        } = usageData;

        const now = new Date();
        const dateRecorded = now.toISOString().split('T')[0]; // YYYY-MM-DD
        const hourRecorded = now.getHours();

        // Insert or update statistics (compatible with existing table structure)
        const query = `
            INSERT INTO ai_usage_stats (
                provider, service_type, model_name, date_recorded, hour_recorded,
                total_calls, successful_calls, failed_calls, total_response_time_ms,
                avg_response_time_ms, cache_hits, estimated_cost_usd
            ) VALUES (?, ?, ?, ?, ?, 1, ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
                total_calls = total_calls + 1,
                successful_calls = successful_calls + ?,
                failed_calls = failed_calls + ?,
                total_response_time_ms = total_response_time_ms + ?,
                avg_response_time_ms = total_response_time_ms / total_calls,
                cache_hits = cache_hits + ?,
                estimated_cost_usd = estimated_cost_usd + ?
        `;

        const values = [
            provider,
            serviceType,
            modelName,
            dateRecorded,
            hourRecorded,
            success ? 1 : 0,
            success ? 0 : 1,
            responseTimeMs || 0,
            responseTimeMs || 0,
            cacheHit ? 1 : 0,
            estimatedCostUsd || 0,
            // For UPDATE part
            success ? 1 : 0,
            success ? 0 : 1,
            responseTimeMs || 0,
            cacheHit ? 1 : 0,
            estimatedCostUsd || 0
        ];

        const connection = await pool.getConnection();
        await connection.query(query, values);
        connection.release();

        console.log(`AI usage recorded: ${provider}/${serviceType}/${modelName} - ${success ? 'SUCCESS' : 'FAILED'}`);
    } catch (error) {
        console.error('Error recording AI usage:', error);
    }
}

// Utility function to record AI errors
async function recordError(errorData) {
    try {
        const {
            provider,
            serviceType,
            errorType,
            errorMessage,
            errorCode,
            requestSummary,
            responseSummary
        } = errorData;

        const query = `
            INSERT INTO ai_errors (
                provider, service_type, error_type, error_message, error_code,
                request_summary, response_summary, error_timestamp
            ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
        `;

        const values = [
            provider,
            serviceType,
            errorType,
            errorMessage?.substring(0, 500) || 'Unknown error',
            errorCode,
            requestSummary?.substring(0, 1000),
            responseSummary?.substring(0, 2000)
        ];

        const connection = await pool.getConnection();
        await connection.query(query, values);
        connection.release();

        console.log(`AI error recorded: ${provider}/${serviceType} - ${errorType}`);
    } catch (error) {
        console.error('Error recording AI error:', error);
    }
}

module.exports = {
    recordUsage,
    recordError
}; 