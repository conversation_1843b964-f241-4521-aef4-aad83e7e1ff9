server {
    listen 3001;
    server_name app.mmcwellness.ca localhost;

    # 添加DNS解析器，用于解析Docker内部主机名
    resolver 127.0.0.11 valid=30s;

    # 添加CORS头
    add_header 'Access-Control-Allow-Origin' 'https://**************:8443' always;
    add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
    add_header 'Access-Control-Allow-Headers' 'Authorization, Content-Type, Accept' always;
    add_header 'Access-Control-Allow-Credentials' 'true' always;

    # 添加更好的缓存控制头
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    add_header Pragma "no-cache";
    add_header Expires "0";

    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
        
        # HTML文件不要缓存
        location ~* \.(?:html)$ {
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }
        
        # JS/CSS文件可以缓存，但需要验证
        location ~* \.(?:js|css)$ {
            add_header Cache-Control "public, max-age=86400, must-revalidate";
        }
        
        # 图片、字体等静态资源可以长时间缓存
        location ~* \.(?:jpg|jpeg|gif|png|ico|svg|woff|woff2|ttf|eot)$ {
            add_header Cache-Control "public, max-age=2592000";
        }
    }

    # Specific proxy for chatbot API routes
    location ~ ^/api/chat(/|$) {
        set $chatbot_upstream http://chatbot-service:3002;
        proxy_pass $chatbot_upstream$request_uri;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Headers needed for streaming
        proxy_http_version 1.1;
        proxy_set_header Connection ""; # Clear connection header for streaming
        proxy_buffering off;            # Disable buffering for streaming
        proxy_cache off;                # Do not cache streamed responses
        proxy_read_timeout 90s;         # Increase timeout for potentially long streams
        proxy_send_timeout 90s;
    }

    # Proxy for uploaded files (static files served by backend)
    location /uploads {
        proxy_pass http://backend:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Cache uploaded images
        proxy_cache_valid 200 1d;
        add_header Cache-Control "public, max-age=86400";
    }

    # Generic API proxy (must come AFTER specific proxies)
    location /api {
        proxy_pass http://backend:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}