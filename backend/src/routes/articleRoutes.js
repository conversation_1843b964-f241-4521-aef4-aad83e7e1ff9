const express = require('express');
const router = express.Router();
const articleController = require('../controllers/articleController');
const auth = require('../middleware/auth'); // Correct middleware import path

// GET / - List all articles (protected)
router.get('/', auth, articleController.listArticles);

// GET /categories - List available article categories (protected)
router.get('/categories', auth, articleController.listCategories);

// GET /:filename - Get content of a specific article (protected)
// Ensure :filename matches the allowed characters in the controller's sanitize function
router.get('/:filename', auth, articleController.getArticle);

// GET /assets/* - Get asset related to articles (images, etc.) (protected)
// Assuming asset path is captured correctly by * or specific handling in controller
router.get('/assets/*', auth, articleController.getAsset);

module.exports = router; 