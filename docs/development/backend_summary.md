# Backend Documentation

This document provides an overview of the backend structure and key components of the MMC Wellness application.

## Entry Point (`backend/src/index.js`)

The main entry point for the backend application. It initializes an Express.js server and sets up:

*   **Middleware:** CORS handling, enhanced request/response logging, JSON body parsing.
*   **Routes:** Mounts various route handlers under `/api/*` prefixes.
*   **Health Check:** Provides a `/health` endpoint.
*   **Error Handling:** Basic global error handler.
*   **Server Startup:** Listens on the port defined by `process.env.PORT` (default 3000).

## API Routes (`backend/src/routes/`)

Routes define the API endpoints and map them to controller functions. Authentication middleware (`auth` or `authenticate`) is applied to protected routes.

*   **`auth.js`:** Handles user registration, login, email verification, password reset, account linking, profile retrieval, and membership management.
*   **`appointmentRoutes.js`:** Manages appointment retrieval (user, viewed, guest), lookup, cancellation, booking, availability checks, and status updates.
*   **`providerRoutes.js`:** Fetches lists of all providers, doctors, and specific provider availability.
*   **`relationshipRoutes.js`:** <PERSON>les logic for switching views between related patient accounts (e.g., family members), including initiation and verification of access codes. **Also defines the `GET /api/relationships/family` endpoint to retrieve the current user's family members.**
*   **`immunizationRoutes.js`:** Retrieves immunization records for a specific patient.
*   **`prescriptionRoutes.js`:** Retrieves prescription records for a specific patient.
*   **`membershipRoutes.js`:** Gets details about membership types.
*   **`labResultRoutes.js`:** Fetches lab reports and measurement data for patients (self or family) and specific report content.
*   **`medicalRecordRoutes.js`:** Retrieves medical records for a specific patient.

## Controllers (`backend/src/controllers/`)

Controllers contain the business logic for handling requests received by the routes.

*   **`appointmentController.js`:** Implements logic for fetching, creating, updating, cancelling, and booking appointments. Includes functions for checking doctor availability, handling guest/lookup access, and formatting appointment data (using `dayjs` for timezones). Interacts heavily with the `appointment`, `provider`, and `user_auth` tables. Contains helper functions for status determination and time conversion.
*   **`providerController.js`:** Implements logic for fetching lists of providers and doctors from the `provider` table. Includes a complex function (`getProviderAvailability`) to determine available appointment slots based on `scheduledate`, `scheduletemplate`, and existing `appointment` data, using `dayjs` for date/time calculations.
*   **`authController.js`:** Handles core authentication and user management logic. Interacts with `user_auth` and `demographic` tables via the `User` model. Manages user registration, login, password hashing (`bcryptjs`), JWT generation (`jsonwebtoken`), email verification (links and codes), password resets, account linking (including handling multiple patients with the same email), profile retrieval, and membership renewal. Uses `nodemailer` for sending emails. Implements in-memory storage for verification codes (`emailVerificationCodes`) and verified emails (`verifiedEmails`) - **Note:** This should be replaced with a persistent store like Redis in production.
    *   **`getProfile`:** Fetches basic user info (`user_auth`) and linked demographic info for the *authenticated user only*.
    *   **`getViewedProfile`:** Fetches demographic info for a specific `demographicNo` provided in the URL (`/api/auth/profile/:demographicNo`). **Includes permission checks to ensure the logged-in user is viewing their own profile, a linked family member (permanent relationship), or has recently verified temporary access via the `view_verification_codes` table (within 30 minutes).** Returns `{ success: true, demographicInfo: {...} }` on success.
*   **`relationshipController.js`:** Manages the temporary access switching between family members. Includes functions to initiate the switch (sending a verification code via email using `nodemailer` to the target member), verify the entered code (comparing against a hashed code stored in `view_verification_codes` table using `bcryptjs`), and check if access was recently granted (within a 30-minute window). Uses helper functions for code generation and hashing. **Also includes `getFamily` function to retrieve the logged-in user's family members list by calling `User.getFamilyMembers`.**
*   **`immunizationController.js`:** Fetches immunization records (`preventions` table) for a specific patient (`demographicNo`). Includes permission checks to ensure the logged-in user is viewing their own records or those of a linked family member (checks `relationships` table).
*   **`prescriptionController.js`:** Fetches prescription records (`prescription` table, joined with `provider`) for a specific patient (`demographicNo`). Includes permission checks similar to `immunizationController`. Attempts to parse medication details from the `textView` field.
*   **`membershipController.js`:** Fetches details about membership types from the `billingservice` table, either all predefined types or a specific type by its code.
*   **`labResultController.js`:** Handles fetching lab/consult documents and measurement data. 
    *   `getPatientLabResults` & `getPatientLabResultsByDemographic`: Retrieve document metadata (`document`, `ctl_document` tables) for a user or their family member, filtering by type ('lab', 'consult') and ensuring the linked file exists and is not empty (checks `/OscarDocument/oscar/document` and `/OscarDocument/oscar/document_cache` paths using `fs`). Includes permission checks.
    *   `getLabReportContent`: Streams the content of a specific document file after verifying permissions (user owns report or is family member). Sets appropriate `Content-Type` and `Content-Disposition` headers.
    *   `getPatientMeasurements` & `getPatientMeasurementsByDemographic`: Fetches and groups measurement data (`measurements` table) by type for a user or their family member. Includes permission checks.
*   **`medicalRecordController.js`:** Fetches medical notes (`casemgmt_note` table) and basic demographic information (`demographic` table) for a specific patient (`demographicNo`). **Note:** This controller currently lacks permission checks to verify if the logged-in user is authorized to view the requested records.

## Middleware (`backend/src/middleware/`)

Contains middleware functions used in the Express application.

*   **`auth.js`:** An authentication middleware that verifies a JWT token. It looks for the token first in the `Authorization: Bearer <token>` header, and falls back to checking a `token` query parameter. If valid, it fetches the user using `User.findById` and attaches the user object to `req.user`.
*   **`authenticate.js`:** A stricter authentication middleware that *only* checks the `Authorization: Bearer <token>` header. It provides more detailed error responses (e.g., `TOKEN_EXPIRED`, `INVALID_TOKEN`) compared to `auth.js`. It also fetches the full user data using `User.findById` and attaches it to `req.user`. It includes `console.log` statements for debugging.

## Database (`backend/src/database/`)

Contains database-related files, primarily schema definitions or migrations.

*   **`email_verification.sql`:** SQL script to modify the `user_auth` table. It adds columns for email verification (`is_verified`, `verification_token`) and password reset (`reset_token`, `reset_token_expires`). It also defaults existing users to `is_verified = TRUE` and adds indexes on the new token columns.

## Models (`backend/src/models/`)

Contains data models, typically encapsulating database interactions.

*   **`user.js`:** A large class (`User`) with static methods for interacting primarily with the `user_auth`, `demographic`, `relationships`, `billingmaster`, `billingservice`, `admission`, `program_client_restriction`, and `view_verification_codes` tables. 
    *   Provides methods for finding users/demographics (`findById`, `findByEmail`, `findByDemographicNo`, `getDemographicInfo`, `findAllDemographicsByEmail`, `getDemographicByHinAndDOB`).
    *   Handles user creation (`create`), demographic creation (`createDemographic`), and linking (`linkDemographic`, `linkOscarPatient`).
    *   Manages password hashing (`hashPassword`, `verifyPassword`) using `bcryptjs`.
    *   Handles email verification (`verifyEmail`, `regenerateVerificationToken`, `isVerified`, `generateVerificationToken`).
    *   Manages password reset tokens (`createPasswordResetToken`, `verifyPasswordResetToken`, `resetPassword`).
    *   Retrieves membership information (`getMembershipInfo`) and history, calculating expiry dates.
    *   Checks email existence across `user_auth` and `demographic` (`checkEmailExists`).
    *   Sets up patient program entries (`setupPatientProgram`) in `admission` and `program_client_restriction`.
    *   Retrieves family members (`getFamilyMembers`) from the `relationships` table (checking both directions) and includes a check for recent view access verification from `view_verification_codes`.

## Config (`backend/src/config/`)

Contains configuration files.

*   **`database.js`:** Configures and exports a MySQL connection pool using `mysql2/promise`. It reads database connection details (host, port, user, password, database name) from environment variables (`process.env`).

## Utils (`backend/src/utils/`)

This directory is currently empty. It would typically contain helper functions or utility modules.

## Root Directory Files (`backend/`)

Contains top-level configuration and scripts for the backend service.

*   **`package.json`:** Defines the Node.js project metadata, scripts (`start`, `dev`, `test`), and dependencies.
    *   **Dependencies:** `bcryptjs`, `cors`, `dayjs`, `dotenv`, `express`, `jsonwebtoken`, `mysql2`, `nodemailer`, `validator`.
    *   **Dev Dependencies:** `jest`, `nodemon`.
*   **`.env`:** Contains environment variables used for configuration. Includes settings for:
    *   Server port (`PORT`).
    *   Database connection (`DB_HOST`, `DB_PORT`, `DB_USER`, `DB_PASSWORD`, `DB_NAME`).
    *   JWT settings (`JWT_SECRET`, `JWT_EXPIRES_IN`).
    *   Frontend URL (`FRONTEND_URL`).
    *   Email service (`EMAIL_HOST`, `EMAIL_PORT`, `EMAIL_SECURE`, `EMAIL_USER`, `EMAIL_PASS`, `EMAIL_FROM`).
    *   OpenRouter API key (`OPENROUTER_API_KEY`).
*   **`test-email.js`:** A standalone script to test email sending functionality using `nodemailer`. It reads credentials from `.env`, creates a transporter for Gmail (smtp.gmail.com, port 587), verifies the connection, and sends a test email to `<EMAIL>`. This is likely used for debugging email setup.

## Tests (`backend/src/tests/`)

This directory exists but its contents were not examined in this review. It would typically contain automated tests (e.g., using Jest as indicated in `package.json`).

## Deployment (Docker)

The backend is deployed as a Docker container orchestrated by `docker-compose.yml`.

*   **Dockerfile (`./Dockerfile`):**
    *   Uses `node:20-alpine` base image.
    *   Installs `curl` for health checks.
    *   Copies `backend/package*.json`, runs `npm install`.
    *   Copies `backend/src` code.
    *   **Note:** Appends a simple text-based `/health` endpoint to `src/index.js`, potentially conflicting with the JSON-based one in the original source.
    *   Exposes port 3000.
    *   Starts the application using `npm start` (`node src/index.js`).
*   **Docker Compose (`./docker-compose.yml`):**
    *   Defines the `backend` service.
    *   Builds the image using the root `Dockerfile`.
    *   Maps port 3000:3000.
    *   Mounts `/home/<USER>/open-osp/volumes/OscarDocument` to `/OscarDocument` in the container.
    *   Injects environment variables (DB credentials, JWT, Email, URLs, Timezone) directly, overriding the source `.env` file.
    *   Connects to `webapp-network` and the external `open-osp_back-tier` network (likely for the database).
    *   Sets `restart: always`.
    *   Includes a health check (`curl http://localhost:3000/health`).
    *   The `frontend` service depends on the `backend` service being healthy.

*(Will add summaries for remaining files)* 