-- Create a table for referral codes
CREATE TABLE IF NOT EXISTS `referral_codes` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `demographic_no` INT NOT NULL,
  `referral_code` VARCHAR(10) NOT NULL UNIQUE,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `is_active` BOOLEAN DEFAULT TRUE,
  FOREIGN KEY (`demographic_no`) REFERENCES `demographic` (`demographic_no`) ON DELETE CASCADE,
  INDEX (`referral_code`)
);

-- Create a table for tracking referrals
CREATE TABLE IF NOT EXISTS `referrals` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `referrer_demographic_no` INT NOT NULL,
  `referred_demographic_no` INT NOT NULL,
  `referral_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `status` ENUM('pending', 'completed', 'rewarded') DEFAULT 'pending',
  `reward_points` INT DEFAULT 0,
  FOR<PERSON><PERSON><PERSON> KEY (`referrer_demographic_no`) REFERENCES `demographic` (`demographic_no`) ON DELETE CASCADE,
  FOREI<PERSON><PERSON> KEY (`referred_demographic_no`) REFERENCES `demographic` (`demographic_no`) ON DELETE CASCADE,
  UNIQUE KEY (`referred_demographic_no`)
);

-- Create a table for reward points history
CREATE TABLE IF NOT EXISTS `reward_points` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `demographic_no` INT NOT NULL,
  `points` INT NOT NULL,
  `description` VARCHAR(255) NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`demographic_no`) REFERENCES `demographic` (`demographic_no`) ON DELETE CASCADE,
  INDEX (`demographic_no`)
); 