FROM node:18-alpine

WORKDIR /app

# 安装cron
RUN apk add --no-cache dcron tzdata

# 设置时区为温哥华
ENV TZ=America/Vancouver

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm install
RUN npm install node-cron

# 创建日志目录
RUN mkdir -p /app/logs

# 复制源代码
COPY . .

# 创建cron作业
RUN echo "0 3 * * * cd /app && node src/scripts/auto-fetch-medical-videos.js >> /app/logs/cron-medical-videos.log 2>&1" > /etc/crontabs/root

# 设置入口点
CMD ["sh", "-c", "crond -f -d 8"] 