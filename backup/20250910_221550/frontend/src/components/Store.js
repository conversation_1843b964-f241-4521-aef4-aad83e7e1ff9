import React, { useState, useEffect } from 'react';
import {
    Container,
    Typography,
    Grid,
    Card,
    CardContent,
    CardMedia,
    CardActions,
    Button,
    Chip,
    Box,
    Tabs,
    Tab,
    TextField,
    InputAdornment,
    IconButton,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Divider,
    List,
    ListItem,
    ListItemText,
    Badge,
    CircularProgress,
    Alert,
    Skeleton
} from '@mui/material';
import {
    Search as SearchIcon,
    ShoppingCart as ShoppingCartIcon,
    Add as AddIcon,
    Remove as RemoveIcon,
    Store as StoreIcon,
    LocalOffer as OfferIcon,
    Schedule as ScheduleIcon,
    Star as StarIcon
} from '@mui/icons-material';
import { useLanguage } from '../context/LanguageContext';
import { useView } from '../context/ViewContext';
import * as storeService from '../services/storeService';

const Store = () => {
    const { t, language } = useLanguage();
    const { viewingDemographicNo } = useView();
    
    const [selectedCategory, setSelectedCategory] = useState('all');
    const [searchTerm, setSearchTerm] = useState('');
    const [products, setProducts] = useState([]);
    const [categories, setCategories] = useState([]);
    const [cart, setCart] = useState([]);
    const [selectedProduct, setSelectedProduct] = useState(null);
    const [productDialogOpen, setProductDialogOpen] = useState(false);
    const [cartDialogOpen, setCartDialogOpen] = useState(false);
    
    // Loading states
    const [loading, setLoading] = useState(true);
    const [categoriesLoading, setCategoriesLoading] = useState(true);
    const [error, setError] = useState(null);

    // 获取商品分类
    useEffect(() => {
        const fetchCategories = async () => {
            try {
                setCategoriesLoading(true);
                const response = await storeService.getCategories();
                if (response.success) {
                    // 添加"全部商品"选项
                    const allCategories = [
                        { id: 'all', name_en: 'All Products', name_zh: '全部商品' },
                        ...response.categories
                    ];
                    setCategories(allCategories);
                } else {
                    console.error('Failed to fetch categories:', response.message);
                    // 使用默认分类
                    setCategories([
                        { id: 'all', name_en: 'All Products', name_zh: '全部商品' }
                    ]);
                }
            } catch (error) {
                console.error('Error fetching categories:', error);
                // 使用默认分类
                setCategories([
                    { id: 'all', name_en: 'All Products', name_zh: '全部商品' }
                ]);
            } finally {
                setCategoriesLoading(false);
            }
        };

        fetchCategories();
    }, []);

    // 获取商品列表
    useEffect(() => {
        const fetchProducts = async () => {
            try {
                setLoading(true);
                setError(null);
                
                const filters = {};
                if (selectedCategory !== 'all') {
                    filters.category_id = selectedCategory;
                }
                if (searchTerm) {
                    filters.search = searchTerm;
                }

                const response = await storeService.getProducts(filters);
                if (response.success) {
                    setProducts(response.products || []);
                } else {
                    setError(response.message || t('failed_to_load_products'));
                    setProducts([]);
                }
            } catch (error) {
                console.error('Error fetching products:', error);
                setError(error.message || t('failed_to_load_products'));
                setProducts([]);
            } finally {
                setLoading(false);
            }
        };

        // 延迟搜索以避免过多API调用
        const timeoutId = setTimeout(fetchProducts, 300);
        return () => clearTimeout(timeoutId);
    }, [selectedCategory, searchTerm, t]);

    // 过滤商品（现在主要在服务器端完成，这里作为备用）
    const filteredProducts = products.filter(product => {
        const matchesCategory = selectedCategory === 'all' || product.category_id === selectedCategory;
        const matchesSearch = searchTerm === '' || 
            product[`name_${language}`]?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            product[`description_${language}`]?.toLowerCase().includes(searchTerm.toLowerCase());
        return matchesCategory && matchesSearch;
    });

    // 添加到购物车
    const addToCart = (product, quantity = 1) => {
        setCart(prevCart => {
            const existingItem = prevCart.find(item => item.id === product.id);
            if (existingItem) {
                return prevCart.map(item =>
                    item.id === product.id
                        ? { ...item, quantity: item.quantity + quantity }
                        : item
                );
            } else {
                return [...prevCart, { ...product, quantity }];
            }
        });
    };

    // 从购物车移除
    const removeFromCart = (productId) => {
        setCart(prevCart => prevCart.filter(item => item.id !== productId));
    };

    // 更新购物车数量
    const updateCartQuantity = (productId, quantity) => {
        if (quantity <= 0) {
            removeFromCart(productId);
        } else {
            setCart(prevCart =>
                prevCart.map(item =>
                    item.id === productId ? { ...item, quantity } : item
                )
            );
        }
    };

    // 计算购物车总价
    const cartTotal = cart.reduce((total, item) => total + (item.price * item.quantity), 0);

    // 处理商品详情
    const handleProductClick = (product) => {
        setSelectedProduct(product);
        setProductDialogOpen(true);
    };

    // 格式化价格
    const formatPrice = (price) => {
        return new Intl.NumberFormat('en-CA', {
            style: 'currency',
            currency: 'CAD'
        }).format(price);
    };

    // 格式化功能列表
    const formatFeatures = (features) => {
        if (!features) return [];
        return features.split('\n').filter(feature => feature.trim() !== '');
    };

    // 获取商品图片URL
    const getProductImageUrl = (product) => {
        if (product.image_url) {
            // 如果是完整的HTTP URL，直接使用
            if (product.image_url.startsWith('http')) {
                return product.image_url;
            }
            // 如果是相对路径，构建完整URL
            if (product.image_url.startsWith('/uploads/') || product.image_url.startsWith('/assets/')) {
                const baseUrl = process.env.REACT_APP_API_URL || 'https://app-backend.mmcwellness.ca';
                return `${baseUrl}${product.image_url}`;
            }
        }
        // 使用占位符图片
        return `https://via.placeholder.com/300x200/e3f2fd/1976d2?text=${encodeURIComponent(product[`name_${language}`] || 'Product')}`;
    };

    // 渲染商品骨架屏
    const renderProductSkeleton = () => (
        <Grid container spacing={3}>
            {[1, 2, 3, 4, 5, 6].map((item) => (
                <Grid item xs={12} sm={6} md={4} key={item}>
                    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                        <Skeleton variant="rectangular" height={200} />
                        <CardContent sx={{ flexGrow: 1 }}>
                            <Skeleton variant="text" sx={{ fontSize: '1.5rem', mb: 1 }} />
                            <Skeleton variant="text" sx={{ mb: 2 }} />
                            <Skeleton variant="text" sx={{ mb: 2 }} />
                            <Skeleton variant="text" width="60%" />
                        </CardContent>
                        <CardActions>
                            <Skeleton variant="rectangular" width="100%" height={36} />
                        </CardActions>
                    </Card>
                </Grid>
            ))}
        </Grid>
    );

    return (
        <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
            {/* 页面标题 */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <StoreIcon sx={{ mr: 2, fontSize: 40, color: 'primary.main' }} />
                    <Typography variant="h4" component="h1">
                        {t('mmc_health_store') || 'MMC健康商城'}
                    </Typography>
                </Box>
                
                {/* 购物车按钮 */}
                <IconButton 
                    color="primary" 
                    onClick={() => setCartDialogOpen(true)}
                    sx={{ position: 'relative' }}
                >
                    <Badge badgeContent={cart.length} color="error">
                        <ShoppingCartIcon fontSize="large" />
                    </Badge>
                </IconButton>
            </Box>

            {/* 搜索栏 */}
            <Box sx={{ mb: 3 }}>
                <TextField
                    fullWidth
                    placeholder={t('search_products') || '搜索商品...'}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    InputProps={{
                        startAdornment: (
                            <InputAdornment position="start">
                                <SearchIcon />
                            </InputAdornment>
                        ),
                    }}
                />
            </Box>

            {/* 分类标签 */}
            <Box sx={{ mb: 4 }}>
                {categoriesLoading ? (
                    <Skeleton variant="rectangular" height={48} />
                ) : (
                    <Tabs
                        value={selectedCategory}
                        onChange={(e, newValue) => setSelectedCategory(newValue)}
                        variant="scrollable"
                        scrollButtons="auto"
                    >
                        {categories.map((category) => (
                            <Tab
                                key={category.id}
                                label={category[`name_${language}`]}
                                value={category.id}
                            />
                        ))}
                    </Tabs>
                )}
            </Box>

            {/* 错误提示 */}
            {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                </Alert>
            )}

            {/* 商品网格 */}
            {loading ? (
                renderProductSkeleton()
            ) : filteredProducts.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 8 }}>
                    <StoreIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                    <Typography variant="h6" color="text.secondary">
                        {t('no_products_found') || '暂无商品'}
                    </Typography>
                </Box>
            ) : (
                <Grid container spacing={3}>
                    {filteredProducts.map((product) => (
                        <Grid item xs={12} sm={6} md={4} key={product.id}>
                            <Card 
                                sx={{ 
                                    height: '100%', 
                                    display: 'flex', 
                                    flexDirection: 'column',
                                    cursor: 'pointer',
                                    '&:hover': {
                                        boxShadow: 6,
                                        transform: 'translateY(-2px)',
                                        transition: 'all 0.3s ease-in-out'
                                    }
                                }}
                                onClick={() => handleProductClick(product)}
                            >
                                <CardMedia
                                    component="img"
                                    height="200"
                                    image={getProductImageUrl(product)}
                                    alt={product[`name_${language}`]}
                                    sx={{ objectFit: 'cover' }}
                                />
                                <CardContent sx={{ flexGrow: 1 }}>
                                    <Typography gutterBottom variant="h6" component="h2">
                                        {product[`name_${language}`]}
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                        {product[`description_${language}`]}
                                    </Typography>
                                    
                                    {/* 标签 */}
                                    <Box sx={{ mb: 2 }}>
                                        {product.is_subscription && (
                                            <Chip 
                                                label={t('subscription') || '订阅服务'} 
                                                color="primary" 
                                                size="small" 
                                                sx={{ mr: 1 }}
                                            />
                                        )}
                                        {product.duration_days && (
                                            <Chip 
                                                icon={<ScheduleIcon />}
                                                label={`${product.duration_days} ${t('days') || '天'}`}
                                                variant="outlined" 
                                                size="small"
                                            />
                                        )}
                                    </Box>

                                    <Typography variant="h5" color="primary" fontWeight="bold">
                                        {formatPrice(product.price)}
                                    </Typography>
                                </CardContent>
                                <CardActions>
                                    <Button
                                        fullWidth
                                        variant="contained"
                                        startIcon={<AddIcon />}
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            addToCart(product);
                                        }}
                                    >
                                        {t('add_to_cart') || '加入购物车'}
                                    </Button>
                                </CardActions>
                            </Card>
                        </Grid>
                    ))}
                </Grid>
            )}

            {/* 商品详情对话框 */}
            <Dialog 
                open={productDialogOpen} 
                onClose={() => setProductDialogOpen(false)}
                maxWidth="md"
                fullWidth
            >
                {selectedProduct && (
                    <>
                        <DialogTitle>
                            {selectedProduct[`name_${language}`]}
                        </DialogTitle>
                        <DialogContent>
                            <Box sx={{ mb: 3 }}>
                                <img
                                    src={getProductImageUrl(selectedProduct)}
                                    alt={selectedProduct[`name_${language}`]}
                                    style={{ width: '100%', maxHeight: '300px', objectFit: 'cover' }}
                                />
                            </Box>
                            
                            <Typography variant="body1" paragraph>
                                {selectedProduct[`description_${language}`]}
                            </Typography>

                            {selectedProduct[`features_${language}`] && (
                                <>
                                    <Typography variant="h6" gutterBottom>
                                        {t('features') || '功能特点'}
                                    </Typography>
                                    <List>
                                        {formatFeatures(selectedProduct[`features_${language}`]).map((feature, index) => (
                                            <ListItem key={index} sx={{ py: 0.5 }}>
                                                <ListItemText primary={feature} />
                                            </ListItem>
                                        ))}
                                    </List>
                                </>
                            )}

                            <Divider sx={{ my: 2 }} />
                            
                            <Typography variant="h4" color="primary" fontWeight="bold">
                                {formatPrice(selectedProduct.price)}
                            </Typography>
                        </DialogContent>
                        <DialogActions>
                            <Button onClick={() => setProductDialogOpen(false)}>
                                {t('close') || '关闭'}
                            </Button>
                            <Button 
                                variant="contained" 
                                startIcon={<AddIcon />}
                                onClick={() => {
                                    addToCart(selectedProduct);
                                    setProductDialogOpen(false);
                                }}
                            >
                                {t('add_to_cart') || '加入购物车'}
                            </Button>
                        </DialogActions>
                    </>
                )}
            </Dialog>

            {/* 购物车对话框 */}
            <Dialog 
                open={cartDialogOpen} 
                onClose={() => setCartDialogOpen(false)}
                maxWidth="sm"
                fullWidth
            >
                <DialogTitle>
                    {t('shopping_cart') || '购物车'} ({cart.length})
                </DialogTitle>
                <DialogContent>
                    {cart.length === 0 ? (
                        <Typography variant="body1" textAlign="center" py={4}>
                            {t('cart_empty') || '购物车为空'}
                        </Typography>
                    ) : (
                        <>
                            {cart.map((item) => (
                                <Box key={item.id} sx={{ display: 'flex', alignItems: 'center', mb: 2, p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                                    <Box sx={{ flexGrow: 1 }}>
                                        <Typography variant="subtitle1">
                                            {item[`name_${language}`]}
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary">
                                            {formatPrice(item.price)}
                                        </Typography>
                                    </Box>
                                    <Box sx={{ display: 'flex', alignItems: 'center', ml: 2 }}>
                                        <IconButton 
                                            size="small"
                                            onClick={() => updateCartQuantity(item.id, item.quantity - 1)}
                                        >
                                            <RemoveIcon />
                                        </IconButton>
                                        <Typography sx={{ mx: 2, minWidth: 20, textAlign: 'center' }}>
                                            {item.quantity}
                                        </Typography>
                                        <IconButton 
                                            size="small"
                                            onClick={() => updateCartQuantity(item.id, item.quantity + 1)}
                                        >
                                            <AddIcon />
                                        </IconButton>
                                    </Box>
                                </Box>
                            ))}
                            <Divider sx={{ my: 2 }} />
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                <Typography variant="h6">
                                    {t('total') || '总计'}:
                                </Typography>
                                <Typography variant="h6" color="primary" fontWeight="bold">
                                    {formatPrice(cartTotal)}
                                </Typography>
                            </Box>
                        </>
                    )}
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setCartDialogOpen(false)}>
                        {t('continue_shopping') || '继续购物'}
                    </Button>
                    {cart.length > 0 && (
                        <Button 
                            variant="contained"
                            onClick={() => {
                                // 暂时显示提示信息，因为支付服务还未配置
                                alert(t('payment_service_unavailable') || '支付服务暂未配置，请联系管理员');
                            }}
                        >
                            {t('checkout') || '结账'}
                        </Button>
                    )}
                </DialogActions>
            </Dialog>
        </Container>
    );
};

export default Store; 