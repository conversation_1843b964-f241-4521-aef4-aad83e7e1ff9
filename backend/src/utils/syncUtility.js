/**
 * 预约数据同步工具
 * 用于在后台定期同步主服务器与本地数据库之间的预约记录
 */

const mysql = require('mysql2/promise');
const pool = require('../config/database');
const mainServerPool = pool.mainServerPool;

/**
 * 定期执行的预约同步函数
 * 从主服务器获取最近预约记录，并确保它们存在于本地数据库中
 */
async function syncAppointmentsScheduled() {
    console.log('[syncUtility] 开始同步预约数据...');
    let mainConn = null;

    try {
        // 获取最近30天的预约记录
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - 30);
        const dateString = cutoffDate.toISOString().split('T')[0];

        console.log(`[syncUtility] 获取${dateString}之后的预约记录...`);

        // 从主服务器获取预约记录
        mainConn = await mainServerPool.getConnection();
        const [mainAppointments] = await mainConn.query(`
            SELECT 
                appointment_no, provider_no, appointment_date, 
                start_time, end_time, name, demographic_no,
                reason, notes, status, creator
            FROM appointment
            WHERE appointment_date >= ?
            ORDER BY appointment_date DESC
            LIMIT 500
        `, [dateString]);

        console.log(`[syncUtility] 从主服务器获取到 ${mainAppointments.length} 条预约记录`);

        // 获取本地数据库中已存在的预约ID
        const [localAppointments] = await pool.query(`
            SELECT appointment_no
            FROM appointment
            WHERE appointment_date >= ?
        `, [dateString]);

        const localAppointmentIds = new Set(localAppointments.map(a => a.appointment_no));
        console.log(`[syncUtility] 本地数据库中有 ${localAppointmentIds.size} 条预约记录`);

        // 找出需要同步的预约
        const appointmentsToSync = mainAppointments.filter(a => !localAppointmentIds.has(a.appointment_no));
        console.log(`[syncUtility] 需要同步 ${appointmentsToSync.length} 条预约记录`);

        // 执行同步
        let successCount = 0;
        let errorCount = 0;

        for (const appt of appointmentsToSync) {
            try {
                // 插入预约记录
                const insertQuery = `
                    INSERT INTO appointment (
                        appointment_no, provider_no, appointment_date, 
                        start_time, end_time, name, demographic_no,
                        reason, notes, status, creator
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `;

                // 确保时间是正确的格式
                let startTime = appt.start_time;
                let endTime = appt.end_time;

                // 如果时间不是字符串格式，转换为字符串格式
                if (typeof startTime !== 'string' || !startTime.includes(':')) {
                    // 假设时间是数字格式如1000表示10:00
                    const startTimeStr = String(startTime).padStart(4, '0');
                    const hours = startTimeStr.substring(0, 2);
                    const minutes = startTimeStr.substring(2, 4);
                    startTime = `${hours}:${minutes}:00`;
                }

                if (typeof endTime !== 'string' || !endTime.includes(':')) {
                    const endTimeStr = String(endTime).padStart(4, '0');
                    const hours = endTimeStr.substring(0, 2);
                    const minutes = endTimeStr.substring(2, 4);
                    endTime = `${hours}:${minutes}:00`;
                }

                const params = [
                    appt.appointment_no,
                    appt.provider_no,
                    appt.appointment_date,
                    startTime,
                    endTime,
                    appt.name,
                    appt.demographic_no,
                    appt.reason,
                    (appt.notes || '') + ' (Auto-synced)',
                    appt.status,
                    appt.creator || 'SyncUtility'
                ];

                await pool.query(insertQuery, params);
                successCount++;
            } catch (err) {
                console.error(`[syncUtility] 同步记录 ${appt.appointment_no} 失败:`, err.message);
                errorCount++;
            }
        }

        console.log('[syncUtility] 同步完成!');
        console.log(`[syncUtility] 成功: ${successCount}, 失败: ${errorCount}`);

        return {
            total: appointmentsToSync.length,
            success: successCount,
            error: errorCount
        };

    } catch (err) {
        console.error('[syncUtility] 同步过程中出错:', err);
        throw err;
    } finally {
        // 释放连接
        if (mainConn) {
            mainConn.release();
            console.log('[syncUtility] 主服务器连接已释放');
        }
    }
}

// 导出工具函数
module.exports = {
    syncAppointmentsScheduled
}; 