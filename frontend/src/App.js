import React, { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { CssBaseline, Box, Alert, Button } from '@mui/material';
import { ViewProvider } from './context/ViewContext';
import { ThemeProvider } from './context/ThemeContext';
import { LanguageProvider } from './context/LanguageContext';
import { useView } from './context/ViewContext';
import './index.css';

import Login from './components/Login';
import Register from './components/Register';
import LinkAccount from './components/LinkAccount';
import Profile from './components/Profile';
import RenewMembership from './components/RenewMembership';
import Membership from './components/Membership';
import ForgotPassword from './components/ForgotPassword';
import EmailVerification from './components/EmailVerification';
import Documents from './components/LabReports';
import Sidebar from './components/Sidebar';
import TopBar from './components/TopBar';
import Appointments from './components/Appointments';
import Settings from './components/Settings';
import Prescriptions from './components/Prescriptions';
import Immunizations from './components/Immunizations';
import BookingPage from './components/BookingPage';
import Chatbot from './components/Chatbot';
import FamilyMembers from './components/FamilyMembers';
import AuthInitializer from './components/AuthInitializer';

import HealthTips from './components/HealthTips';
import YouTubeVideos from './components/YouTubeVideos';
import VideoConference from './components/VideoConference';
import AdminGuard from './components/AdminGuard';
import MemberGuard from './components/MemberGuard';
import YouTubeAdmin from './components/YouTubeAdmin';
import StoreAdmin from './components/StoreAdmin';
import DieticianPage from './components/DieticianPage';
import Store from './components/Store';
import MemberResources from './components/MemberResources';
import HealthGuide from './components/HealthGuide';
import HealthGuideAdmin from './components/HealthGuideAdmin';


import AIDashboard from './components/AIDashboard';
import AdminClientSearch from './components/AdminClientSearch';
// import HealthReportAnalyzer from './components/HealthReportAnalyzer';
// import AdminClientSearch from './components/AdminClientSearch';
// import AdminDashboard from './components/AdminDashboard';
// import AdminUserManagement from './components/AdminUserManagement';
// Eforms component removed

// 自动退出登录功能
const AutoLogout = () => {
    useEffect(() => {
        let inactivityTimer;
        const TIMEOUT_DURATION = 5 * 60 * 1000; // 5分钟转换为毫秒

        // 重置计时器
        const resetTimer = () => {
            if (inactivityTimer) clearTimeout(inactivityTimer);
            inactivityTimer = setTimeout(logout, TIMEOUT_DURATION);
        };

        // 退出登录函数
        const logout = () => {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            window.location.href = '/login';
        };

        // 监听用户活动事件
        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
        events.forEach(event => {
            document.addEventListener(event, resetTimer);
        });

        // 为无痕模式和普通模式提供一个更健壮的方案
        try {
            // 监听页面关闭/刷新事件
            window.addEventListener('beforeunload', (event) => {
                try {
                    // 对于可能无法设置存储的无痕模式，使用try-catch包裹
                    sessionStorage.setItem('pageRefreshing', 'true');

                    // 设置关闭标记，短期存在
                    // 页面刷新时标记会保留，关闭时会在下次打开时消失
                    localStorage.setItem('lastActivity', Date.now().toString());
                } catch (e) {
                    console.log('Storage in incognito mode may be limited');
                }
            });

            // 页面加载时设置页面加载时间戳
            const nowTimestamp = Date.now().toString();
            localStorage.setItem('pageLoadTime', nowTimestamp);

            // 30秒钟（保守估计刷新加载时间）后移除刷新标记
            // 如果是真正的刷新，此时刷新标记应该已经被新页面检查和处理
            setTimeout(() => {
                sessionStorage.removeItem('pageRefreshing');
            }, 30000);

            // 检查是否是刷新页面
            const isRefresh = sessionStorage.getItem('pageRefreshing') === 'true';
            if (isRefresh) {
                // 是刷新，清除标记并保持登录状态
                sessionStorage.removeItem('pageRefreshing');
            } else {
                // 可能是关闭后重新打开，检查上次活动时间
                const lastActivity = localStorage.getItem('lastActivity');
                const pageLoadTime = localStorage.getItem('pageLoadTime');

                if (lastActivity && pageLoadTime) {
                    const timeDiff = parseInt(pageLoadTime) - parseInt(lastActivity);

                    // 如果时间差大于5分钟，视为关闭浏览器后重新打开
                    if (timeDiff > 5 * 60 * 1000) { // 5分钟
                        // 执行登出
                        if (localStorage.getItem('token')) {
                            logout();
                        }
                    }
                }
            }
        } catch (e) {
            console.error('Error in session management:', e);
            // 对于无痕模式下可能出现的存储异常，使用更保守的退出策略
            // 仅保留超时退出功能
        }

        // 初始启动计时器
        resetTimer();

        // 清理函数
        return () => {
            if (inactivityTimer) clearTimeout(inactivityTimer);
            events.forEach(event => {
                document.removeEventListener(event, resetTimer);
            });
        };
    }, []);

    return null;
};

// 不需要侧边栏的页面路径
const publicPaths = [
    '/login',
    '/register',
    '/forgot-password',
    '/reset-password',
    '/verify-email',
    '/email-verification'
];

// Layout component that handles the main app layout
const drawerWidth = 250; // Define drawer width for reuse

const AppLayout = () => {
    const location = useLocation();
    const isAuthenticated = !!localStorage.getItem('token');
    const [mobileOpen, setMobileOpen] = useState(false); // State for mobile drawer

    const handleDrawerToggle = () => {
        setMobileOpen(!mobileOpen);
    };

    // Add view context for global alert
    const { viewingDemographicNo, loggedInUser, setViewAs } = useView();
    const isViewingOther = viewingDemographicNo && loggedInUser && viewingDemographicNo !== loggedInUser.demographic_no;

    if (!isAuthenticated) {
        return <Navigate to="/login" />;
    }

    return (
        <Box sx={{ display: 'flex' }}>
            <TopBar handleDrawerToggle={handleDrawerToggle} drawerWidth={drawerWidth} />
            <Sidebar
                drawerWidth={drawerWidth}
                mobileOpen={mobileOpen}
                handleDrawerToggle={handleDrawerToggle}
            />
            <Box
                component="main"
                sx={{
                    flexGrow: 1,
                    p: { xs: 1.5, sm: 2, md: 2.5 },
                    width: { 
                        xs: '100%', // Explicitly 100% on extra-small screens
                        sm: `calc(100% - ${drawerWidth}px)` 
                    },
                    mt: `64px`, 
                    pb: { xs: 8, sm: 3 } 
                }}
            >
                {/* Global alert for viewing other user */}
                {isViewingOther && (
                    <Alert
                        severity="info"
                        action={
                            <Button color="inherit" size="small" onClick={() => setViewAs(loggedInUser.demographic_no)}>
                                切回自己
                            </Button>
                        }
                        sx={{ mb: 2 }}
                    >
                        当前正在查看：{viewingDemographicNo}（非本人档案）
                    </Alert>
                )}
                <Routes>
                    <Route path="/" element={<Navigate to="/profile" />} />
                    <Route path="/profile" element={<Profile />} />
                    <Route path="/profile/:demographicNo" element={<Profile />} />
                    <Route path="/family" element={<FamilyMembers />} />
                    <Route path="/family/:demographicNo" element={<FamilyMembers />} />
                    <Route path="/appointments" element={<Appointments />} />
                    <Route path="/appointments/:demographicNo" element={<Appointments />} />
                    <Route path="/membership" element={<Membership />} />
                    <Route path="/membership/:demographicNo" element={<Membership />} />
                    <Route path="/renew-membership" element={<RenewMembership />} />
                    <Route path="/renew-membership/:demographicNo" element={<RenewMembership />} />
                    <Route path="/lab-reports" element={<MemberGuard><Documents /></MemberGuard>} />
                    <Route path="/lab-reports/:demographicNo" element={<MemberGuard><Documents /></MemberGuard>} />
                    <Route path="/prescriptions" element={<MemberGuard><Prescriptions /></MemberGuard>} />
                    <Route path="/prescriptions/:demographicNo" element={<MemberGuard><Prescriptions /></MemberGuard>} />
                    <Route path="/immunizations" element={<MemberGuard><Immunizations /></MemberGuard>} />
                    <Route path="/immunizations/:demographicNo" element={<MemberGuard><Immunizations /></MemberGuard>} />
                    {/* Eforms route removed */}
                    <Route path="/settings" element={<Settings />} />
                    <Route path="/booking" element={<BookingPage />} />
                    <Route path="/booking/:demographicNo" element={<BookingPage />} />
                    <Route path="/member-resources" element={<MemberGuard><MemberResources /></MemberGuard>} />
                    <Route path="/health-guide" element={<MemberGuard><HealthGuide /></MemberGuard>} />
                    <Route path="/health-tips" element={<MemberGuard><HealthTips /></MemberGuard>} />
                    <Route path="/youtube-videos" element={<MemberGuard><YouTubeVideos /></MemberGuard>} /> 
                    <Route path="/dietician" element={<MemberGuard><DieticianPage /></MemberGuard>} />
                    <Route path="/dietician/:demographicNo" element={<MemberGuard><DieticianPage /></MemberGuard>} />
                    <Route path="/store" element={<Store />} />
                    <Route path="/video-conference" element={
                        <VideoConference />
                    } />
                    <Route path="/video-conference/:demographicNo" element={
                        <VideoConference />
                    } />
                    {/* <Route path="/health-report-analyzer" element={<HealthReportAnalyzer />} /> */}
                    {/* Admin Route for YouTube Management */}
                    <Route 
                        path="/admin/youtube"
                        element={<AdminGuard><YouTubeAdmin /></AdminGuard>}
                    />
                    {/* Admin Route for Store Management */}
                    <Route 
                        path="/admin/store"
                        element={<AdminGuard><StoreAdmin /></AdminGuard>}
                    />
                    {/* Admin Route for Health Guide Management */}
                    <Route 
                        path="/admin/health-guides"
                        element={<AdminGuard><HealthGuideAdmin /></AdminGuard>}
                    />


                    {/* Admin Route for AI Dashboard */}
                    <Route 
                        path="/admin/ai-dashboard"
                        element={<AdminGuard><AIDashboard /></AdminGuard>}
                    />
                    {/* Admin Route for Client Search */}
                    <Route 
                        path="/admin/client-search"
                        element={<AdminGuard><AdminClientSearch /></AdminGuard>}
                    />
                    <Route path="*" element={<Navigate to="/profile" />} />
                </Routes>
                <Chatbot />
            </Box>
        </Box>
    );
};

function App() {
    return (
        <Router>
            <ThemeProvider>
                <LanguageProvider>
                    <CssBaseline />
                    <AutoLogout />

                    <Routes>
                        <Route path="/login" element={<Login />} />
                        <Route path="/register" element={<Register />} />
                        <Route path="/forgot-password" element={<ForgotPassword />} />
                        <Route path="/reset-password" element={<ForgotPassword />} />
                        <Route path="/verify-email" element={<Login />} />
                        <Route path="/email-verification" element={<EmailVerification />} />
                        <Route path="/link-account" element={<LinkAccount />} />

                        <Route
                            path="/*"
                            element={
                                <AuthInitializer>
                                    <ViewProvider>
                                        <AppLayout />
                                    </ViewProvider>
                                </AuthInitializer>
                            }
                        />
                    </Routes>
                </LanguageProvider>
            </ThemeProvider>
        </Router>
    );
}

export default App;