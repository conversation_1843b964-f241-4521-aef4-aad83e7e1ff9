import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { API_URL } from '../utils/env';
import {
    Container,
    Paper,
    Typography,
    CircularProgress,
    Alert,
    List,
    ListItem,
    ListItemText,
    ListItemIcon,
    Avatar,
    Divider,
    Box,
    Button,
    Chip,
    Dialog,
    DialogActions,
    DialogContent,
    DialogContentText,
    DialogTitle,
    TextField,
    Grid,
    Card,
    CardContent,
    CardActions,
    IconButton,
    useTheme,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    FormHelperText
} from '@mui/material';
import PersonIcon from '@mui/icons-material/Person';
import FamilyRestroomIcon from '@mui/icons-material/FamilyRestroom';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import EmailIcon from '@mui/icons-material/Email';
import LockOpenIcon from '@mui/icons-material/LockOpen';
import FilterListIcon from '@mui/icons-material/FilterList';
import ChildCareIcon from '@mui/icons-material/ChildCare';
import { useView } from '../context/ViewContext';
import { useLanguage } from '../context/LanguageContext';
import { styled } from '@mui/material/styles';
import axios from 'axios';
import DeleteIcon from '@mui/icons-material/Delete';
import LinkIcon from '@mui/icons-material/Link';
import ErrorIcon from '@mui/icons-material/Error';
import InfoIcon from '@mui/icons-material/Info';
import FamilyMemberDisplay from './FamilyMemberDisplay';

const StyledPaper = styled(Paper)(({ theme }) => ({
    marginTop: theme.spacing(3),
    marginBottom: theme.spacing(3),
    padding: theme.spacing(2),
    [theme.breakpoints.up('sm')]: {
        padding: theme.spacing(3),
    },
}));

const FamilyMembers = () => {
    const { t } = useLanguage();
    const { loggedInUser, setViewAs } = useView();
    const [familyMembers, setFamilyMembers] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);
    const [relationshipFilter, setRelationshipFilter] = useState('all'); // Filter state: 'all', 'children', 'spouse', etc.

    const [targetMember, setTargetMember] = useState(null);
    const [consentDialogOpen, setConsentDialogOpen] = useState(false);
    const [verificationDialogOpen, setVerificationDialogOpen] = useState(false);
    const [verificationCode, setVerificationCode] = useState('');
    const [dialogLoading, setDialogLoading] = useState(false);
    const [dialogError, setDialogError] = useState('');
    const [verificationSuccess, setVerificationSuccess] = useState(false);

    // Helper function to calculate age from year_of_birth
    const calculateAge = (yearOfBirth) => {
        if (!yearOfBirth) return null;
        const currentYear = new Date().getFullYear();
        return currentYear - parseInt(yearOfBirth);
    };

    // Helper function to check if a member is a child under 18
    const isUnder18 = (member) => {
        const age = calculateAge(member.year_of_birth);
        return age !== null && age < 18;
    };

    // Helper function to check if a member is a direct relative (child or parent)
    const isDirectRelative = (member) => {
        if (!member.relationship_type) return false;

        // 将关系类型转换为小写以进行不区分大小写的比较
        const relationType = member.relationship_type.toLowerCase();

        // 排除的关系类型
        const excludedRelations = ['other', 'partner'];

        // 检查是否是排除的关系类型
        if (excludedRelations.some(type => relationType.includes(type))) {
            return false;
        }

        // 允许所有其他家庭关系类型
        return true;
    };

    // --- REMOVE DEBUGGING --- Remove logs for context
    // const langContext = useLanguage(); // Get the whole context
    // console.log("DEBUG: t function received:", t);
    // console.log("DEBUG: Full language context:", langContext);
    // --- END REMOVE DEBUGGING ---

    const fetchFamilyMembers = useCallback(async () => {
        setIsLoading(true);
        setError(null);
        const token = localStorage.getItem('token');

        if (!token || !loggedInUser) {
            setError('User not logged in.');
            setIsLoading(false);
            return;
        }

        try {
            const response = await axios.get(`${API_URL}/api/relationships/family`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                },
                timeout: 10000
            });

            if (response.data && response.data.success) {
                setFamilyMembers(response.data.familyMembers || []);
            } else {
                throw new Error(response.data.message || 'Failed to fetch family members.');
            }
        } catch (err) {
            let errorMsg = 'An error occurred while fetching family members.';
            if (axios.isCancel(err)) {
                errorMsg = 'Request timed out.';
            } else if (err.response) {
                errorMsg = err.response.data?.message || `Server error (${err.response.status})`;
            } else if (err.request) {
                errorMsg = 'Could not connect to server.';
            } else {
                errorMsg = err.message || errorMsg;
            }
            setError(errorMsg);
            console.error("Error fetching family members:", err);
            setFamilyMembers([]);
        } finally {
            setIsLoading(false);
        }
    }, [loggedInUser]);

    useEffect(() => {
        fetchFamilyMembers();
    }, [fetchFamilyMembers]);

    // 只显示直系亲属
    const filteredFamilyMembers = useMemo(() => {
        if (!familyMembers || familyMembers.length === 0) return [];

        // 直接过滤掉非直系亲属
        return familyMembers.filter(member => isDirectRelative(member));
    }, [familyMembers]);

    const handleRequestAccessClick = (member) => {
        console.log("Requesting access for:", member);
        setTargetMember(member);
        setDialogError('');
        setVerificationSuccess(false);
        setConsentDialogOpen(true);
    };

    const handleConsentConfirm = async () => {
        if (!targetMember) return;
        setDialogLoading(true);
        setDialogError('');
        const token = localStorage.getItem('token');

        try {
            console.log(`Initiating switch view for ${targetMember.relative_demographic_no}`);
            const response = await axios.post(`${API_URL}/api/relationships/switch-view/initiate`,
                { targetDemographicNo: targetMember.relative_demographic_no },
                {
                    headers: { 'Authorization': `Bearer ${token}` },
                    timeout: 15000
                }
            );

            if (response.data && response.data.success) {
                setConsentDialogOpen(false);
                setVerificationDialogOpen(true);
                setVerificationCode('');
            } else {
                throw new Error(response.data.message || 'Failed to initiate access request.');
            }
        } catch (err) {
            let errorMsg = 'Could not send verification code.';
            if (axios.isCancel(err)) {
                errorMsg = 'Request timed out.';
            } else if (err.response) {
                errorMsg = err.response.data?.message || `Server error (${err.response.status})`;
            } else if (err.request) {
                errorMsg = 'Could not connect to server.';
            } else {
                errorMsg = err.message || errorMsg;
            }
            setDialogError(errorMsg);
            console.error("Error initiating switch view:", err);
        } finally {
            setDialogLoading(false);
        }
    };

    const handleVerifyCodeSubmit = async () => {
        if (!targetMember || !verificationCode) return;
        setDialogLoading(true);
        setDialogError('');
        const token = localStorage.getItem('token');

        try {
            console.log(`Verifying code for ${targetMember.relative_demographic_no}`);
            const response = await axios.post(`${API_URL}/api/relationships/switch-view/verify`,
                {
                    targetDemographicNo: targetMember.relative_demographic_no,
                    verificationCode: verificationCode
                },
                {
                    headers: { 'Authorization': `Bearer ${token}` },
                    timeout: 10000
                }
            );

            if (response.data && response.data.success) {
                console.log('Verification successful! Switching view and refreshing list.');
                setVerificationSuccess(true);
                fetchFamilyMembers();
                setTimeout(() => {
                    setViewAs(targetMember.relative_demographic_no);
                    setVerificationDialogOpen(false);
                    setTargetMember(null);
                }, 1500);
            } else {
                throw new Error(response.data.message || 'Verification failed.');
            }
        } catch (err) {
            let errorMsg = 'Verification failed.';
            if (axios.isCancel(err)) {
                errorMsg = 'Request timed out.';
            } else if (err.response) {
                errorMsg = err.response.data?.message || `Server error (${err.response.status})`;
            } else if (err.request) {
                errorMsg = 'Could not connect to server.';
            } else {
                errorMsg = err.message || errorMsg;
            }
            setDialogError(errorMsg);
            console.error("Error verifying code:", err);
        } finally {
            setDialogLoading(false);
        }
    };

    const handleCloseDialogs = () => {
        setConsentDialogOpen(false);
        setVerificationDialogOpen(false);
        setTargetMember(null);
        setDialogError('');
        setVerificationCode('');
        setDialogLoading(false);
        setVerificationSuccess(false);
    };

    if (isLoading) {
        return (
            <Container maxWidth="md">
                <StyledPaper sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', p: 5 }}>
                    <CircularProgress />
                    <Typography sx={{ ml: 2 }}>Loading Family Members...</Typography>
                </StyledPaper>
            </Container>
        );
    }

    if (error) {
        return (
            <Container maxWidth="md">
                <StyledPaper>
                    <Typography variant="h5" component="h2" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                        <FamilyRestroomIcon sx={{ mr: 1 }} /> {t('family_members')}
                    </Typography>
                    <Alert severity="error">{error}</Alert>
                </StyledPaper>
            </Container>
        );
    }

    return (
        <Container maxWidth="md">
            <StyledPaper elevation={3}>
                <Typography variant="h5" component="h2" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <FamilyRestroomIcon sx={{ mr: 1 }} /> {t('family_members')}
                </Typography>

                {/* 标题下方的说明文字 */}
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {t('direct_relatives_only') || '显示家庭成员（不包括Other和Partner关系）'}
                </Typography>

                <Divider sx={{ mb: 2 }} />
                {familyMembers.length === 0 ? (
                    <Typography sx={{ textAlign: 'center', mt: 3, color: 'text.secondary' }}>
                        {t('no_family_members_found')}
                    </Typography>
                ) : filteredFamilyMembers.length === 0 ? (
                    <Typography sx={{ textAlign: 'center', mt: 3, color: 'text.secondary' }}>
                        {t('no_matching_family_members') || 'No family members match the selected filter'}
                    </Typography>
                ) : (
                    <List disablePadding>
                        {filteredFamilyMembers.map((member, index) => (
                            <React.Fragment key={member.relative_demographic_no}>
                                <ListItem sx={{ py: 2, display: 'flex', flexWrap: 'wrap', justifyContent: 'space-between' }}>
                                    <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1, mb: { xs: 2, sm: 0 } }}>
                                        <ListItemIcon sx={{ minWidth: 'auto', mr: 2 }}>
                                            <Avatar sx={{ bgcolor: 'secondary.main' }}>
                                                <PersonIcon />
                                            </Avatar>
                                        </ListItemIcon>
                                        <ListItemText
                                            primary={`${member.relative_first_name} ${member.relative_last_name}`}
                                            secondary={
                                                <React.Fragment>
                                                    <span>{member.relationship_type || 'Family Member'}</span>
                                                    {member.year_of_birth && (
                                                        <Chip
                                                            size="small"
                                                            label={`${calculateAge(member.year_of_birth)} ${t('years_old') || 'years old'}`}
                                                            sx={{ ml: 1, fontSize: '0.7rem' }}
                                                            color={isUnder18(member) ? "secondary" : "default"}
                                                            variant="outlined"
                                                        />
                                                    )}
                                                </React.Fragment>
                                            }
                                            primaryTypographyProps={{ fontWeight: 'medium' }}
                                        />
                                    </Box>
                                    <Box sx={{ display: 'flex', alignItems: 'center', flexShrink: 0 }}>
                                        {member.hasRecentAccess ? (
                                            <Chip
                                                icon={<CheckCircleIcon />}
                                                label={t('access_active')}
                                                color="success"
                                                size="small"
                                                sx={{ mr: 1 }}
                                            />
                                        ) : null}
                                        {member.hasRecentAccess ? (
                                            <Button
                                                variant="contained"
                                                size="small"
                                                onClick={() => setViewAs(member.relative_demographic_no)}
                                            >
                                                {t('switch_view')}
                                            </Button>
                                        ) : (isUnder18(member)) ? (
                                            <Button
                                                variant="outlined"
                                                size="small"
                                                startIcon={<ChildCareIcon />}
                                                onClick={() => handleRequestAccessClick(member)}
                                                color="primary"
                                            >
                                                {t('request_access')}
                                            </Button>
                                        ) : (
                                            <Chip
                                                label={isUnder18(member) ? (t('minor_member') || 'Minor Member') : (t('adult_member') || 'Adult Member')}
                                                size="small"
                                                color="default"
                                                variant="outlined"
                                            />
                                        )}
                                    </Box>
                                </ListItem>
                                {index < filteredFamilyMembers.length - 1 && <Divider variant="inset" component="li" />}
                            </React.Fragment>
                        ))}
                    </List>
                )}
            </StyledPaper>

            <Dialog open={consentDialogOpen} onClose={handleCloseDialogs}>
                <DialogTitle>{t('request_access_title')}</DialogTitle>
                <DialogContent>
                    {targetMember && targetMember.relative_first_name && targetMember.relative_last_name ? (
                        <Box sx={{ '& > *': { mb: 1.5 } }}>
                            <Typography variant="body1">
                                {t('request_access_dear_user')}
                            </Typography>
                            <Typography variant="body1">
                                {t('request_access_intro', { name: `${targetMember.relative_first_name} ${targetMember.relative_last_name}` })}
                            </Typography>

                            <Typography variant="body1">
                                <Typography component="span" sx={{ fontWeight: 'bold' }}>
                                    1. {t('request_access_point1_title')}
                                </Typography>
                                {' '}
                                {t('request_access_point1_text', { name: `${targetMember.relative_first_name} ${targetMember.relative_last_name}` })}
                            </Typography>

                            <Typography variant="body1">
                                <Typography component="span" sx={{ fontWeight: 'bold' }}>
                                    2. {t('request_access_point2_title')}
                                </Typography>
                                {' '}
                                {t('request_access_point2_text')}
                            </Typography>

                            <Typography variant="body1">
                                <Typography component="span" sx={{ fontWeight: 'bold' }}>
                                    3. {t('request_access_point3_title')}
                                </Typography>
                                {' '}
                                {t('request_access_point3_text')}
                            </Typography>

                            <Typography variant="body1">
                                <Typography component="span" sx={{ fontWeight: 'bold' }}>
                                    4. {t('request_access_point4_title')}
                                </Typography>
                                {' '}
                                {t('request_access_point4_text')}
                            </Typography>

                            <Typography variant="body1" sx={{ mt: 2 }}>
                                {t('request_access_confirmation', { name: `${targetMember.relative_first_name} ${targetMember.relative_last_name}` })}
                            </Typography>

                            <Typography variant="body1" sx={{ mt: 2 }}>
                                {t('request_access_sincerely')}
                            </Typography>
                            <Typography variant="body1">
                                {t('request_access_team')}
                            </Typography>
                        </Box>
                    ) : (
                        <Typography>Loading details...</Typography>
                    )}
                    {dialogError && <Alert severity="error" sx={{ mt: 2 }}>{dialogError}</Alert>}
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDialogs} disabled={dialogLoading}>{t('cancel')}</Button>
                    <Button onClick={handleConsentConfirm} variant="contained" disabled={dialogLoading}>
                        {dialogLoading ? <CircularProgress size={20} /> : t('confirm')}
                    </Button>
                </DialogActions>
            </Dialog>

            <Dialog open={verificationDialogOpen} onClose={handleCloseDialogs}>
                <DialogTitle>{t('verification_title')}</DialogTitle>
                <DialogContent>
                    {verificationSuccess ? (
                        <Alert severity="success">{t('verification_success')}</Alert>
                    ) : (
                        <>
                            <DialogContentText>
                                {t('verification_message', { email: targetMember?.relative_email || 'their email' })}
                            </DialogContentText>
                            <TextField
                                autoFocus
                                margin="dense"
                                id="verificationCode"
                                label={t('verification_code')}
                                type="text"
                                fullWidth
                                variant="standard"
                                value={verificationCode}
                                onChange={(e) => setVerificationCode(e.target.value)}
                                disabled={dialogLoading}
                                inputProps={{ maxLength: 6 }}
                            />
                            {dialogError && <Alert severity="error" sx={{ mt: 2 }}>{dialogError}</Alert>}
                        </>
                    )}
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDialogs} disabled={dialogLoading || verificationSuccess}>{t('cancel')}</Button>
                    <Button
                        onClick={handleVerifyCodeSubmit}
                        variant="contained"
                        disabled={dialogLoading || verificationSuccess || verificationCode.length < 6}
                        startIcon={verificationSuccess ? <CheckCircleIcon /> : <LockOpenIcon />}
                    >
                        {dialogLoading ? <CircularProgress size={20} /> : (verificationSuccess ? 'Verified' : t('verify'))}
                    </Button>
                </DialogActions>
            </Dialog>
        </Container>
    );
};

export default FamilyMembers;