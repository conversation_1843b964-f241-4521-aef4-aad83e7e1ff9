# PDF到Markdown转换服务使用指南

## 🚀 功能特性

- ✅ **高质量转换**: 使用PyMuPDF4LLM实现高质量PDF到Markdown转换
- 📊 **表格支持**: 完美处理PDF中的表格，转换为Markdown表格格式
- 🖼️ **图片提取**: 自动提取PDF中的图片并生成引用
- 📄 **分页处理**: 支持页面级别的分块处理
- 🔤 **格式保持**: 保留粗体、斜体、标题层级等格式
- 🏥 **医疗优化**: 专门针对医疗报告优化的转换选项

## 📋 API接口

### 1. 基础PDF转换

```bash
POST /api/pdf-converter/convert
```

**请求体:**
```json
{
  "filePath": "/path/to/your/file.pdf",
  "options": {
    "pages": [0, 1, 2],          // 可选：指定页面范围
    "writeImages": true,          // 可选：是否提取图片
    "imagePath": "/tmp/images",   // 可选：图片保存路径
    "pageChunks": true,          // 可选：是否按页分块
    "extractWords": true         // 可选：是否提取单词级别信息
  }
}
```

**响应:**
```json
{
  "success": true,
  "markdown": "# 文档标题\n\n这是转换后的Markdown内容...",
  "message": "PDF converted successfully"
}
```

### 2. 转换并保存文件

```bash
POST /api/pdf-converter/convert-and-save
```

**请求体:**
```json
{
  "filePath": "/path/to/input.pdf",
  "outputPath": "/path/to/output.md",
  "options": {
    "writeImages": true,
    "pageChunks": false
  }
}
```

### 3. 批量转换

```bash
POST /api/pdf-converter/batch-convert
```

**请求体:**
```json
{
  "pdfFiles": [
    "/path/to/file1.pdf",
    "/path/to/file2.pdf"
  ],
  "outputDir": "/path/to/output/",
  "options": {
    "writeImages": true
  }
}
```

### 4. 医疗报告专用转换

```bash
POST /api/pdf-converter/medical-report/:demographicNo/:reportType/:reportId
```

**参数:**
- `demographicNo`: 患者ID
- `reportType`: 报告类型 (如 "labs", "imaging")  
- `reportId`: 报告ID

**请求体:**
```json
{
  "pages": [0, 1],           // 可选：指定页面
  "extractImages": true      // 可选：是否提取图片
}
```

## 🛠️ 使用示例

### 前端JavaScript调用

```javascript
// 转换单个PDF文件
async function convertPDF(filePath) {
    try {
        const response = await fetch('/api/pdf-converter/convert', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                filePath: filePath,
                options: {
                    writeImages: true,
                    pageChunks: true
                }
            })
        });
        
        const result = await response.json();
        if (result.success) {
            console.log('转换成功:', result.markdown);
            return result.markdown;
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('PDF转换失败:', error);
        throw error;
    }
}

// 转换医疗报告
async function convertMedicalReport(demographicNo, reportId) {
    try {
        const response = await fetch(`/api/pdf-converter/medical-report/${demographicNo}/labs/${reportId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                extractImages: true
            })
        });
        
        const result = await response.json();
        return result.data;
    } catch (error) {
        console.error('医疗报告转换失败:', error);
        throw error;
    }
}
```

### Node.js后端使用

```javascript
const pdfToMarkdownService = require('./services/pdfToMarkdownService');

// 直接使用服务
async function convertPDFExample() {
    try {
        const markdown = await pdfToMarkdownService.convertPDFToMarkdown(
            '/path/to/medical-report.pdf',
            {
                writeImages: true,
                pageChunks: true,
                extractWords: true
            }
        );
        
        console.log('转换结果:', markdown);
        return markdown;
    } catch (error) {
        console.error('转换失败:', error);
    }
}
```

### cURL命令示例

```bash
# 基础转换
curl -X POST "http://localhost:3000/api/pdf-converter/convert" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "filePath": "/OscarDocument/615/labs/report.pdf",
    "options": {
      "writeImages": true,
      "pageChunks": true
    }
  }'

# 医疗报告转换
curl -X POST "http://localhost:3000/api/pdf-converter/medical-report/615/labs/12345" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "extractImages": true
  }'
```

## ⚙️ 转换选项说明

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `pages` | Array | null | 指定要转换的页面范围，如 [0,1,2]。null表示全部页面 |
| `writeImages` | Boolean | false | 是否提取并保存PDF中的图片 |
| `imagePath` | String | null | 图片保存路径 |
| `pageChunks` | Boolean | false | 是否按页面分块返回结果 |
| `extractWords` | Boolean | false | 是否提取单词级别的详细信息 |

## 🏥 医疗报告转换优化

针对医疗报告，系统会自动应用以下优化设置：

- ✅ **分页处理**: 自动启用页面分块，便于逐页分析
- ✅ **图片提取**: 自动提取X光片、CT扫描等医学图像
- ✅ **详细信息**: 启用单词级别提取，提高OCR准确性
- ✅ **结构保持**: 保留医疗报告的原始结构和格式

## 🔍 输出格式示例

### 标准Markdown输出
```markdown
# 医疗检查报告

## 患者信息
- **姓名**: 张三
- **年龄**: 45岁
- **检查日期**: 2024-01-15

## 检查结果

### 血常规
| 项目 | 结果 | 参考范围 |
|------|------|----------|
| 白细胞计数 | 6.5 | 4.0-10.0 |
| 红细胞计数 | 4.8 | 4.0-5.5 |

![检查图像](image-0-0.png)

### 诊断意见
血常规检查结果**正常**，建议定期复查。
```

### 分页JSON输出
```json
[
  {
    "page": 0,
    "markdown": "# 医疗检查报告\n\n## 患者信息...",
    "images": ["image-0-0.png"],
    "metadata": {
      "page_number": 1,
      "word_count": 156
    }
  }
]
```

## 🚨 注意事项

1. **文件路径**: 确保PDF文件路径正确且容器内可访问
2. **权限验证**: 所有API都需要有效的JWT token
3. **内存使用**: 大文件转换可能需要较多内存
4. **图片存储**: 提取的图片会保存在指定路径，注意磁盘空间
5. **中文支持**: 完整支持中文PDF转换

## 🎯 集成到现有功能

可以将PDF转换功能集成到以下场景：

- **实验室报告查看**: 将PDF报告转换为可搜索的Markdown
- **病历记录**: 扫描的病历转换为结构化文本
- **医学图像**: 提取报告中的图像进行单独展示
- **数据分析**: 将PDF内容转换为可分析的结构化数据

## 🔧 故障排除

### 常见问题

1. **Python模块未找到**
   ```bash
   docker exec mmcwebapp-backend-1 python3 -c "import pymupdf4llm; print('OK')"
   ```

2. **文件路径不存在**
   ```bash
   docker exec mmcwebapp-backend-1 ls -la /OscarDocument/
   ```

3. **内存不足**
   - 增加Docker内存限制
   - 减少并发转换任务

### 测试命令

```bash
# 测试PyMuPDF4LLM安装
docker exec mmcwebapp-backend-1 node src/test/testPDFConverter.js

# 检查API健康状态
curl http://localhost:3000/health
```

---

🎉 **现在您就可以开始使用高质量的PDF到Markdown转换功能了！** 