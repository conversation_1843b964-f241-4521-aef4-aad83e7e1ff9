# 用户类型系统指南 (User Type System Guide)

## 概述 (Overview)

本系统实现了基于membership状态的用户类型自动分类，提供不同级别的功能访问权限。

This system implements automatic user type classification based on membership status, providing different levels of feature access.

## 用户类型 (User Types)

### 1. **user** - 普通用户
- **描述**: 新注册用户或没有有效membership的用户
- **权限**: 只能访问基础功能
- **可访问功能**:
  - ✅ 个人信息管理 (Profile Management)
  - ✅ 预约管理 (Appointment Management)
  - ✅ 基础设置 (Basic Settings)
- **限制功能**:
  - ❌ 健康指南 (Health Guides)
  - ❌ 健康小贴士 (Health Tips)
  - ❌ 实验室结果 (Lab Results)
  - ❌ 处方记录 (Prescriptions)
  - ❌ 疫苗记录 (Immunizations)
  - ❌ 预约医疗笔记 (Appointment Notes)

### 2. **member** - 会员用户
- **描述**: 拥有有效membership的用户
- **权限**: 可以访问所有会员功能
- **可访问功能**:
  - ✅ 所有user功能
  - ✅ 健康指南 (Health Guides)
  - ✅ 健康小贴士 (Health Tips)
  - ✅ 实验室结果 (Lab Results)
  - ✅ 处方记录 (Prescriptions)
  - ✅ 疫苗记录 (Immunizations)
  - ✅ 预约医疗笔记 (Appointment Notes)

### 3. **admin** - 管理员
- **描述**: 系统管理员
- **权限**: 可以访问所有功能，包括管理功能
- **可访问功能**:
  - ✅ 所有member功能
  - ✅ 管理员面板 (Admin Panel)
  - ✅ 用户管理 (User Management)
  - ✅ 内容管理 (Content Management)

## 自动检测机制 (Automatic Detection)

### 检测逻辑
系统在用户每次登录时自动检测用户类型：

1. **检查membership状态**: 查询`billingmaster`表中的有效membership记录
2. **验证有效期**: 检查membership是否在有效期内
3. **更新用户类型**: 根据membership状态自动更新用户的`role`字段

### 检测规则
```javascript
// 伪代码
if (user.role === 'admin') {
    // 管理员保持admin权限，不进行检测
    return 'admin';
} else if (haValidMembership && !isExpired) {
    return 'member';
} else {
    return 'user';
}
```

## API端点 (API Endpoints)

### 获取用户类型信息
```
GET /api/auth/user-type
Authorization: Bearer <token>
```

**响应示例**:
```json
{
  "success": true,
  "userType": "member",
  "permissions": {
    "isUser": true,
    "isMember": true,
    "isAdmin": false
  },
  "demographicNo": 2
}
```

## 权限中间件 (Permission Middleware)

### 可用中间件

1. **requireUser**: 要求用户为user或更高级别
2. **requireMember**: 要求用户为member或admin
3. **requireAdmin**: 要求用户为admin
4. **addUserTypeInfo**: 添加用户权限信息（不阻止访问）

### 使用示例
```javascript
const { requireMember, requireUser, requireAdmin } = require('../middleware/userTypeAuth');

// 只有member和admin可以访问
router.get('/health-guides', auth, requireMember, controller.getHealthGuides);

// 所有登录用户可以访问
router.get('/appointments', auth, requireUser, controller.getAppointments);

// 只有admin可以访问
router.get('/admin/users', auth, requireAdmin, controller.getUsers);
```

## 注册流程 (Registration Process)

### 新用户注册
1. **开放注册**: 不再需要referral code（可选）
2. **默认类型**: 新用户默认为'user'类型
3. **自动升级**: 当用户购买membership后，下次登录时自动升级为'member'

### 注册API
```
POST /api/auth/register
```

**请求体**:
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "John",
  "lastName": "Doe",
  "phone": "**********",
  "dateOfBirth": "1990-01-01",
  "referralCode": "OPTIONAL123"  // 可选
}
```

## 前端集成 (Frontend Integration)

### 检查用户权限
```javascript
// 获取用户类型信息
const getUserType = async () => {
  const response = await fetch('/api/auth/user-type', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  const data = await response.json();
  return data;
};

// 根据权限显示/隐藏功能
const userType = await getUserType();
if (userType.permissions.isMember) {
  // 显示会员功能
  showHealthGuides();
  showLabResults();
} else {
  // 显示升级提示
  showUpgradePrompt();
}
```

### 权限检查组件示例
```jsx
const ProtectedFeature = ({ children, requireMember = false }) => {
  const { userType } = useAuth();
  
  if (requireMember && userType !== 'member' && userType !== 'admin') {
    return <UpgradePrompt />;
  }
  
  return children;
};
```

## 数据库结构 (Database Structure)

### user_auth表
```sql
-- role字段存储用户类型
ALTER TABLE user_auth MODIFY COLUMN role VARCHAR(20) DEFAULT 'user';
```

### membership检测
系统通过以下查询检测membership状态：
```sql
SELECT bm.demographic_no, bm.billing_code, bm.bill_amount, bm.service_date
FROM billingmaster bm
INNER JOIN billingservice bs ON bs.service_code = bm.billing_code
WHERE bm.demographic_no = ? 
  AND bm.billingstatus = 'A'
  AND bm.billing_code LIKE 'AHM%'
ORDER BY service_date DESC 
LIMIT 1
```

## 测试 (Testing)

### 测试用户
- **demographic_no=2**: 有有效membership，应该是'member'
- **demographic_no=54897**: admin用户，保持'admin'

### 测试脚本
```bash
node test_user_types.js
```

## 错误处理 (Error Handling)

### 权限不足响应
```json
{
  "success": false,
  "message": "This feature requires an active membership. Please upgrade to access this content.",
  "userType": "user",
  "requiresUpgrade": true
}
```

### 前端处理
```javascript
if (response.status === 403 && data.requiresUpgrade) {
  // 显示升级提示
  showMembershipUpgradeModal();
}
```

## 安全考虑 (Security Considerations)

1. **自动检测**: 每次登录时自动检测，确保权限实时更新
2. **服务器端验证**: 所有权限检查在服务器端进行
3. **管理员保护**: admin用户不会被自动降级
4. **优雅降级**: 权限不足时提供友好的升级提示

## 维护 (Maintenance)

### 手动更新用户类型
```sql
-- 手动设置用户为member
UPDATE user_auth SET role = 'member' WHERE id = ?;

-- 手动设置用户为admin
UPDATE user_auth SET role = 'admin' WHERE id = ?;
```

### 监控
- 监控用户类型分布
- 监控权限检查失败率
- 监控membership状态变化

## 更新日志 (Changelog)

- **v1.0.0** (2024-06-09): 初始版本
  - 实现用户类型自动检测
  - 添加权限中间件
  - 开放新用户注册
  - 应用权限控制到主要功能 