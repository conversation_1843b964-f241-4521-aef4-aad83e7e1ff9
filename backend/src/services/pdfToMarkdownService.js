const { spawn } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const logger = require('../utils/logger');

class PDFToMarkdownService {
    constructor() {
        this.tempDir = path.join(__dirname, '../../temp');
        this.ensureTempDir();
    }

    async ensureTempDir() {
        try {
            await fs.mkdir(this.tempDir, { recursive: true });
        } catch (error) {
            logger.error('Error creating temp directory:', error);
        }
    }

    /**
     * Convert PDF to Markdown using PyMuPDF4LLM
     * @param {string} pdfPath - Path to the PDF file
     * @param {Object} options - Conversion options
     * @returns {Promise<string>} - Markdown content
     */
    async convertPDFToMarkdown(pdfPath, options = {}) {
        try {
            const {
                pages = null, // 页面范围，如 [0, 1, 2] 或 null (全部页面)
                writeImages = false, // 是否提取图片
                imagePath = null, // 图片保存路径
                pageChunks = false, // 是否按页分块
                extractWords = false // 是否提取单词级别信息
            } = options;

            logger.info(`Converting PDF to Markdown: ${pdfPath}`);

            // 构建Python脚本参数
            const pythonScript = this.generatePythonScript(pdfPath, options);
            const scriptPath = path.join(this.tempDir, `convert_${Date.now()}.py`);
            
            // 写入临时Python脚本
            await fs.writeFile(scriptPath, pythonScript);

            // 执行Python脚本
            const result = await this.executePythonScript(scriptPath);
            
            // 清理临时文件
            await fs.unlink(scriptPath).catch(() => {});

            logger.info(`PDF conversion completed successfully`);
            return result;

        } catch (error) {
            logger.error('Error converting PDF to Markdown:', error);
            throw new Error(`PDF conversion failed: ${error.message}`);
        }
    }

    /**
     * 生成Python脚本内容
     */
    generatePythonScript(pdfPath, options) {
        const {
            pages,
            writeImages,
            imagePath,
            pageChunks,
            extractWords
        } = options;

        return `
import sys
import json
import pymupdf4llm

try:
    # 设置转换参数
    kwargs = {}
    
    ${pages ? `kwargs['pages'] = ${JSON.stringify(pages)}` : ''}
    ${writeImages ? `kwargs['write_images'] = True` : ''}
    ${imagePath ? `kwargs['image_path'] = "${imagePath}"` : ''}
    ${pageChunks ? `kwargs['page_chunks'] = True` : ''}
    ${extractWords ? `kwargs['extract_words'] = True` : ''}
    
    # 执行转换
    result = pymupdf4llm.to_markdown("${pdfPath}", **kwargs)
    
    # 输出结果
    if isinstance(result, list):
        # 如果是分块结果，转换为JSON
        print(json.dumps(result, ensure_ascii=False, indent=2))
    else:
        # 如果是字符串，直接输出
        print(result)
        
except Exception as e:
    print(f"Error: {str(e)}", file=sys.stderr)
    sys.exit(1)
`;
    }

    /**
     * 执行Python脚本
     */
    async executePythonScript(scriptPath) {
        return new Promise((resolve, reject) => {
            const python = spawn('python3', [scriptPath]);
            
            let stdout = '';
            let stderr = '';

            python.stdout.on('data', (data) => {
                stdout += data.toString();
            });

            python.stderr.on('data', (data) => {
                stderr += data.toString();
            });

            python.on('close', (code) => {
                if (code !== 0) {
                    reject(new Error(`Python script failed: ${stderr}`));
                } else {
                    resolve(stdout.trim());
                }
            });

            python.on('error', (error) => {
                reject(new Error(`Failed to spawn Python process: ${error.message}`));
            });
        });
    }

    /**
     * 转换PDF文件并保存为Markdown文件
     */
    async convertAndSave(pdfPath, outputPath, options = {}) {
        try {
            const markdown = await this.convertPDFToMarkdown(pdfPath, options);
            await fs.writeFile(outputPath, markdown, 'utf8');
            
            logger.info(`Markdown saved to: ${outputPath}`);
            return outputPath;
        } catch (error) {
            logger.error('Error converting and saving PDF:', error);
            throw error;
        }
    }

    /**
     * 批量转换PDF文件
     */
    async batchConvert(pdfFiles, outputDir, options = {}) {
        const results = [];
        
        for (const pdfPath of pdfFiles) {
            try {
                const fileName = path.basename(pdfPath, '.pdf');
                const outputPath = path.join(outputDir, `${fileName}.md`);
                
                await this.convertAndSave(pdfPath, outputPath, options);
                results.push({ success: true, pdfPath, outputPath });
                
            } catch (error) {
                logger.error(`Failed to convert ${pdfPath}:`, error);
                results.push({ success: false, pdfPath, error: error.message });
            }
        }
        
        return results;
    }
}

module.exports = new PDFToMarkdownService(); 