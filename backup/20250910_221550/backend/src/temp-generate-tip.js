const path = require('path');
const { execSync } = require('child_process');
const fs = require('fs');

// 获取生成脚本的路径
const generateDailyTipPath = path.join(__dirname, 'scripts/generateDailyTip.js');

console.log(`[${new Date().toISOString()}] 开始生成临时健康小贴士测试...`);

try {
    if (fs.existsSync(generateDailyTipPath)) {
        console.log(`找到生成脚本: ${generateDailyTipPath}`);

        // 执行生成脚本
        const output = execSync(`node ${generateDailyTipPath}`, { encoding: 'utf8' });
        console.log(`健康小贴士生成结果: ${output}`);

        // 检查是否生成成功
        const documentsPath = process.env.LOCAL_TIPS_PATH || path.join(__dirname, '../documents');
        console.log(`检查目录: ${documentsPath}`);

        // 获取当前日期格式(YYYYMMDD)
        const today = new Date().toISOString().split('T')[0].replace(/-/g, '');

        // 检查是否有今天日期的文件
        const files = fs.readdirSync(documentsPath, { recursive: true });
        const todayFiles = files.filter(file =>
            typeof file === 'string' && file.includes(today)
        );

        if (todayFiles.length > 0) {
            console.log(`成功! 找到今天生成的文件:`);
            todayFiles.forEach(file => console.log(`- ${file}`));
        } else {
            console.log(`未找到今天生成的文件，可能生成失败或保存在子目录中`);

            // 递归搜索子目录
            console.log('正在递归搜索子目录...');
            const foundFiles = [];

            function searchFiles(dir) {
                const entries = fs.readdirSync(dir, { withFileTypes: true });
                for (const entry of entries) {
                    const fullPath = path.join(dir, entry.name);
                    if (entry.isDirectory()) {
                        searchFiles(fullPath);
                    } else if (entry.isFile() && entry.name.includes(today)) {
                        foundFiles.push(fullPath);
                    }
                }
            }

            try {
                searchFiles(documentsPath);
                if (foundFiles.length > 0) {
                    console.log(`递归搜索找到今天生成的文件:`);
                    foundFiles.forEach(file => console.log(`- ${file}`));
                } else {
                    console.log('递归搜索也未找到任何今天生成的文件');
                }
            } catch (searchErr) {
                console.error(`搜索文件时出错: ${searchErr.message}`);
            }
        }
    } else {
        console.error(`生成脚本不存在: ${generateDailyTipPath}`);
    }
} catch (error) {
    console.error(`生成健康小贴士出错: ${error.message}`);
    if (error.stdout) console.log(`输出: ${error.stdout}`);
    if (error.stderr) console.error(`错误输出: ${error.stderr}`);
} 