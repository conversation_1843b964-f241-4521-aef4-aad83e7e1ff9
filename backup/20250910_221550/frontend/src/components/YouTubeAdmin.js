import React, { useState, useEffect } from 'react';
import {
    Container, Paper, Typography, TextField, Button, Box, CircularProgress, Alert,
    Table, TableBody, TableCell, TableContainer, TableHead, TableRow, IconButton, Switch, FormControlLabel,
    Grid,
    Chip
} from '@mui/material';
import { Edit as EditIcon, Delete as DeleteIcon, Add as AddIcon, Visibility, VisibilityOff } from '@mui/icons-material';
import { API_URL } from '../utils/env'; // Assuming API_URL is configured
import { useLanguage } from '../context/LanguageContext';

const YouTubeAdmin = () => {
    const { t } = useLanguage();
    const [videos, setVideos] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');

    // Form state for adding/editing videos
    const [isEditing, setIsEditing] = useState(false);
    const [currentVideo, setCurrentVideo] = useState(null);
    const [title, setTitle] = useState('');
    const [description, setDescription] = useState('');
    const [thumbnailUrl, setThumbnailUrl] = useState('');
    const [youtubeLink, setYoutubeLink] = useState('');
    const [category, setCategory] = useState('');
    const [isActive, setIsActive] = useState(true);

    const fetchAdminVideos = async () => {
        setIsLoading(true);
        setError('');
        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`${API_URL}/api/youtube/videos/admin`, { // MODIFIED
                headers: { 'Authorization': `Bearer ${token}` },
            });
            if (!response.ok) throw new Error(t('error_fetching_videos'));
            const data = await response.json();
            setVideos(data || []);
        } catch (err) {
            setError(err.message || t('error_fetching_videos'));
            setVideos([]);
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchAdminVideos();
    }, [t]);

    const clearForm = () => {
        setIsEditing(false);
        setCurrentVideo(null);
        setTitle('');
        setDescription('');
        setThumbnailUrl('');
        setYoutubeLink('');
        setCategory('');
        setIsActive(true);
    };

    const handleEdit = (video) => {
        setIsEditing(true);
        setCurrentVideo(video);
        setTitle(video.title);
        setDescription(video.description || '');
        setThumbnailUrl(video.thumbnailUrl || '');
        setYoutubeLink(video.youtubeLink);
        setCategory(video.category);
        setIsActive(video.is_active);
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setIsLoading(true);
        setError('');
        setSuccess('');
        const token = localStorage.getItem('token');

        const videoData = {
            title,
            description,
            thumbnailUrl,
            youtubeLink,
            category,
            is_active: isActive,
            // Include views and likes if your update endpoint expects them, otherwise remove or set to currentVideo values
            views: isEditing && currentVideo ? currentVideo.views : 0,
            likes: isEditing && currentVideo ? currentVideo.likes : 0,
        };

        try {
            let response;
            if (isEditing) {
                response = await fetch(`${API_URL}/api/youtube/videos/${currentVideo.id}`, { // MODIFIED
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` },
                    body: JSON.stringify(videoData),
                });
            } else {
                response = await fetch(`${API_URL}/api/youtube/videos`, { // MODIFIED
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` },
                    body: JSON.stringify(videoData),
                });
            }

            const result = await response.json();
            if (!response.ok || !result.success) {
                throw new Error(result.message || (isEditing ? t('error_updating_video') : t('error_adding_video')));
            }

            setSuccess(isEditing ? t('video_updated_successfully') : t('video_added_successfully'));
            clearForm();
            fetchAdminVideos(); // Refresh list
        } catch (err) {
            setError(err.message);
        } finally {
            setIsLoading(false);
        }
    };

    const handleDelete = async (videoId) => {
        if (!window.confirm(t('confirm_delete_video'))) return;
        setIsLoading(true);
        setError('');
        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`${API_URL}/api/youtube/videos/${videoId}`, { // MODIFIED
                method: 'DELETE',
                headers: { 'Authorization': `Bearer ${token}` },
            });
            const result = await response.json();
            if (!response.ok || !result.success) {
                throw new Error(result.message || t('error_deleting_video'));
            }
            setSuccess(t('video_deleted_successfully'));
            fetchAdminVideos(); // Refresh list
        } catch (err) {
            setError(err.message);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
            <Paper sx={{ p: 3, mb: 3 }}>
                <Typography variant="h5" gutterBottom component="div">
                    {isEditing ? t('edit_video') : t('add_new_video')}
                    {isEditing && <Button onClick={clearForm} size="small" sx={{ ml: 2 }}>{t('cancel_edit')}</Button>}
                </Typography>
                <form onSubmit={handleSubmit}>
                    <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                            <TextField label={t('title')} value={title} onChange={(e) => setTitle(e.target.value)} fullWidth required />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <TextField label={t('youtube_link')} value={youtubeLink} onChange={(e) => setYoutubeLink(e.target.value)} fullWidth required />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <TextField label={t('category')} value={category} onChange={(e) => setCategory(e.target.value)} fullWidth required />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <TextField label={t('thumbnail_url_optional')} value={thumbnailUrl} onChange={(e) => setThumbnailUrl(e.target.value)} fullWidth />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField label={t('description_optional')} value={description} onChange={(e) => setDescription(e.target.value)} fullWidth multiline rows={3} />
                        </Grid>
                        <Grid item xs={12}>
                            <FormControlLabel
                                control={<Switch checked={isActive} onChange={(e) => setIsActive(e.target.checked)} />}
                                label={isActive ? t('video_active') : t('video_inactive')}
                            />
                        </Grid>
                    </Grid>
                    <Button type="submit" variant="contained" color="primary" sx={{ mt: 2 }} disabled={isLoading} startIcon={isLoading ? <CircularProgress size={20}/> : <AddIcon />}>
                        {isEditing ? t('save_changes') : t('add_video')}
                    </Button>
                </form>
                {error && <Alert severity="error" sx={{ mt: 2 }}>{error}</Alert>}
                {success && <Alert severity="success" sx={{ mt: 2 }}>{success}</Alert>}
            </Paper>

            <Typography variant="h5" gutterBottom component="div" sx={{ mt: 4 }}>
                {t('manage_videos')}
            </Typography>
            {isLoading && !videos.length ? <CircularProgress /> : null}
            <TableContainer component={Paper}>
                <Table sx={{ minWidth: 650 }} aria-label="simple table">
                    <TableHead>
                        <TableRow>
                            <TableCell>{t('title')}</TableCell>
                            <TableCell>{t('category')}</TableCell>
                            <TableCell align="center">{t('status')}</TableCell>
                            <TableCell align="right">{t('actions')}</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {videos.map((video) => (
                            <TableRow key={video.id}>
                                <TableCell component="th" scope="row">
                                    <Box display="flex" alignItems="center">
                                        {video.thumbnailUrl ?
                                            <img 
                                                src={video.thumbnailUrl} 
                                                alt={video.title} 
                                                style={{ 
                                                    width: 80, // Increased width for better visibility
                                                    height: 45, // Adjusted height for 16:9 aspect ratio
                                                    objectFit: 'cover', 
                                                    marginRight: 10, 
                                                    borderRadius: 4
                                                }} 
                                            />
                                        :
                                            <Box sx={{ 
                                                width: 80, // Consistent width
                                                height: 45, // Consistent height
                                                bgcolor: 'grey.300', // Slightly darker placeholder
                                                mr: 1.25, // 10px / 8px theme spacing
                                                borderRadius: 1, 
                                                display:'flex', 
                                                alignItems:'center', 
                                                justifyContent:'center'
                                            }}>
                                                <VisibilityOff fontSize="default"/> {/* Slightly larger icon */}
                                            </Box> 
                                        }
                                        {video.title}
                                    </Box>
                                </TableCell>
                                <TableCell>{video.category}</TableCell>
                                <TableCell align="center">
                                    {video.is_active ? 
                                        <Chip label={t('active')} color="success" size="small" /> : 
                                        <Chip label={t('inactive')} color="default" size="small" />
                                    }
                                </TableCell>
                                <TableCell align="right">
                                    <IconButton onClick={() => handleEdit(video)} size="small"><EditIcon /></IconButton>
                                    <IconButton onClick={() => handleDelete(video.id)} size="small"><DeleteIcon /></IconButton>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>
            {error && !videos.length && <Alert severity="warning" sx={{ mt: 2 }}>{t('no_videos_found_or_error')}</Alert>}
            {!isLoading && !error && videos.length === 0 && 
                <Alert severity="info" sx={{ mt: 2 }}>{t('no_videos_yet_add_one')}</Alert>
            }
        </Container>
    );
};

export default YouTubeAdmin; 