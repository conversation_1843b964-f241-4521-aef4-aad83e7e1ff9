const jwt = require('jsonwebtoken');
const User = require('../models/user');
const pool = require('../config/database');
const bcrypt = require('bcryptjs');

const generateToken = (userId) => {
    return jwt.sign({ id: userId }, process.env.JWT_SECRET, {
        expiresIn: process.env.JWT_EXPIRES_IN
    });
};

// 在未来实现时添加的邮件发送函数
const sendVerificationEmail = async (email, token) => {
    try {
        const nodemailer = require('nodemailer');

        // 创建邮件传输器
        const transporter = nodemailer.createTransport({
            host: process.env.EMAIL_HOST || 'smtp.gmail.com',
            port: parseInt(process.env.EMAIL_PORT || '587'),
            secure: process.env.EMAIL_SECURE === 'true',
            auth: {
                user: process.env.EMAIL_USER,
                pass: process.env.EMAIL_PASS,
            },
        });

        // 验证链接
        const verificationUrl = `${process.env.FRONTEND_URL || 'https://app.mmcwellness.ca'}/verify-email?token=${token}`;

        // 邮件内容
        const mailOptions = {
            from: process.env.EMAIL_FROM || '"MMC Wellness" <<EMAIL>>',
            to: email,
            subject: 'Verify Your MMC Wellness Account',
            html: `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <h2 style="color: #3f51b5;">Welcome to MMC Wellness!</h2>
                    <p>Thank you for registering. Please verify your email address by clicking the link below:</p>
                    <p>
                        <a href="${verificationUrl}" 
                           style="background-color: #3f51b5; color: white; padding: 10px 20px; 
                                  text-decoration: none; border-radius: 4px; display: inline-block;">
                            Verify Email Address
                        </a>
                    </p>
                    <p>Or copy and paste this URL into your browser:</p>
                    <p>${verificationUrl}</p>
                    <p>If you didn't create this account, please ignore this email.</p>
                    <p>Best regards,<br>MMC Wellness Team</p>
                </div>
            `,
        };

        // 发送邮件
        const info = await transporter.sendMail(mailOptions);
        console.log(`Verification email sent to ${email}: ${info.messageId}`);

        return true;
    } catch (error) {
        console.error('Failed to send verification email:', error);
        // 即使邮件发送失败，也让用户注册成功，但记录错误
        return false;
    }
};

// 发送密码重置邮件
const sendPasswordResetEmail = async (email, token) => {
    try {
        const nodemailer = require('nodemailer');

        // 创建邮件传输器
        const transporter = nodemailer.createTransport({
            host: process.env.EMAIL_HOST || 'smtp.gmail.com',
            port: parseInt(process.env.EMAIL_PORT || '587'),
            secure: process.env.EMAIL_SECURE === 'true',
            auth: {
                user: process.env.EMAIL_USER,
                pass: process.env.EMAIL_PASS,
            },
        });

        // 重置链接
        const resetUrl = `${process.env.FRONTEND_URL || 'https://app.mmcwellness.ca'}/reset-password?token=${token}`;

        // 邮件内容
        const mailOptions = {
            from: process.env.EMAIL_FROM || '"MMC Wellness" <<EMAIL>>',
            to: email,
            subject: 'Reset Your MMC Wellness Password',
            html: `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <h2 style="color: #3f51b5;">Password Reset Request</h2>
                    <p>You recently requested to reset your password for your MMC Wellness account.</p>
                    <p>Please click the button below to reset it:</p>
                    <p>
                        <a href="${resetUrl}" 
                           style="background-color: #3f51b5; color: white; padding: 10px 20px; 
                                  text-decoration: none; border-radius: 4px; display: inline-block;">
                            Reset Password
                        </a>
                    </p>
                    <p>Or copy and paste this URL into your browser:</p>
                    <p>${resetUrl}</p>
                    <p>This password reset link is only valid for the next 60 minutes.</p>
                    <p>If you didn't request a password reset, please ignore this email or contact support if you have concerns.</p>
                    <p>Best regards,<br>MMC Wellness Team</p>
                </div>
            `,
        };

        // 发送邮件
        const info = await transporter.sendMail(mailOptions);
        console.log(`Password reset email sent to ${email}: ${info.messageId}`);

        return true;
    } catch (error) {
        console.error('Failed to send password reset email:', error);
        return false;
    }
};

// 发送验证码邮件
const sendVerificationCode = async (email) => {
    try {
        const nodemailer = require('nodemailer');

        // 生成6位数字验证码
        const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();

        // 创建邮件传输器
        const transporter = nodemailer.createTransport({
            host: process.env.EMAIL_HOST || 'smtp.gmail.com',
            port: parseInt(process.env.EMAIL_PORT || '587'),
            secure: process.env.EMAIL_SECURE === 'true',
            auth: {
                user: process.env.EMAIL_USER,
                pass: process.env.EMAIL_PASS,
            },
        });

        // 邮件内容
        const mailOptions = {
            from: process.env.EMAIL_FROM || '"MMC Wellness" <<EMAIL>>',
            to: email,
            subject: 'Your MMC Wellness Verification Code',
            html: `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <h2 style="color: #3f51b5;">MMC Wellness Verification</h2>
                    <p>Please use the following verification code to complete your login:</p>
                    <p style="font-size: 24px; font-weight: bold; letter-spacing: 5px; 
                              background-color: #f5f5f5; padding: 10px; 
                              text-align: center; border-radius: 4px;">
                        ${verificationCode}
                    </p>
                    <p>This code will expire in 15 minutes.</p>
                    <p>If you didn't request this code, you can safely ignore this email.</p>
                    <p>Best regards,<br>MMC Wellness Team</p>
                </div>
            `,
        };

        // 发送邮件
        const info = await transporter.sendMail(mailOptions);
        console.log(`Verification code sent to ${email}: ${info.messageId}`);

        // 返回验证码，以便存储在后端
        return { success: true, verificationCode };
    } catch (error) {
        console.error('Failed to send verification code email:', error);
        return { success: false, error: error.message };
    }
};

// 存储邮箱验证码的对象 (生产环境中应该使用Redis或数据库)
const emailVerificationCodes = {};
exports.emailVerificationCodes = emailVerificationCodes;

// 跟踪已验证的邮箱
const verifiedEmails = new Set();
exports.verifiedEmails = verifiedEmails;

// 发送邮箱验证码
exports.sendEmailVerificationCode = async (req, res) => {
    try {
        const { email } = req.body;

        if (!email) {
            return res.status(400).json({ message: 'Email is required' });
        }

        // 发送验证码
        const result = await sendVerificationCode(email);

        if (!result.success) {
            return res.status(500).json({
                message: 'Failed to send verification code',
                error: result.error
            });
        }

        // 存储验证码并设置过期时间 (15分钟)
        emailVerificationCodes[email] = {
            code: result.verificationCode,
            expiresAt: Date.now() + 15 * 60 * 1000 // 15分钟后过期
        };

        res.json({
            success: true,
            message: 'Verification code sent successfully'
        });
    } catch (error) {
        console.error('Send verification code error:', error);
        res.status(500).json({ message: 'Failed to send verification code' });
    }
};

// 验证邮箱验证码
exports.verifyEmailCode = async (req, res) => {
    try {
        const { email, code } = req.body;

        if (!email || !code) {
            return res.status(400).json({ message: 'Email and verification code are required' });
        }

        // 检查验证码是否存在
        if (!emailVerificationCodes[email]) {
            return res.status(400).json({ message: 'Verification code not found or expired' });
        }

        const storedCode = emailVerificationCodes[email];

        // 检查验证码是否过期
        if (Date.now() > storedCode.expiresAt) {
            // 删除过期的验证码
            delete emailVerificationCodes[email];
            return res.status(400).json({ message: 'Verification code has expired' });
        }

        // 检查验证码是否匹配
        if (storedCode.code !== code) {
            return res.status(400).json({ message: 'Invalid verification code' });
        }

        // 验证成功，删除已使用的验证码并记录已验证的邮箱
        delete emailVerificationCodes[email];
        verifiedEmails.add(email); // 添加到已验证集合

        // 更新数据库中的验证状态
        const [result] = await pool.query(
            'UPDATE user_auth SET is_verified = true WHERE email = ?',
            [email]
        );

        // 如果用户尚未注册，不需要处理数据库更新结果

        res.json({
            success: true,
            message: 'Email verification successful'
        });
    } catch (error) {
        console.error('Verify email code error:', error);
        res.status(500).json({ message: 'Failed to verify email code' });
    }
};

exports.register = async (req, res) => {
    try {
        const { 
            email, 
            password, 
            firstName, 
            lastName, 
            phone, 
            dateOfBirth,
            referralCode
        } = req.body;

        // Validate input
        if (!email || !password || !firstName || !lastName) {
            return res.status(400).json({ 
                success: false, 
                message: 'Missing required fields' 
            });
        }

        // Check if referral code is provided (it's now required)
        if (!referralCode) {
            return res.status(400).json({
                success: false,
                message: 'A valid referral code is required to register'
            });
        }

        // Validate referral code
        const isValidReferralCode = await User.validateReferralCode(referralCode);
        if (!isValidReferralCode) {
            return res.status(400).json({
                success: false,
                message: 'Invalid referral code'
            });
        }

        // Check if email already exists
        const emailExists = await User.checkEmailExists(email);
        if (emailExists) {
            return res.status(400).json({ 
                success: false, 
                message: 'Email already in use' 
            });
        }

        // Register user
        const user = await User.create({
            email,
            password,
            firstName,
            lastName,
            phone,
            dateOfBirth,
            referralCode
        });

        // Send verification email
        await sendVerificationEmail(email, user.verificationToken);

        res.status(201).json({
            success: true,
            message: 'User registered successfully',
            user: {
                id: user.userId,
                email: user.email,
                demographicNo: user.demographicNo
            }
        });
    } catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Registration failed', 
            error: error.message 
        });
    }
};

exports.login = async (req, res) => {
    try {
        const { email, password } = req.body;

        // Find user
        const user = await User.findByEmail(email);
        if (!user) {
            return res.status(401).json({ message: 'Invalid email or password' });
        }

        // Verify password
        const isValidPassword = await User.verifyPassword(password, user.password_hash);
        if (!isValidPassword) {
            return res.status(401).json({ message: 'Invalid email or password' });
        }

        // 检查用户邮箱是否已验证 - 如果账户标记需要验证邮箱
        if (user.is_verified === false) {
            return res.status(403).json({
                message: 'Please verify your email address before logging in',
                needsVerification: true,
                userId: user.id
            });
        }

        // Get demographic information
        const demographicInfo = user.demographic_no ?
            await User.getDemographicInfo(user.demographic_no) : null;

        // Generate JWT token
        const token = generateToken(user.id);

        res.json({
            success: true,
            token,
            user: {
                id: user.id,
                email: user.email,
                demographic_no: user.demographic_no,
                role: user.role,
                demographicInfo,
                isVerified: user.is_verified === 1
            }
        });
    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ message: 'Login failed, please try again later' });
    }
};

exports.linkAccount = async (req, res) => {
    try {
        const { email, password } = req.body;

        if (!email || !password) {
            return res.status(400).json({ message: 'Email and password are required' });
        }

        // 检查邮箱是否已验证 - 从已验证集合或数据库中检查
        const hasVerified = verifiedEmails.has(email);
        if (!hasVerified) {
            return res.status(403).json({
                message: 'Email verification required',
                requireVerification: true
            });
        }

        // 查找与邮箱关联的所有患者记录
        const emailCheck = await User.checkEmailExists(email);

        if (!emailCheck.existsInOscar || emailCheck.demographicCount === 0) {
            return res.status(404).json({
                message: 'No patient records found with this email',
                patientCount: 0
            });
        }

        // 如果找到多个患者记录，让用户选择
        if (emailCheck.demographicCount > 1) {
            return res.status(200).json({
                message: 'Multiple patient records found. Please select one.',
                patientCount: emailCheck.demographicCount,
                patientRecords: emailCheck.demographics
            });
        }

        // 如果只找到一个患者记录，直接关联
        const demographicInfo = emailCheck.demographics[0];

        // 检查是否已经关联
        const existingUser = await User.findByDemographicNo(demographicInfo.demographic_no);
        if (existingUser) {
            return res.status(400).json({ message: 'This patient record is already linked to another account' });
        }

        // 创建新用户账户
        const userId = await User.create({
            email,
            password,
            demographic_no: demographicInfo.demographic_no
        });

        // 从已验证集合中移除该邮箱
        verifiedEmails.delete(email);

        // 生成 JWT 令牌
        const token = generateToken(userId);

        res.json({
            success: true,
            message: 'Account linked successfully',
            token,
            user: {
                id: userId,
                email,
                demographic_no: demographicInfo.demographic_no,
                demographicInfo
            }
        });
    } catch (error) {
        console.error('Account linking error:', error);
        res.status(500).json({ message: 'Account linking failed, please try again later' });
    }
};

// Get user profile
exports.getProfile = async (req, res) => {
    try {
        // req.user is set by the authenticate middleware and should include the role
        if (!req.user) {
            return res.status(401).json({ success: false, message: '用户未登录' });
        }

        const userId = req.user.id;
        const demographicNo = req.user.demographic_no;

        // The user object from req.user should already have basic auth info including role
        const userAuthInfo = {
            id: req.user.id,
            email: req.user.email,
            demographic_no: req.user.demographic_no,
            isVerified: req.user.isVerified,
            role: req.user.role, // Ensure role is explicitly included
            // Add any other fields from user_auth you want to expose directly on the user object
        };

        let demographicInfo = null;
        if (demographicNo) {
            demographicInfo = await User.getDemographicInfo(demographicNo);
        }

        res.json({
            success: true,
            user: {
                ...userAuthInfo, // Spread basic auth info
                demographicInfo: demographicInfo // Nest demographic info
            }
        });

    } catch (error) {
        console.error('获取用户资料失败:', error);
        res.status(500).json({ success: false, message: '获取用户资料失败' });
    }
};

exports.renewMembership = async (req, res) => {
    try {
        const user = req.user;
        const { membershipType } = req.body;

        if (!user.demographic_no) {
            return res.status(400).json({ message: 'No linked patient record found' });
        }

        // 获取会员类型对应的价格
        const membershipPrices = {
            'AHM_EM': 2350,
            'AHM_EM_KD': 850,
            'AHM_SIG': 5500,
            'AHM_SIG_KD': 850
        };

        const price = membershipPrices[membershipType];
        if (!price) {
            return res.status(400).json({ message: 'Invalid membership type' });
        }

        // 创建新的会员记录
        const [result] = await pool.query(
            `INSERT INTO billingmaster (
                demographic_no, service_date, billing_code, 
                bill_amount, billingstatus
            ) VALUES (?, NOW(), ?, ?, 'A')`,
            [user.demographic_no, membershipType, price]
        );

        res.json({
            success: true,
            message: 'Membership renewed successfully'
        });
    } catch (error) {
        console.error('Renew membership error:', error);
        res.status(500).json({ message: 'Failed to renew membership, please try again later' });
    }
};

// 检查邮箱是否存在
exports.checkEmail = async (req, res) => {
    try {
        const { email } = req.body;

        if (!email) {
            return res.status(400).json({ message: 'Email is required' });
        }

        // 使用 User 模型的 checkEmailExists 方法检查邮箱状态
        const result = await User.checkEmailExists(email);

        // 如果邮箱已经在系统中注册
        if (result.exists) {
            return res.status(400).json({
                message: 'This email is already registered. Please login instead.',
                exists: true,
                inUserAuth: true
            });
        }

        // 返回结果，包括是否在Oscar系统中存在及关联的患者记录
        return res.json({
            exists: result.exists,
            existsInOscar: result.existsInOscar,
            demographicCount: result.demographicCount,
            demographics: result.demographics
        });
    } catch (error) {
        console.error('Check email error:', error);
        res.status(500).json({ message: 'Failed to check email, please try again later' });
    }
};

// 为 Oscar 现有患者创建登录账户
exports.linkOscarPatient = async (req, res) => {
    try {
        const { email, password, demographicNo } = req.body;

        if (!email || !password) {
            return res.status(400).json({ message: 'Email and password are required' });
        }

        // 检查邮箱是否已验证
        if (!verifiedEmails.has(email)) {
            return res.status(403).json({
                message: 'Email verification required',
                requireVerification: true
            });
        }

        // 如果存在多个患者记录但未提供 demographicNo，则返回错误
        if (!demographicNo) {
            const result = await User.checkEmailExists(email);
            if (result.demographicCount > 1) {
                return res.status(400).json({
                    message: 'Multiple patient records found with this email. Please select a specific patient.',
                    patientRecords: result.demographics
                });
            }
        }

        const result = await User.linkOscarPatient(email, password, demographicNo);

        // 设置患者项目
        await User.setupPatientProgram(result.demographicNo);

        // 从已验证集合中移除该邮箱
        verifiedEmails.delete(email);

        // 生成 JWT 令牌
        const token = generateToken(result.userId);

        res.json({
            success: true,
            message: 'Account created successfully',
            token,
            user: {
                id: result.userId,
                email,
                demographic_no: result.demographicNo,
                demographicInfo: result.demographicInfo
            }
        });
    } catch (error) {
        console.error('Link Oscar patient error:', error);
        res.status(500).json({ message: error.message || 'Failed to create account, please try again later' });
    }
};

// 验证邮箱
exports.verifyEmail = async (req, res) => {
    try {
        const { token } = req.params;

        if (!token) {
            return res.status(400).json({ message: 'Verification token is required' });
        }

        const result = await User.verifyEmail(token);

        if (!result.success) {
            return res.status(400).json({ message: result.message });
        }

        res.json({
            success: true,
            message: result.message
        });
    } catch (error) {
        console.error('Email verification error:', error);
        res.status(500).json({ message: 'Email verification failed, please try again later' });
    }
};

// 重新发送验证邮件
exports.resendVerificationEmail = async (req, res) => {
    try {
        const { userId } = req.body;

        if (!userId) {
            return res.status(400).json({ message: 'User ID is required' });
        }

        // 查找用户邮箱
        const user = await User.findById(userId);
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // 重新生成验证令牌
        const result = await User.regenerateVerificationToken(userId);

        if (!result.success) {
            return res.status(400).json({ message: result.message });
        }

        // 发送验证邮件
        await sendVerificationEmail(user.email, result.verificationToken);

        res.json({
            success: true,
            message: 'Verification email has been sent'
        });
    } catch (error) {
        console.error('Resend verification email error:', error);
        res.status(500).json({ message: 'Failed to resend verification email, please try again later' });
    }
};

// 请求密码重置
exports.forgotPassword = async (req, res) => {
    try {
        const { email } = req.body;

        if (!email) {
            return res.status(400).json({ message: 'Email is required' });
        }

        // 创建密码重置令牌
        const result = await User.createPasswordResetToken(email);

        if (!result.success) {
            // 为了安全起见，即使用户不存在也返回成功
            return res.json({
                success: true,
                message: 'If your email address exists in our database, you will receive a password recovery link shortly.'
            });
        }

        // 发送密码重置邮件
        await sendPasswordResetEmail(result.email, result.resetToken);

        res.json({
            success: true,
            message: 'If your email address exists in our database, you will receive a password recovery link shortly.'
        });
    } catch (error) {
        console.error('Forgot password error:', error);
        res.status(500).json({ message: 'An error occurred while processing your request.' });
    }
};

// 验证重置令牌
exports.verifyResetToken = async (req, res) => {
    try {
        const { token } = req.params;

        if (!token) {
            return res.status(400).json({ message: 'Reset token is required' });
        }

        const result = await User.verifyPasswordResetToken(token);

        res.json({
            success: result.success,
            message: result.message
        });
    } catch (error) {
        console.error('Verify reset token error:', error);
        res.status(500).json({ message: 'An error occurred while verifying the reset token.' });
    }
};

// 重设密码
exports.resetPassword = async (req, res) => {
    try {
        const { token, newPassword } = req.body;

        if (!token || !newPassword) {
            return res.status(400).json({ message: 'Token and new password are required' });
        }

        // 验证令牌
        const [tokenRecord] = await pool.query(
            'SELECT * FROM password_reset_tokens WHERE token = ? AND expires_at > NOW()',
            [token]
        );

        if (tokenRecord.length === 0) {
            return res.status(400).json({ message: 'Invalid or expired token' });
        }

        const { email } = tokenRecord[0];

        // 哈希新密码
        const saltRounds = 10;
        const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

        // 更新密码
        await pool.query('UPDATE user_auth SET password_hash = ? WHERE email = ?', [hashedPassword, email]);

        // 删除使用过的令牌
        await pool.query('DELETE FROM password_reset_tokens WHERE token = ?', [token]);

        return res.json({ success: true, message: 'Password reset successful' });
    } catch (error) {
        console.error('Error resetting password:', error);
        return res.status(500).json({ message: 'Server error' });
    }
};

// 直接重设密码（通过验证邮箱后）
exports.resetPasswordDirect = async (req, res) => {
    try {
        const { email, newPassword } = req.body;

        if (!email || !newPassword) {
            return res.status(400).json({ message: 'Email and new password are required' });
        }

        // 检查用户是否存在
        const [user] = await pool.query('SELECT * FROM user_auth WHERE email = ?', [email]);

        if (user.length === 0) {
            return res.status(404).json({ message: 'User not found' });
        }

        // 检查邮箱是否已验证
        if (!user[0].is_verified) {
            return res.status(400).json({ message: 'Email not verified' });
        }

        // 哈希新密码
        const saltRounds = 10;
        const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

        // 更新密码
        await pool.query('UPDATE user_auth SET password_hash = ? WHERE email = ?', [hashedPassword, email]);

        return res.json({ success: true, message: 'Password reset successful' });
    } catch (error) {
        console.error('Error resetting password:', error);
        return res.status(500).json({ message: 'Server error' });
    }
};

// Get profile for a specific demographic number (self or family member)
exports.getViewedProfile = async (req, res) => {
    console.log(`--- Entering getViewedProfile for target ${req.params.demographicNo} by user ${req.user?.id} (demo ${req.user?.demographic_no}) ---`); // Entry log
    try {
        const loggedInUser = req.user;
        const targetDemographicNo = parseInt(req.params.demographicNo, 10);

        if (!loggedInUser || !loggedInUser.demographic_no) {
            console.log('getViewedProfile check failed: No loggedInUser or demographic_no.');
            return res.status(403).json({ message: 'Authentication required or user not linked.' });
        }

        // Admin check - admins can view any patient's profile
        if (loggedInUser.role === 'admin') {
            console.log(`getViewedProfile check: Admin user ${loggedInUser.id} granted access to view demographic ${targetDemographicNo}`);

            // Fetch the demographic info for admin
            const demographicInfo = await User.getDemographicInfo(targetDemographicNo);
            if (!demographicInfo) {
                return res.status(404).json({ message: 'Target profile not found.' });
            }

            return res.json({
                success: true,
                demographicInfo
            });
        }

        const loggedInDemoNo = loggedInUser.demographic_no;

        // Check if viewing self
        const isSelf = targetDemographicNo === loggedInDemoNo;
        console.log(`getViewedProfile check: isSelf = ${isSelf}`);

        let isAllowedRelative = false;
        let hasRecentVerification = false; // Flag for temporary access

        // If not viewing self, check permanent family relationships first
        if (!isSelf) {
            try {
                console.log(`getViewedProfile check: Checking permanent relationship for ${loggedInDemoNo} -> ${targetDemographicNo}`);
                const family = await User.getFamilyMembers(loggedInDemoNo);
                isAllowedRelative = family.some(member => member.relative_demographic_no === targetDemographicNo);
                console.log(`getViewedProfile check: isAllowedRelative = ${isAllowedRelative}`);
            } catch (relError) {
                console.error('Error checking permanent relationship:', relError);
                return res.status(500).json({ message: 'Failed to verify relationship permissions.' });
            }
        }

        // If not self and not a permanent relative, check for recent temporary verification
        if (!isSelf && !isAllowedRelative) {
            try {
                console.log(`getViewedProfile check: Checking recent verification for ${loggedInDemoNo} -> ${targetDemographicNo}`);
                const [verification] = await pool.query(
                    `SELECT 1
                     FROM view_verification_codes
                     WHERE requester_demographic_no = ?
                       AND target_demographic_no = ?
                       AND verified_at > NOW() - INTERVAL 30 MINUTE
                     LIMIT 1`,
                    [loggedInDemoNo, targetDemographicNo]
                );
                hasRecentVerification = verification.length > 0;
                console.log(`getViewedProfile check: hasRecentVerification = ${hasRecentVerification}`);
            } catch (verificationError) {
                console.error('Error checking view verification codes:', verificationError);
                return res.status(500).json({ message: 'Failed to verify access permissions.' });
            }
        }

        // Check permissions: Allow if self, related, or recently verified
        console.log(`getViewedProfile check: Final permission check: isSelf=${isSelf}, isAllowedRelative=${isAllowedRelative}, hasRecentVerification=${hasRecentVerification}`);
        if (!isSelf && !isAllowedRelative && !hasRecentVerification) {
            console.log(`getViewedProfile check failed: Permission denied for ${loggedInDemoNo} to view ${targetDemographicNo}.`);
            return res.status(403).json({ message: 'Permission denied. Verification required or expired.' }); // Updated message
        }

        // Permission granted, fetch the target demographic info
        console.log(`getViewedProfile check: Permission granted. Fetching info for ${targetDemographicNo}`);
        const demographicInfo = await User.getDemographicInfo(targetDemographicNo);
        if (!demographicInfo) {
            return res.status(404).json({ message: 'Target profile not found.' });
        }

        // We only return demographicInfo for the viewed profile, not full user object
        res.json({
            success: true,
            demographicInfo // Only send the specific info needed
        });

    } catch (error) {
        console.error('Get Viewed Profile error:', error);
        res.status(500).json({ message: 'Failed to retrieve viewed profile' });
    }
};

// 获取用户及其家庭成员的会员信息
exports.getMembershipInfo = async (req, res) => {
    try {
        const { demographicNo } = req.params;

        if (!demographicNo) {
            return res.status(400).json({ message: 'Demographic number is required' });
        }

        // 获取当前用户的会员信息
        const membershipInfo = await User.getMembershipInfo(demographicNo);

        // 获取家庭成员
        const familyMembers = await User.getFamilyMembers(demographicNo);
        const familyMemberships = [];

        // 获取每个家庭成员的会员信息
        for (const member of familyMembers) {
            const membershipData = await User.getMembershipInfo(member.relative_demographic_no);
            if (membershipData) {
                familyMemberships.push({
                    demographic_no: member.relative_demographic_no,
                    first_name: member.relative_first_name,
                    last_name: member.relative_last_name,
                    relationship: member.relationship_type,
                    membership: membershipData
                });
            } else {
                familyMemberships.push({
                    demographic_no: member.relative_demographic_no,
                    first_name: member.relative_first_name,
                    last_name: member.relative_last_name,
                    relationship: member.relationship_type,
                    membership: null
                });
            }
        }

        res.json({
            success: true,
            membershipInfo,
            familyMemberships
        });
    } catch (error) {
        console.error('Get membership info error:', error);
        res.status(500).json({ message: 'Failed to retrieve membership information, please try again later' });
    }
};

// 检查用户是否为管理员
exports.checkAdmin = async (req, res) => {
    try {
        // 假设用户角色存储在user_auth表的role字段
        const [rows] = await pool.query(
            'SELECT role FROM user_auth WHERE id = ?',
            [req.user.id]
        );

        if (rows.length === 0) {
            return res.status(404).json({ success: false, message: '用户不存在', isAdmin: false });
        }

        const isAdmin = rows[0].role === 'admin';
        res.json({ success: true, isAdmin });
    } catch (error) {
        console.error('检查管理员状态失败:', error);
        res.status(500).json({ success: false, message: '服务器错误', isAdmin: false });
    }
};

// Admin: Search clients flexibly
exports.adminSearchClients = async (req, res) => {
    try {
        // Check admin role
        if (!req.user || req.user.role !== 'admin') {
            return res.status(403).json({ message: 'Admin access required' });
        }
        const { name, email, phone, hin, page = 1, limit = 20 } = req.query;
        const result = await User.searchDemographics({
            name,
            email,
            phone,
            hin,
            page: parseInt(page, 10) || 1,
            limit: parseInt(limit, 10) || 20
        });
        res.json(result);
    } catch (error) {
        console.error('Admin search clients error:', error);
        res.status(500).json({ message: 'Server error searching clients' });
    }
};

module.exports = exports; 