const fs = require('fs');
const path = require('path');
const pool = require('../config/database');
const { createLogger } = require('../utils/logger');
const logger = createLogger('applyReferralMigration');

/**
 * Apply referral system migration SQL
 */
async function applyReferralMigration() {
    try {
        logger.info('Starting referral system migration...');
        
        const migrationPath = path.join(__dirname, '../database/migrations/referral_system.sql');
        if (!fs.existsSync(migrationPath)) {
            logger.error(`Migration file not found: ${migrationPath}`);
            process.exit(1);
        }
        
        const sql = fs.readFileSync(migrationPath, 'utf8');
        const statements = sql.split(';').filter(stmt => stmt.trim().length > 0);
        
        const connection = await pool.getConnection();
        await connection.beginTransaction();
        
        try {
            for (const statement of statements) {
                await connection.query(statement);
                logger.info(`Executed SQL: ${statement.substring(0, 50)}...`);
            }
            
            await connection.commit();
            logger.info('Referral system migration completed successfully');
        } catch (error) {
            await connection.rollback();
            logger.error('Failed to apply migration. Rolling back...', error);
            throw error;
        } finally {
            connection.release();
        }
    } catch (error) {
        logger.error('Error applying referral system migration:', error);
        process.exit(1);
    }
}

// Run the migration if this file is executed directly
if (require.main === module) {
    applyReferralMigration();
}

module.exports = applyReferralMigration; 