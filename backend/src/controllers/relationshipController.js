const crypto = require('crypto');
const bcrypt = require('bcryptjs');
const pool = require('../config/database');
const User = require('../models/user'); // Assuming User model has relationship/demographic fetching logic
const nodemailer = require('nodemailer'); // Import nodemailer
const { sendVerificationCode } = require('./authController'); // Reuse or adapt email sending

// Helper function to generate secure 6-digit code
function generateVerificationCode() {
    return Math.floor(100000 + Math.random() * 900000).toString();
}

// Helper function to hash code
async function hashCode(code) {
    const salt = await bcrypt.genSalt(10);
    return await bcrypt.hash(code, salt);
}

// Helper function to compare code with hash
async function compareCode(plainCode, hashedCode) {
    return await bcrypt.compare(plainCode, hashedCode);
}

/**
 * Initiate the process to switch view to a family member.
 * Sends a verification code to the target member's email.
 * POST /api/relationships/switch-view/initiate
 * Body: { targetDemographicNo: number }
 */
exports.initiateSwitchView = async (req, res) => {
    const requesterUserId = req.user.id; // Assuming auth middleware adds user object to req
    const requesterDemoNo = req.user.demographic_no;
    const requesterFirstName = req.user.demographicInfo?.first_name || 'Your'; // Get requester's name if available
    const { targetDemographicNo } = req.body;

    if (!requesterUserId || !requesterDemoNo) {
        return res.status(401).json({ success: false, message: 'User not authenticated or missing demographic info.' });
    }

    if (!targetDemographicNo || typeof targetDemographicNo !== 'number') {
        return res.status(400).json({ success: false, message: 'Target demographic number is required.' });
    }

    if (requesterDemoNo === targetDemographicNo) {
        return res.status(400).json({ success: false, message: 'Cannot initiate verification for yourself.' });
    }

    try {
        // 1. Verify relationship exists (e.g., using a method from User model)
        const familyMembers = await User.getFamilyMembers(requesterDemoNo);
        const isAllowedRelative = familyMembers.some(member => member.relative_demographic_no === targetDemographicNo);

        if (!isAllowedRelative) {
            return res.status(403).json({ success: false, message: 'Permission denied. Not a linked family member.' });
        }

        // 2. Get target member's email address
        const targetDemographicInfo = await User.getDemographicInfo(targetDemographicNo);
        if (!targetDemographicInfo || !targetDemographicInfo.email) {
            // Decide how to handle missing email - maybe disallow switching?
            console.error(`Cannot initiate switch view: Target demographic ${targetDemographicNo} has no email address.`);
            return res.status(400).json({ success: false, message: 'Target family member does not have an email address registered.' });
        }
        const targetEmail = targetDemographicInfo.email;

        // 3. Generate code and hash
        const verificationCode = generateVerificationCode();
        const verificationCodeHash = await hashCode(verificationCode);

        // 4. Store in DB
        const expiresMinutes = 15; // Set expiry time (e.g., 15 minutes)
        const expiresAt = new Date(Date.now() + expiresMinutes * 60 * 1000);

        const insertQuery = `
            INSERT INTO view_verification_codes
            (requester_user_id, target_demographic_no, target_email, verification_code_hash, expires_at)
            VALUES (?, ?, ?, ?, ?)
        `;
        await pool.query(insertQuery, [
            requesterUserId,
            targetDemographicNo,
            targetEmail,
            verificationCodeHash,
            expiresAt
        ]);

        // 5. Send email with plain code using Nodemailer
        try {
            const transporter = nodemailer.createTransport({
                host: process.env.EMAIL_HOST || 'smtp.gmail.com',
                port: parseInt(process.env.EMAIL_PORT || '587'),
                secure: process.env.EMAIL_SECURE === 'true',
                auth: {
                    user: process.env.EMAIL_USER,
                    pass: process.env.EMAIL_PASS,
                },
            });

            // --- Updated Bilingual Email Content ---
            const subject = `[Action Required / 需操作] Authorize Family Member Access / 授权家人访问 - MMC Wellness Portal`;
            const htmlBody = `
                <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 20px auto; border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
                    <div style="background-color: #f7f7f7; padding: 15px 20px; border-bottom: 1px solid #ddd;">
                        <h2 style="margin: 0; color: #3f51b5;">MMC Wellness Portal - Authorization Request / 授权请求</h2>
                    </div>
                    <div style="padding: 20px;">
                        <p>Dear ${targetDemographicInfo?.first_name || 'Patient'},</p>
                        <p>A request has been made by <strong>${requesterFirstName}</strong> (${req.user.email || 'user'}) to temporarily access your health information profile on the MMC Wellness portal.</p>
                        <p>To authorize this access for this session only (valid for ${expiresMinutes} minutes), please provide the following Authorization Code to ${requesterFirstName} when prompted:</p>
                        <div style="background-color: #eef1f5; padding: 15px; margin: 20px 0; text-align: center; border-radius: 4px;">
                            <p style="margin: 0; font-size: 28px; font-weight: bold; letter-spacing: 5px; color: #3f51b5;">
                                ${verificationCode}
                            </p>
                        </div>
                        <p><strong>If you did not expect this request or do not wish to grant access, please ignore this email.</strong> No access will be granted without entering this code correctly.</p>
                        <hr style="border: 0; border-top: 1px solid #eee; margin: 20px 0;">
                        <p style="font-size: 0.9em; color: #666;"><strong>Please note:</strong> Sharing access to your health information is solely your decision and responsibility. MMC Wellness provides this feature as a convenience but is not responsible for how your information is viewed or used by the person you authorize. Access granted is temporary and will expire automatically.</p>
                    </div>

                    <hr style="border: 0; height: 1px; background-color: #ccc; margin: 20px 0;">

                    <div style="padding: 20px;">
                        <p>尊敬的 ${targetDemographicInfo?.first_name || '患者'}：</p>
                        <p>用户 <strong>${requesterFirstName}</strong> (${req.user.email || '用户'}) 请求临时访问您在 MMC Wellness 平台上的健康信息档案。</p>
                        <p>如需授权此临时访问权限（有效期 ${expiresMinutes} 分钟），请在系统提示时向 ${requesterFirstName} 提供以下授权码：</p>
                        <div style="background-color: #eef1f5; padding: 15px; margin: 20px 0; text-align: center; border-radius: 4px;">
                             <p style="margin: 0; font-size: 28px; font-weight: bold; letter-spacing: 5px; color: #3f51b5;">
                                ${verificationCode}
                            </p>
                        </div>
                        <p><strong>如果您未预期此请求或不希望授权访问，请忽略此邮件。</strong> 未正确输入此验证码将不会授予任何访问权限。</p>
                        <hr style="border: 0; border-top: 1px solid #eee; margin: 20px 0;">
                        <p style="font-size: 0.9em; color: #666;"><strong>请注意：</strong> 共享您的健康信息访问权限完全由您自行决定并承担责任。MMC Wellness 提供此功能旨在方便，但对于您授权的人员如何查看或使用您的信息不承担任何责任。所授予的访问权限是临时的，并将自动过期。</p>
                    </div>

                    <div style="background-color: #f7f7f7; padding: 10px 20px; border-top: 1px solid #ddd; font-size: 0.8em; text-align: center; color: #777;">
                        Thank you / 谢谢,<br>The MMC Wellness Team / MMC Wellness 团队
                    </div>
                </div>
            `;
            // Fallback text version (Bilingual)
            const textBody = `
                Dear ${targetDemographicInfo?.first_name || 'Patient'},

A request has been made by ${requesterFirstName} (${req.user.email || 'user'}) to temporarily access your health information profile on the MMC Wellness portal.

To authorize this access for this session only (valid for ${expiresMinutes} minutes), please provide the following Authorization Code to ${requesterFirstName} when prompted:

Verification Code: ${verificationCode}

If you did not expect this request or do not wish to grant access, please ignore this email. No access will be granted without entering this code correctly.

Please note: Sharing access to your health information is solely your decision and responsibility. MMC Wellness provides this feature as a convenience but is not responsible for how your information is viewed or used by the person you authorize. Access granted is temporary and will expire automatically.

--------------------------------------------------

尊敬的 ${targetDemographicInfo?.first_name || '患者'}：

用户 ${requesterFirstName} (${req.user.email || '用户'}) 请求临时访问您在 MMC Wellness 平台上的健康信息档案。

如需授权此临时访问权限（有效期 ${expiresMinutes} 分钟），请在系统提示时向 ${requesterFirstName} 提供以下授权码：

验证码：${verificationCode}

如果您未预期此请求或不希望授权访问，请忽略此邮件。未正确输入此验证码将不会授予任何访问权限。

请注意：共享您的健康信息访问权限完全由您自行决定并承担责任。MMC Wellness 提供此功能旨在方便，但对于您授权的人员如何查看或使用您的信息不承担任何责任。所授予的访问权限是临时的，并将自动过期。

--------------------------------------------------

Thank you / 谢谢,
The MMC Wellness Team / MMC Wellness 团队
            `;

            const mailOptions = {
                from: process.env.EMAIL_FROM || '"MMC Wellness" <<EMAIL>>',
                to: targetEmail,
                subject: subject, // Bilingual subject
                html: htmlBody, // Bilingual HTML
                text: textBody // Bilingual Text
            };

            const info = await transporter.sendMail(mailOptions);
            console.log(`Authorization request email sent to ${targetEmail}: ${info.messageId}`);
        } catch (emailError) {
            console.error(`Failed to send authorization request email to ${targetEmail}:`, emailError);
            // Decide if failure to send email should prevent the user from trying
            // For now, we'll let the API request succeed, but log the error.
            // Maybe return a specific message indicating email might not have sent?
        }

        res.status(200).json({
            success: true,
            message: 'Verification code sent to the family member\'s email address.',
            targetEmail: targetEmail
            // DO NOT send the code back in the response
        });

    } catch (error) {
        console.error('Error initiating switch view:', error);
        res.status(500).json({ success: false, message: 'Internal server error initiating switch view.' });
    }
};

/**
 * Verify the code entered by the user attempting to switch view.
 * POST /api/relationships/switch-view/verify
 * Body: { targetDemographicNo: number, verificationCode: string }
 */
exports.verifySwitchViewCode = async (req, res) => {
    const requesterUserId = req.user.id; // Assuming auth middleware adds user object to req
    const { targetDemographicNo, verificationCode } = req.body;

    if (!requesterUserId) {
        return res.status(401).json({ success: false, message: 'User not authenticated.' });
    }

    if (!targetDemographicNo || !verificationCode) {
        return res.status(400).json({ success: false, message: 'Target demographic number and verification code are required.' });
    }

    try {
        // 1. Find the latest unverified code for this requester and target within expiry
        const selectQuery = `
            SELECT id, verification_code_hash, expires_at
            FROM view_verification_codes
            WHERE requester_user_id = ?
              AND target_demographic_no = ?
              AND is_verified = FALSE
              AND expires_at > NOW()
            ORDER BY created_at DESC
            LIMIT 1
        `;
        const [rows] = await pool.query(selectQuery, [requesterUserId, targetDemographicNo]);

        if (rows.length === 0) {
            return res.status(400).json({ success: false, message: 'Invalid or expired verification code.' });
        }

        const record = rows[0];

        // 2. Compare the provided code with the stored hash
        const isMatch = await compareCode(verificationCode, record.verification_code_hash);

        if (!isMatch) {
            return res.status(400).json({ success: false, message: 'Invalid or expired verification code.' });
        }

        // 3. Mark the code as verified in the DB and record the time
        const updateQuery = `
            UPDATE view_verification_codes
            SET is_verified = TRUE, verified_at = NOW()
            WHERE id = ?
        `;
        await pool.query(updateQuery, [record.id]);

        // 4. Verification successful
        res.status(200).json({
            success: true,
            message: 'Verification successful. You can now switch the view.'
            // The frontend will now call setViewAs upon receiving this success
        });

    } catch (error) {
        console.error('Error verifying switch view code:', error);
        res.status(500).json({ success: false, message: 'Internal server error verifying code.' });
    }
};

/**
 * Check if the logged-in user has recently verified access to a target family member.
 * GET /api/relationships/check-access/:targetDemographicNo
 */
exports.checkSwitchViewAccess = async (req, res) => {
    const requesterUserId = req.user.id;
    const targetDemographicNo = parseInt(req.params.targetDemographicNo, 10);
    const accessWindowMinutes = 30;

    if (!requesterUserId) {
        return res.status(401).json({ success: false, message: 'User not authenticated.' });
    }

    if (isNaN(targetDemographicNo)) {
        return res.status(400).json({ success: false, message: 'Invalid target demographic number.' });
    }

    try {
        // Check if user is admin - admins should have access to all patients
        if (req.user.role === 'admin') {
            console.log(`Admin user ${requesterUserId} granted automatic access to demo ${targetDemographicNo}`);
            return res.status(200).json({ success: true, accessGranted: true });
        }

        const selectQuery = `
            SELECT 1
            FROM view_verification_codes
            WHERE requester_user_id = ?
              AND target_demographic_no = ?
              AND is_verified = TRUE
              AND verified_at >= NOW() - INTERVAL ? MINUTE
            ORDER BY verified_at DESC
            LIMIT 1
        `;
        const [rows] = await pool.query(selectQuery, [
            requesterUserId,
            targetDemographicNo,
            accessWindowMinutes
        ]);

        const accessGranted = rows.length > 0;
        console.log(`Access check for user ${requesterUserId} to demo ${targetDemographicNo}: ${accessGranted}`);

        res.status(200).json({ success: true, accessGranted });

    } catch (error) {
        console.error('Error checking switch view access:', error);
        res.status(500).json({ success: false, message: 'Internal server error checking access.' });
    }
};

/**
 * Get the list of family members for the currently logged-in user.
 * GET /api/relationships/family
 */
exports.getFamily = async (req, res) => {
    const requesterUserId = req.user?.id;
    const requesterDemoNo = req.user?.demographic_no;

    if (!requesterUserId || !requesterDemoNo) {
        return res.status(401).json({ success: false, message: 'User not authenticated or missing demographic info.' });
    }

    try {
        // Use the User model method to fetch family members
        // It already includes the check for recent access via view_verification_codes
        const familyMembers = await User.getFamilyMembers(requesterDemoNo, requesterUserId);

        res.status(200).json({
            success: true,
            familyMembers: familyMembers || [] // Ensure an array is always returned
        });

    } catch (error) {
        console.error(`Error fetching family members for user ${requesterUserId} (demo_no ${requesterDemoNo}):`, error);
        res.status(500).json({ success: false, message: 'Internal server error fetching family members.' });
    }
};

// Placeholder for potentially listing relationships (if needed elsewhere)
exports.getRelationships = async (req, res) => {
    // Implementation would go here if needed
    res.status(501).json({ message: 'Not implemented yet.' });
}; 