const mysql = require('mysql2/promise');
const config = require('../config/database');

async function updateProductImages() {
    let connection;
    
    try {
        connection = await mysql.createConnection(config.main);
        console.log('Connected to database');

        // 更新商品图片URL
        const updates = [
            { id: 1, image_url: '/assets/images/products/product-1.jpg' },
            { id: 2, image_url: '/assets/images/products/product-2.jpg' },
            { id: 3, image_url: '/assets/images/products/product-3.jpg' },
            { id: 4, image_url: '/assets/images/products/product-4.jpg' },
            { id: 5, image_url: '/assets/images/products/product-5.jpg' },
            { id: 6, image_url: '/assets/images/products/product-6.jpg' },
            { id: 7, image_url: '/assets/images/products/product-7.jpg' },
            { id: 8, image_url: '/assets/images/products/product-8.jpg' },
            { id: 9, image_url: '/assets/images/products/product-9.jpg' },
            { id: 10, image_url: '/assets/images/products/product-10.jpg' },
            { id: 11, image_url: '/assets/images/products/product-11.jpg' },
            { id: 12, image_url: '/assets/images/products/product-12.jpg' }
        ];

        for (const update of updates) {
            await connection.execute(
                'UPDATE products SET image_url = ? WHERE id = ?',
                [update.image_url, update.id]
            );
            console.log(`Updated product ${update.id} with image ${update.image_url}`);
        }

        console.log('All product images updated successfully!');
        
    } catch (error) {
        console.error('Error updating product images:', error);
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

// 运行脚本
if (require.main === module) {
    updateProductImages();
}

module.exports = updateProductImages; 