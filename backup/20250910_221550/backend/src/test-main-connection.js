// 测试主服务器连接
console.log('正在测试主服务器数据库连接...');
const pool = require('./config/database');
const mainServerPool = pool.mainServerPool;

async function testMainConnection() {
    let connection;
    try {
        console.log('尝试连接到主服务器...');
        connection = await mainServerPool.getConnection();
        console.log('成功连接到主服务器!');

        // 执行一个简单的查询以验证连接
        const [result] = await connection.query('SELECT 1 as test');
        console.log('查询测试结果:', result);

        return true;
    } catch (error) {
        console.error('主服务器连接失败:', error.message);
        console.error('详细错误信息:', error);
        return false;
    } finally {
        if (connection) {
            console.log('释放数据库连接');
            connection.release();
        }
    }
}

// 测试本地数据库连接
async function testLocalConnection() {
    try {
        console.log('\n尝试连接到本地服务器...');
        const [result] = await pool.query('SELECT 1 as test');
        console.log('本地服务器连接成功!');
        console.log('查询测试结果:', result);
        return true;
    } catch (error) {
        console.error('本地服务器连接失败:', error.message);
        console.error('详细错误信息:', error);
        return false;
    }
}

// 运行测试
async function runTests() {
    // 打印当前环境变量
    console.log('主服务器配置信息:');
    console.log('- MAIN_DB_HOST:', process.env.MAIN_DB_HOST || process.env.MAIN_HOST);
    console.log('- MAIN_DB_PORT:', process.env.MAIN_DB_PORT || '3306');
    console.log('- MAIN_DB_USER:', process.env.MAIN_DB_USER ? '已设置' : '未设置');
    console.log('- MAIN_DB_PASSWORD:', process.env.MAIN_DB_PASSWORD ? '已设置' : '未设置');
    console.log('- MAIN_DB_NAME:', process.env.MAIN_DB_NAME || process.env.DB_NAME);

    const mainResult = await testMainConnection();
    const localResult = await testLocalConnection();

    console.log('\n测试结果摘要:');
    console.log('- 主服务器连接:', mainResult ? '成功' : '失败');
    console.log('- 本地服务器连接:', localResult ? '成功' : '失败');

    process.exit(0);
}

runTests(); 