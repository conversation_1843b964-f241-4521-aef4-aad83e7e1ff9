const pool = require('../config/database');

exports.getPatientRecords = async (req, res) => {
    try {
        const { demographicNo } = req.params;

        // 获取病历记录
        const [records] = await pool.query(
            `SELECT 
                note_id,
                update_date,
                observation_date,
                provider_no,
                note,
                signed,
                encounter_type,
                program_no
            FROM casemgmt_note 
            WHERE demographic_no = ? 
            ORDER BY observation_date DESC`,
            [demographicNo]
        );

        // 获取病人基本信息
        const [demographic] = await pool.query(
            `SELECT 
                demographic_no,
                title,
                last_name,
                first_name,
                sex,
                year_of_birth,
                month_of_birth,
                date_of_birth,
                hin,
                provider_no
            FROM demographic 
            WHERE demographic_no = ?`,
            [demographicNo]
        );

        if (!demographic[0]) {
            return res.status(404).json({ message: 'Patient not found' });
        }

        res.json({
            success: true,
            patient: demographic[0],
            records: records.map(record => ({
                noteId: record.note_id,
                updateDate: record.update_date,
                observationDate: record.observation_date,
                providerNo: record.provider_no,
                note: record.note,
                signed: record.signed === 1,
                encounterType: record.encounter_type,
                programNo: record.program_no
            }))
        });
    } catch (error) {
        console.error('Error fetching medical records:', error);
        res.status(500).json({ message: 'Failed to fetch medical records' });
    }
}; 