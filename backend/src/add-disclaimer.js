const fs = require('fs');
const path = require('path');

// 免责声明文本
const disclaimer = `

---

**免责声明**: 本文由 MMC 健康管理中心提供，仅供一般健康信息参考。内容不能替代专业的医疗建议、诊断或治疗。如有任何健康问题，请务必咨询医生或其他合格的医疗保健提供者。您可以考虑联系 MMC 健康管理中心获取专业问诊服务。`;

// 更新所有健康贴士添加免责声明
function addDisclaimerToFiles(directory) {
    try {
        const files = fs.readdirSync(directory, { withFileTypes: true });

        for (const file of files) {
            const fullPath = path.join(directory, file.name);

            if (file.isDirectory()) {
                // 递归处理子目录
                addDisclaimerToFiles(fullPath);
            } else if (file.name.endsWith('.md') && !file.name.startsWith('.')) {
                // 读取markdown文件
                const content = fs.readFileSync(fullPath, 'utf8');

                // 检查是否已经有免责声明
                if (!content.includes('免责声明')) {
                    console.log(`添加免责声明到文件: ${fullPath}`);
                    // 添加免责声明并写回文件
                    fs.writeFileSync(fullPath, content + disclaimer);
                }
            }
        }
    } catch (error) {
        console.error(`处理目录时出错 ${directory}:`, error);
    }
}

// 处理所有相关目录
const directories = [
    path.join(__dirname, '../documents/health'),
    path.join(__dirname, '../documents/tips'),
    path.join(__dirname, '../documents/preventive-care'),
    path.join(__dirname, '../documents/disease-info')
];

// 在Docker环境中的目录
const dockerDirectories = [
    '/app/documents/health',
    '/app/documents/tips',
    '/app/documents/preventive-care',
    '/app/documents/disease-info'
];

// 合并所有可能的目录
const allDirectories = [...directories, ...dockerDirectories];

let processedDirs = 0;

for (const dir of allDirectories) {
    if (fs.existsSync(dir)) {
        console.log(`处理目录: ${dir}`);
        addDisclaimerToFiles(dir);
        processedDirs++;
    } else {
        console.log(`目录不存在: ${dir}`);
    }
}

if (processedDirs > 0) {
    console.log('完成添加免责声明到所有文件');
} else {
    console.log('未找到任何有效的目录，请检查路径');
} 