import React, { createContext, useState, useContext, useEffect } from 'react';

// Translation data
const translations = {
    en: {
        // Navigation groups
        'basic_info': 'Basic Information',
        'appointment_mgmt': 'Appointment Management',
        'health_records': 'Health Records',
        'membership_services': 'Membership Services',
        'system_settings': 'System Settings',
        'admin_panel': 'Admin Panel',

        // Navigation items
        'profile': 'Personal Profile',
        'membership': 'Membership Info',
        'appointments': 'Appointments',
        'booking': 'Schedule Appointment',
        'lab_reports': 'Test Results',
        'prescriptions': 'Prescriptions',
        'immunizations': 'Vaccinations',
        'settings': 'Settings',
        'health_assistant': 'AI Health Assistant',
        'dietician': 'Dietician', // Added Dietician EN translation

        // User menu
        'logout': 'Logout',
        'return_to_profile': 'Return to Personal Profile',
        'viewing_family_member': 'You are viewing family member information',
        'preferences': 'Preferences',
        'language': 'Language',
        'theme': 'Theme',
        'switch_to_chinese': 'Switch to Chinese',
        'switch_to_english': 'Switch to English',
        'light_mode': 'Light Mode',
        'dark_mode': 'Dark Mode',

        // Common buttons and labels
        'save': 'Save',
        'cancel': 'Cancel',
        'loading': 'Loading...',
        'error': 'Error',
        'success': 'Success',
        'no_data': 'No data available',
        'confirm': 'Confirm',
        'delete': 'Delete',
        'edit': 'Edit',
        'view': 'View',
        'user': 'User',
        'not_selected': 'Not Selected',

        // App title
        'app_title': 'MMC WELLNESS',
        'app_subtitle': 'Your Personal Health Management Platform',

        // Profile page
        'personal_information': 'Personal Information',
        'full_name': 'Full Name',
        'address': 'Address',
        'phone': 'Phone',
        'email': 'Email',
        'care_card': 'Health Card No.',
        'sex': 'Gender',
        'family_members': 'Family Members',
        'health_review': 'Health Review',
        'health_summary': 'Health Summary',
        'half_year_summaries': 'Half-Year Health Summaries',
        'request_access': 'Request Access',
        'access_active': 'Access Active',
        'adult_member': 'Adult Member',
        'minor_member': 'Minor Member',
        'direct_relatives_only': 'Showing direct relatives only',
        'eforms': 'Health Forms',
        'no_eforms_found': 'No health forms found',
        'form_date': 'Date',
        'form_name': 'Form Type',
        'form_content': 'Form Content',
        'no_form_content': 'No form content available',
        'provider': 'Provider',
        'no_membership': 'No Membership',
        'child': 'Child',
        'spouse': 'Spouse',
        'mr': 'MR',
        'mrs': 'MRS',
        'ms': 'MS',
        'change_avatar': 'Change Avatar',
        'delete_avatar': 'Delete Avatar',
        'delete_avatar_confirmation': 'Are you sure you want to delete your avatar?',
        'delete_avatar_success': 'Avatar deleted successfully',
        'delete_avatar_error': 'Failed to delete avatar',
        'delete_avatar_warning': 'This action cannot be undone',
        'delete_avatar_button': 'Delete Avatar',

        // Appointment page
        'your_appointments': 'Your Appointments',
        'upcoming_appointments': 'Upcoming Appointments',
        'past_appointments': 'Past Appointments',
        'latest_appointments': 'Latest Appointments',
        'date': 'Date',
        'time': 'Time',
        'doctor': 'Doctor',
        'location': 'Location',
        'notes': 'Notes',
        'reason': 'Reason for Visit',
        'status': 'Status',
        'confirmed': 'Confirmed',
        'completed': 'Completed',
        'cancelled': 'Cancelled',
        'pending': 'Pending',
        'reschedule': 'Reschedule',
        'cancelled_appointments': 'Cancelled Appointments',
        'cancelled_short': 'Cancelled',
        'no_cancelled_appointments': 'There are no cancelled appointments',

        // Booking page
        'book_appointment': 'Online Appointment',
        'select_provider': 'Select Doctor',
        'select_date_time': 'Select Date & Time',
        'confirm_booking': 'Confirm Appointment Details',
        'provider': 'Doctor',
        'next': 'Continue',
        'previous': 'Previous',
        'appointment_details': 'Appointment Details',
        'appointment_confirmation': 'Appointment Confirmed',
        'appointment_reason': 'Reason for Visit',
        'reason_placeholder': 'Please briefly describe the reason for your visit (e.g., symptoms, check-up) to help the doctor prepare.',
        'important_notice': 'Important Notice',
        'booking_notice': 'You will receive a confirmation email upon booking. Please note: To cancel appointments, at least 48 hours notice is required, otherwise a $75 fee will be charged. Thank you for your understanding and cooperation.',
        'booking_success_title': 'Appointment Confirmed',
        'booking_success_message': 'Your appointment is scheduled. Confirmation details have been sent to your email.',
        'regular_appointment': 'General Appointment',
        'select_location': 'Select Location',
        'online_phone': 'By phone',
        'in_clinic': 'In Person',
        'in_person': 'In Person',
        'reason_for_visit': 'Reason for Visit (Required)',
        'reason_for_visit_label': 'Reason for Visit (Details) *',
        'reason_for_visit_placeholder': 'Please describe the reason for your visit (e.g., symptoms, check-up).',
        'zuòfèi': '已作废',
        'dàichǔlǐ': '待处理',
        'chóngxīn_ānpái': '重新安排',
        'yǐ_qǔxiāo_yùyuē': '已取消预约',
        'yǐ_qǔxiāo_duǎn': '已取消',
        'méiyǒu_yǐ_qǔxiāo_yùyuē': '没有已取消的预约记录',
        'yùyuē': '预约就诊',
        'select_visit_date': 'Select Visit Date',

        // Image Upload (Booking Page)
        'error_invalid_image_file': 'Invalid file type. Please select an image.',
        'upload_reason_image_optional': 'Upload Image for Reason (Optional)',
        'select_image': 'Select Image',
        'select_images': 'Select Images',
        'add_more_images': 'Add More Images',
        'upload_reason_images_optional': 'Upload reason images (optional)',
        'error_max_images': 'Maximum {{max}} images allowed',
        'error_invalid_image_files': 'Some files are not valid images',
        'image_preview_alt': 'Image preview for booking reason',
        'remove_image_aria': 'Remove selected image',


        // Appointment policy and terms
        'appointment_policy': 'Appointment & Cancellation Policy',
        'arrival_notice': 'Please arrive 15 minutes before your scheduled appointment time.',
        'documents_notice': 'Bring your health card and any relevant medical documents.',
        'cancellation_notice': 'Cancellations require at least 2 business days notice to avoid fees.',
        'emergency_notice': 'For urgent medical issues, please call 911 or visit the nearest emergency room.',
        'terms_agreement': 'I agree to the appointment policy and understand the cancellation terms.',
        'jul_dec': 'Jul-Dec',
        'jan_jun': 'Jan-Jun',
        'request_access_point3_title': 'Confidentiality:',

        // Lab reports page -> Test Results
        'your_medical_documents': 'Your Reports & Documents',
        'family_medical_documents': 'Family Member Reports & Documents',
        'documents': 'Reports & Documents',
        'test_values': 'Test Values',
        'document': 'Document',
        'document_viewer': 'Document Viewer',
        'no_documents': 'No reports or documents found.',
        'no_documents_of_type': 'No documents of type found.',
        'no_measurements': 'No measurement data found in your records.',
        'viewing_family_lab_reports': 'You are viewing test results for a family member.',
        'invalid_response': 'Invalid response format. Expected JSON, got: ',
        'lab_document': 'Test Report',
        'consult_document': 'Specialist Report',
        'lab': 'Lab Results',
        'consult': 'Specialist Report',
        'eform': 'Requisition Form',
        'medical imaging': 'Medical Imaging',
        'referral': 'Specialist Referral',
        'others': 'Other Documents',
        'download': 'Download',
        'view_document': 'View Document',

        // Settings page
        'security': 'Security',
        'notifications': 'Notifications',
        'general_preferences': 'General Preferences',
        'app_experience_settings': 'These settings control your application experience',
        'change_password': 'Change Password',
        'current_password': 'Current Password',
        'new_password': 'New Password',
        'confirm_new_password': 'Confirm New Password',
        'update_password': 'Update Password',
        'passwords_do_not_match': 'New passwords do not match',
        'password_too_short': 'Password must be at least 8 characters',
        'settings_updated': 'Settings updated successfully!',
        'notification_preferences': 'Notification Preferences',
        'email_notifications': 'Email Notifications',
        'receive_general_notifications': 'Receive general notifications via email',
        'appointment_reminders': 'Appointment Reminders',
        'receive_appointment_reminders': 'Receive reminders before your scheduled appointments',
        'document_notifications': 'Report/Document Notifications',
        'receive_document_notifications': 'Receive notifications when new reports/documents are available',
        'marketing_communications': 'Marketing Communications',
        'receive_marketing_emails': 'Receive promotional emails about services and events',
        'use_24hour_format': 'Use 24-hour time format',

        // General for medical documents
        'unknown_date': 'Unknown date',
        'not_available': 'N/A',
        'not_authenticated': 'Not authenticated. Please log in.',
        'fetch_failed': 'Failed to fetch data.',
        'fetch_error': 'An error occurred while fetching data.',
        'api_error': 'API returned error.',
        'switch_back': 'Switch Back to My Profile',

        // Prescriptions
        'prescribed_by': 'Prescribing Doctor',
        'details': 'Details',
        'comments': 'Comments',
        'no_prescriptions': 'No prescriptions found.',
        'viewing_family_prescriptions': 'You are currently viewing prescriptions for a family member.',

        // Immunizations -> Vaccinations
        'vaccine_type': 'Vaccine / Prevention Type',
        'date_administered': 'Date Administered',
        'administered_by': 'Administered By Doctor/Nurse',
        'next_due_date': 'Next Due Date',
        'no_immunizations': 'No vaccination records found.',
        'viewing_family_immunizations': 'You are currently viewing vaccinations for a family member.',

        // Additional appointment page translations
        'network_error': 'Network Connection Issue',
        'network_error_message': 'Unable to connect to the server. Please check your network connection and refresh the page.',
        'refresh': 'Refresh Page',
        'viewing_family_appointments': 'You are currently viewing appointments for a family member.',
        'family_appointments': 'Family Member Appointments',
        'error_loading_data': 'Error loading data',
        'upcoming_short': 'Upcoming',
        'past_short': 'Past',
        'past_appointments_latest': 'Past Appointments (Latest 10)',
        'loading_appointments': 'Loading appointments...',
        'no_appointments_found': 'No appointments found',
        'no_upcoming_appointments': 'There are no upcoming appointments',
        'no_past_appointments': 'There are no past appointments',
        'invalid_patient_id': 'Invalid patient ID. Please log in again or contact customer service.',
        'no_permission_appointments': 'You do not have permission to view this patient\'s appointments.',
        'fetch_appointments_failed': 'Failed to retrieve appointment information',
        'new_appointment': 'Schedule New Appointment',
        'view_appointments': 'View My Appointments',

        // Additional profile page translations
        'request_access_title': 'Confirm Authorization Request',
        'request_access_dear_user': 'Dear User,',
        'request_access_intro': 'You are requesting temporary access to view sensitive health information for {name}. Please be advised of the following:',
        'request_access_point1_title': 'Verification Required:',
        'request_access_point1_text': 'Access requires verification via an email sent to the registered address of {name}. This is a security measure to protect patient privacy.',
        'request_access_point2_title': 'Temporary Access:',
        'request_access_point2_text': 'If verification is successful, access granted is temporary and may be subject to time limits or other restrictions as defined by MMC Wellness policy.',
        'request_access_point4_title': 'Disclaimer:',
        'request_access_point4_text': 'MMC Wellness provides this access mechanism as a convenience. While we strive for accuracy, MMC Wellness is not liable for any decisions made based on the information viewed or for any misuse of this access feature.',
        'request_access_confirmation': 'By clicking "Confirm", you acknowledge that you understand these terms and wish to proceed with the verification process to request access to {name}\'s records.',
        'request_access_sincerely': 'Sincerely,',
        'request_access_team': 'The MMC Wellness Team',
        'verification_title': 'Enter Verification Code',
        'verification_message': 'Please enter the verification code sent to {email}',
        'verification_code': 'Verification Code',
        'verify': 'Verify & View Profile',
        'no_family_members': 'No family members are currently linked to your account.',
        'no_profile_data': 'No profile data available.',
        'verification_error': 'Verification failed. Please try again or contact support.',
        'verification_success': 'Verification successful! Redirecting...',
        'profile_access_granted': 'Access granted. You can now view this profile.',
        'profile_access_denied': 'Access denied. Please contact support if you believe this is an error.',
        'profile_access_expired': 'Access has expired. Please request access again.',
        'profile_loading': 'Loading profile information...',
        'request_verification': 'Request Verification Code',
        'resend_code': 'Resend Code',
        'last_updated': 'Last updated',
        'last_updated_tooltip': 'The date and time when this summary was last generated or updated',
        'refresh': 'Refresh',

        // Membership page
        'membership_info': 'Membership Information',
        'family_members_status': 'Family Members Status',
        'your_membership': 'Your Membership',
        'self': 'Self',
        'family_member': 'Family Member',
        'no_membership': 'No Membership',
        'purchase_membership': 'Join Membership Plan',
        'add_membership': 'Add Membership',
        'contact_to_add_membership': 'Contact us to add membership',
        'click_below_to_purchase': 'Click below to purchase membership',
        'expired': 'Expired',
        'active': 'Active',
        'start_date': 'Start Date',
        'end_date': 'End Date',
        'membership_type': 'Membership Plan',
        'membership_start_date': 'Start Date',
        'membership_end_date': 'End Date',
        'membership_status': 'Status',
        'payment_amount': 'Payment Amount',
        'renew_membership': 'Renew Membership',
        'renew_early': 'Renew Early',
        'hide_history': 'Hide History',
        'view_history': 'View History',
        'membership_history': 'Membership History',
        'loading_membership': 'Loading membership information...',
        'no_membership_records': 'No membership records found',
        'membership_details': 'Membership Details',
        'available_membership_types': 'Available Membership Plans',
        'price': 'Price',
        'no_membership_types': 'No membership plans available',
        'membership_benefits_note': 'Note: Membership fees are non-refundable. Please contact our office for any questions regarding membership.',
        'select_membership_type': 'Please select the membership plan you wish to purchase',
        'proceed_to_payment': 'Continue to Payment',
        'renewal_success': 'Membership renewed successfully!',
        'payment_process_note': 'Note: This will be processed as a new membership purchase. Your membership will be valid for one year from today.',
        'authentication_failed': 'Authentication failed. Please login again.',
        'server_invalid_format': 'Server returned invalid format.',
        'server_invalid_structure': 'Server returned invalid data structure.',
        'fetch_membership_failed': 'Failed to fetch membership information',
        'fetch_membership_failed_retry': 'Failed to fetch membership information. Please try again later.',
        'fetch_membership_types_failed': 'Failed to retrieve membership plans.',
        'request_timeout': 'Request timeout. Please check your network connection and try again.',
        'status_code': 'Error Code',

        // Chatbot
        'chatbot': 'AI Health Assistant',
        'chat_window_title': 'AI Health Assistant',
        'chat_bot_name': 'MMC AI Health Assistant',
        'chat_greeting': 'Hello! I\'m the MMC AI Health Assistant.How can I help you with your health management questions today?',
        'type_message': 'Ask your health question...',

        // Appointment Cancellation
        'cancel_appointment_confirmation': 'Confirm Appointment Cancellation',
        'cancel_appointment_warning': 'Please note that appointments must be cancelled at least 48 hours in advance. A notification will be sent to the clinic about this cancellation.',
        'cancel_policy': 'To cancel appointments, at least 48 hours notice is required, otherwise a $75 fee will be charged. Thank you for your understanding and cooperation.',
        'cancellation_lead_time': 'Cancellations require at least 48 hours notice',
        'cancellation_email_notice': 'An email notification will be sent to the clinic (<EMAIL>) about your cancellation.',
        'confirm_cancel': 'Confirm Cancel',
        'cancelling': 'Cancelling...',
        'appointment_cancelled_successfully': 'Appointment cancelled successfully',
        'back': 'Back',
        'close': 'Close',
        'cannot_cancel_late': 'This appointment cannot be cancelled online as it is within 2 business days. Please call the clinic directly to discuss cancellation options.',
        'auth_required': 'Authentication required. Please log in again.',
        'cancel_failed': 'Failed to cancel appointment. Please try again or contact the clinic directly.',

        // Booking page additions/refinements
        'in_person': 'In Person',
        'by_phone': 'By Phone',
        'service_type': 'Service Type',
        'pap_test': 'Pap Test',
        'booking_intro': 'Schedule your appointment online in just a few steps. Choose your preferred service type or doctor.',
        'book_by_service': 'Book by Service Type',
        'book_by_doctor': 'Book by Doctor',
        'select_service_type': 'Select Service Type',
        'select_date_time_doctor': 'Select Date, Time & Doctor',
        'service_type_description': 'Please select the type of service you need. Different services may be provided by different specialists.',
        'service_no_description': 'Specialized medical service provided by our professional team',

        // Service types
        'service_types': {
            '健康管理咨询': {
                en: 'Health Management Consultation',
                zh: '健康管理咨询',
                description: {
                    en: 'Comprehensive health assessment and personalized management plan',
                    zh: '全面评估您的健康状况，制定个性化健康管理方案，预防疾病发生'
                }
            },
            '健康管理（电话）': {
                en: 'Telehealth Consultation',
                zh: '远程健康咨询',
                description: {
                    en: 'Convenient phone consultation with our healthcare professionals',
                    zh: '便捷的电话咨询服务，无需亲临医院，即可获得专业医疗建议'
                }
            },
            '功能医学咨询': {
                en: 'Functional Medicine',
                zh: '功能医学评估',
                description: {
                    en: 'Address root causes of health issues with personalized functional approach',
                    zh: '通过功能医学方法找出健康问题根源，制定个性化治疗方案'
                }
            },
            'Weight Loss': {
                en: 'Weight Loss Program',
                zh: '体重管理项目',
                description: {
                    en: 'Medically supervised program for weight management.',
                    zh: '医疗监督下的体重管理项目。'
                }
            },
            'Peptides': {
                en: 'Peptide Therapy Consultation',
                zh: '肽疗法咨询',
                description: {
                    en: 'Consultation regarding peptide therapies.',
                    zh: '关于肽疗法的咨询服务。'
                }
            },
            'Pap Test': {
                en: 'Pap Test 宫颈癌抹片筛查',
                zh: 'Pap Test 宫颈癌抹片筛查',
                description: {
                    en: 'Examined and sampled by an experienced female physician at MMC.',
                    zh: '由MMC经验丰富的妇科女医生亲自检查取样'
                }
            }
        },

        // Date selection refinements
        'date_selection_description': 'Please select your preferred date, then choose from available times and doctors below.',
        'select_date': 'Choose Date',
        'appointment_date': 'Appointment Date',
        'date_notice': 'Note: Appointments must be scheduled at least 3 days in advance',
        'loading_available_dates': 'Checking available dates...',
        'no_available_dates': 'No available dates found for this service type. Please try another service.',
        'available_dates_info': 'Available dates found for your selected service',

        // Navigation buttons
        'doctor_selection_description': 'Please select your preferred doctor, then view their available appointment times.',
        'doctor_selection_prompt': 'Select Preferred Doctor',
        'available_times': 'Available Times',
        'no_slots_available': 'No Appointments Available for this Date',
        'select_date_first': 'Please Select a Date',
        'selected_service': 'Selected Service',
        'selected_doctor': 'Selected Doctor',
        'selected_date': 'Selected Date',
        'selected_time': 'Selected Time',
        'available_slots': 'Available Times',

        // Chatbot related translations
        'mmc_wellness_chat': 'MMC AI Health Assistant',
        'chat_history': 'Chat History',
        'conversations': 'Conversations',
        'new_chat': 'New Chat',
        'new_conversation': 'New Conversation',
        'delete_conversation': 'Delete Conversation',
        'no_conversations': 'No conversations yet',
        'welcome_to_mmc_chat': 'Welcome to MMC AI Health Assistant',
        'chat_intro_expanded': 'I can provide information about common health conditions, seasonal health reminders, and MMC Wellness services. How can I help you today?',
        'type_message': 'Ask your health question...',
        'send': 'Send',
        'sending': 'Sending...',
        'you': 'You',
        'more_options': 'More Options',
        'chat_greeting': 'Hello! How can I assist you with your health-related questions today?',
        'cannot_load_chat_history': 'Could not load chat history. Please try again later.',
        'conversation_deleted': 'Conversation deleted successfully',
        'failed_to_delete': 'Failed to delete conversation',
        'error_loading_conversation': 'Error loading conversation',
        'search_chats': 'Search conversations',

        // Errors
        'error_fetching_providers': 'Error retrieving doctor list',
        'error_fetching_availability': 'Error retrieving available times',
        'error_fetching_available_dates': 'Error retrieving available dates',
        'select_doctor_for_slot': 'Please select a doctor for the chosen time',
        'error_fetching_doctor_services': 'Error fetching services for the selected doctor',
        'select_service_for_doctor': 'Please select a service offered by the doctor',
        'select_service_for_provider': 'Select Service',
        'select_service_provided_by': 'Please choose a service provided by',
        'no_services_found_for_doctor': 'No specific services listed for this doctor for online booking.',
        'error_missing_details': 'Booking failed: Missing required details (provider, date, or time).',
        'booking_failed': 'Booking failed. Please try again or contact the clinic.',

        // Member Resources (新增)
        'member_resources': 'Member Resources',
        'member_resources_description': 'Exclusive articles and resources for our members',
        'resource_not_available': 'This resource is currently not available',
        'read_more': 'Read More',
        'loading_resources': 'Loading resources...',
        'no_resources_found': 'No resources found',
        'view_all_resources': 'View All Resources',
        'resource_details': 'Resource Details',
        'back_to_resources': 'Back to Resources',
        'article_loading': 'Loading article...',

        // File sharing
        'file_sharing': 'File Sharing',
        'file_sharing_description': 'Access and share your medical documents',
        'upload_file': 'Upload File',
        'download_file': 'Download',
        'delete_file': 'Delete',
        'share_file': 'Share',
        'my_files': 'My Files',
        'shared_files': 'Shared Files',
        'file_name': 'File Name',
        'file_size': 'Size',
        'file_type': 'Type',
        'upload_date': 'Upload Date',
        'shared_with': 'Shared With',
        'shared_by': 'Shared By',
        'no_files_found': 'No files found',
        'upload_success': 'File uploaded successfully',
        'delete_success': 'File deleted successfully',
        'file_operations': 'File Operations',
        'select_file': 'Select File',
        'uploading': 'Uploading...',
        'upload': 'Upload',
        'cancel': 'Cancel',
        'share_file_with': 'Share file with family members',
        'search_family_members': 'Search family members',
        'select_recipient': 'Select recipient',
        'sharing': 'Sharing...',
        'share': 'Share',
        'person_count': 'people',
        'not_shared': 'Not shared',
        'please_select_file': 'Please select a file',
        'no_shared_files': 'No shared files found',

        // RedBook Videos Page
        'redbook_videos': 'Health Video Channel',
        'redbook_videos_description': 'Watch our latest health videos and tutorials from our clinic team',
        'video_categories': 'Video Categories',
        'no_videos_found': 'No videos found for this category',
        'open_redbook': 'View on RedBook',
        'view_on_redbook': 'View on Xiaohongshu',
        'video_preview_unavailable': 'Video preview is unavailable in the portal. Please click below to view on Xiaohongshu',
        'close': 'Close',

        // YouTube Videos Page - 新增
        'youtube_videos': 'Health & Wellness Videos',
        'youtube_videos_description': 'Browse our curated collection of health and wellness videos',
        'view_on_youtube': 'Watch on YouTube',
        'no_videos_found': 'No videos found in this category',
        'all_videos': 'All Videos',
        'nutrition': 'Nutrition',
        'fitness': 'Fitness',
        'mental_health': 'Mental Health',
        'sleep': 'Sleep',
        'general_health': 'General Health',
        'video_dialog_title': 'Watch Video',
        'swipe_up_down': 'Swipe up/down for more videos',
        'tap_to_play_pause': 'Tap to play/pause',
        'swipe_mode': 'Full Screen Mode',
        'card_mode': 'Card Mode',
        'video_mode': 'Video View Mode',
        'share_video': 'Share Video',
        'video_loading': 'Loading video...',
        'video_load_error': 'Error loading video',

        // Appointment Notes
        'appointment_note': 'Medical Visit Note',
        'view_note': 'View Note',
        'original_note': 'Original Medical Note',
        'ai_summary': 'AI Simplified Summary',
        'ai_generated_summary': 'Patient-Friendly AI Summary',
        'no_note_content': 'No detailed note content available for this visit',
        'no_medical_note': 'No medical note found for this appointment',
        'ai_summary_not_available': 'AI summary is not available for this note',
        'by': 'by',
        'cancel_appointment': 'Cancel Appointment',
        'no_appointments': 'No appointments found',
        'date_format_short': 'MM/DD/YYYY',

        // Dietician Page
        'no_dietician_comments': 'No dietician consultations found',
        'dietician_consultation_available': 'Dietician consultation available',
        'view_summary': 'View Summary',
        'dietician_consultation_summary': 'Dietician Consultation Summary',
        'consultation_details': 'Consultation Details',
        'nutrition_summary': 'Nutrition Summary',
        'generating_summary': 'Generating AI summary...',
        'summary_will_be_generated': 'Summary will be generated automatically',
        'summary_fetch_failed': 'Failed to fetch summary',
        'summary_fetch_error': 'Error fetching summary',

        // Referral-related translations
        referral_code: 'Referral Code',
        referral_code_explanation: 'Enter a referral code from an existing member to register',
        invalid_referral_code: 'Invalid referral code',
        valid_referral_code: 'Valid referral code',
        enter_referral_code: 'Enter referral code from an existing member',
        your_referral_code: 'Your Referral Code',
        referred_users: 'Referred Users',
        referral_code_title: 'Your Personal Referral Code',
        referral_code_description: 'Share this code with friends and family. When they register using your code, you will earn reward points that can be used for membership discounts.',
        copy_code: 'Copy code',
        copied: 'Copied!',
        share_referral: 'Share Your Code',
        share_referral_title: 'Join me on MMC Wellness',
        share_referral_text: 'I am inviting you to join MMC Wellness. Use my referral code: {code} when you register.',
        referred_users_title: 'People You Have Referred',
        no_referrals_yet: 'You have not referred anyone yet.',
        get_your_referral_code: 'Get your referral code',
        total_reward_points: 'Total Reward Points',
        referred_on: 'Referred on {date}',
        status_pending: 'Pending',
        status_completed: 'Completed',
        status_rewarded: '{points} Points Earned',

        // Registration Page Translations
        'referral_code_label': 'Referral Code',
        'tooltip_enter_referral_code': 'Please enter referral code',
        'error_invalid_referral_code': 'Invalid referral code',
        'success_valid_referral_code': 'Referral code is valid',
        'helper_enter_referral_code': 'Please enter referral code',
        'section_title_personal_information': 'Personal Information',
        'label_title': 'Title',
        'label_first_name': 'First Name',
        'label_last_name': 'Last Name',
        'label_date_of_birth': 'Date of Birth',
        'label_sex': 'Sex',
        'sex_male': 'Male',
        'sex_female': 'Female',
        'sex_other': 'Other',
        'section_title_contact_information': 'Contact Information',
        'label_phone_number': 'Phone Number',
        'label_email': 'Email',
        'section_title_address': 'Address Information',
        'label_address': 'Detailed Address',
        'label_city': 'City',
        'label_province': 'Province/Region',
        'label_postal_code': 'Postal Code',
        'section_title_account_credentials': 'Account Credentials',
        'label_password': 'Password',
        'label_confirm_password': 'Confirm Password',
        'section_title_optional_information': 'Optional Information',
        'label_health_insurance_number_optional': 'Health Card Number (Optional)',
        'helper_text_optional_field_hin': 'This is an optional field. If you have a health card number, please enter it',
        'button_register': 'Register',
        'link_already_have_account': 'Already have an account? Sign In',

        // Registration Page Error/Generic Messages
        'patient_registration_title': 'Patient Registration',
        'error_required_fields_missing': 'Required fields are missing',
        'error_passwords_do_not_match': 'Passwords do not match',
        'error_registration_failed': 'Registration failed. Please try again later',

        // Login Page
        'login_title': 'Login to Your Account',
        'login_button': 'Login',
        'forgot_password_link': 'Forgot Password?',
        'no_account_prompt': "Don't have an account?",
        'register_link': 'Register here',
        'login_failed_message': 'Login failed. Please check your email and password.',
        'verification_required_message': 'Please verify your email before logging in. Check your inbox for a verification link.',
        'verification_sent_message': 'Verification email sent. Please check your inbox.',
        'resend_verification_button': 'Resend Verification Email',
        'email_verified_message': 'Email verified successfully! You can now log in.',
        'invalid_or_expired_token_message': 'Invalid or expired verification token.',

        // Registration Page
        'patient_registration_title': 'Patient Registration',
        'referral_code_label': 'Referral Code',
        'referral_code_section_title': 'Referral Code',
        'error_invalid_or_missing_referral_code': 'Invalid or missing referral code.',
        'referral_code_validated': 'Referral code validated successfully!',
        'referral_code_helper_text': 'Enter the referral code from an existing member.',
        'personal_information_section_title': 'Personal Information',
        'title_label': 'Title',
        'first_name_label': 'First Name',
        'last_name_label': 'Last Name',
        'date_of_birth_label': 'Date of Birth',
        'sex_label': 'Gender',
        'gender_male': 'Male',
        'gender_female': 'Female',
        'gender_other': 'Other',
        'contact_information_section_title': 'Contact Information',
        'email_label': 'Email Address',
        'phone_number_label': 'Phone Number',
        'address_information_section_title': 'Address Information',
        'address_label': 'Street Address',
        'city_label': 'City',
        'province_label': 'Province/Territory',
        'postal_code_label': 'Postal Code',
        'health_security_section_title': 'Health & Security',
        'health_card_number_label': 'Health Card Number (HIN/Care Card)',
        'health_card_number_tooltip': 'Please enter exactly 10 digits for your health card number.',
        'password_label': 'Password',
        'password_helper_text': 'Password must be at least 8 characters.',
        'confirm_password_label': 'Confirm Password',
        'error_passwords_do_not_match': 'Passwords do not match.',
        'register_button': 'Register',
        'already_have_account': 'Already have an account?',
        'login_link': 'Login here',
        'error_invalid_referral_code': 'Invalid referral code. Please check and try again.',
        'success_valid_referral_code': 'Referral code is valid!',
        'helper_enter_referral_code': 'Enter the referral code provided by an existing member.',
        'tooltip_enter_referral_code': 'If you have a referral code, enter it here.',
        'section_title_personal_information': 'Personal Information',
        'label_title': 'Title',
        'label_first_name': 'First Name',
        'label_last_name': 'Last Name',
        'label_date_of_birth': 'Date of Birth',
        'label_sex': 'Gender',
        'sex_male': 'Male',
        'sex_female': 'Female',
        'sex_other': 'Other',
        'section_title_contact_information': 'Contact Information',
        'label_phone_number': 'Phone Number',
        'label_email': 'Email Address',
        'section_title_address': 'Address Information',
        'label_address': 'Street Address',
        'label_city': 'City',
        'label_province': 'Province/Territory',
        'label_postal_code': 'Postal Code',
        'section_title_account_credentials': 'Account Credentials',
        'label_password': 'Password',
        'label_confirm_password': 'Confirm Password',
        'section_title_optional_information': 'Optional Information',
        'label_health_insurance_number_optional': 'Health Card Number (Optional)',
        'helper_text_optional_field_hin': 'This is an optional field. If you have a health card number, please enter it',
        'button_register': 'Register',
        'link_already_have_account': 'Already have an account? Login',
        'error_required_fields_missing': 'Required fields are missing',
        'error_registration_failed': 'Registration failed. Please try again later',

        // Titles from Register.js
        'Mr.': 'Mr.',
        'Mrs.': 'Mrs.',
        'Ms.': 'Ms.',
        'Dr.': 'Dr.',

        // Provinces from Register.js (abbreviations will be used as keys)
        'AB': 'Alberta',
        'BC': 'British Columbia',
        'MB': 'Manitoba',
        'NB': 'New Brunswick',
        'NL': 'Newfoundland and Labrador',
        'NS': 'Nova Scotia',
        'ON': 'Ontario',
        'PE': 'Prince Edward Island',
        'QC': 'Quebec',
        'SK': 'Saskatchewan',
        'NT': 'Northwest Territories',
        'NU': 'Nunavut',
        'YT': 'Yukon',

        // Login Page (Chinese)
        'login_title_zh': '登录您的账户',
        'login_button_zh': '登录',
        'forgot_password_link_zh': '忘记密码？',
        'no_account_prompt_zh': '还没有账户？',
        'register_link_zh': '在此注册',
        'login_failed_message_zh': '登录失败。请检查您的邮箱和密码。',
        'verification_required_message_zh': '登录前请验证您的邮箱。请检查您的收件箱中的验证链接。',
        'verification_sent_message_zh': '验证邮件已发送。请检查您的收件箱。',
        'resend_verification_button_zh': '重新发送验证邮件',
        'email_verified_message_zh': '邮箱验证成功！您现在可以登录了。',
        'invalid_or_expired_token_message_zh': '无效或已过期的验证令牌。',
        
        // Registration Page (Chinese)
        'patient_registration_title_zh': '患者注册',
        'referral_code_label_zh': '推荐码',
        'referral_code_section_title_zh': '推荐码区',
        'error_invalid_or_missing_referral_code_zh': '无效或缺少推荐码。',
        'referral_code_validated_zh': '推荐码验证成功!',
        'referral_code_helper_text_zh': '请输入现有会员的推荐码。',
        'personal_information_section_title_zh': '个人信息区',
        'title_label_zh': '称谓',
        'first_name_label_zh': '名',
        'last_name_label_zh': '姓',
        'date_of_birth_label_zh': '出生日期',
        'sex_label_zh': '性别',
        'gender_male_zh': '男性',
        'gender_female_zh': '女性',
        'gender_other_zh': '其他',
        'contact_information_section_title_zh': '联系信息区',
        'email_label_zh': '电子邮箱地址',
        'phone_number_label_zh': '电话号码',
        'address_information_section_title_zh': '地址信息区',
        'address_label_zh': '街道地址',
        'city_label_zh': '城市',
        'province_label_zh': '省/地区',
        'postal_code_label_zh': '邮政编码',
        'health_security_section_title_zh': '健康与安全区',
        'health_card_number_label_zh': '健康卡号（HIN/Care Card）',
        'health_card_number_tooltip_zh': '请输入您的10位健康卡号码。',
        'password_label_zh': '密码',
        'password_helper_text_zh': '密码必须至少为8个字符。',
        'confirm_password_label_zh': '确认密码',
        'error_passwords_do_not_match_zh': '密码不匹配。',
        'register_button_zh': '注册',
        'already_have_account_zh': '已有账户？',
        'login_link_zh': '在此登录',
        'error_invalid_referral_code_zh': '推荐码无效。请检查后重试。',
        'success_valid_referral_code_zh': '推荐码有效！',
        'helper_enter_referral_code_zh': '请输入现有会员提供的推荐码。',
        'tooltip_enter_referral_code_zh': '如果您有推荐码，请在此输入。',
        'section_title_personal_information_zh': '个人信息',
        'label_title_zh': '称呼',
        'label_first_name_zh': '名字',
        'label_last_name_zh': '姓氏',
        'label_date_of_birth_zh': '出生日期',
        'label_sex_zh': '性别',
        'sex_male_zh': '男',
        'sex_female_zh': '女',
        'sex_other_zh': '其他',
        'section_title_contact_information_zh': '联系信息',
        'label_phone_number_zh': '电话号码',
        'label_email_zh': '电子邮箱',
        'section_title_address_zh': '地址',
        'label_address_zh': '地址',
        'label_city_zh': '城市',
        'label_province_zh': '省份',
        'label_postal_code_zh': '邮政编码',
        'section_title_account_credentials_zh': '账户凭证',
        'label_password_zh': '密码',
        'label_confirm_password_zh': '确认密码',
        'section_title_optional_information_zh': '可选信息',
        'label_health_insurance_number_optional_zh': '健康保险号码（可选）',
        'helper_text_optional_field_hin_zh': '此字段为可选。',
        'button_register_zh': '注册',
        'link_already_have_account_zh': '已有账户？登录',
        'error_required_fields_missing_zh': '必填字段缺失。',
        'error_registration_failed_zh': '注册失败。请再试一次。',

        // Titles from Register.js (Chinese)
        'Mr._zh': '先生',
        'Mrs._zh': '夫人',
        'Ms._zh': '女士',
        'Dr._zh': '博士',

        // Provinces from Register.js (Chinese) - using key + _zh
        'AB_zh': '阿尔伯塔省',
        'BC_zh': '不列颠哥伦比亚省',
        'MB_zh': '马尼托巴省',
        'NB_zh': '新不伦瑞克省',
        'NL_zh': '纽芬兰和拉布拉多省',
        'NS_zh': '新斯科舍省',
        'ON_zh': '安大略省',
        'PE_zh': '爱德华王子岛省',
        'QC_zh': '魁北克省',
        'SK_zh': '萨斯喀彻温省',
        'NT_zh': '西北地区',
        'NU_zh': '努纳武特地区',
        'YT_zh': '育空地区',

        // Video Conference
        'video_conference': '视频会议',
        'start_conference': '开始会议',
        'join_conference': '加入会议',
        'conference_id': '会议号',
        'create_conference': '创建会议',
        'conference_settings': '会议设置',
        'waiting_room': '等候室',
        'end_conference': '结束会议',
        'video_quality': '视频质量',
        'audio_settings': '音频设置',
        'share_screen': '共享屏幕',
        'participants': '参会者',
        'chat': '聊天',

        // YouTubeAdmin Page Translations (Chinese)
        'error_fetching_videos': '获取视频列表失败',
        'edit_video': '编辑视频',
        'add_new_video': '添加新视频',
        'cancel_edit': '取消编辑',
        'title': '标题',
        'youtube_link': 'YouTube链接',
        'category': '分类',
        'thumbnail_url_optional': '缩略图URL (可选)',
        'description_optional': '描述 (可选)',
        'video_active': '视频启用',
        'video_inactive': '视频禁用',
        'save_changes': '保存更改',
        'add_video': '添加视频',
        'manage_videos': '管理视频',
        // 'status': '状态', // Already defined
        // 'actions': '操作', // This might be a general term; if specific, define as 'video_actions'
        // 'active': '有效', // Already defined
        // 'inactive': '无效', // Already defined
        'confirm_delete_video': '您确定要删除此视频吗？',
        'error_updating_video': '更新视频失败',
        'error_adding_video': '添加视频失败',
        'video_updated_successfully': '视频更新成功',
        'video_added_successfully': '视频添加成功',
        'error_deleting_video': '删除视频失败',
        'video_deleted_successfully': '视频删除成功',
        'no_videos_found_or_error': '未找到视频或发生错误。',
        'no_videos_yet_add_one': '暂无视频。请使用上方表单添加一个！',

        // Store/Shop translations
        'mmc_health_store': 'MMC健康商城',
        'search_products': '搜索商品...',
        'no_products_found': '暂无商品',
        'failed_to_load_products': '加载商品失败',
        'subscription': '订阅服务',
        'days': '天',
        'add_to_cart': '加入购物车',
        'features': '功能特点',
        'close': '关闭',
        'shopping_cart': '购物车',
        'cart_empty': '购物车为空',
        'continue_shopping': '继续购物',
        'checkout': '结账',
        'total': '总计',
        'view_summary': '查看摘要',
        'product_details': '商品详情',
        'price': '价格',
        'quantity': '数量',
        'remove_from_cart': '从购物车移除',
        'update_cart': '更新购物车',
        'order_summary': '订单摘要',
        'place_order': '下单',
        'order_confirmation': '订单确认',
        'order_number': '订单号',
        'order_total': '订单总额',
        'payment_method': '支付方式',
        'billing_address': '账单地址',
        'shipping_address': '收货地址',
        'order_status': '订单状态',
        'order_date': '订单日期',
        'estimated_delivery': '预计送达',
        'track_order': '跟踪订单',
        'order_history': '订单历史',
        'no_orders_found': '未找到订单',
        'order_details': '订单详情',
        'payment_successful': '支付成功',
        'payment_failed': '支付失败',
        'processing_payment': '正在处理支付...',
        'secure_checkout': '安全结账',
        'credit_card': '信用卡',
        'paypal': 'PayPal',
        'apple_pay': 'Apple Pay',
        'google_pay': 'Google Pay',
        'payment_service_unavailable': '支付服务暂未配置，请联系管理员',

        // Phone appointment policy
        'phone_arrival_notice': '如果您预约的是电话问诊，医生会在您预约的时间前后半小时左右打给您，来电号码可能会显示未知来电；如果您错过或者没有接上电话，请联系我们重新预约。',
        'phone_documents_notice': '请准备好您的医疗卡号码和相关医疗文件以便电话中核实。',
        'phone_cancellation_notice': '如需取消电话预约，请至少提前2个工作日通知，以避免相关费用。',
        'phone_emergency_notice': '电话预约不适用于紧急医疗情况，如遇紧急问题请拨打911或前往最近的急诊室。',

        // Pap Test specific appointment policy
        'pap_arrival_notice': 'Please arrive 5-10 minutes early to the clinic.',
        'pap_documents_notice': 'Please bring your Care Card.',
        'pap_clothing_notice': 'Please wear loose-fitting clothing for your convenience during the examination.',
        'pap_scope_notice': 'This appointment is for Pap (cervical cancer) screening only. For any other medical concerns, please consult your family doctor.',
        'pap_family_doctor_notice': 'Please have your family doctor\'s information ready. The test report will be sent directly to your family doctor\'s office in approximately 8-12 weeks.',
        'pap_cancellation_notice': 'Cancellations or reschedules must be made at least 48 hours in advance. Same-day cancellations or no-shows will be charged $75.',
    },
    zh: {
        // Navigation groups
        'basic_info': '基本信息',
        'appointment_mgmt': '预约管理',
        'health_records': '健康记录',
        'membership_services': '会员服务',
        'system_settings': '系统设置',
        'admin_panel': '管理面板',

        // Navigation items
        'profile': '个人资料',
        'membership': '会员信息',
        'appointments': '预约记录',
        'booking': '在线预约',
        'lab_reports': '检测结果',
        'prescriptions': '处方记录',
        'immunizations': '疫苗接种',
        'settings': '设置',
        'health_assistant': 'AI健康助手',
        'dietician': '营养师', // Added Dietician ZH translation

        // User menu
        'logout': '退出登录',
        'return_to_profile': '返回个人页面',
        'viewing_family_member': '您正在查看家庭成员信息',
        'preferences': '偏好设置',
        'language': '语言',
        'theme': '主题',
        'switch_to_chinese': '切换到中文',
        'switch_to_english': '切换到英文',
        'light_mode': '浅色模式',
        'dark_mode': '深色模式',

        // Common buttons and labels
        'save': '保存',
        'cancel': '取消',
        'loading': '加载中...',
        'error': '错误',
        'success': '成功',
        'no_data': '暂无数据',
        'confirm': '确认',
        'delete': '删除',
        'edit': '编辑',
        'view': '查看',
        'user': '用户',
        'not_selected': '未选择',

        // App title
        'app_title': 'MMC健康管理',
        'app_subtitle': '您的个人健康管理平台',

        // Profile page
        'personal_information': '个人信息',
        'full_name': '姓名',
        'address': '地址',
        'phone': '电话',
        'email': '邮箱',
        'care_card': '医疗卡号',
        'sex': '性别',
        'family_members': '家庭成员',
        'health_review': '健康摘要',
        'health_summary': '健康总结',
        'half_year_summaries': '半年度健康总结',
        'request_access': '请求访问权限',
        'access_active': '访问权限有效',
        'adult_member': '成年家庭成员',
        'minor_member': '未成年家庭成员',
        'direct_relatives_only': '只显示直系亲属',
        'eforms': '健康表格',
        'no_eforms_found': '未找到健康表格',
        'form_date': '日期',
        'form_name': '表格类型',
        'form_content': '表格内容',
        'no_form_content': '没有可用的表格内容',
        'provider': '医生',
        'no_membership': '无会员资格',
        'child': '子女',
        'spouse': '配偶',
        'mr': '先生',
        'mrs': '女士',
        'ms': '女士',

        // Profile page
        'change_avatar': '更换头像',
        'delete_avatar': '删除头像',
        'delete_avatar_confirmation': '您确定要删除您的头像吗？',
        'delete_avatar_success': '头像删除成功',
        'delete_avatar_error': '删除头像失败',
        'delete_avatar_warning': '此操作无法撤销',
        'delete_avatar_button': '删除头像',

        // Appointment page
        'your_appointments': '您的预约',
        'upcoming_appointments': '即将到来的预约',
        'past_appointments': '历史预约',
        'latest_appointments': '最近预约',
        'date': '日期',
        'time': '时间',
        'doctor': '医生',
        'location': '地点',
        'notes': '备注',
        'reason': '就诊原因',
        'status': '状态',
        'confirmed': '已确认',
        'completed': '已完成',
        'cancelled': '已取消',
        'pending': '待确认',
        'reschedule': '重新安排',
        'cancelled_appointments': '已取消预约',
        'cancelled_short': '已取消',
        'no_cancelled_appointments': '没有已取消的预约记录',

        // Booking page
        'in_person': '当面就诊',
        'service_type': '就诊类型',
        'pap_test': 'HPV检测',
        'by_phone': '电话就诊',
        'book_appointment': '在线预约',
        'select_provider': '选择医生',
        'select_date_time': '选择日期和时间',
        'confirm_booking': '确认预约详情',
        'provider': '医生',
        'next': '继续',
        'previous': '返回',
        'appointment_details': '预约详情',
        'appointment_confirmation': '预约确认',
        'appointment_reason': '就诊原因',
        'reason_placeholder': '请简要描述您的就诊原因（例如：症状、体检）以帮助医生做好准备。',
        'important_notice': '重要提示',
        'booking_notice': '预约成功后您将收到确认邮件。请注意：若需取消预约，请至少提前48小时通知，否则将收取75元的费用。感谢您的理解与配合。',
        'booking_success_title': '预约已确认',
        'booking_success_message': '您的预约已安排。确认详情已发送至您的邮箱。',
        'regular_appointment': '普通就诊',
        'select_location': '选择就诊方式',
        'online_phone': '电话预约',
        'in_clinic': '面诊',
        'in_person': '面诊',
        'zuòfèi': '已作废',
        'dàichǔlǐ': '待处理',
        'chóngxīn_ānpái': '重新安排',
        'yǐ_qǔxiāo_yùyuē': '已取消预约',
        'yǐ_qǔxiāo_duǎn': '已取消',
        'méiyǒu_yǐ_qǔxiāo_yùyuē': '没有已取消的预约记录',
        'yùyuē': '预约就诊',
        'select_visit_date': '选择就诊日期',
        'reason_for_visit': '就诊原因（必填）',
        'reason_for_visit_label': '就诊原因 (详细说明) *',
        'reason_for_visit_placeholder': '请描述您的就诊原因（例如：症状、体检等）。',

        // Image Upload (Booking Page)
        'error_invalid_image_file': '文件类型无效。请选择一个图像文件。',
        'upload_reason_image_optional': '上传原因图片 (可选)',
        'select_image': '选择图片',
        'select_images': '选择图片',
        'add_more_images': '添加更多图片',
        'upload_reason_images_optional': '上传原因图片（可选）',
        'error_max_images': '最多允许{{max}}张图片',
        'error_invalid_image_files': '部分文件不是有效的图片格式',
        'image_preview_alt': '预约原因图片预览',
        'remove_image_aria': '移除所选图片',

        // Appointment policy and terms
        'appointment_policy': '预约及取消政策',
        'arrival_notice': '请在预约时间前15分钟到达诊所。',
        'documents_notice': '请携带您的医疗卡和相关医疗文件。',
        'cancellation_notice': '如需取消预约，请至少提前2个工作日通知，以避免相关费用。',
        'emergency_notice': '如遇紧急医疗问题，请拨打911或前往最近的急诊室。',
        'terms_agreement': '我同意预约政策并理解取消条款。',
        'jul_dec': '7月-12月',
        'jan_jun': '1月-6月',
        'request_access_point3_title': '保密责任：',

        // Lab reports page -> Test Results
        'your_medical_documents': '您的报告与文档',
        'family_medical_documents': '家庭成员报告与文档',
        'documents': '报告与文档',
        'test_values': '检验值',
        'document': '文档',
        'document_viewer': '文档查看器',
        'no_documents': '未找到任何报告或文档。',
        'no_documents_of_type': '未找到此类型的文档。',
        'no_measurements': '在您的记录中没有找到测量数据。',
        'viewing_family_lab_reports': '您正在查看家庭成员的检测结果。',
        'invalid_response': '无效的响应格式。预期为JSON，收到: ',
        'lab_document': '检测报告',
        'consult_document': '专科报告',
        'lab': '检验结果',
        'consult': '专科报告',
        'eform': '检查申请单',
        'medical imaging': '影像检查报告',
        'referral': '专科转诊',
        'others': '其他文档',
        'download': '下载',
        'view_document': '查看文档',

        // Settings page
        'security': '安全',
        'notifications': '通知',
        'general_preferences': '通用设置',
        'app_experience_settings': '这些设置控制您的应用体验',
        'change_password': '修改密码',
        'current_password': '当前密码',
        'new_password': '新密码',
        'confirm_new_password': '确认新密码',
        'update_password': '更新密码',
        'passwords_do_not_match': '新密码不匹配',
        'password_too_short': '密码必须至少8个字符',
        'settings_updated': '设置更新成功！',
        'notification_preferences': '通知偏好',
        'email_notifications': '电子邮件通知',
        'receive_general_notifications': '接收常规通知邮件',
        'appointment_reminders': '预约提醒',
        'receive_appointment_reminders': '在预约前接收提醒',
        'document_notifications': '报告/文档通知',
        'receive_document_notifications': '当有新报告或文档可用时接收通知',
        'marketing_communications': '营销通讯',
        'receive_marketing_emails': '接收关于服务和活动的促销邮件',
        'use_24hour_format': '使用24小时制',

        // General for medical documents
        'unknown_date': '未知日期',
        'not_available': '暂无',
        'not_authenticated': '未认证。请登录。',
        'fetch_failed': '获取数据失败。',
        'fetch_error': '获取数据时发生错误。',
        'api_error': 'API返回错误。',
        'switch_back': '切换回我的个人资料',

        // Prescriptions
        'prescribed_by': '开具处方的医生',
        'details': '详情',
        'comments': '备注',
        'no_prescriptions': '没有找到处方记录。',
        'viewing_family_prescriptions': '您正在查看家庭成员的处方记录。',

        // Immunizations -> Vaccinations
        'vaccine_type': '疫苗/预防类型',
        'date_administered': '接种日期',
        'administered_by': '接种医护人员',
        'next_due_date': '下次接种日期',
        'no_immunizations': '未找到疫苗接种记录。',
        'viewing_family_immunizations': '您正在查看家庭成员的疫苗接种记录。',

        // Additional appointment page translations
        'network_error': '网络连接问题',
        'network_error_message': '无法连接到服务器，请检查您的网络连接并刷新页面。',
        'refresh': '刷新页面',
        'viewing_family_appointments': '您正在查看家庭成员的预约记录。',
        'family_appointments': '家庭成员预约记录',
        'error_loading_data': '加载数据错误',
        'upcoming_short': '即将到来',
        'past_short': '历史',
        'past_appointments_latest': '历史预约（最近10条）',
        'loading_appointments': '加载预约记录中...',
        'no_appointments_found': '没有找到预约记录',
        'no_upcoming_appointments': '没有即将到来的预约',
        'no_past_appointments': '没有历史预约记录',
        'invalid_patient_id': '无效的患者ID。请重新登录或联系客服。',
        'no_permission_appointments': '您没有权限查看此患者的预约信息。',
        'fetch_appointments_failed': '获取预约信息失败',
        'new_appointment': '预约新诊疗',
        'view_appointments': '查看我的预约',

        // Additional profile page translations
        'request_access_title': '确认授权请求',
        'request_access_dear_user': '尊敬的用户：',
        'request_access_intro': '您正在请求临时访问 {name} 的敏感健康信息。请注意以下事项：',
        'request_access_point1_title': '需要验证：',
        'request_access_point1_text': '访问权限需要通过发送到 {name} 注册邮箱地址的电子邮件进行验证。这是一项保护患者隐私的安全措施。',
        'request_access_point2_title': '临时访问：',
        'request_access_point2_text': '如果验证成功，授予的访问权限是临时的，并可能受到 MMC 健康管理中心政策规定的时间限制或其他限制。',
        'request_access_point4_title': '免责声明：',
        'request_access_point4_text': 'MMC 健康管理中心提供此访问机制是为了方便用户。尽管我们力求信息准确，但对于基于所查看信息做出的任何决定或任何滥用此访问功能的行为，MMC 健康管理中心不承担任何责任。',
        'request_access_confirmation': '点击"确认"，即表示您已理解这些条款，并希望继续进行验证过程以请求访问 {name} 的记录。',
        'request_access_sincerely': '此致，',
        'request_access_team': 'MMC 健康管理中心团队',
        'verification_title': '输入验证码',
        'verification_message': '请输入发送到{email}的验证码',
        'verification_code': '验证码',
        'verify': '验证并查看资料',
        'no_family_members': '目前没有家庭成员与您的账户关联。',
        'no_profile_data': '没有可用的个人资料数据。',
        'verification_error': '验证失败。请重试或联系客服。',
        'verification_success': '验证成功！正在重定向...',
        'profile_access_granted': '访问已授权。您现在可以查看此个人资料。',
        'profile_access_denied': '访问被拒绝。如果您认为这是错误，请联系客服。',
        'profile_access_expired': '访问已过期。请再次请求访问。',
        'profile_loading': '正在加载个人资料信息...',
        'request_verification': '请求验证码',
        'resend_code': '重新发送验证码',
        'last_updated': '最近更新',
        'last_updated_tooltip': '此摘要最近一次生成或更新的日期和时间',
        'refresh': '刷新',

        // Membership page
        'membership_info': '会员信息',
        'family_members_status': '家庭成员状态',
        'your_membership': '您的会员',
        'self': '本人',
        'family_member': '家庭成员',
        'no_membership': '无会员信息',
        'purchase_membership': '加入会员计划',
        'add_membership': '添加会员',
        'contact_to_add_membership': '联系我们添加会员',
        'click_below_to_purchase': '点击下方购买会员',
        'expired': '已过期',
        'active': '有效',
        'start_date': '开始日期',
        'end_date': '到期日期',
        'membership_type': '会员计划',
        'membership_start_date': '开始日期',
        'membership_end_date': '到期日期',
        'membership_status': '状态',
        'payment_amount': '支付金额',
        'renew_membership': '续订会员',
        'renew_early': '提前续订',
        'hide_history': '隐藏历史记录',
        'view_history': '查看历史记录',
        'membership_history': '会员历史记录',
        'loading_membership': '加载会员信息中...',
        'no_membership_records': '没有找到会员记录',
        'membership_details': '会员说明',
        'available_membership_types': '可选会员计划',
        'price': '价格',
        'no_membership_types': '暂无可用会员计划',
        'membership_benefits_note': '注意: 会员费不予退还。如对会员有任何疑问，请联系我们的办公室。',
        'select_membership_type': '请选择您想要购买的会员计划',
        'proceed_to_payment': '继续支付',
        'renewal_success': '会员续订成功！',
        'payment_process_note': '注意: 这将作为新会员购买处理。会员资格将从今天起一年内有效。',
        'authentication_failed': '认证失败，请重新登录',
        'server_invalid_format': '服务器返回格式错误',
        'server_invalid_structure': '服务器返回无效的数据结构',
        'fetch_membership_failed': '获取会员信息失败',
        'fetch_membership_failed_retry': '获取会员信息失败，请稍后再试',
        'fetch_membership_types_failed': '获取会员计划信息失败',
        'request_timeout': '请求超时，请检查您的网络连接后重试',
        'status_code': '错误代码',

        // Chatbot
        'chatbot': 'AI健康助手',
        'chat_window_title': 'AI健康助手',
        'chat_bot_name': 'MMC AI健康助手',
        'chat_greeting': '您好！我是MMC AI健康助手。今天我能帮您解答哪些健康管理问题？',
        'type_message': '请在此输入您的问题...',

        // Appointment Cancellation
        'cancel_appointment_confirmation': '确认取消预约',
        'cancel_appointment_warning': '请注意，预约需至少提前48小时取消。取消信息将通知诊所。',
        'cancel_policy': '若需取消预约，请至少提前48小时通知，否则将收取75元的费用。感谢您的理解与配合。',
        'cancellation_lead_time': '取消预约需要提前48小时通知',
        'cancellation_email_notice': '关于您的取消预约，系统将自动发送通知邮件至诊所(<EMAIL>)。',
        'confirm_cancel': '确认取消',
        'cancelling': '取消中...',
        'appointment_cancelled_successfully': '预约已成功取消',
        'back': '返回',
        'close': '关闭',
        'cannot_cancel_late': '由于距离预约时间不足2个工作日，此预约无法在线取消。请直接致电诊所讨论取消选项。',
        'auth_required': '需要身份验证。请重新登录。',
        'cancel_failed': '取消预约失败。请重试或直接联系诊所。',

        // Booking page additions/refinements
        'booking_intro': '在线轻松安排就诊。您可以按服务类型或指定医生进行预约。',
        'book_by_service': '按服务类型预约',
        'book_by_doctor': '按医生预约',
        'select_service_type': '选择服务类型',
        'select_date_time_doctor': '选择日期、时间与医生',
        'service_type_description': '请选择您需要的医疗服务类型。我们提供多种专业服务，由经验丰富的专科医生提供。',
        'service_no_description': '由我院专业医疗团队提供的专科医疗服务',

        // Service types
        'service_types': {
            '健康管理咨询': {
                en: 'Health Management Consultation',
                zh: '健康管理咨询',
                description: {
                    en: 'Comprehensive health assessment and personalized management plan',
                    zh: '全面评估您的健康状况，制定个性化健康管理方案，预防疾病发生'
                }
            },
            '健康管理（电话）': {
                en: 'Telehealth Consultation',
                zh: '远程健康咨询',
                description: {
                    en: 'Convenient phone consultation with our healthcare professionals',
                    zh: '便捷的电话咨询服务，无需亲临医院，即可获得专业医疗建议'
                }
            },
            '功能医学咨询': {
                en: 'Functional Medicine',
                zh: '功能医学评估',
                description: {
                    en: 'Address root causes of health issues with personalized functional approach',
                    zh: '通过功能医学方法找出健康问题根源，制定个性化治疗方案'
                }
            },
            'Weight Loss': {
                en: 'Weight Loss Program',
                zh: '体重管理项目',
                description: {
                    en: 'Medically supervised program for weight management.',
                    zh: '医疗监督下的体重管理项目。'
                }
            },
            'Peptides': {
                en: 'Peptide Therapy Consultation',
                zh: '肽疗法咨询',
                description: {
                    en: 'Consultation regarding peptide therapies.',
                    zh: '关于肽疗法的咨询服务。'
                }
            },
            'Pap Test': {
                en: 'Pap Test 宫颈癌抹片筛查',
                zh: 'Pap Test 宫颈癌抹片筛查',
                description: {
                    en: 'Examined and sampled by an experienced female physician at MMC.',
                    zh: '由MMC经验丰富的妇科女医生亲自检查取样'
                }
            }
        },

        // Date selection refinements
        'date_selection_description': '请选择您希望就诊的日期，然后从下方选择可用的时间和医生。',
        'select_date': '选择就诊日期',
        'appointment_date': '预约日期',
        'date_notice': '注意：预约需至少提前3天进行',
        'loading_available_dates': '正在查询可预约日期...',
        'no_available_dates': '当前服务类型暂无可预约日期，请尝试其他服务。',
        'available_dates_info': '已找到可预约日期，请选择合适的时间',

        // Navigation buttons
        'doctor_selection_description': '请选择您偏好的医生，然后查看该医生的可预约时间。',
        'doctor_selection_prompt': '选择首选医生',
        'available_times': '可预约时间',
        'no_slots_available': '当日无可用预约时间',
        'select_date_first': '请先选择日期',
        'selected_service': '所选服务',
        'selected_doctor': '所选医生',
        'selected_date': '所选日期',
        'selected_time': '所选时间',
        'available_slots': '可预约时间',

        // Chatbot related translations
        'mmc_wellness_chat': 'MMC AI健康助手',
        'chat_history': '聊天历史',
        'conversations': '对话记录',
        'new_chat': '新对话',
        'new_conversation': '新对话',
        'delete_conversation': '删除对话',
        'no_conversations': '暂无对话记录',
        'welcome_to_mmc_chat': '欢迎使用MMC AI健康助手',
        'chat_intro_expanded': '我可以提供常见健康问题、季节性健康提醒和MMC服务的相关信息。有什么可以帮到您的吗？',
        'type_message': '请在此输入您的问题...',
        'send': '发送',
        'sending': '发送中...',
        'you': '您',
        'more_options': '更多选项',
        'chat_greeting': '您好！有什么健康方面的问题我可以帮您解答？',
        'cannot_load_chat_history': '无法加载聊天历史。请稍后再试。',
        'conversation_deleted': '对话已成功删除',
        'failed_to_delete': '删除对话失败',
        'error_loading_conversation': '加载对话失败',
        'search_chats': '搜索对话记录',

        // Errors
        'error_fetching_providers': '获取医生列表失败',
        'error_fetching_availability': '获取可预约时间失败',
        'error_fetching_available_dates': '获取可预约日期失败',
        'select_doctor_for_slot': '请为所选时段选择医生',
        'error_fetching_doctor_services': '获取所选医生的服务项目失败',
        'select_service_for_doctor': '请选择该医生提供的一项服务',
        'select_service_for_provider': '选择服务项目',
        'select_service_provided_by': '请选择医生提供的服务项目：',
        'no_services_found_for_doctor': '未找到该医生可在线预约的特定服务项目。',
        'error_missing_details': '预约失败：缺少必要信息（医生、日期或时间）。',
        'booking_failed': '预约失败。请重试或联系诊所。',

        // Member Resources (新增)
        'member_resources': '会员资源',
        'member_resources_description': '为会员专享的文章和资源',
        'resource_not_available': '此资源暂时不可用',
        'read_more': '阅读更多',
        'loading_resources': '加载资源中...',
        'no_resources_found': '未找到资源',
        'view_all_resources': '查看所有资源',
        'resource_details': '资源详情',
        'back_to_resources': '返回资源列表',
        'article_loading': '加载文章中...',

        // File sharing
        'file_sharing': '文件共享',
        'file_sharing_description': '访问和共享您的医疗文档',
        'upload_file': '上传文件',
        'download_file': '下载',
        'delete_file': '删除',
        'share_file': '共享',
        'my_files': '我的文件',
        'shared_files': '共享文件',
        'file_name': '文件名',
        'file_size': '大小',
        'file_type': '类型',
        'upload_date': '上传日期',
        'shared_with': '共享给',
        'shared_by': '共享人',
        'no_files_found': '未找到文件',
        'upload_success': '文件上传成功',
        'delete_success': '文件删除成功',
        'file_operations': '文件操作',
        'select_file': '选择文件',
        'uploading': '上传中...',
        'upload': '上传',
        'cancel': '取消',
        'share_file_with': '与家庭成员共享文件',
        'search_family_members': '搜索家庭成员',
        'select_recipient': '选择接收人',
        'sharing': '共享中...',
        'share': '共享',
        'person_count': '人',
        'not_shared': '未共享',
        'please_select_file': '请选择文件',
        'no_shared_files': '未找到共享文件',

        // RedBook Videos Page
        'redbook_videos': '健康视频频道',
        'redbook_videos_description': '观看我们诊所团队提供的最新健康视频和教程',
        'video_categories': '视频分类',
        'no_videos_found': '未找到此类别的视频',
        'open_redbook': '在小红书查看',
        'view_on_redbook': '在小红书上查看',
        'video_preview_unavailable': '平台内无法预览视频，请点击下方按钮在小红书上观看',
        'close': '关闭',

        // YouTube Videos Page - 新增
        'youtube_videos': 'YouTube健康视频',
        'youtube_videos_description': '观看专业医疗团队精选的健康视频与相关教程',
        'view_on_youtube': '在YouTube上观看完整视频',
        'no_videos_found': '该分类下没有视频',
        'all_videos': '全部视频',
        'nutrition': '营养',
        'fitness': '健身',
        'mental_health': '心理健康',
        'sleep': '睡眠',
        'general_health': '综合健康',
        'video_dialog_title': '观看视频',
        'swipe_up_down': '上滑下滑切换视频',
        'tap_to_play_pause': '点击播放/暂停',
        'swipe_mode': '滑动模式',
        'card_mode': '卡片模式',
        'video_mode': '视频浏览模式',
        'share_video': '分享视频',
        'video_loading': '加载视频中...',
        'video_load_error': '视频加载失败',

        // Appointment Notes
        'appointment_note': '就诊记录',
        'view_note': '查看记录',
        'original_note': '原始医疗记录',
        'ai_summary': 'AI简化摘要',
        'ai_generated_summary': '患者友好AI摘要',
        'no_note_content': '此次就诊没有详细记录内容',
        'no_medical_note': '未找到此次预约的医疗记录',
        'ai_summary_not_available': '此记录暂无AI摘要',
        'by': '医生：',
        'cancel_appointment': '取消预约',
        'no_appointments': '未找到预约记录',
        'date_format_short': 'MM/DD/YYYY',

        // Dietician Page
        'no_dietician_comments': '未找到营养师咨询记录',
        'dietician_consultation_available': '营养师咨询记录可查看',
        'view_summary': '查看总结',
        'dietician_consultation_summary': '营养师咨询总结',
        'consultation_details': '咨询详情',
        'nutrition_summary': '营养总结',
        'generating_summary': '正在生成AI总结...',
        'summary_will_be_generated': '总结将自动生成',
        'summary_fetch_failed': '获取总结失败',
        'summary_fetch_error': '获取总结时出错',

        // Referral-related translations
        referral_code: '推荐码',
        referral_code_explanation: '输入现有会员的推荐码进行注册',
        invalid_referral_code: '无效的推荐码',
        valid_referral_code: '有效的推荐码',
        enter_referral_code: '输入现有会员提供的推荐码',
        your_referral_code: '您的推荐码',
        referred_users: '已推荐用户',
        referral_code_title: '您的个人推荐码',
        referral_code_description: '与朋友和家人分享此代码。当他们使用您的代码注册时，您将获得奖励积分，可用于会员折扣。',
        copy_code: '复制代码',
        copied: '已复制！',
        share_referral: '分享您的推荐码',
        share_referral_title: '加入MMC健康管理中心',
        share_referral_text: '我邀请您加入MMC健康管理中心。注册时请使用我的推荐码：{code}',
        referred_users_title: '您已推荐的用户',
        no_referrals_yet: '您还没有推荐任何人。',
        get_your_referral_code: '获取您的推荐码',
        total_reward_points: '总奖励积分',
        referred_on: '推荐日期：{date}',
        status_pending: '待处理',
        status_completed: '已完成',
        status_rewarded: '已获得{points}积分',

        // Registration Page Translations
        'referral_code_label': '推荐码',
        'tooltip_enter_referral_code': '请输入推荐码',
        'error_invalid_referral_code': '无效的推荐码',
        'success_valid_referral_code': '推荐码有效',
        'helper_enter_referral_code': '请输入推荐码',
        'section_title_personal_information': '个人信息',
        'label_title': '称谓',
        'label_first_name': '名字',
        'label_last_name': '姓氏',
        'label_date_of_birth': '出生日期',
        'label_sex': '性别',
        'sex_male': '男性',
        'sex_female': '女性',
        'sex_other': '其他',
        'section_title_contact_information': '联系方式',
        'label_phone_number': '电话号码',
        'label_email': '电子邮箱',
        'section_title_address': '地址信息',
        'label_address': '详细地址',
        'label_city': '城市',
        'label_province': '省份/地区',
        'label_postal_code': '邮政编码',
        'section_title_account_credentials': '账户凭证',
        'label_password': '密码',
        'label_confirm_password': '确认密码',
        'section_title_optional_information': '可选信息',
        'label_health_insurance_number_optional': '健康卡号 (可选)',
        'helper_text_optional_field_hin': '此为可选字段，如有健康卡号请填写',
        'button_register': '注册',
        'link_already_have_account': '已有账户？点此登录',

        // Registration Page Error/Generic Messages
        'patient_registration_title': '患者注册',
        'error_required_fields_missing': '必填字段缺失',
        'error_passwords_do_not_match': '密码不匹配',
        'error_registration_failed': '注册失败，请稍后重试',

        // Login Page
        'login_title': 'Login to Your Account',
        'login_button': 'Login',
        'forgot_password_link': 'Forgot Password?',
        'no_account_prompt': "Don't have an account?",
        'register_link': 'Register here',
        'login_failed_message': 'Login failed. Please check your email and password.',
        'verification_required_message': 'Please verify your email before logging in. Check your inbox for a verification link.',
        'verification_sent_message': 'Verification email sent. Please check your inbox.',
        'resend_verification_button': 'Resend Verification Email',
        'email_verified_message': 'Email verified successfully! You can now log in.',
        'invalid_or_expired_token_message': 'Invalid or expired verification token.',

        // Registration Page
        'patient_registration_title': 'Patient Registration',
        'referral_code_label': 'Referral Code',
        'referral_code_section_title': 'Referral Code',
        'error_invalid_or_missing_referral_code': 'Invalid or missing referral code.',
        'referral_code_validated': 'Referral code validated successfully!',
        'referral_code_helper_text': 'Enter the referral code from an existing member.',
        'personal_information_section_title': 'Personal Information',
        'title_label': 'Title',
        'first_name_label': 'First Name',
        'last_name_label': 'Last Name',
        'date_of_birth_label': 'Date of Birth',
        'sex_label': 'Gender',
        'gender_male': 'Male',
        'gender_female': 'Female',
        'gender_other': 'Other',
        'contact_information_section_title': 'Contact Information',
        'email_label': 'Email Address',
        'phone_number_label': 'Phone Number',
        'address_information_section_title': 'Address Information',
        'address_label': 'Street Address',
        'city_label': 'City',
        'province_label': 'Province/Territory',
        'postal_code_label': 'Postal Code',
        'health_security_section_title': 'Health & Security',
        'health_card_number_label': 'Health Card Number (HIN/Care Card)',
        'health_card_number_tooltip': 'Please enter exactly 10 digits for your health card number.',
        'password_label': 'Password',
        'password_helper_text': 'Password must be at least 8 characters.',
        'confirm_password_label': 'Confirm Password',
        'error_passwords_do_not_match': 'Passwords do not match.',
        'register_button': 'Register',
        'already_have_account': 'Already have an account?',
        'login_link': 'Login here',
        'error_invalid_referral_code': 'Invalid referral code. Please check and try again.',
        'success_valid_referral_code': 'Referral code is valid!',
        'helper_enter_referral_code': 'Enter the referral code provided by an existing member.',
        'tooltip_enter_referral_code': 'If you have a referral code, enter it here.',
        'section_title_personal_information': 'Personal Information',
        'label_title': 'Title',
        'label_first_name': 'First Name',
        'label_last_name': 'Last Name',
        'label_date_of_birth': 'Date of Birth',
        'label_sex': 'Gender',
        'sex_male': 'Male',
        'sex_female': 'Female',
        'sex_other': 'Other',
        'section_title_contact_information': 'Contact Information',
        'label_phone_number': 'Phone Number',
        'label_email': 'Email Address',
        'section_title_address': 'Address Information',
        'label_address': 'Street Address',
        'label_city': 'City',
        'label_province': 'Province/Territory',
        'label_postal_code': 'Postal Code',
        'section_title_account_credentials': 'Account Credentials',
        'label_password': 'Password',
        'label_confirm_password': 'Confirm Password',
        'section_title_optional_information': 'Optional Information',
        'label_health_insurance_number_optional': 'Health Card Number (Optional)',
        'helper_text_optional_field_hin': 'This is an optional field. If you have a health card number, please enter it',
        'button_register': 'Register',
        'link_already_have_account': 'Already have an account? Login',
        'error_required_fields_missing': 'Required fields are missing',
        'error_registration_failed': 'Registration failed. Please try again later',

        // Titles from Register.js
        'Mr.': 'Mr.',
        'Mrs.': 'Mrs.',
        'Ms.': 'Ms.',
        'Dr.': 'Dr.',

        // Provinces from Register.js (abbreviations will be used as keys)
        'AB': 'Alberta',
        'BC': 'British Columbia',
        'MB': 'Manitoba',
        'NB': 'New Brunswick',
        'NL': 'Newfoundland and Labrador',
        'NS': 'Nova Scotia',
        'ON': 'Ontario',
        'PE': 'Prince Edward Island',
        'QC': 'Quebec',
        'SK': 'Saskatchewan',
        'NT': 'Northwest Territories',
        'NU': 'Nunavut',
        'YT': 'Yukon',

        // Login Page (Chinese)
        'login_title_zh': '登录您的账户',
        'login_button_zh': '登录',
        'forgot_password_link_zh': '忘记密码？',
        'no_account_prompt_zh': '还没有账户？',
        'register_link_zh': '在此注册',
        'login_failed_message_zh': '登录失败。请检查您的邮箱和密码。',
        'verification_required_message_zh': '登录前请验证您的邮箱。请检查您的收件箱中的验证链接。',
        'verification_sent_message_zh': '验证邮件已发送。请检查您的收件箱。',
        'resend_verification_button_zh': '重新发送验证邮件',
        'email_verified_message_zh': '邮箱验证成功！您现在可以登录了。',
        'invalid_or_expired_token_message_zh': '无效或已过期的验证令牌。',
        
        // Registration Page (Chinese)
        'patient_registration_title_zh': '患者注册',
        'referral_code_label_zh': '推荐码',
        'referral_code_section_title_zh': '推荐码区',
        'error_invalid_or_missing_referral_code_zh': '无效或缺少推荐码。',
        'referral_code_validated_zh': '推荐码验证成功!',
        'referral_code_helper_text_zh': '请输入现有会员的推荐码。',
        'personal_information_section_title_zh': '个人信息区',
        'title_label_zh': '称谓',
        'first_name_label_zh': '名',
        'last_name_label_zh': '姓',
        'date_of_birth_label_zh': '出生日期',
        'sex_label_zh': '性别',
        'gender_male_zh': '男性',
        'gender_female_zh': '女性',
        'gender_other_zh': '其他',
        'contact_information_section_title_zh': '联系信息区',
        'email_label_zh': '电子邮箱地址',
        'phone_number_label_zh': '电话号码',
        'address_information_section_title_zh': '地址信息区',
        'address_label_zh': '街道地址',
        'city_label_zh': '城市',
        'province_label_zh': '省/地区',
        'postal_code_label_zh': '邮政编码',
        'health_security_section_title_zh': '健康与安全区',
        'health_card_number_label_zh': '健康卡号（HIN/Care Card）',
        'health_card_number_tooltip_zh': '请输入您的10位健康卡号码。',
        'password_label_zh': '密码',
        'password_helper_text_zh': '密码必须至少为8个字符。',
        'confirm_password_label_zh': '确认密码',
        'error_passwords_do_not_match_zh': '密码不匹配。',
        'register_button_zh': '注册',
        'already_have_account_zh': '已有账户？',
        'login_link_zh': '在此登录',
        'error_invalid_referral_code_zh': '推荐码无效。请检查后重试。',
        'success_valid_referral_code_zh': '推荐码有效！',
        'helper_enter_referral_code_zh': '请输入现有会员提供的推荐码。',
        'tooltip_enter_referral_code_zh': '如果您有推荐码，请在此输入。',
        'section_title_personal_information_zh': '个人信息',
        'label_title_zh': '称呼',
        'label_first_name_zh': '名字',
        'label_last_name_zh': '姓氏',
        'label_date_of_birth_zh': '出生日期',
        'label_sex_zh': '性别',
        'sex_male_zh': '男',
        'sex_female_zh': '女',
        'sex_other_zh': '其他',
        'section_title_contact_information_zh': '联系信息',
        'label_phone_number_zh': '电话号码',
        'label_email_zh': '电子邮箱',
        'section_title_address_zh': '地址',
        'label_address_zh': '地址',
        'label_city_zh': '城市',
        'label_province_zh': '省份',
        'label_postal_code_zh': '邮政编码',
        'section_title_account_credentials_zh': '账户凭证',
        'label_password_zh': '密码',
        'label_confirm_password_zh': '确认密码',
        'section_title_optional_information_zh': '可选信息',
        'label_health_insurance_number_optional_zh': '健康保险号码（可选）',
        'helper_text_optional_field_hin_zh': '此字段为可选。',
        'button_register_zh': '注册',
        'link_already_have_account_zh': '已有账户？登录',
        'error_required_fields_missing_zh': '必填字段缺失。',
        'error_registration_failed_zh': '注册失败。请再试一次。',

        // Titles from Register.js (Chinese)
        'Mr._zh': '先生',
        'Mrs._zh': '夫人',
        'Ms._zh': '女士',
        'Dr._zh': '博士',

        // Provinces from Register.js (Chinese) - using key + _zh
        'AB_zh': '阿尔伯塔省',
        'BC_zh': '不列颠哥伦比亚省',
        'MB_zh': '马尼托巴省',
        'NB_zh': '新不伦瑞克省',
        'NL_zh': '纽芬兰和拉布拉多省',
        'NS_zh': '新斯科舍省',
        'ON_zh': '安大略省',
        'PE_zh': '爱德华王子岛省',
        'QC_zh': '魁北克省',
        'SK_zh': '萨斯喀彻温省',
        'NT_zh': '西北地区',
        'NU_zh': '努纳武特地区',
        'YT_zh': '育空地区',

        // Video Conference
        'video_conference': '视频会议',
        'start_conference': '开始会议',
        'join_conference': '加入会议',
        'conference_id': '会议号',
        'create_conference': '创建会议',
        'conference_settings': '会议设置',
        'waiting_room': '等候室',
        'end_conference': '结束会议',
        'video_quality': '视频质量',
        'audio_settings': '音频设置',
        'share_screen': '共享屏幕',
        'participants': '参会者',
        'chat': '聊天',

        // YouTubeAdmin Page Translations (Chinese)
        'error_fetching_videos': '获取视频列表失败',
        'edit_video': '编辑视频',
        'add_new_video': '添加新视频',
        'cancel_edit': '取消编辑',
        'title': '标题',
        'youtube_link': 'YouTube链接',
        'category': '分类',
        'thumbnail_url_optional': '缩略图URL (可选)',
        'description_optional': '描述 (可选)',
        'video_active': '视频启用',
        'video_inactive': '视频禁用',
        'save_changes': '保存更改',
        'add_video': '添加视频',
        'manage_videos': '管理视频',
        // 'status': '状态', // Already defined
        // 'actions': '操作', // This might be a general term; if specific, define as 'video_actions'
        // 'active': '有效', // Already defined
        // 'inactive': '无效', // Already defined
        'confirm_delete_video': '您确定要删除此视频吗？',
        'error_updating_video': '更新视频失败',
        'error_adding_video': '添加视频失败',
        'video_updated_successfully': '视频更新成功',
        'video_added_successfully': '视频添加成功',
        'error_deleting_video': '删除视频失败',
        'video_deleted_successfully': '视频删除成功',
        'no_videos_found_or_error': '未找到视频或发生错误。',
        'no_videos_yet_add_one': '暂无视频。请使用上方表单添加一个！',

        // Store/Shop translations
        'mmc_health_store': 'MMC健康商城',
        'search_products': '搜索商品...',
        'no_products_found': '暂无商品',
        'failed_to_load_products': '加载商品失败',
        'subscription': '订阅服务',
        'days': '天',
        'add_to_cart': '加入购物车',
        'features': '功能特点',
        'close': '关闭',
        'shopping_cart': '购物车',
        'cart_empty': '购物车为空',
        'continue_shopping': '继续购物',
        'checkout': '结账',
        'total': '总计',
        'view_summary': '查看摘要',
        'product_details': '商品详情',
        'price': '价格',
        'quantity': '数量',
        'remove_from_cart': '从购物车移除',
        'update_cart': '更新购物车',
        'order_summary': '订单摘要',
        'place_order': '下单',
        'order_confirmation': '订单确认',
        'order_number': '订单号',
        'order_total': '订单总额',
        'payment_method': '支付方式',
        'billing_address': '账单地址',
        'shipping_address': '收货地址',
        'order_status': '订单状态',
        'order_date': '订单日期',
        'estimated_delivery': '预计送达',
        'track_order': '跟踪订单',
        'order_history': '订单历史',
        'no_orders_found': '未找到订单',
        'order_details': '订单详情',
        'payment_successful': '支付成功',
        'payment_failed': '支付失败',
        'processing_payment': '正在处理支付...',
        'secure_checkout': '安全结账',
        'credit_card': '信用卡',
        'paypal': 'PayPal',
        'apple_pay': 'Apple Pay',
        'google_pay': 'Google Pay',
        'payment_service_unavailable': '支付服务暂未配置，请联系管理员',

        // Phone appointment policy
        'phone_arrival_notice': '如果您预约的是电话问诊，医生会在您预约的时间前后半小时左右打给您，来电号码可能会显示未知来电；如果您错过或者没有接上电话，请联系我们重新预约。',
        'phone_documents_notice': '请准备好您的医疗卡号码和相关医疗文件以便电话中核实。',
        'phone_cancellation_notice': '如需取消电话预约，请至少提前2个工作日通知，以避免相关费用。',
        'phone_emergency_notice': '电话预约不适用于紧急医疗情况，如遇紧急问题请拨打911或前往最近的急诊室。',

        // Pap Test specific appointment policy
        'pap_arrival_notice': '请提前5-10分钟到达诊所。',
        'pap_documents_notice': '请携带您的医疗卡（Care Card）。',
        'pap_clothing_notice': '请穿着宽松衣物，以方便检查。',
        'pap_scope_notice': '本次预约仅用于宫颈抹片（Pap）宫颈癌筛查，如有其他医疗需求，请咨询您的家庭医生。',
        'pap_family_doctor_notice': '请准备好您家庭医生的相关信息，检查报告将在约8-12周后直接发送至您的家庭医生诊所。',
        'pap_cancellation_notice': '如需取消或更改预约，请至少提前48小时通知；若当天取消或当天没有来，将收取 $75 费用。',
    },
};

// Create the language context
const LanguageContext = createContext();

export const LanguageProvider = ({ children }) => {
    // Try to get language from localStorage, default to Chinese
    const [language, setLanguage] = useState(() => {
        const savedLanguage = localStorage.getItem('language');
        return savedLanguage || 'zh';
    });

    // Save language preference to localStorage when it changes
    useEffect(() => {
        localStorage.setItem('language', language);
    }, [language]);

    // Function to toggle language
    const toggleLanguage = () => {
        setLanguage(prevLang => prevLang === 'en' ? 'zh' : 'en');
    };

    // Translate function - UPDATED to handle placeholder replacement
    const t = (key, options = {}) => { // Accept options object
        const currentTranslations = translations[language];
        let translation = currentTranslations[key] || key; // Get base translation or key itself

        // Replace placeholders like {key} with values from options object
        if (typeof translation === 'string' && options) {
            Object.keys(options).forEach((optionKey) => {
                // Ensure the key is a valid string for regex replacement and not just a number
                if (typeof optionKey === 'string' && isNaN(parseInt(optionKey))) {
                    try {
                        // Use a regex to replace all occurrences of {optionKey}
                        // 正确转义花括号，确保正则表达式有效
                        const regex = new RegExp(`\\{${optionKey}\\}`, 'g');
                        translation = translation.replace(regex, options[optionKey]);
                    } catch (e) {
                        // Log regex error if it happens, but don't crash
                        console.error(`Error creating/using regex for key: ${optionKey}`, e);
                    }
                }
            });
        }

        return translation;
    };

    // Context value
    const contextValue = {
        language,
        setLanguage,
        toggleLanguage,
        t
    };

    return (
        <LanguageContext.Provider value={contextValue}>
            {children}
        </LanguageContext.Provider>
    );
};

// Custom hook to use the language context
export const useLanguage = () => {
    const context = useContext(LanguageContext);
    if (!context) {
        throw new Error('useLanguage must be used within a LanguageProvider');
    }
    return context;
};