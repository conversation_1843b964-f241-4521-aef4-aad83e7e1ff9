import React, { useState, useEffect } from 'react';
import {
    Container,
    Typography,
    <PERSON>,
    Card,
    CardContent,
    CardActions,
    Button,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    TextField,
    Switch,
    FormControlLabel,
    Grid,
    Chip,
    Alert,
    Snackbar,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    IconButton,
    Tooltip,
    Select,
    MenuItem,
    FormControl,
    InputLabel,
    CircularProgress,
    InputAdornment
} from '@mui/material';
import {
    Add as AddIcon,
    Edit as EditIcon,
    Delete as DeleteIcon,
    Visibility as VisibilityIcon,
    Archive as ArchiveIcon,
    Unarchive as UnarchiveIcon,
    Save as SaveIcon,
    Cancel as CancelIcon,
    HealthAndSafety as HealthIcon,
    Search as SearchIcon,
    Star as StarIcon,
    StarBorder as StarBorderIcon
} from '@mui/icons-material';
import { useLanguage } from '../context/LanguageContext';
import { API_URL } from '../utils/env';
import axios from 'axios';
import MarkdownPreview from './MarkdownPreview';

const HealthGuideAdmin = () => {
    const { t } = useLanguage();
    
    // 状态管理
    const [guides, setGuides] = useState([]);
    const [categories, setCategories] = useState([]);
    const [loading, setLoading] = useState(true);
    const [dialogOpen, setDialogOpen] = useState(false);
    const [editingGuide, setEditingGuide] = useState(null);
    const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [categoryFilter, setCategoryFilter] = useState('all');
    const [showPreview, setShowPreview] = useState(false);
    
    // 表单状态
    const [formData, setFormData] = useState({
        title: '',
        description: '',
        content: '',
        category: '',
        featured: false,
        status: 'published'
    });

    // 获取健康指南数据
    useEffect(() => {
        fetchData();
    }, []);

    const fetchData = async () => {
        try {
            setLoading(true);
            const [guidesResponse, categoriesResponse] = await Promise.all([
                axios.get(`${API_URL}/api/health-guides/admin/all`),
                axios.get(`${API_URL}/api/health-guides/categories`)
            ]);
            
            setGuides(guidesResponse.data.guides || []);
            setCategories(categoriesResponse.data || []);
        } catch (error) {
            console.error('Error fetching health guides:', error);
            showSnackbar('获取数据失败: ' + (error.response?.data?.message || error.message), 'error');
        } finally {
            setLoading(false);
        }
    };

    // 显示提示消息
    const showSnackbar = (message, severity = 'success') => {
        setSnackbar({ open: true, message, severity });
    };

    // 关闭提示消息
    const handleCloseSnackbar = () => {
        setSnackbar({ ...snackbar, open: false });
    };

    // 打开添加/编辑对话框
    const handleOpenDialog = (guide = null) => {
        if (guide) {
            setEditingGuide(guide);
            setFormData({
                title: guide.title || '',
                description: guide.description || '',
                content: guide.content || '',
                category: guide.category || '',
                featured: guide.featured || false,
                status: guide.status || 'published'
            });
        } else {
            setEditingGuide(null);
            setFormData({
                title: '',
                description: '',
                content: '',
                category: '',
                featured: false,
                status: 'published'
            });
        }
        setShowPreview(false);
        setDialogOpen(true);
    };

    // 关闭对话框
    const handleCloseDialog = () => {
        setDialogOpen(false);
        setEditingGuide(null);
    };

    // 处理表单输入
    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    // 保存健康指南
    const handleSaveGuide = async () => {
        try {
            if (!formData.title || !formData.description || !formData.content) {
                showSnackbar('请填写所有必填字段', 'error');
                return;
            }

            if (editingGuide) {
                // 更新现有指南
                await axios.put(`${API_URL}/api/health-guides/${editingGuide.id}`, formData);
                showSnackbar('健康指南更新成功');
            } else {
                // 创建新指南
                await axios.post(`${API_URL}/api/health-guides`, formData);
                showSnackbar('健康指南创建成功');
            }
            
            handleCloseDialog();
            fetchData();
        } catch (error) {
            console.error('Error saving health guide:', error);
            showSnackbar('保存失败: ' + (error.response?.data?.message || error.message), 'error');
        }
    };

    // 切换指南状态
    const handleToggleStatus = async (guide) => {
        try {
            const newStatus = guide.status === 'published' ? 'archived' : 'published';
            await axios.put(`${API_URL}/api/health-guides/${guide.id}`, {
                status: newStatus
            });
            showSnackbar(`健康指南已${newStatus === 'published' ? '发布' : '归档'}`);
            fetchData();
        } catch (error) {
            console.error('Error toggling guide status:', error);
            showSnackbar('状态更新失败: ' + (error.response?.data?.message || error.message), 'error');
        }
    };

    // 切换推荐状态
    const handleToggleFeatured = async (guide) => {
        try {
            await axios.put(`${API_URL}/api/health-guides/${guide.id}`, {
                featured: !guide.featured
            });
            showSnackbar(`健康指南已${!guide.featured ? '设为推荐' : '取消推荐'}`);
            fetchData();
        } catch (error) {
            console.error('Error toggling featured status:', error);
            showSnackbar('推荐状态更新失败: ' + (error.response?.data?.message || error.message), 'error');
        }
    };

    // 删除健康指南
    const handleDeleteGuide = async (guideId) => {
        if (!window.confirm('确定要删除这个健康指南吗？此操作不可恢复。')) {
            return;
        }

        try {
            await axios.delete(`${API_URL}/api/health-guides/${guideId}`);
            showSnackbar('健康指南删除成功');
            fetchData();
        } catch (error) {
            console.error('Error deleting guide:', error);
            showSnackbar('删除失败: ' + (error.response?.data?.message || error.message), 'error');
        }
    };

    // 获取状态颜色
    const getStatusColor = (status) => {
        switch (status) {
            case 'published': return 'success';
            case 'draft': return 'warning';
            case 'archived': return 'default';
            default: return 'default';
        }
    };

    // 获取状态标签
    const getStatusLabel = (status) => {
        switch (status) {
            case 'published': return '已发布';
            case 'draft': return '草稿';
            case 'archived': return '已归档';
            default: return status;
        }
    };

    // 获取分类名称
    const getCategoryName = (categoryKey) => {
        const category = categories.find(cat => cat.category === categoryKey);
        return category ? `${categoryKey} (${category.count})` : categoryKey;
    };

    // 过滤健康指南
    const filteredGuides = guides.filter(guide => {
        const matchesSearch = guide.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            guide.description.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesStatus = statusFilter === 'all' || guide.status === statusFilter;
        const matchesCategory = categoryFilter === 'all' || guide.category === categoryFilter;
        
        return matchesSearch && matchesStatus && matchesCategory;
    });

    return (
        <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
            {/* 页面标题 */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <HealthIcon sx={{ fontSize: 32, mr: 2, color: 'primary.main' }} />
                <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
                    健康指南管理
                </Typography>
            </Box>

            {/* 操作栏 */}
            <Card sx={{ mb: 3 }}>
                <CardContent>
                    <Grid container spacing={2} alignItems="center">
                        <Grid item xs={12} sm={4}>
                            <TextField
                                fullWidth
                                placeholder="搜索健康指南..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                InputProps={{
                                    startAdornment: (
                                        <InputAdornment position="start">
                                            <SearchIcon />
                                        </InputAdornment>
                                    ),
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={2}>
                            <FormControl fullWidth>
                                <InputLabel>状态</InputLabel>
                                <Select
                                    value={statusFilter}
                                    label="状态"
                                    onChange={(e) => setStatusFilter(e.target.value)}
                                >
                                    <MenuItem value="all">全部</MenuItem>
                                    <MenuItem value="published">已发布</MenuItem>
                                    <MenuItem value="draft">草稿</MenuItem>
                                    <MenuItem value="archived">已归档</MenuItem>
                                </Select>
                            </FormControl>
                        </Grid>
                        <Grid item xs={12} sm={3}>
                            <FormControl fullWidth>
                                <InputLabel>分类</InputLabel>
                                <Select
                                    value={categoryFilter}
                                    label="分类"
                                    onChange={(e) => setCategoryFilter(e.target.value)}
                                >
                                    <MenuItem value="all">全部分类</MenuItem>
                                    {categories.map((category) => (
                                        <MenuItem key={category.category} value={category.category}>
                                            {getCategoryName(category.category)}
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Grid>
                        <Grid item xs={12} sm={3}>
                            <Button
                                variant="contained"
                                startIcon={<AddIcon />}
                                onClick={() => handleOpenDialog()}
                                fullWidth
                                sx={{ height: 56 }}
                            >
                                添加健康指南
                            </Button>
                        </Grid>
                    </Grid>
                </CardContent>
            </Card>

            {/* 健康指南列表 */}
            <Card>
                <CardContent>
                    {loading ? (
                        <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                            <CircularProgress />
                        </Box>
                    ) : (
                        <TableContainer component={Paper} elevation={0}>
                            <Table>
                                <TableHead>
                                    <TableRow>
                                        <TableCell>标题</TableCell>
                                        <TableCell>分类</TableCell>
                                        <TableCell>状态</TableCell>
                                        <TableCell>推荐</TableCell>
                                        <TableCell>创建时间</TableCell>
                                        <TableCell>操作</TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {filteredGuides.length === 0 ? (
                                        <TableRow>
                                            <TableCell colSpan={6} align="center">
                                                <Typography color="text.secondary" sx={{ py: 4 }}>
                                                    {searchTerm || statusFilter !== 'all' || categoryFilter !== 'all' 
                                                        ? '没有找到匹配的健康指南' 
                                                        : '暂无健康指南'
                                                    }
                                                </Typography>
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        filteredGuides.map((guide) => (
                                            <TableRow key={guide.id} hover>
                                                <TableCell>
                                                    <Box>
                                                        <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                                                            {guide.title}
                                                        </Typography>
                                                        <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                                                            {guide.description?.substring(0, 100)}
                                                            {guide.description?.length > 100 && '...'}
                                                        </Typography>
                                                    </Box>
                                                </TableCell>
                                                <TableCell>
                                                    <Chip 
                                                        label={guide.category || '未分类'} 
                                                        size="small" 
                                                        variant="outlined"
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    <Chip 
                                                        label={getStatusLabel(guide.status)} 
                                                        color={getStatusColor(guide.status)}
                                                        size="small"
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    <IconButton
                                                        onClick={() => handleToggleFeatured(guide)}
                                                        color={guide.featured ? 'warning' : 'default'}
                                                    >
                                                        {guide.featured ? <StarIcon /> : <StarBorderIcon />}
                                                    </IconButton>
                                                </TableCell>
                                                <TableCell>
                                                    <Typography variant="body2" color="text.secondary">
                                                        {new Date(guide.created_at).toLocaleDateString('zh-CN')}
                                                    </Typography>
                                                </TableCell>
                                                <TableCell>
                                                    <Box sx={{ display: 'flex', gap: 1 }}>
                                                        <Tooltip title="编辑">
                                                            <IconButton
                                                                size="small"
                                                                onClick={() => handleOpenDialog(guide)}
                                                            >
                                                                <EditIcon />
                                                            </IconButton>
                                                        </Tooltip>
                                                        <Tooltip title={guide.status === 'published' ? '归档' : '发布'}>
                                                            <IconButton
                                                                size="small"
                                                                onClick={() => handleToggleStatus(guide)}
                                                            >
                                                                {guide.status === 'published' ? <ArchiveIcon /> : <UnarchiveIcon />}
                                                            </IconButton>
                                                        </Tooltip>
                                                        <Tooltip title="删除">
                                                            <IconButton
                                                                size="small"
                                                                color="error"
                                                                onClick={() => handleDeleteGuide(guide.id)}
                                                            >
                                                                <DeleteIcon />
                                                            </IconButton>
                                                        </Tooltip>
                                                    </Box>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </TableContainer>
                    )}
                </CardContent>
            </Card>

            {/* 添加/编辑对话框 */}
            <Dialog
                open={dialogOpen}
                onClose={handleCloseDialog}
                maxWidth="md"
                fullWidth
                scroll="paper"
            >
                <DialogTitle>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="h6">
                            {editingGuide ? '编辑健康指南' : '添加健康指南'}
                        </Typography>
                        <Button 
                            variant="outlined" 
                            size="small"
                            onClick={() => setShowPreview(!showPreview)}
                        >
                            {showPreview ? '编辑模式' : '预览模式'}
                        </Button>
                    </Box>
                </DialogTitle>
                <DialogContent dividers>
                    {!showPreview ? (
                        <Grid container spacing={3}>
                            <Grid item xs={12}>
                                <TextField
                                    fullWidth
                                    label="标题 *"
                                    value={formData.title}
                                    onChange={(e) => handleInputChange('title', e.target.value)}
                                    required
                                />
                            </Grid>
                            <Grid item xs={12} sm={6}>
                                <TextField
                                    fullWidth
                                    label="分类"
                                    value={formData.category}
                                    onChange={(e) => handleInputChange('category', e.target.value)}
                                    placeholder="如: cardiovascular, nutrition, mental-health"
                                />
                            </Grid>
                            <Grid item xs={12} sm={6}>
                                <FormControl fullWidth>
                                    <InputLabel>状态 *</InputLabel>
                                    <Select
                                        value={formData.status}
                                        label="状态 *"
                                        onChange={(e) => handleInputChange('status', e.target.value)}
                                    >
                                        <MenuItem value="draft">草稿</MenuItem>
                                        <MenuItem value="published">已发布</MenuItem>
                                        <MenuItem value="archived">已归档</MenuItem>
                                    </Select>
                                </FormControl>
                            </Grid>
                            <Grid item xs={12}>
                                <TextField
                                    fullWidth
                                    label="描述 *"
                                    value={formData.description}
                                    onChange={(e) => handleInputChange('description', e.target.value)}
                                    multiline
                                    rows={3}
                                    required
                                />
                            </Grid>
                            <Grid item xs={12}>
                                <TextField
                                    fullWidth
                                    label="内容 (支持Markdown) *"
                                    value={formData.content}
                                    onChange={(e) => handleInputChange('content', e.target.value)}
                                    multiline
                                    rows={12}
                                    required
                                    placeholder="# 健康指南标题

## 章节一
内容...

## 章节二
- 要点一
- 要点二
"
                                />
                            </Grid>
                            <Grid item xs={12}>
                                <FormControlLabel
                                    control={
                                        <Switch
                                            checked={formData.featured}
                                            onChange={(e) => handleInputChange('featured', e.target.checked)}
                                        />
                                    }
                                    label="设为推荐"
                                />
                            </Grid>
                        </Grid>
                    ) : (
                        <Box>
                            <Typography variant="h6" sx={{ mb: 2 }}>
                                预览效果
                            </Typography>
                            <MarkdownPreview content={formData.content} maxHeight={600} />
                        </Box>
                    )}
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDialog} startIcon={<CancelIcon />}>
                        取消
                    </Button>
                    <Button 
                        onClick={handleSaveGuide} 
                        variant="contained" 
                        startIcon={<SaveIcon />}
                    >
                        {editingGuide ? '更新' : '创建'}
                    </Button>
                </DialogActions>
            </Dialog>

            {/* 提示消息 */}
            <Snackbar
                open={snackbar.open}
                autoHideDuration={6000}
                onClose={handleCloseSnackbar}
                anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
            >
                <Alert 
                    onClose={handleCloseSnackbar} 
                    severity={snackbar.severity}
                    variant="filled"
                >
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </Container>
    );
};

export default HealthGuideAdmin; 