#!/bin/bash

# 快速 Docker Compose 重建脚本
# 执行: docker compose down && docker image prune -a -f && docker compose up -d --build

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 开始快速重建 Docker 服务...${NC}"

# 步骤1: 停止并移除容器
echo -e "${YELLOW}📦 停止并移除容器...${NC}"
docker compose down

# 步骤2: 清理所有未使用的镜像
echo -e "${YELLOW}🧹 清理未使用的镜像...${NC}"
docker image prune -a -f

# 步骤3: 重新构建并启动服务
echo -e "${YELLOW}🔨 重新构建并启动服务...${NC}"
docker compose up -d --build

# 显示服务状态
echo -e "${YELLOW}📊 检查服务状态...${NC}"
docker compose ps

echo -e "${GREEN}✅ 快速重建完成！${NC}" 