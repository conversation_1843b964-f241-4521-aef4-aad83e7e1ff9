# 商品图片说明

## 图片存放规则

### 文件命名
- 格式：`product-{商品ID}.{扩展名}`
- 支持格式：`.jpg`, `.jpeg`, `.png`, `.webp`
- 示例：`product-1.jpg`, `product-2.png`

### 图片规格建议
- **尺寸**：建议 600x400 像素或更高
- **比例**：3:2 或 4:3
- **格式**：JPG（照片）或 PNG（图标/透明背景）
- **大小**：建议小于 500KB

### 当前商品列表
根据数据库中的商品，您需要准备以下图片：

1. `product-1.jpg` - 会员服务相关
2. `product-2.jpg` - 功能医学相关  
3. `product-3.jpg` - 减重管理相关
4. `product-4.jpg` - 健康检查相关
5. `product-5.jpg` - 营养补充相关
6. `product-6.jpg` - 在线咨询相关
7. `product-7.jpg` - 高级会员相关
8. `product-8.jpg` - 功能医学咨询相关
9. `product-9.jpg` - 减重计划相关
10. `product-10.jpg` - 全面体检相关
11. `product-11.jpg` - 营养补充剂相关
12. `product-12.jpg` - 专家咨询相关

### 使用方法
1. 将图片文件放入此目录
2. 运行更新脚本：`node backend/src/scripts/updateProductImages.js`
3. 重新构建前端容器以包含新图片

### 备用方案
如果没有图片，系统会自动显示带有商品名称的占位符图片。 