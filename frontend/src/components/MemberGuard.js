import React from 'react';
import { Box, Alert, AlertT<PERSON>le, Button, Typography, Paper } from '@mui/material';
import { useUserType } from '../hooks/useUserType';
import { useLanguage } from '../context/LanguageContext';
import { useNavigate } from 'react-router-dom';
import LockIcon from '@mui/icons-material/Lock';
import StarIcon from '@mui/icons-material/Star';

const MemberGuard = ({ children, requireMember = true, fallbackMessage = null }) => {
    const { isMember, loading } = useUserType();
    const { t } = useLanguage();
    const navigate = useNavigate();

    if (loading) {
        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                <Typography>加载中...</Typography>
            </Box>
        );
    }

    if (requireMember && !isMember) {
        return (
            <Box sx={{ p: 3 }}>
                <Paper 
                    elevation={3} 
                    sx={{ 
                        p: 4, 
                        textAlign: 'center',
                        background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
                        border: '2px solid #e3f2fd'
                    }}
                >
                    <Box sx={{ mb: 3 }}>
                        <LockIcon sx={{ fontSize: 64, color: 'primary.main', mb: 2 }} />
                        <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 600 }}>
                            会员专享功能
                        </Typography>
                    </Box>
                    
                    <Alert severity="info" sx={{ mb: 3, textAlign: 'left' }}>
                        <AlertTitle sx={{ display: 'flex', alignItems: 'center' }}>
                            <StarIcon sx={{ mr: 1 }} />
                            会员权限需要
                        </AlertTitle>
                        {fallbackMessage || '此功能仅限有效会员使用。请确保您的会员资格处于有效状态。'}
                    </Alert>

                    <Box sx={{ mb: 3 }}>
                        <Typography variant="h6" gutterBottom sx={{ color: 'primary.main' }}>
                            会员专享服务包括：
                        </Typography>
                        <Box component="ul" sx={{ textAlign: 'left', pl: 2 }}>
                            <Typography component="li" variant="body1" sx={{ mb: 1 }}>
                                • 健康指南和专业医疗内容
                            </Typography>
                            <Typography component="li" variant="body1" sx={{ mb: 1 }}>
                                • 健康小贴士和个性化建议
                            </Typography>
                            <Typography component="li" variant="body1" sx={{ mb: 1 }}>
                                • 实验室报告和医疗记录查看
                            </Typography>
                            <Typography component="li" variant="body1" sx={{ mb: 1 }}>
                                • 处方记录和免疫接种历史
                            </Typography>
                            <Typography component="li" variant="body1" sx={{ mb: 1 }}>
                                • 预约记录和医生笔记
                            </Typography>
                            <Typography component="li" variant="body1" sx={{ mb: 1 }}>
                                • 特定医生（如Dr. Miao）的预约服务
                            </Typography>
                        </Box>
                    </Box>

                    <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                        <Button
                            variant="contained"
                            size="large"
                            onClick={() => navigate('/membership')}
                            sx={{ 
                                px: 4, 
                                py: 1.5,
                                background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                                '&:hover': {
                                    background: 'linear-gradient(45deg, #1976D2 30%, #1CB5F3 90%)'
                                }
                            }}
                        >
                            查看会员状态
                        </Button>
                        <Button
                            variant="outlined"
                            size="large"
                            onClick={() => navigate('/renew-membership')}
                            sx={{ px: 4, py: 1.5 }}
                        >
                            续费会员
                        </Button>
                    </Box>
                </Paper>
            </Box>
        );
    }

    return children;
};

export default MemberGuard; 