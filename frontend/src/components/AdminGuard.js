import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useView } from '../context/ViewContext';
import { Box, CircularProgress, Typography } from '@mui/material';

const AdminGuard = ({ children }) => {
    const { loggedInUser, isLoading } = useView();
    const location = useLocation();

    if (isLoading) {
        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
                <CircularProgress />
                <Typography sx={{ ml: 2 }}>Verifying admin access...</Typography>
            </Box>
        );
    }

    if (!loggedInUser) {
        // User not logged in, redirect to login with a return path
        return <Navigate to={`/login?returnTo=${location.pathname}`} replace />;
    }

    if (loggedInUser.role !== 'admin') {
        // User is logged in but not an admin, redirect to profile or an "unauthorized" page
        // For now, redirecting to profile.
        console.warn('AdminGuard: User is not an admin. Redirecting to /profile.');
        return <Navigate to="/profile" replace />;
    }

    // User is an admin, render the protected component
    return children;
};

export default AdminGuard; 