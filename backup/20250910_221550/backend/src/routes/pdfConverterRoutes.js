const express = require('express');
const router = express.Router();
const pdfConverterController = require('../controllers/pdfConverterController');
const authenticateToken = require('../middleware/auth');

// 基础PDF转换路由
router.post('/convert', authenticateToken, pdfConverterController.convertPDF);
router.post('/convert-and-save', authenticateToken, pdfConverterController.convertAndSave);
router.post('/batch-convert', authenticateToken, pdfConverterController.batchConvert);

// 医疗报告专用转换路由
router.post('/medical-report/:demographicNo/:reportType/:reportId', 
    authenticateToken, 
    pdfConverterController.convertMedicalReport
);

module.exports = router; 