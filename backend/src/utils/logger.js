console.log('>>> Loading backend/src/utils/logger.js...'); // <<< DEBUG LOG
const winston = require('winston');

const createLogger = (module) => {
    return winston.createLogger({
        level: process.env.LOG_LEVEL || 'info',
        format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json()
        ),
        defaultMeta: { module },
        transports: [
            new winston.transports.Console({
                format: winston.format.combine(
                    winston.format.colorize(),
                    winston.format.simple()
                )
            }),
            new winston.transports.File({
                filename: 'logs/error.log',
                level: 'error'
            }),
            new winston.transports.File({
                filename: 'logs/combined.log'
            })
        ]
    });
};

module.exports = {
    createLogger
}; 