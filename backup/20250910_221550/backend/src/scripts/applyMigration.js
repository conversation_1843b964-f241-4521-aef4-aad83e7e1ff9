const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
const { createLogger } = require('../utils/logger');

const logger = createLogger('ApplyMigration');

async function applyMigration() {
    // 创建与Docker中数据库的连接
    const pool = mysql.createPool({
        host: 'open-osp-db-1',     // Docker数据库容器名
        port: 3306,
        user: 'root',
        password: 'V4ntNpYiwBI=',  // 从.env文件中获取的密码
        database: 'oscar',         // 数据库名
        waitForConnections: true,
        connectionLimit: 5,
        queueLimit: 0
    });

    try {
        // 读取SQL文件
        const sqlFilePath = path.join(__dirname, '../migrations/create_prescription_explanation_table.sql');
        const sql = fs.readFileSync(sqlFilePath, 'utf8');

        console.log('读取SQL文件成功');

        // 分割SQL语句
        const queries = sql.split(';').filter(query => query.trim().length > 0);

        // 执行每个查询
        for (const query of queries) {
            console.log(`执行查询: ${query.trim()}`);
            await pool.query(query.trim());
            console.log('查询执行成功');
        }

        console.log('迁移成功应用!');
    } catch (error) {
        console.error('执行迁移时发生错误:', error);
    } finally {
        // 关闭连接池
        await pool.end();
        console.log('数据库连接已关闭');
    }
}

// 执行迁移
applyMigration(); 