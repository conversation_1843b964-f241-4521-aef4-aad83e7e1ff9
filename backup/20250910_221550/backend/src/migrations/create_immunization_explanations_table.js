const db = require('../config/database');
const { createLogger } = require('../utils/logger');

const logger = createLogger('ImmunizationExplanationMigration');

/**
 * 创建疫苗接种解释表
 */
async function createImmunizationExplanationsTable() {
    try {
        logger.info('Starting immunization explanations table migration');

        // 检查表是否已存在
        const [tables] = await db.query(`
            SHOW TABLES LIKE 'immunization_explanations';
        `);

        if (tables.length > 0) {
            logger.info('Immunization explanations table already exists');
            return { success: true, message: 'Table already exists' };
        }

        // 创建表
        await db.query(`
            CREATE TABLE immunization_explanations (
                id INT(11) NOT NULL AUTO_INCREMENT,
                immunization_id INT(11) NOT NULL,
                explanation TEXT NOT NULL,
                language VARCHAR(10) NOT NULL DEFAULT 'zh',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                UNIQUE KEY unique_immunization_language (immunization_id, language)
            ) ENGINE=Aria DEFAULT CHARSET=utf8mb4;
        `);

        logger.info('Immunization explanations table created successfully');
        return { success: true, message: 'Table created successfully' };
    } catch (error) {
        logger.error('Error creating immunization explanations table:', error);
        return { success: false, message: error.message };
    }
}

// 执行迁移
(async () => {
    try {
        const result = await createImmunizationExplanationsTable();
        console.log(result);
        process.exit(0);
    } catch (error) {
        console.error('Migration failed:', error);
        process.exit(1);
    }
})(); 