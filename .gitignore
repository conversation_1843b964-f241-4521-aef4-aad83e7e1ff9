# Node modules
/node_modules
/frontend/node_modules
/backend/node_modules
/chatbot-service/node_modules

# Build files
/build
/frontend/build
/dist

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*
/logs
logs/

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 自动生成的文档和内容文件
/backend/documents/health/*.md
/backend/documents/tips/*.md
/backend/documents/articles/*.md
# 保留.gitkeep以确保目录结构存在
!/backend/documents/health/.gitkeep
!/backend/documents/tips/.gitkeep
!/backend/documents/articles/.gitkeep

# YouTube视频功能相关文件（未完成）
/backend/Dockerfile.cron
/backend/src/scripts/auto-fetch-medical-videos.js
/backend/src/scripts/create_youtube_table.sql
/backend/src/scripts/applyYoutubeTableScript.js
/backend/src/scripts/schedule-medical-videos.js
/setup-youtube-feature.sh
/start-auto-youtube.sh

# Ignore operating system generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Ignore node_modules
node_modules/

# Ignore local environment files
.env
.env.*
!.env.example

# Ignore log files
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Ignore build output directories
build/
dist/

# Ignore editor/IDE specific files
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Ignore test reports and coverage
coverage/
.nyc_output/

# Ignore package manager lock files if you prefer not to commit them (though often recommended to commit)
# package-lock.json (if not committing)
# yarn.lock (if not committing)

# Ignore sensitive files / credentials
credentials.json

# !!! IMPORTANT: Ignore the remotely mounted documents directory !!!
remote-documents/

# !!! IMPORTANT: Ignore the local documents directory !!!
local-documents/
