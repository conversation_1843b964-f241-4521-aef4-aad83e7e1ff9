import React, { useState, useEffect } from 'react';
import { API_URL } from '../utils/env';
import { useNavigate } from 'react-router-dom';
import {
    Container,
    Paper,
    Box,
    Typography,
    TextField,
    Button,
    Alert,
    CircularProgress,
    Snackbar,
    Link,
    Grid,
    IconButton,
    Collapse,
    Dialog,
    DialogActions,
    DialogContent,
    DialogContentText,
    DialogTitle
} from '@mui/material';
import { styled } from '@mui/material/styles';
import CloseIcon from '@mui/icons-material/Close';
import axios from 'axios';
import EmailIcon from '@mui/icons-material/Email';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import KeyIcon from '@mui/icons-material/Key';
import { useLanguage } from '../context/LanguageContext';

const StyledPaper = styled(Paper)(({ theme }) => ({
    marginTop: theme.spacing(8),
    padding: theme.spacing(4),
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
}));

const StyledForm = styled('form')(({ theme }) => ({
    width: '100%',
    marginTop: theme.spacing(1),
}));

const StyledButton = styled(Button)(({ theme }) => ({
    margin: theme.spacing(3, 0, 2),
}));

const ForgotPassword = () => {
    const [email, setEmail] = useState('');
    const [resetToken, setResetToken] = useState('');
    const [newPassword, setNewPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [error, setError] = useState('');
    const [message, setMessage] = useState('');
    const [loading, setLoading] = useState(false);
    const [showResetForm, setShowResetForm] = useState(false);
    const [successMessage, setSuccessMessage] = useState('');
    const [showSuccess, setShowSuccess] = useState(false);
    const [codeSent, setCodeSent] = useState(false);
    const [verificationCode, setVerificationCode] = useState('');
    const [isVerified, setIsVerified] = useState(false);
    const [sendingCode, setSendingCode] = useState(false);
    const [verifyingCode, setVerifyingCode] = useState(false);
    const [showResetSuccess, setShowResetSuccess] = useState(false);
    const [showVerificationSent, setShowVerificationSent] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [showSuccessDialog, setShowSuccessDialog] = useState(false);
    const [redirectCountdown, setRedirectCountdown] = useState(5);

    const navigate = useNavigate();

    useEffect(() => {
        let timer;
        if (showSuccessDialog && redirectCountdown > 0) {
            timer = setTimeout(() => {
                setRedirectCountdown(redirectCountdown - 1);
            }, 1000);
        } else if (showSuccessDialog && redirectCountdown === 0) {
            navigate('/login');
        }
        return () => clearTimeout(timer);
    }, [showSuccessDialog, redirectCountdown, navigate]);

    const sendVerificationCode = async () => {
        if (!email || !email.includes('@')) {
            setError('Please enter a valid email address');
            return;
        }

        setSendingCode(true);
        setError('');

        try {
            const apiUrl = API_URL || 'https://app-backend.mmcwellness.ca';
            const response = await fetch(`${apiUrl}/api/auth/send-verification-code`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email }),
            });

            const data = await response.json();

            if (response.ok) {
                setCodeSent(true);
                setSuccessMessage('Verification code sent to your email');
                setShowSuccess(true);
                return true;
            } else {
                throw new Error(data.message || 'Failed to send verification code');
            }
        } catch (err) {
            console.error('Send verification code error:', err);
            setError(`Error: ${err.message}`);
            return false;
        } finally {
            setSendingCode(false);
        }
    };

    const verifyEmailCode = async () => {
        if (!verificationCode) {
            setError('Please enter the verification code');
            return;
        }

        setVerifyingCode(true);
        setError('');

        try {
            const apiUrl = API_URL || 'https://app-backend.mmcwellness.ca';
            const response = await fetch(`${apiUrl}/api/auth/verify-email-code`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    email: email,
                    code: verificationCode
                }),
            });

            const data = await response.json();

            if (response.ok) {
                setIsVerified(true);
                setSuccessMessage('Email verified successfully');
                setShowSuccess(true);

                // 验证成功后直接显示密码重置表单，不需要请求重置邮件
                setShowResetForm(true);
                setMessage('Email verified successfully. You can now reset your password.');
                return true;
            } else {
                throw new Error(data.message || 'Invalid verification code');
            }
        } catch (err) {
            console.error('Verify code error:', err);
            setError(`Error: ${err.message}`);
            return false;
        } finally {
            setVerifyingCode(false);
        }
    };

    const handleSubmit = async (event) => {
        event.preventDefault();
        setIsSubmitting(true);
        setError('');
        setSuccessMessage('');

        // If we're at the password reset stage, handle password reset
        if (showResetForm) {
            if (newPassword !== confirmPassword) {
                setError('Passwords do not match.');
                setIsSubmitting(false);
                return;
            }

            try {
                // 使用直接重置密码的端点
                const response = await axios.post(`${API_URL}/api/auth/reset-password-direct`, {
                    email,
                    newPassword: newPassword
                });

                if (response.data.success) {
                    setSuccessMessage('Password has been reset successfully. You can now login with your new password.');
                    setShowResetSuccess(true); // 显示重置成功提示
                    setShowSuccessDialog(true); // 显示成功对话框
                    setRedirectCountdown(5); // 设置5秒倒计时
                } else {
                    setError(response.data.message || 'Failed to reset password. Please try again.');
                }
            } catch (error) {
                console.error('Reset password error:', error);
                setError(error.response?.data?.message || 'An error occurred while resetting password.');
            }
        } else {
            // Email verification step
            try {
                // 首先检查邮箱是否存在并已验证
                const response = await axios.post(`${API_URL}/api/auth/check-email`, { email });

                if (response.data.exists && response.data.verified) {
                    // 如果邮箱存在且已验证，直接显示密码重置表单
                    setShowResetForm(true);
                } else if (response.data.exists && !response.data.verified) {
                    // 如果邮箱存在但未验证，发送验证邮件
                    await axios.post(`${API_URL}/api/auth/send-verification`, { email });
                    setShowVerificationSent(true);
                } else {
                    // 邮箱不存在
                    setError('Email not found. Please register first.');
                }
            } catch (error) {
                console.error('Check email error:', error);
                setError(error.response?.data?.message || 'An error occurred while checking email.');
            }
        }

        setIsSubmitting(false);
    };

    return (
        <Container component="main" maxWidth="xs">
            <StyledPaper elevation={3}>
                <Typography component="h1" variant="h5">
                    Password Recovery
                </Typography>

                {error && (
                    <Alert
                        severity="error"
                        sx={{ mt: 2, width: '100%' }}
                        action={
                            <IconButton
                                color="inherit"
                                size="small"
                                onClick={() => setError('')}
                            >
                                <CloseIcon fontSize="inherit" />
                            </IconButton>
                        }
                    >
                        {error}
                    </Alert>
                )}

                {message && (
                    <Alert
                        severity="info"
                        sx={{ mt: 2, width: '100%' }}
                        action={
                            <IconButton
                                color="inherit"
                                size="small"
                                onClick={() => setMessage('')}
                            >
                                <CloseIcon fontSize="inherit" />
                            </IconButton>
                        }
                    >
                        {message}
                    </Alert>
                )}

                {!showResetForm ? (
                    <StyledForm onSubmit={handleSubmit}>
                        <TextField
                            variant="outlined"
                            margin="normal"
                            required
                            fullWidth
                            id="email"
                            label="Email Address"
                            name="email"
                            autoComplete="email"
                            autoFocus
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                        />

                        {codeSent && !isVerified && (
                            <TextField
                                variant="outlined"
                                margin="normal"
                                required
                                fullWidth
                                id="verificationCode"
                                label="Verification Code"
                                name="verificationCode"
                                value={verificationCode}
                                onChange={(e) => setVerificationCode(e.target.value)}
                                helperText="Enter the verification code sent to your email"
                            />
                        )}

                        {!codeSent ? (
                            <StyledButton
                                fullWidth
                                variant="contained"
                                color="primary"
                                onClick={sendVerificationCode}
                                disabled={loading || sendingCode}
                            >
                                {sendingCode ? <CircularProgress size={24} /> : 'Send Verification Code'}
                            </StyledButton>
                        ) : !isVerified ? (
                            <StyledButton
                                fullWidth
                                variant="contained"
                                color="primary"
                                onClick={verifyEmailCode}
                                disabled={loading || verifyingCode}
                            >
                                {verifyingCode ? <CircularProgress size={24} /> : 'Verify Email'}
                            </StyledButton>
                        ) : null}
                    </StyledForm>
                ) : (
                    <StyledForm onSubmit={handleSubmit}>
                        <TextField
                            variant="outlined"
                            margin="normal"
                            required
                            fullWidth
                            name="newPassword"
                            label="New Password"
                            type="password"
                            id="newPassword"
                            value={newPassword}
                            onChange={(e) => setNewPassword(e.target.value)}
                        />

                        <TextField
                            variant="outlined"
                            margin="normal"
                            required
                            fullWidth
                            name="confirmPassword"
                            label="Confirm New Password"
                            type="password"
                            id="confirmPassword"
                            value={confirmPassword}
                            onChange={(e) => setConfirmPassword(e.target.value)}
                        />

                        <StyledButton
                            type="submit"
                            fullWidth
                            variant="contained"
                            color="primary"
                            disabled={loading}
                        >
                            {loading ? <CircularProgress size={24} /> : 'Reset Password'}
                        </StyledButton>
                    </StyledForm>
                )}

                <Box mt={3}>
                    <Link href="/login" variant="body2">
                        Return to Login
                    </Link>
                </Box>
            </StyledPaper>

            <Snackbar
                open={showSuccess}
                autoHideDuration={6000}
                onClose={() => setShowSuccess(false)}
                message={successMessage}
            />

            <Collapse in={showResetSuccess}>
                <Alert
                    severity="success"
                    sx={{ mt: 2, width: '100%' }}
                >
                    {successMessage}
                </Alert>
            </Collapse>

            <Dialog
                open={showSuccessDialog}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <DialogTitle id="alert-dialog-title">
                    {"Password Reset Successful"}
                </DialogTitle>
                <DialogContent>
                    <DialogContentText id="alert-dialog-description">
                        Your password has been reset successfully. You will be redirected to the login page in {redirectCountdown} seconds.
                    </DialogContentText>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => navigate('/login')} color="primary" autoFocus>
                        Login Now
                    </Button>
                </DialogActions>
            </Dialog>
        </Container>
    );
};

export default ForgotPassword; 