const express = require('express');
const router = express.Router();
const {
    getHealthGuides,
    getHealthGuideById,
    createHealthGuide,
    updateHealthGuide,
    deleteHealthGuide,
    getHealthGuideCategories,
    getHealthGuidesAdmin
} = require('../controllers/healthGuideController');
const auth = require('../middleware/auth');
const { requireMember, requireAdmin } = require('../middleware/userTypeAuth');

// Member-only routes - 需要有效membership才能访问健康指南
router.get('/', auth, requireMember, getHealthGuides);
router.get('/categories', auth, requireMember, getHealthGuideCategories);
router.get('/:id', auth, requireMember, getHealthGuideById);

// 管理员路由 - 需要认证和管理员权限
router.get('/admin/all', auth, requireAdmin, getHealthGuidesAdmin);
router.post('/', auth, requireAdmin, createHealthGuide);
router.put('/:id', auth, requireAdmin, updateHealthGuide);
router.delete('/:id', auth, requireAdmin, deleteHealthGuide);

module.exports = router; 