const pool = require('../config/database');

async function checkUserAuthTable() {
    try {
        console.log('Checking user_auth table structure...');
        
        // 检查表结构
        const [structure] = await pool.query("DESCRIBE user_auth");
        console.log('user_auth table structure:');
        console.table(structure);
        
        // 检查是否有数据
        const [count] = await pool.query("SELECT COUNT(*) as count FROM user_auth");
        console.log(`\nTotal records in user_auth: ${count[0].count}`);
        
        // 检查ID字段的类型
        const idField = structure.find(field => field.Field === 'id');
        if (idField) {
            console.log(`\nID field type: ${idField.Type}`);
            console.log(`ID field key: ${idField.Key}`);
        }
        
    } catch (error) {
        console.error('Error checking user_auth table:', error);
    } finally {
        await pool.end();
    }
}

checkUserAuthTable(); 