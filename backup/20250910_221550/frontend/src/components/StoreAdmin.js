import React, { useState, useEffect } from 'react';
import {
    Container,
    Typography,
    Box,
    Card,
    CardContent,
    CardActions,
    Button,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    TextField,
    Switch,
    FormControlLabel,
    Grid,
    Chip,
    Alert,
    Snackbar,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    IconButton,
    Tooltip,
    Select,
    MenuItem,
    FormControl,
    InputLabel,
    CircularProgress
} from '@mui/material';
import {
    Add as AddIcon,
    Edit as EditIcon,
    Delete as DeleteIcon,
    Visibility as VisibilityIcon,
    VisibilityOff as VisibilityOffIcon,
    Store as StoreIcon,
    Save as SaveIcon,
    Cancel as CancelIcon,
    CloudUpload as CloudUploadIcon,
    Image as ImageIcon
} from '@mui/icons-material';
import { useLanguage } from '../context/LanguageContext';
import * as storeService from '../services/storeService';

const StoreAdmin = () => {
    const { t, language } = useLanguage();
    
    // 状态管理
    const [products, setProducts] = useState([]);
    const [categories, setCategories] = useState([]);
    const [loading, setLoading] = useState(true);
    const [dialogOpen, setDialogOpen] = useState(false);
    const [editingProduct, setEditingProduct] = useState(null);
    const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
    
    // 图片上传相关状态
    const [imageFile, setImageFile] = useState(null);
    const [imagePreview, setImagePreview] = useState('');
    const [uploading, setUploading] = useState(false);
    
    // 表单状态
    const [formData, setFormData] = useState({
        name_en: '',
        name_zh: '',
        description_en: '',
        description_zh: '',
        price: '',
        category_id: '',
        image_url: '',
        features_en: '',
        features_zh: '',
        is_subscription: false,
        duration_days: '',
        status: 'active'
    });

    // 获取商品和分类数据
    useEffect(() => {
        fetchData();
    }, []);

    const fetchData = async () => {
        try {
            setLoading(true);
            const [productsResponse, categoriesResponse] = await Promise.all([
                storeService.getProducts(),
                storeService.getCategories()
            ]);
            
            if (productsResponse.success) {
                setProducts(productsResponse.products || []);
            }
            
            if (categoriesResponse.success) {
                setCategories(categoriesResponse.categories || []);
            }
        } catch (error) {
            console.error('Error fetching data:', error);
            showSnackbar('获取数据失败', 'error');
        } finally {
            setLoading(false);
        }
    };

    // 显示提示消息
    const showSnackbar = (message, severity = 'success') => {
        setSnackbar({ open: true, message, severity });
    };

    // 关闭提示消息
    const handleCloseSnackbar = () => {
        setSnackbar({ ...snackbar, open: false });
    };

    // 打开添加/编辑对话框
    const handleOpenDialog = (product = null) => {
        if (product) {
            setEditingProduct(product);
            setFormData({
                name_en: product.name_en || '',
                name_zh: product.name_zh || '',
                description_en: product.description_en || '',
                description_zh: product.description_zh || '',
                price: product.price || '',
                category_id: product.category_id || '',
                image_url: product.image_url || '',
                features_en: product.features_en || '',
                features_zh: product.features_zh || '',
                is_subscription: product.is_subscription || false,
                duration_days: product.duration_days || '',
                status: product.status || 'active'
            });
            // 设置现有图片预览
            setImagePreview(product.image_url || '');
        } else {
            setEditingProduct(null);
            setFormData({
                name_en: '',
                name_zh: '',
                description_en: '',
                description_zh: '',
                price: '',
                category_id: '',
                image_url: '',
                features_en: '',
                features_zh: '',
                is_subscription: false,
                duration_days: '',
                status: 'active'
            });
            setImagePreview('');
        }
        setImageFile(null);
        setDialogOpen(true);
    };

    // 关闭对话框
    const handleCloseDialog = () => {
        setDialogOpen(false);
        setEditingProduct(null);
        setImageFile(null);
        setImagePreview('');
    };

    // 处理表单输入
    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    // 处理图片文件选择
    const handleImageFileChange = (event) => {
        const file = event.target.files[0];
        if (file) {
            // 验证文件类型
            if (!file.type.startsWith('image/')) {
                showSnackbar('请选择图片文件', 'error');
                return;
            }
            
            // 验证文件大小 (5MB)
            if (file.size > 5 * 1024 * 1024) {
                showSnackbar('图片文件大小不能超过5MB', 'error');
                return;
            }

            setImageFile(file);
            
            // 创建预览
            const reader = new FileReader();
            reader.onload = (e) => {
                setImagePreview(e.target.result);
            };
            reader.readAsDataURL(file);
        }
    };

    // 上传图片
    const handleUploadImage = async () => {
        if (!imageFile) {
            showSnackbar('请先选择图片文件', 'error');
            return;
        }

        try {
            setUploading(true);
            const response = await storeService.uploadProductImage(imageFile);
            
            if (response.success) {
                // 更新表单数据中的图片URL
                setFormData(prev => ({
                    ...prev,
                    image_url: response.imageUrl
                }));
                showSnackbar('图片上传成功');
                setImageFile(null);
            } else {
                showSnackbar(response.message || '图片上传失败', 'error');
            }
        } catch (error) {
            console.error('Error uploading image:', error);
            showSnackbar('图片上传失败', 'error');
        } finally {
            setUploading(false);
        }
    };

    // 删除图片
    const handleRemoveImage = () => {
        setImageFile(null);
        setImagePreview('');
        setFormData(prev => ({
            ...prev,
            image_url: ''
        }));
    };

    // 保存商品
    const handleSaveProduct = async () => {
        try {
            if (editingProduct) {
                // 更新商品
                const response = await storeService.updateProduct(editingProduct.id, formData);
                if (response.success) {
                    showSnackbar('商品更新成功');
                    fetchData();
                } else {
                    showSnackbar(response.message || '更新失败', 'error');
                }
            } else {
                // 创建新商品
                const response = await storeService.createProduct(formData);
                if (response.success) {
                    showSnackbar('商品创建成功');
                    fetchData();
                } else {
                    showSnackbar(response.message || '创建失败', 'error');
                }
            }
            handleCloseDialog();
        } catch (error) {
            console.error('Error saving product:', error);
            showSnackbar('保存失败', 'error');
        }
    };

    // 切换商品状态
    const handleToggleStatus = async (product) => {
        try {
            const newStatus = product.status === 'active' ? 'inactive' : 'active';
            const response = await storeService.updateProduct(product.id, { status: newStatus });
            
            if (response.success) {
                showSnackbar(`商品已${newStatus === 'active' ? '上架' : '下架'}`);
                fetchData();
            } else {
                showSnackbar(response.message || '状态更新失败', 'error');
            }
        } catch (error) {
            console.error('Error toggling status:', error);
            showSnackbar('状态更新失败', 'error');
        }
    };

    // 删除商品
    const handleDeleteProduct = async (productId) => {
        if (window.confirm('确定要删除这个商品吗？')) {
            try {
                const response = await storeService.deleteProduct(productId);
                if (response.success) {
                    showSnackbar('商品删除成功');
                    fetchData();
                } else {
                    showSnackbar(response.message || '删除失败', 'error');
                }
            } catch (error) {
                console.error('Error deleting product:', error);
                showSnackbar('删除失败', 'error');
            }
        }
    };

    // 格式化价格
    const formatPrice = (price) => {
        return new Intl.NumberFormat('en-CA', {
            style: 'currency',
            currency: 'CAD'
        }).format(price);
    };

    // 获取分类名称
    const getCategoryName = (categoryId) => {
        const category = categories.find(cat => cat.id === categoryId);
        return category ? category[`name_${language}`] : '未知分类';
    };

    if (loading) {
        return (
            <Container maxWidth="lg" sx={{ mt: 4, mb: 4, textAlign: 'center' }}>
                <CircularProgress />
                <Typography variant="h6" sx={{ mt: 2 }}>
                    加载中...
                </Typography>
            </Container>
        );
    }

    return (
        <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
            {/* 页面标题 */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <StoreIcon sx={{ mr: 2, fontSize: 40, color: 'primary.main' }} />
                    <Typography variant="h4" component="h1">
                        商城管理
                    </Typography>
                </Box>
                
                <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => handleOpenDialog()}
                >
                    添加商品
                </Button>
            </Box>

            {/* 商品列表 */}
            <TableContainer component={Paper}>
                <Table>
                    <TableHead>
                        <TableRow>
                            <TableCell>ID</TableCell>
                            <TableCell>商品名称</TableCell>
                            <TableCell>分类</TableCell>
                            <TableCell>价格</TableCell>
                            <TableCell>状态</TableCell>
                            <TableCell>订阅服务</TableCell>
                            <TableCell align="center">操作</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {products.map((product) => (
                            <TableRow key={product.id}>
                                <TableCell>{product.id}</TableCell>
                                <TableCell>
                                    <Box>
                                        <Typography variant="body1" fontWeight="bold">
                                            {product[`name_${language}`]}
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary">
                                            {product[`description_${language}`]?.substring(0, 50)}...
                                        </Typography>
                                    </Box>
                                </TableCell>
                                <TableCell>{getCategoryName(product.category_id)}</TableCell>
                                <TableCell>{formatPrice(product.price)}</TableCell>
                                <TableCell>
                                    <Chip
                                        label={product.status === 'active' ? '已上架' : '已下架'}
                                        color={product.status === 'active' ? 'success' : 'default'}
                                        size="small"
                                    />
                                </TableCell>
                                <TableCell>
                                    {product.is_subscription ? (
                                        <Chip label="是" color="primary" size="small" />
                                    ) : (
                                        <Chip label="否" variant="outlined" size="small" />
                                    )}
                                </TableCell>
                                <TableCell align="center">
                                    <Tooltip title="编辑">
                                        <IconButton
                                            size="small"
                                            onClick={() => handleOpenDialog(product)}
                                        >
                                            <EditIcon />
                                        </IconButton>
                                    </Tooltip>
                                    
                                    <Tooltip title={product.status === 'active' ? '下架' : '上架'}>
                                        <IconButton
                                            size="small"
                                            onClick={() => handleToggleStatus(product)}
                                            color={product.status === 'active' ? 'warning' : 'success'}
                                        >
                                            {product.status === 'active' ? <VisibilityOffIcon /> : <VisibilityIcon />}
                                        </IconButton>
                                    </Tooltip>
                                    
                                    <Tooltip title="删除">
                                        <IconButton
                                            size="small"
                                            onClick={() => handleDeleteProduct(product.id)}
                                            color="error"
                                        >
                                            <DeleteIcon />
                                        </IconButton>
                                    </Tooltip>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>

            {/* 添加/编辑对话框 */}
            <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
                <DialogTitle>
                    {editingProduct ? '编辑商品' : '添加商品'}
                </DialogTitle>
                <DialogContent>
                    <Grid container spacing={2} sx={{ mt: 1 }}>
                        <Grid item xs={12} sm={6}>
                            <TextField
                                fullWidth
                                label="英文名称"
                                value={formData.name_en}
                                onChange={(e) => handleInputChange('name_en', e.target.value)}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <TextField
                                fullWidth
                                label="中文名称"
                                value={formData.name_zh}
                                onChange={(e) => handleInputChange('name_zh', e.target.value)}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <TextField
                                fullWidth
                                label="英文描述"
                                multiline
                                rows={3}
                                value={formData.description_en}
                                onChange={(e) => handleInputChange('description_en', e.target.value)}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <TextField
                                fullWidth
                                label="中文描述"
                                multiline
                                rows={3}
                                value={formData.description_zh}
                                onChange={(e) => handleInputChange('description_zh', e.target.value)}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <TextField
                                fullWidth
                                label="价格 (CAD)"
                                type="number"
                                value={formData.price}
                                onChange={(e) => handleInputChange('price', e.target.value)}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <FormControl fullWidth>
                                <InputLabel>分类</InputLabel>
                                <Select
                                    value={formData.category_id}
                                    onChange={(e) => handleInputChange('category_id', e.target.value)}
                                    label="分类"
                                >
                                    {categories.map((category) => (
                                        <MenuItem key={category.id} value={category.id}>
                                            {category[`name_${language}`]}
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Grid>
                        <Grid item xs={12}>
                            <TextField
                                fullWidth
                                label="图片URL"
                                value={formData.image_url}
                                onChange={(e) => handleInputChange('image_url', e.target.value)}
                                placeholder="/assets/images/products/product-1.jpg"
                            />
                        </Grid>
                        
                        {/* 图片上传区域 */}
                        <Grid item xs={12}>
                            <Box sx={{ border: '2px dashed #ccc', borderRadius: 2, p: 3, textAlign: 'center' }}>
                                <Typography variant="h6" gutterBottom>
                                    商品图片
                                </Typography>
                                
                                {/* 图片预览 */}
                                {imagePreview && (
                                    <Box sx={{ mb: 2 }}>
                                        <img
                                            src={imagePreview.startsWith('data:') ? imagePreview : `${process.env.REACT_APP_API_URL || 'https://app-backend.mmcwellness.ca'}${imagePreview}`}
                                            alt="预览"
                                            style={{
                                                maxWidth: '200px',
                                                maxHeight: '200px',
                                                objectFit: 'cover',
                                                borderRadius: '8px'
                                            }}
                                        />
                                        <Box sx={{ mt: 1 }}>
                                            <Button
                                                size="small"
                                                color="error"
                                                onClick={handleRemoveImage}
                                                startIcon={<DeleteIcon />}
                                            >
                                                删除图片
                                            </Button>
                                        </Box>
                                    </Box>
                                )}
                                
                                {/* 文件选择 */}
                                <input
                                    accept="image/*"
                                    style={{ display: 'none' }}
                                    id="image-upload-input"
                                    type="file"
                                    onChange={handleImageFileChange}
                                />
                                <label htmlFor="image-upload-input">
                                    <Button
                                        variant="outlined"
                                        component="span"
                                        startIcon={<ImageIcon />}
                                        sx={{ mr: 2 }}
                                    >
                                        选择图片
                                    </Button>
                                </label>
                                
                                {/* 上传按钮 */}
                                {imageFile && (
                                    <Button
                                        variant="contained"
                                        onClick={handleUploadImage}
                                        disabled={uploading}
                                        startIcon={uploading ? <CircularProgress size={20} /> : <CloudUploadIcon />}
                                    >
                                        {uploading ? '上传中...' : '上传图片'}
                                    </Button>
                                )}
                                
                                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                                    支持 JPG、PNG、GIF 格式，文件大小不超过 5MB
                                </Typography>
                            </Box>
                        </Grid>
                        
                        <Grid item xs={12} sm={6}>
                            <TextField
                                fullWidth
                                label="英文功能特性"
                                multiline
                                rows={3}
                                value={formData.features_en}
                                onChange={(e) => handleInputChange('features_en', e.target.value)}
                                placeholder="每行一个特性"
                            />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <TextField
                                fullWidth
                                label="中文功能特性"
                                multiline
                                rows={3}
                                value={formData.features_zh}
                                onChange={(e) => handleInputChange('features_zh', e.target.value)}
                                placeholder="每行一个特性"
                            />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <FormControlLabel
                                control={
                                    <Switch
                                        checked={formData.is_subscription}
                                        onChange={(e) => handleInputChange('is_subscription', e.target.checked)}
                                    />
                                }
                                label="订阅服务"
                            />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <TextField
                                fullWidth
                                label="服务天数"
                                type="number"
                                value={formData.duration_days}
                                onChange={(e) => handleInputChange('duration_days', e.target.value)}
                                disabled={!formData.is_subscription}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <FormControl fullWidth>
                                <InputLabel>状态</InputLabel>
                                <Select
                                    value={formData.status}
                                    onChange={(e) => handleInputChange('status', e.target.value)}
                                    label="状态"
                                >
                                    <MenuItem value="active">上架</MenuItem>
                                    <MenuItem value="inactive">下架</MenuItem>
                                </Select>
                            </FormControl>
                        </Grid>
                    </Grid>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDialog} startIcon={<CancelIcon />}>
                        取消
                    </Button>
                    <Button onClick={handleSaveProduct} variant="contained" startIcon={<SaveIcon />}>
                        保存
                    </Button>
                </DialogActions>
            </Dialog>

            {/* 提示消息 */}
            <Snackbar
                open={snackbar.open}
                autoHideDuration={6000}
                onClose={handleCloseSnackbar}
            >
                <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </Container>
    );
};

export default StoreAdmin; 