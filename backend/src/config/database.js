console.log('>>> Loading backend/src/config/database.js...'); // <<< DEBUG LOG
const mysql = require('mysql2/promise');
require('dotenv').config();

// 主服务器连接池 - 用于所有数据库操作
console.log('>>> 配置主服务器连接池，主机地址:', process.env.MAIN_HOST);
const pool = mysql.createPool({
    host: process.env.MAIN_HOST, // 使用主服务器IP
    port: process.env.MAIN_DB_PORT || 3306,
    user: process.env.MAIN_DB_USER,
    password: process.env.MAIN_DB_PASSWORD,
    database: process.env.MAIN_DB_NAME || 'oscar',
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0,
    // 使用系统时区配置，避免警告
    // timezone: 'America/Vancouver'
});

// 本地（备用）服务器连接池 - 在主服务器不可用时使用
const backupPool = mysql.createPool({
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    waitForConnections: true,
    connectionLimit: 5, // 减少连接数，因为这是备用
    queueLimit: 0,
    // 使用系统时区配置，避免警告
    // timezone: 'America/Vancouver'
});

// 测试并记录连接配置
const testConfigs = async () => {
    try {
        // 测试主服务器连接时区
        const [mainResult] = await pool.query('SELECT NOW()');
        console.log('主服务器当前时间:', mainResult[0]['NOW()']);

        // 测试本地连接时区
        const [localResult] = await backupPool.query('SELECT NOW()');
        console.log('本地数据库当前时间:', localResult[0]['NOW()']);
    } catch (err) {
        console.error('测试数据库时区配置失败:', err.message);
    }
};

// 执行测试
testConfigs();

// 保持原有导出方式 - 现在pool直接指向主服务器
module.exports = pool;

// 导出备用池，以便需要时使用
module.exports.backupPool = backupPool; 