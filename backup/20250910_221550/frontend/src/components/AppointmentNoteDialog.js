import React, { useState, useEffect } from 'react';
import {
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Button,
    Typography,
    Box,
    Divider,
    Paper,
    CircularProgress,
    Alert,
    Tabs,
    Tab,
    ToggleButton,
    ToggleButtonGroup
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { alpha } from '@mui/material/styles';
import EventNoteIcon from '@mui/icons-material/EventNote';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import DescriptionIcon from '@mui/icons-material/Description';
import TranslateIcon from '@mui/icons-material/Translate';
import { useLanguage } from '../context/LanguageContext';
import { format } from 'date-fns';
import AIDisclaimer from './AIDisclaimer';

// 样式化内容区块
const ContentSection = styled(Box)(({ theme }) => ({
    marginBottom: theme.spacing(3),
    padding: theme.spacing(2),
    borderRadius: theme.shape.borderRadius,
    backgroundColor: theme.palette.background.paper,
    boxShadow: theme.shadows[1],
}));

// 笔记内容
const NoteContent = styled(Box)(({ theme }) => ({
    whiteSpace: 'pre-wrap',
    fontFamily: theme.typography.fontFamily,
    fontSize: '0.95rem',
    lineHeight: 1.6,
    padding: theme.spacing(2.5),
    backgroundColor: theme.palette.mode === 'dark'
        ? theme.palette.background.paper
        : theme.palette.grey[50],
    borderRadius: theme.shape.borderRadius,
    border: `1px solid ${theme.palette.divider}`,
    maxHeight: '350px',
    overflow: 'auto',
    '& .highlight': {
        backgroundColor: theme.palette.mode === 'dark'
            ? alpha(theme.palette.primary.main, 0.15)
            : alpha(theme.palette.primary.main, 0.1),
        padding: '0 4px',
        borderRadius: '3px',
    },
    '& p': {
        marginTop: theme.spacing(1),
        marginBottom: theme.spacing(1),
    }
}));

// AI摘要内容
const SummaryContent = styled(Box)(({ theme }) => ({
    whiteSpace: 'pre-wrap',
    fontFamily: theme.typography.fontFamily,
    fontSize: '0.95rem',
    lineHeight: 1.6,
    padding: theme.spacing(2.5),
    backgroundColor: theme.palette.mode === 'dark'
        ? alpha(theme.palette.primary.main, 0.08)
        : alpha(theme.palette.primary.main, 0.05),
    borderRadius: theme.shape.borderRadius,
    border: `1px solid ${theme.palette.mode === 'dark'
        ? alpha(theme.palette.primary.main, 0.2)
        : alpha(theme.palette.primary.main, 0.15)}`,
    maxHeight: '350px',
    overflow: 'auto',
    '& p': {
        marginTop: theme.spacing(1),
        marginBottom: theme.spacing(1),
    }
}));

function TabPanel(props) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`note-tabpanel-${index}`}
            aria-labelledby={`note-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ pt: 2 }}>
                    {children}
                </Box>
            )}
        </div>
    );
}

// 处理医疗笔记文本格式，将原始文本转换为更易读的格式
const formatNoteContent = (content) => {
    if (!content) return '';

    // 替换常见的医疗记录分隔符为HTML元素
    let formattedContent = content
        // 处理日期标记 [日期] 为高亮显示
        .replace(/\[(.*?)\]/g, '<span class="highlight">[$1]</span>')
        // 处理多行，确保段落之间有适当的间距
        .replace(/\n{2,}/g, '</p><p>')
        // 处理单行换行
        .replace(/\n(?!\n)/g, '<br />')
        // 处理键值对格式
        .replace(/([\w\s]+):\s*/g, '<strong>$1:</strong> ')
        // 处理医学缩写词（如Ph, AP）为粗体
        .replace(/\b(Ph|AP|A\/P|S|O|Hx|Rx|Dx|Tx)\b[:]/g, '<strong>$1:</strong>');

    return `<p>${formattedContent}</p>`;
};

const AppointmentNoteDialog = ({ open, onClose, appointmentId, appointmentData }) => {
    const { t, language: userLanguage } = useLanguage();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [noteData, setNoteData] = useState(null);
    const [aiSummary, setAiSummary] = useState(null);
    const [tabValue, setTabValue] = useState(1);
    const [summaryLanguage, setSummaryLanguage] = useState(userLanguage || 'en');
    const [generatingSummary, setGeneratingSummary] = useState(false);

    const handleTabChange = (event, newValue) => {
        setTabValue(newValue);
    };

    const handleLanguageChange = async (event, newLanguage) => {
        if (newLanguage && newLanguage !== summaryLanguage) {
            setSummaryLanguage(newLanguage);
            await fetchSummary(newLanguage);
        }
    };

    // 单独的获取AI总结的函数
    const fetchSummary = async (lang = summaryLanguage) => {
        if (!appointmentId) return;

        setGeneratingSummary(true);
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                throw new Error('Authentication required');
            }

            // 添加语言参数到请求
            const response = await fetch(`${process.env.REACT_APP_API_URL}/api/appointments/note/summary/${appointmentId}?lang=${lang}`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || 'Failed to fetch AI summary');
            }

            const data = await response.json();

            if (data.success && data.summary) {
                setAiSummary(data.summary);
                // 切换到AI总结标签
                setTabValue(1);
            } else {
                throw new Error(data.summaryError || 'Failed to generate AI summary');
            }
        } catch (err) {
            console.error('Error fetching AI summary:', err);
            setError(err.message);
        } finally {
            setGeneratingSummary(false);
        }
    };

    // 获取笔记数据和AI总结
    useEffect(() => {
        const fetchNoteData = async () => {
            if (!open || !appointmentId) return;

            setLoading(true);
            setError(null);

            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    throw new Error('Authentication required');
                }

                // 请求预约笔记和AI总结
                const response = await fetch(`${process.env.REACT_APP_API_URL}/api/appointments/note/summary/${appointmentId}?lang=${summaryLanguage}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.message || 'Failed to fetch appointment note');
                }

                const data = await response.json();

                if (data.success) {
                    setNoteData(data.note);
                    setAiSummary(data.summary);
                    // 如果有AI总结，默认显示AI总结标签
                    if (data.summary) {
                        setTabValue(1);
                    }
                } else {
                    throw new Error(data.message || 'Failed to load note data');
                }
            } catch (err) {
                console.error('Error fetching appointment note:', err);
                setError(err.message || 'Failed to load appointment note');
            } finally {
                setLoading(false);
            }
        };

        fetchNoteData();
    }, [open, appointmentId, summaryLanguage]);

    // 格式化日期
    const formatDate = (dateString) => {
        if (!dateString) return '';
        const options = { year: 'numeric', month: 'long', day: 'numeric' };
        return new Date(dateString).toLocaleDateString(undefined, options);
    };

    return (
        <Dialog
            open={open}
            onClose={onClose}
            maxWidth="md"
            fullWidth
            aria-labelledby="appointment-note-dialog-title"
        >
            <DialogTitle id="appointment-note-dialog-title" sx={{ display: 'flex', alignItems: 'center' }}>
                <EventNoteIcon sx={{ mr: 1 }} />
                {t('appointment_note')}
            </DialogTitle>

            <DialogContent dividers>
                {loading ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                        <CircularProgress />
                    </Box>
                ) : error ? (
                    <Alert severity="error" sx={{ mb: 2 }}>
                        {error}
                    </Alert>
                ) : noteData ? (
                    <>
                        {/* 预约信息摘要 */}
                        <ContentSection>
                            <Typography variant="h6" gutterBottom>
                                {t('appointment_details')}
                            </Typography>
                            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                                <Typography variant="body1">
                                    <strong>{t('date')}:</strong> {formatDate(appointmentData?.date || noteData?.appointment?.appointment_date)}
                                </Typography>
                                <Typography variant="body1">
                                    <strong>{t('doctor')}:</strong> {appointmentData?.doctor || noteData?.providerName}
                                </Typography>
                                <Typography variant="body1">
                                    <strong>{t('reason')}:</strong> {appointmentData?.reason || noteData?.appointment?.reason || t('general_consultation')}
                                </Typography>
                            </Box>
                        </ContentSection>

                        {/* 标签页切换 */}
                        <Box sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            borderBottom: 1,
                            borderColor: 'divider'
                        }}>
                            <Tabs
                                value={tabValue}
                                onChange={handleTabChange}
                                aria-label="note tabs"
                                sx={{ flexGrow: 1 }}
                            >
                                {/*
                                <Tab
                                    label={t('original_note')}
                                    icon={<DescriptionIcon />}
                                    iconPosition="start"
                                    id="note-tab-0"
                                    aria-controls="note-tabpanel-0"
                                />
                                */}
                                <Tab
                                    label={t('ai_summary')}
                                    icon={<AutoAwesomeIcon />}
                                    iconPosition="start"
                                    id="note-tab-1"
                                    aria-controls="note-tabpanel-1"
                                    disabled={!aiSummary && !generatingSummary}
                                />
                            </Tabs>

                            {/* 语言切换按钮组 - 仅在AI总结标签下显示 */}
                            {tabValue === 1 && (
                                <ToggleButtonGroup
                                    value={summaryLanguage}
                                    exclusive
                                    onChange={handleLanguageChange}
                                    aria-label="summary language"
                                    size="small"
                                    sx={{ ml: 2 }}
                                    disabled={generatingSummary}
                                >
                                    <ToggleButton value="en" aria-label="english">
                                        EN
                                    </ToggleButton>
                                    <ToggleButton value="zh" aria-label="chinese">
                                        中
                                    </ToggleButton>
                                </ToggleButtonGroup>
                            )}
                        </Box>

                        {/* 原始笔记内容 */}
                        {/*
                        <TabPanel value={tabValue} index={0}>
                            <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold' }}>
                                {formatDate(noteData.observationDate)} {t('by')} {noteData.providerName}
                            </Typography>
                            <NoteContent
                                dangerouslySetInnerHTML={{ __html: formatNoteContent(noteData.content) || t('no_note_content') }}
                            />
                        </TabPanel>
                        */}

                        {/* AI总结内容 */}
                        <TabPanel value={tabValue} index={1}>
                            <AIDisclaimer sx={{ mb: 2 }} />
                            {generatingSummary ? (
                                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 4 }}>
                                    <CircularProgress sx={{ mr: 2 }} />
                                    <Typography>
                                        {t('generating_summary')}
                                    </Typography>
                                </Box>
                            ) : aiSummary ? (
                                <ContentSection>
                                    <Box
                                        sx={{
                                            whiteSpace: 'pre-wrap',
                                            lineHeight: 1.6,
                                            '& h1': { fontSize: '1.4rem', mb: 1.5, fontWeight: 600 },
                                            '& h2': { fontSize: '1.2rem', mb: 1, mt: 1.5, fontWeight: 600 },
                                            '& h3': { fontSize: '1.1rem', mb: 0.8, mt: 1.2, fontWeight: 600 },
                                            '& p': { mb: 1 },
                                            '& ul, & ol': { mb: 1.5, pl: 2 },
                                            '& li': { mb: 0.5 },
                                            '& strong': { fontWeight: 600 },
                                            '& em': { fontStyle: 'italic' }
                                        }}
                                        dangerouslySetInnerHTML={{ __html: formatNoteContent(aiSummary) }}
                                    />
                                </ContentSection>
                            ) : (
                                <Alert severity="info">
                                    {t('no_ai_summary_available')}
                                </Alert>
                            )}
                        </TabPanel>
                    </>
                ) : (
                    <Alert severity="info">
                        {t('no_medical_note')}
                    </Alert>
                )}
            </DialogContent>

            <DialogActions>
                <Button onClick={onClose} color="primary">
                    {t('close')}
                </Button>
            </DialogActions>
        </Dialog>
    );
};

export default AppointmentNoteDialog; 