# Docker build context exclusions

# Ignore Git files
.git
.gitignore

# Ignore Node.js dependencies (they are installed in the container)
node_modules/

# Ignore local development environment files
.env

# Ignore IDE/editor specific files
.vscode/
.idea/

# Ignore build artifacts if any are generated locally
build/
dist/

# Ignore logs directory
logs/

# !!! IMPORTANT: Ignore the remotely mounted documents directory !!!
# remote-documents/

# Ignore frontend build artifacts if they are not part of the context for this Dockerfile
frontend/build/
frontend/node_modules/

# Ignore backend build artifacts if they are not part of the context for this Dockerfile
backend/dist/
backend/node_modules/ 