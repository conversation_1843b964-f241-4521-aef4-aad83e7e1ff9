const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const storeController = require('../controllers/storeController');

// 公开路由 - 不需要认证
router.get('/products', storeController.getProducts);
router.get('/products/:productId', storeController.getProductById);
router.get('/categories', storeController.getCategories);

// Stripe Webhook - 不需要认证但需要原始body
router.post('/webhook/stripe', express.raw({ type: 'application/json' }), storeController.handleStripeWebhook);

// 需要认证的路由
router.use(auth); // 从这里开始所有路由都需要认证

// 订单相关
router.post('/orders', storeController.createOrder);
router.get('/orders', storeController.getUserOrders);
router.get('/orders/:orderId', storeController.getOrderById);

// 支付相关
router.post('/payment/create-intent', storeController.createPaymentIntent);

// 管理员路由 - 订单管理
router.get('/admin/orders', storeController.getAllOrders);
router.put('/admin/orders/:orderId/status', storeController.updateOrderStatus);

// 管理员路由 - 商品管理
router.post('/products', storeController.createProduct);
router.put('/products/:productId', storeController.updateProduct);
router.delete('/products/:productId', storeController.deleteProduct);

module.exports = router; 