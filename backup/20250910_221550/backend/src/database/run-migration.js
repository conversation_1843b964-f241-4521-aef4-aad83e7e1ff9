const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'oscar_mcmaster',
  port: process.env.DB_PORT || 3306,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};

async function runMigration(sqlFilePath) {
  console.log(`Running migration: ${sqlFilePath}`);
  
  // 读取SQL文件内容
  const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
  
  // 创建数据库连接
  const connection = await mysql.createConnection(dbConfig);
  
  try {
    console.log('Connected to database');
    
    // 分割SQL语句（按分号分割，但忽略注释中的分号）
    const statements = sqlContent.split(';')
      .map(statement => statement.trim())
      .filter(statement => statement.length > 0);
    
    // 执行每个SQL语句
    for (const statement of statements) {
      console.log(`Executing: ${statement.substring(0, 100)}...`);
      await connection.query(statement);
    }
    
    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Error executing migration:', error);
  } finally {
    // 关闭连接
    await connection.end();
    console.log('Database connection closed');
  }
}

// 获取命令行参数中的SQL文件路径
const sqlFilePath = process.argv[2];

if (!sqlFilePath) {
  console.error('Please provide the path to the SQL file');
  process.exit(1);
}

// 运行迁移
runMigration(path.resolve(__dirname, sqlFilePath))
  .catch(err => {
    console.error('Migration failed:', err);
    process.exit(1);
  });
