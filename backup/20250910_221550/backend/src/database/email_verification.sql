-- 更新 user_auth 表，添加电子邮件验证所需的字段
ALTER TABLE user_auth 
ADD COLUMN is_verified BOOLEAN NOT NULL DEFAULT FALSE,
ADD COLUMN verification_token VARCHAR(255),
ADD COLUMN reset_token VARCHAR(255),
ADD COLUMN reset_token_expires DATETIME;

-- 更新现有用户默认为已验证
UPDATE user_auth SET is_verified = TRUE;

-- 添加索引以加速查询
CREATE INDEX idx_verification_token ON user_auth(verification_token);
CREATE INDEX idx_reset_token ON user_auth(reset_token); 