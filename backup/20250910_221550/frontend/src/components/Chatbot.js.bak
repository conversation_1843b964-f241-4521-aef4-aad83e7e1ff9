import React, { useState, useEffect, useRef } from 'react';
import {
    Paper,
    Typography,
    Box,
    TextField,
    Button,
    List,
    ListItem,
    ListItemText,
    CircularProgress,
    IconButton,
    useTheme,
    Avatar,
    Alert,
    Fab,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Drawer,
    Divider,
    ListItemButton,
    ListItemIcon,
    Menu,
    MenuItem,
    Tooltip
} from '@mui/material';
import SendIcon from '@mui/icons-material/Send';
import PersonIcon from '@mui/icons-material/Person';
import SmartToyIcon from '@mui/icons-material/SmartToy'; // Bot icon
import ChatIcon from '@mui/icons-material/Chat'; // Icon for Fab
import CloseIcon from '@mui/icons-material/Close'; // Icon for closing dialog
import HistoryIcon from '@mui/icons-material/History'; // Icon for chat history
import AddIcon from '@mui/icons-material/Add'; // Icon for new chat
import DeleteIcon from '@mui/icons-material/Delete'; // Icon for delete conversation
import MoreVertIcon from '@mui/icons-material/MoreVert'; // Icon for more options
import { styled } from '@mui/material/styles';
import { useLanguage } from '../context/LanguageContext';
import { useView } from '../context/ViewContext'; // To get user info
import dayjs from 'dayjs';

const StyledPaper = styled(Paper)(({ theme }) => ({
    padding: theme.spacing(2),
    height: '70vh', // Adjust height for dialog view
    width: '100%', // Take full width of dialog content area
    display: 'flex',
    flexDirection: 'column',
    boxShadow: 'none', // Dialog provides elevation
    overflow: 'hidden', // Prevent internal elements from overflowing paper
}));

const MessagesContainer = styled(Box)(({ theme }) => ({
    flexGrow: 1,
    overflowY: 'auto',
    marginBottom: theme.spacing(2),
    padding: theme.spacing(1),
}));

const InputContainer = styled(Box)(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    paddingTop: theme.spacing(1),
    borderTop: `1px solid ${theme.palette.divider}`,
}));

// Drawer width for chat history
const DRAWER_WIDTH = 280;

// New component for rendering a single chat message item
const ChatMessageItem = ({ msg, isUser, userName, userInitials, botName, theme }) => {
    return (
        <ListItem sx={{
            alignItems: 'flex-start',
            mb: 1.5, // Increased margin bottom
            flexDirection: isUser ? 'row-reverse' : 'row' // Align user to right
        }}>
            <Avatar
                sx={{
                    bgcolor: isUser ? theme.palette.primary.light : theme.palette.secondary.light, // Slightly lighter colors
                    color: isUser ? theme.palette.primary.contrastText : theme.palette.secondary.contrastText,
                    mx: 1.5, // Consistent horizontal margin
                    mt: 0.5
                }}
            >
                {isUser ? userInitials : <SmartToyIcon />}
            </Avatar>
            <ListItemText
                primary={isUser ? userName : botName}
                secondary={
                    // Apply different background for better bubble effect
                    <Box sx={{
                        bgcolor: isUser ? theme.palette.action.hover : theme.palette.background.default,
                        p: 1.5, // Padding inside bubble
                        borderRadius: '10px',
                        display: 'inline-block', // Fit content
                        maxWidth: '100%' // Prevent overflow
                    }}>
                        <Typography variant="body1" component="span" sx={{ whiteSpace: 'pre-wrap', display: 'block' }}>
                            {msg.content}
                        </Typography>
                    </Box>
                }
                primaryTypographyProps={{
                    fontWeight: 'medium',
                    textAlign: isUser ? 'right' : 'left', // Align primary text
                    mb: 0.5 // Space between name and bubble
                }}
                secondaryTypographyProps={{ component: 'div' }} // Keep this
                sx={{ textAlign: isUser ? 'right' : 'left' }} // Align the whole ListItemText block
            />
        </ListItem>
    );
};

const Chatbot = () => {
    const { t } = useLanguage();
    // Consume context, including new chat state and toggle function
    const { loggedInUser, isChatOpen, toggleChat } = useView();
    const theme = useTheme();
    const [messages, setMessages] = useState([]);
    const [input, setInput] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState('');
    const messagesEndRef = useRef(null);
    const abortControllerRef = useRef(null);

    // 聊天历史相关状态
    const [historyDrawerOpen, setHistoryDrawerOpen] = useState(false);
    const [chatHistory, setChatHistory] = useState([]);
    const [isLoadingHistory, setIsLoadingHistory] = useState(false);
    const [currentConversationId, setCurrentConversationId] = useState(null);
    const [conversationMenuAnchor, setConversationMenuAnchor] = useState(null);
    const [selectedConversation, setSelectedConversation] = useState(null);

    // 获取聊天历史
    const fetchChatHistory = async () => {
        if (!loggedInUser || !loggedInUser.id) return;

        setIsLoadingHistory(true);
        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`/api/chat/history/${loggedInUser.id}`, {
                headers: {
                    ...(token && { 'Authorization': `Bearer ${token}` })
                }
            });

            if (!response.ok) {
                throw new Error('Failed to fetch chat history');
            }

            const data = await response.json();
            setChatHistory(data.conversations || []);
        } catch (error) {
            console.error('Error fetching chat history:', error);
            setError('Failed to load chat history');
        } finally {
            setIsLoadingHistory(false);
        }
    };

    // 加载特定会话的消息
    const loadConversation = async (conversationId) => {
        setIsLoading(true);
        setMessages([]);

        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`/api/chat/conversation/${conversationId}`, {
                headers: {
                    ...(token && { 'Authorization': `Bearer ${token}` })
                }
            });

            if (!response.ok) {
                throw new Error('Failed to load conversation');
            }

            const data = await response.json();

            if (data.messages && Array.isArray(data.messages)) {
                // 转换消息格式以匹配前端需要的格式
                const formattedMessages = data.messages.map((msg, index) => ({
                    id: `${conversationId}-${index}`,
                    role: msg.role,
                    content: msg.content
                }));

                setMessages(formattedMessages);
                setCurrentConversationId(conversationId);
                setHistoryDrawerOpen(false);
            }
        } catch (error) {
            console.error('Error loading conversation:', error);
            setError('Failed to load conversation messages');
        } finally {
            setIsLoading(false);
        }
    };

    // 删除会话
    const deleteConversation = async (conversationId) => {
        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`/api/chat/conversation/${conversationId}`, {
                method: 'DELETE',
                headers: {
                    ...(token && { 'Authorization': `Bearer ${token}` })
                }
            });

            if (!response.ok) {
                throw new Error('Failed to delete conversation');
            }

            // 从本地状态中移除会话
            setChatHistory(prevHistory => prevHistory.filter(conv => conv.id !== conversationId));

            // 如果删除的是当前会话，创建一个新会话
            if (currentConversationId === conversationId) {
                startNewConversation();
            }
        } catch (error) {
            console.error('Error deleting conversation:', error);
            setError('Failed to delete conversation');
        } finally {
            setConversationMenuAnchor(null);
        }
    };

    // 开始新会话
    const startNewConversation = () => {
        setMessages([]);
        setCurrentConversationId(Date.now().toString());
        setHistoryDrawerOpen(false);
    };

    // 处理会话菜单
    const handleConversationMenuOpen = (event, conversation) => {
        setConversationMenuAnchor(event.currentTarget);
        setSelectedConversation(conversation);
    };

    const handleConversationMenuClose = () => {
        setConversationMenuAnchor(null);
        setSelectedConversation(null);
    };

    // Function to handle streaming response
    const processStream = async (reader, assistantMessageId) => {
        const decoder = new TextDecoder();
        let buffer = '';

        try {
            while (true) {
                const { value, done } = await reader.read();
                if (done) {
                    console.log('Stream finished.');
                    break;
                }

                buffer += decoder.decode(value, { stream: true });

                // Process Server-Sent Events (SSE)
                const lines = buffer.split('\n');
                buffer = lines.pop(); // Keep the last (potentially incomplete) line

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const dataChunk = line.substring(6);
                        if (dataChunk.trim() === '[DONE]') {
                            console.log('Stream marked DONE.');
                            return; // End processing if [DONE] signal received
                        }
                        try {
                            const parsed = JSON.parse(dataChunk);
                            if (parsed.choices && parsed.choices[0]?.delta?.content) {
                                const contentChunk = parsed.choices[0].delta.content;
                                // Update the specific assistant message incrementally
                                setMessages(prevMessages =>
                                    prevMessages.map(msg =>
                                        msg.id === assistantMessageId
                                            ? { ...msg, content: msg.content + contentChunk }
                                            : msg
                                    )
                                );
                            }
                        } catch (e) {
                            console.error('Error parsing stream data chunk:', dataChunk, e);
                        }
                    }
                }
            }
        } catch (error) {
            if (error.name === 'AbortError') {
                console.log('Fetch aborted by user.');
                setError('Stream cancelled');
            } else {
                console.error('Error reading stream:', error);
                setError('Error processing response stream.');
            }
            // Ensure the potentially incomplete message is updated with an error state or removed
            setMessages(prevMessages => prevMessages.map(msg =>
                msg.id === assistantMessageId ? { ...msg, content: msg.content + ' [Error processing stream]' } : msg
            ));
        }
    };

    // Send message to backend and process stream
    const sendMessage = async (messageContent) => {
        setIsLoading(true);
        setError('');
        // Cancel previous stream if any
        abortControllerRef.current?.abort();
        abortControllerRef.current = new AbortController();

        // 确保有会话ID，如果没有就创建一个新的
        const conversationId = currentConversationId || Date.now().toString();
        if (!currentConversationId) {
            setCurrentConversationId(conversationId);
        }

        const userMessage = { id: Date.now() + '-user', role: 'user', content: messageContent };
        const assistantMessageId = Date.now() + '-assistant';
        const assistantPlaceholder = { id: assistantMessageId, role: 'assistant', content: '' };

        setMessages(prevMessages => [...prevMessages, userMessage, assistantPlaceholder]);

        try {
            const token = localStorage.getItem('token');
            const response = await fetch('/api/chat', { // Assumes Nginx proxy is set up for /api
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    // Include auth token if your chatbot service requires it
                    ...(token && { 'Authorization': `Bearer ${token}` })
                },
                body: JSON.stringify({
                    messages: [...messages, userMessage],
                    userId: loggedInUser?.id,
                    conversationId
                }), // Send history, userId and conversationId
                signal: abortControllerRef.current.signal, // Pass the abort signal
            });

            if (!response.ok) {
                const errData = await response.json().catch(() => ({ message: 'Failed to get response from bot' }));
                throw new Error(errData.message || `HTTP error! status: ${response.status}`);
            }

            if (!response.body) {
                throw new Error('Response body is null');
            }

            const reader = response.body.getReader();
            await processStream(reader, assistantMessageId); // Process the stream

            // 刷新聊天历史
            fetchChatHistory();

        } catch (err) {
            if (err.name !== 'AbortError') {
                console.error("Send message error:", err);
                setError(err.message || 'Failed to send message');
                // Update placeholder with error
                setMessages(prevMessages => prevMessages.map(msg =>
                    msg.id === assistantMessageId ? { ...msg, content: `[Error: ${err.message}]` } : msg
                ));
            }
        } finally {
            setIsLoading(false);
            abortControllerRef.current = null; // Clear the controller
        }
    };

    const handleSend = () => {
        if (input.trim() && !isLoading) {
            sendMessage(input.trim());
            setInput('');
        }
    };

    const handleKeyPress = (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSend();
        }
    };

    // 当聊天对话框打开时获取聊天历史
    useEffect(() => {
        if (isChatOpen && loggedInUser?.id) {
            fetchChatHistory();

            // 如果没有当前会话ID，创建一个新会话
            if (!currentConversationId) {
                setCurrentConversationId(Date.now().toString());
            }
        }
    }, [isChatOpen, loggedInUser?.id]);

    // Scroll to bottom when messages change
    useEffect(() => {
        // Scroll only if the chat is open (using context state)
        if (isChatOpen) {
            setTimeout(() => messagesEndRef.current?.scrollIntoView({ behavior: "smooth" }), 100);
        }
    }, [messages, isChatOpen]);

    const getUserInitials = () => {
        if (loggedInUser?.firstName && loggedInUser?.lastName) {
            return `${loggedInUser.firstName.charAt(0)}${loggedInUser.lastName.charAt(0)}`;
        }
        return <PersonIcon />;
    };

    const getUserName = () => {
        if (loggedInUser?.firstName && loggedInUser?.lastName) {
            return `${loggedInUser.firstName} ${loggedInUser.lastName}`;
        }
        return t('you');
    };

    const toggleHistoryDrawer = () => {
        setHistoryDrawerOpen(!historyDrawerOpen);
    };

    const formatDate = (dateString) => {
        return dayjs(dateString).format('YYYY-MM-DD HH:mm');
    };

    return (
        <>
            {/* Floating chat button */}
            <Fab
                color="primary"
                aria-label="chat"
                sx={{
                    position: 'fixed',
                    bottom: 20,
                    right: 20,
                    display: isChatOpen ? 'none' : 'flex',
                    zIndex: 1000
                }}
                onClick={toggleChat}
            >
                <ChatIcon />
            </Fab>

            {/* Chat dialog */}
            <Dialog
                open={isChatOpen}
                onClose={toggleChat}
                fullWidth
                maxWidth="md"
                sx={{
                    '& .MuiDialog-paper': {
                        height: '80vh',
                        maxHeight: '800px',
                        m: { xs: 1, sm: 2 },
                        borderRadius: 2
                    }
                }}
            >
                <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', pb: 1 }}>
                    <Box display="flex" alignItems="center">
                        <SmartToyIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
                        <Typography variant="h6" component="div">
                            {t('mmc_wellness_chat')}
                        </Typography>
                    </Box>
                    <Box>
                        <Tooltip title={t('chat_history')}>
                            <IconButton
                                onClick={toggleHistoryDrawer}
                                color={historyDrawerOpen ? "primary" : "default"}
                            >
                                <HistoryIcon />
                            </IconButton>
                        </Tooltip>
                        <Tooltip title={t('close')}>
                            <IconButton onClick={toggleChat} edge="end">
                                <CloseIcon />
                            </IconButton>
                        </Tooltip>
                    </Box>
                </DialogTitle>

                <DialogContent sx={{
                    display: 'flex',
                    p: 0,
                    position: 'relative',
                    overflow: 'hidden'
                }}>
                    {/* 聊天历史侧边栏 */}
                    <Drawer
                        variant="persistent"
                        anchor="left"
                        open={historyDrawerOpen}
                        sx={{
                            width: DRAWER_WIDTH,
                            flexShrink: 0,
                            '& .MuiDrawer-paper': {
                                width: DRAWER_WIDTH,
                                boxSizing: 'border-box',
                                position: 'absolute',
                                height: '100%',
                                border: 'none',
                                borderRight: `1px solid ${theme.palette.divider}`
                            },
                        }}
                    >
                        <Box sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            p: 2,
                            borderBottom: `1px solid ${theme.palette.divider}`
                        }}>
                            <Typography variant="subtitle1">{t('conversations')}</Typography>
                            <Tooltip title={t('new_chat')}>
                                <IconButton onClick={startNewConversation} color="primary">
                                    <AddIcon />
                                </IconButton>
                            </Tooltip>
                        </Box>

                        <List sx={{ overflow: 'auto', flex: 1 }}>
                            {isLoadingHistory ? (
                                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                                    <CircularProgress size={24} />
                                </Box>
                            ) : chatHistory.length > 0 ? (
                                chatHistory.map((conv) => (
                                    <ListItem
                                        key={conv.id}
                                        disablePadding
                                        secondaryAction={
                                            <IconButton
                                                edge="end"
                                                onClick={(e) => handleConversationMenuOpen(e, conv)}
                                            >
                                                <MoreVertIcon fontSize="small" />
                                            </IconButton>
                                        }
                                    >
                                        <ListItemButton
                                            onClick={() => loadConversation(conv.id)}
                                            selected={currentConversationId === conv.id}
                                            sx={{ pr: 6 }} // Leave space for the action button
                                        >
                                            <ListItemIcon sx={{ minWidth: 36 }}>
                                                <ChatIcon fontSize="small" />
                                            </ListItemIcon>
                                            <ListItemText
                                                primary={conv.summary}
                                                secondary={formatDate(conv.date)}
                                                primaryTypographyProps={{
                                                    noWrap: true,
                                                    fontSize: '0.875rem'
                                                }}
                                                secondaryTypographyProps={{
                                                    noWrap: true,
                                                    fontSize: '0.75rem'
                                                }}
                                            />
                                        </ListItemButton>
                                    </ListItem>
                                ))
                            ) : (
                                <Box sx={{ p: 2, textAlign: 'center' }}>
                                    <Typography variant="body2" color="text.secondary">
                                        {t('no_conversations')}
                                    </Typography>
                                </Box>
                            )}
                        </List>
                    </Drawer>

                    {/* 会话菜单 */}
                    <Menu
                        anchorEl={conversationMenuAnchor}
                        open={Boolean(conversationMenuAnchor)}
                        onClose={handleConversationMenuClose}
                    >
                        <MenuItem onClick={() => {
                            if (selectedConversation) {
                                deleteConversation(selectedConversation.id);
                            }
                        }}>
                            <ListItemIcon>
                                <DeleteIcon fontSize="small" />
                            </ListItemIcon>
                            <ListItemText>{t('delete')}</ListItemText>
                        </MenuItem>
                    </Menu>

                    {/* 主聊天区域 */}
                    <Box sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        flex: 1,
                        marginLeft: historyDrawerOpen ? DRAWER_WIDTH : 0,
                        transition: theme.transitions.create(['margin', 'width'], {
                            easing: theme.transitions.easing.sharp,
                            duration: theme.transitions.duration.leavingScreen,
                        }),
                    }}>
                        <StyledPaper>
                            {/* Display error if any */}
                            {error && (
                                <Alert
                                    severity="error"
                                    sx={{ mb: 2 }}
                                    onClose={() => setError('')}
                                >
                                    {error}
                                </Alert>
                            )}

                            <MessagesContainer>
                                {messages.length === 0 ? (
                                    <Box sx={{
                                        display: 'flex',
                                        flexDirection: 'column',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        height: '100%',
                                        opacity: 0.7
                                    }}>
                                        <SmartToyIcon sx={{ fontSize: 64, color: theme.palette.primary.main, mb: 2 }} />
                                        <Typography variant="h6" gutterBottom>
                                            {t('welcome_to_mmc_chat')}
                                        </Typography>
                                        <Typography variant="body2" textAlign="center" sx={{ maxWidth: 400 }}>
                                            {t('chat_intro_expanded')}
                                        </Typography>
                                    </Box>
                                ) : (
                                    <List>
                                        {messages.map((msg, index) => (
                                            <ChatMessageItem
                                                key={msg.id || index}
                                                msg={msg}
                                                isUser={msg.role === 'user'}
                                                userName={getUserName()}
                                                userInitials={getUserInitials()}
                                                botName="MMC Assistant"
                                                theme={theme}
                                            />
                                        ))}
                                        <div ref={messagesEndRef} /> {/* Empty div for scroll target */}
                                    </List>
                                )}
                            </MessagesContainer>

                            <InputContainer>
                                <TextField
                                    fullWidth
                                    variant="outlined"
                                    placeholder={t('type_message')}
                                    value={input}
                                    onChange={(e) => setInput(e.target.value)}
                                    onKeyPress={handleKeyPress}
                                    disabled={isLoading}
                                    multiline
                                    maxRows={4}
                                    sx={{ mr: 1 }}
                                    size="small"
                                />
                                <Button
                                    variant="contained"
                                    color="primary"
                                    endIcon={isLoading ? <CircularProgress size={20} /> : <SendIcon />}
                                    onClick={handleSend}
                                    disabled={isLoading || !input.trim()}
                                >
                                    {isLoading ? t('sending') : t('send')}
                                </Button>
                            </InputContainer>
                        </StyledPaper>
                    </Box>
                </DialogContent>
            </Dialog>
        </>
    );
};

export default Chatbot; 