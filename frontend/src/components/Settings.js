import React, { useState } from 'react';
import {
    Container,
    Paper,
    Typography,
    Box,
    Tabs,
    Tab,
    TextField,
    Button,
    Switch,
    FormControlLabel,
    Divider,
    Alert,
    Grid,
    InputAdornment,
    IconButton,
    useTheme
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import SaveIcon from '@mui/icons-material/Save';
import SecurityIcon from '@mui/icons-material/Security';
import NotificationsIcon from '@mui/icons-material/Notifications';
import LanguageIcon from '@mui/icons-material/Language';
import { useLanguage } from '../context/LanguageContext';
import { useThemeMode } from '../context/ThemeContext';

const StyledPaper = styled(Paper)(({ theme }) => ({
    marginTop: theme.spacing(4),
    padding: theme.spacing(4),
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
}));

function TabPanel(props) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`settings-tabpanel-${index}`}
            aria-labelledby={`settings-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3 }}>
                    {children}
                </Box>
            )}
        </div>
    );
}

function Settings() {
    const { t, language, setLanguage } = useLanguage();
    const { isDarkMode, setThemeMode } = useThemeMode();
    const theme = useTheme();

    const [value, setValue] = useState(0);
    const [showPassword, setShowPassword] = useState(false);
    const [showNewPassword, setShowNewPassword] = useState(false);
    const [passwordForm, setPasswordForm] = useState({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
    });
    const [notificationSettings, setNotificationSettings] = useState({
        emailNotifications: true,
        appointmentReminders: true,
        marketingEmails: false,
        documentUploads: true
    });
    const [success, setSuccess] = useState(false);
    const [error, setError] = useState('');
    const [timeFormat, setTimeFormat] = useState(true);

    const handleTabChange = (event, newValue) => {
        setValue(newValue);
        setSuccess(false);
        setError('');
    };

    const handlePasswordChange = (e) => {
        setPasswordForm({
            ...passwordForm,
            [e.target.name]: e.target.value
        });
    };

    const handleNotificationChange = (event) => {
        setNotificationSettings({
            ...notificationSettings,
            [event.target.name]: event.target.checked
        });
    };

    const handleClickShowPassword = () => {
        setShowPassword(!showPassword);
    };

    const handleClickShowNewPassword = () => {
        setShowNewPassword(!showNewPassword);
    };

    const handlePasswordSubmit = (e) => {
        e.preventDefault();
        // Validation
        if (passwordForm.newPassword !== passwordForm.confirmPassword) {
            setError(t('passwords_do_not_match'));
            return;
        }

        if (passwordForm.newPassword.length < 8) {
            setError(t('password_too_short'));
            return;
        }

        // TODO: Send to API
        // For demo purposes, simulate success
        setSuccess(true);
        setError('');
        setPasswordForm({
            currentPassword: '',
            newPassword: '',
            confirmPassword: ''
        });
    };

    const handleNotificationSubmit = (e) => {
        e.preventDefault();
        // TODO: Send to API
        // For demo purposes, simulate success
        setSuccess(true);
    };

    const handleLanguageChange = (e) => {
        setLanguage(e.target.value);
    };

    const handleTimeFormatChange = (e) => {
        setTimeFormat(e.target.checked);
    };

    const handleThemeChange = (e) => {
        setThemeMode(e.target.checked ? 'dark' : 'light');
    };

    return (
        <Container component="main" maxWidth="md">
            <StyledPaper elevation={3}>
                <Typography component="h1" variant="h4" gutterBottom>
                    {t('settings')}
                </Typography>

                <Box sx={{ width: '100%' }}>
                    <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                        <Tabs
                            value={value}
                            onChange={handleTabChange}
                            aria-label="settings tabs"
                            centered
                        >
                            <Tab icon={<SecurityIcon />} label={t('security')} id="settings-tab-0" />
                            <Tab icon={<NotificationsIcon />} label={t('notifications')} id="settings-tab-1" />
                            <Tab icon={<LanguageIcon />} label={t('preferences')} id="settings-tab-2" />
                        </Tabs>
                    </Box>

                    {success && (
                        <Alert severity="success" sx={{ mt: 2 }}>
                            {t('settings_updated')}
                        </Alert>
                    )}

                    {error && (
                        <Alert severity="error" sx={{ mt: 2 }}>
                            {error}
                        </Alert>
                    )}

                    <TabPanel value={value} index={0}>
                        <Typography variant="h6" gutterBottom>
                            {t('change_password')}
                        </Typography>
                        <Box component="form" onSubmit={handlePasswordSubmit} sx={{ mt: 2 }}>
                            <TextField
                                margin="normal"
                                required
                                fullWidth
                                name="currentPassword"
                                label={t('current_password')}
                                type={showPassword ? 'text' : 'password'}
                                id="currentPassword"
                                value={passwordForm.currentPassword}
                                onChange={handlePasswordChange}
                                InputProps={{
                                    endAdornment: (
                                        <InputAdornment position="end">
                                            <IconButton
                                                aria-label="toggle password visibility"
                                                onClick={handleClickShowPassword}
                                                edge="end"
                                            >
                                                {showPassword ? <VisibilityOff /> : <Visibility />}
                                            </IconButton>
                                        </InputAdornment>
                                    )
                                }}
                            />
                            <TextField
                                margin="normal"
                                required
                                fullWidth
                                name="newPassword"
                                label={t('new_password')}
                                type={showNewPassword ? 'text' : 'password'}
                                id="newPassword"
                                value={passwordForm.newPassword}
                                onChange={handlePasswordChange}
                                InputProps={{
                                    endAdornment: (
                                        <InputAdornment position="end">
                                            <IconButton
                                                aria-label="toggle password visibility"
                                                onClick={handleClickShowNewPassword}
                                                edge="end"
                                            >
                                                {showNewPassword ? <VisibilityOff /> : <Visibility />}
                                            </IconButton>
                                        </InputAdornment>
                                    )
                                }}
                            />
                            <TextField
                                margin="normal"
                                required
                                fullWidth
                                name="confirmPassword"
                                label={t('confirm_new_password')}
                                type={showNewPassword ? 'text' : 'password'}
                                id="confirmPassword"
                                value={passwordForm.confirmPassword}
                                onChange={handlePasswordChange}
                            />
                            <Button
                                type="submit"
                                variant="contained"
                                startIcon={<SaveIcon />}
                                sx={{ mt: 3, mb: 2 }}
                            >
                                {t('update_password')}
                            </Button>
                        </Box>
                    </TabPanel>

                    <TabPanel value={value} index={1}>
                        <Typography variant="h6" gutterBottom>
                            {t('notification_preferences')}
                        </Typography>
                        <Box component="form" onSubmit={handleNotificationSubmit} sx={{ mt: 2 }}>
                            <Grid container spacing={2}>
                                <Grid item xs={12}>
                                    <FormControlLabel
                                        control={
                                            <Switch
                                                checked={notificationSettings.emailNotifications}
                                                onChange={handleNotificationChange}
                                                name="emailNotifications"
                                                color="primary"
                                            />
                                        }
                                        label={t('email_notifications')}
                                    />
                                    <Typography variant="caption" color="text.secondary" display="block">
                                        {t('receive_general_notifications')}
                                    </Typography>
                                </Grid>

                                <Grid item xs={12}>
                                    <Divider sx={{ my: 1 }} />
                                </Grid>

                                <Grid item xs={12}>
                                    <FormControlLabel
                                        control={
                                            <Switch
                                                checked={notificationSettings.appointmentReminders}
                                                onChange={handleNotificationChange}
                                                name="appointmentReminders"
                                                color="primary"
                                            />
                                        }
                                        label={t('appointment_reminders')}
                                    />
                                    <Typography variant="caption" color="text.secondary" display="block">
                                        {t('receive_appointment_reminders')}
                                    </Typography>
                                </Grid>

                                <Grid item xs={12}>
                                    <Divider sx={{ my: 1 }} />
                                </Grid>

                                <Grid item xs={12}>
                                    <FormControlLabel
                                        control={
                                            <Switch
                                                checked={notificationSettings.documentUploads}
                                                onChange={handleNotificationChange}
                                                name="documentUploads"
                                                color="primary"
                                            />
                                        }
                                        label={t('document_notifications')}
                                    />
                                    <Typography variant="caption" color="text.secondary" display="block">
                                        {t('receive_document_notifications')}
                                    </Typography>
                                </Grid>

                                <Grid item xs={12}>
                                    <Divider sx={{ my: 1 }} />
                                </Grid>

                                <Grid item xs={12}>
                                    <FormControlLabel
                                        control={
                                            <Switch
                                                checked={notificationSettings.marketingEmails}
                                                onChange={handleNotificationChange}
                                                name="marketingEmails"
                                                color="primary"
                                            />
                                        }
                                        label={t('marketing_communications')}
                                    />
                                    <Typography variant="caption" color="text.secondary" display="block">
                                        {t('receive_marketing_emails')}
                                    </Typography>
                                </Grid>
                            </Grid>

                            <Button
                                type="submit"
                                variant="contained"
                                startIcon={<SaveIcon />}
                                sx={{ mt: 3, mb: 2 }}
                            >
                                {t('save')}
                            </Button>
                        </Box>
                    </TabPanel>

                    <TabPanel value={value} index={2}>
                        <Typography variant="h6" gutterBottom>
                            {t('general_preferences')}
                        </Typography>
                        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                            {t('app_experience_settings')}
                        </Typography>

                        <Grid container spacing={3}>
                            <Grid item xs={12}>
                                <FormControlLabel
                                    control={
                                        <Switch
                                            checked={timeFormat}
                                            onChange={handleTimeFormatChange}
                                            color="primary"
                                        />
                                    }
                                    label={t('use_24hour_format')}
                                />
                            </Grid>

                            <Grid item xs={12}>
                                <Divider sx={{ my: 1 }} />
                            </Grid>

                            <Grid item xs={12}>
                                <FormControlLabel
                                    control={
                                        <Switch
                                            checked={isDarkMode}
                                            onChange={handleThemeChange}
                                            color="primary"
                                        />
                                    }
                                    label={t('dark_mode')}
                                />
                            </Grid>

                            <Grid item xs={12}>
                                <Divider sx={{ my: 1 }} />
                            </Grid>

                            <Grid item xs={12}>
                                <TextField
                                    select
                                    fullWidth
                                    label={t('language')}
                                    value={language}
                                    onChange={handleLanguageChange}
                                    SelectProps={{
                                        native: true,
                                    }}
                                >
                                    <option value="en">English</option>
                                    <option value="zh">中文 (Chinese)</option>
                                </TextField>
                            </Grid>
                        </Grid>

                        <Button
                            variant="contained"
                            startIcon={<SaveIcon />}
                            sx={{ mt: 3, mb: 2 }}
                        >
                            {t('save')}
                        </Button>
                    </TabPanel>
                </Box>
            </StyledPaper>
        </Container>
    );
}

export default Settings; 