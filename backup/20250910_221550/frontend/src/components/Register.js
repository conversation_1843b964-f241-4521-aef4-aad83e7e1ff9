import React, { useState, useEffect } from 'react';
import { API_URL } from '../utils/env';
import { useNavigate } from 'react-router-dom';
import {
    Container,
    Paper,
    TextField,
    Button,
    Typography,
    Link,
    MenuItem,
    Box,
    CircularProgress,
    InputAdornment,
    Divider,
    Stack,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import InfoIcon from '@mui/icons-material/Info';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import Tooltip from '@mui/material/Tooltip';
import { useLanguage } from '../context/LanguageContext';

const StyledPaper = styled(Paper)(({ theme }) => ({
    marginTop: theme.spacing(4),
    padding: theme.spacing(3),
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
}));

const StyledForm = styled('form')(({ theme }) => ({
    width: '100%',
    marginTop: theme.spacing(2),
}));

const StyledButton = styled(Button)(({ theme }) => ({
    margin: theme.spacing(3, 0, 2),
    padding: theme.spacing(1.5),
    fontSize: '1rem',
}));

const titles = [
    { value: 'Mr.', label: 'Mr.' },
    { value: 'Mrs.', label: 'Mrs.' },
    { value: 'Ms.', label: 'Ms.' },
    { value: 'Dr.', label: 'Dr.' },
];

const provinces = [
    { value: 'AB', label: 'Alberta' },
    { value: 'BC', label: 'British Columbia' },
    { value: 'MB', label: 'Manitoba' },
    { value: 'NB', label: 'New Brunswick' },
    { value: 'NL', label: 'Newfoundland and Labrador' },
    { value: 'NS', label: 'Nova Scotia' },
    { value: 'ON', label: 'Ontario' },
    { value: 'PE', label: 'Prince Edward Island' },
    { value: 'QC', label: 'Quebec' },
    { value: 'SK', label: 'Saskatchewan' },
    { value: 'NT', label: 'Northwest Territories' },
    { value: 'NU', label: 'Nunavut' },
    { value: 'YT', label: 'Yukon' },
];

const Register = () => {
    const { t } = useLanguage();
    const [formData, setFormData] = useState({
        title: '',
        firstName: '',
        lastName: '',
        address: '',
        city: '',
        province: '',
        postalCode: '',
        phoneNumber: '',
        email: '',
        dateOfBirth: '',
        healthInsuranceNumber: '',
        sex: '',
        password: '',
        confirmPassword: '',
        referralCode: '',
    });
    const [error, setError] = useState('');
    const [loading, setLoading] = useState(false);
    const [referralCodeValid, setReferralCodeValid] = useState(null);
    const [isValidatingCode, setIsValidatingCode] = useState(false);
    const [emailVerified, setEmailVerified] = useState(false);
    const [emailVerificationSent, setEmailVerificationSent] = useState(false);
    const [isVerifyingEmail, setIsVerifyingEmail] = useState(false);
    const [verificationCode, setVerificationCode] = useState('');
    const navigate = useNavigate();

    useEffect(() => {
        const searchParams = new URLSearchParams(window.location.search);
        const email = searchParams.get('email');
        if (email) {
            setFormData(prev => ({ ...prev, email }));
        }
    }, []);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData({ ...formData, [name]: value });
        if (name === 'referralCode') {
            setReferralCodeValid(null);
        }
        if (name === 'email') {
            setEmailVerified(false);
            setEmailVerificationSent(false);
            setVerificationCode('');
        }
    };

    const validateReferralCode = async () => {
        if (!formData.referralCode.trim()) {
            setReferralCodeValid(false);
            return false;
        }
        try {
            setIsValidatingCode(true);
            const response = await fetch(`${API_URL}/api/referrals/validate/${formData.referralCode.trim()}`);
            const data = await response.json();
            const isValid = response.ok && data.success && data.valid;
            setReferralCodeValid(isValid);
            return isValid;
        } catch (error) {
            console.error('Error validating referral code:', error);
            setReferralCodeValid(false);
            return false;
        } finally {
            setIsValidatingCode(false);
        }
    };

    const sendEmailVerification = async () => {
        if (!formData.email || !formData.email.includes('@')) {
            setError('Please enter a valid email address');
            return;
        }
        
        try {
            setIsVerifyingEmail(true);
            setError('');
            const response = await fetch(`${API_URL}/api/auth/send-verification-code`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email: formData.email })
            });
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || 'Failed to send verification email');
            }
            
            setEmailVerificationSent(true);
        } catch (error) {
            console.error('Error sending verification email:', error);
            setError(error.message || 'Failed to send verification email');
        } finally {
            setIsVerifyingEmail(false);
        }
    };

    const verifyEmailCode = async () => {
        if (!verificationCode.trim()) {
            setError('Please enter the verification code');
            return;
        }
        
        try {
            setIsVerifyingEmail(true);
            setError('');
            const response = await fetch(`${API_URL}/api/auth/verify-email-code`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ 
                    email: formData.email, 
                    code: verificationCode.trim() 
                })
            });
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || 'Invalid verification code');
            }
            
            setEmailVerified(true);
        } catch (error) {
            console.error('Error verifying email code:', error);
            setError(error.message || 'Invalid verification code');
        } finally {
            setIsVerifyingEmail(false);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setError('');
        
        // Check all required fields
        const requiredFields = [
            { field: 'title', label: 'Title' },
            { field: 'firstName', label: 'First Name' },
            { field: 'lastName', label: 'Last Name' },
            { field: 'dateOfBirth', label: 'Date of Birth' },
            { field: 'sex', label: 'Gender' },
            { field: 'email', label: 'Email Address' },
            { field: 'phoneNumber', label: 'Phone Number' },
            { field: 'address', label: 'Street Address' },
            { field: 'city', label: 'City' },
            { field: 'province', label: 'Province/Territory' },
            { field: 'postalCode', label: 'Postal Code' },
            { field: 'healthInsuranceNumber', label: 'Health Insurance Number' },
            { field: 'password', label: 'Password' },
            { field: 'confirmPassword', label: 'Confirm Password' }
        ];
        
        const missingFields = requiredFields.filter(({ field }) => 
            !formData[field] || formData[field].trim() === ''
        );
        
        if (missingFields.length > 0) {
            const fieldNames = missingFields.map(({ label }) => label).join(', ');
            setError(`The following required fields are missing: ${fieldNames}`);
            return;
        }
        
        // Check email verification
        if (!emailVerified) {
            setError('Please verify your email address before registering');
            return;
        }
        
        if (formData.password !== formData.confirmPassword) {
            setError('Passwords do not match');
            return;
        }

        // Validate health insurance number format (exactly 10 digits)
        if (!/^\d{10}$/.test(formData.healthInsuranceNumber)) {
            setError('Health Insurance Number must be exactly 10 digits');
            return;
        }

        // Referral code is now optional - only validate if provided
        if (formData.referralCode.trim()) {
            const isValidCode = await validateReferralCode();
            if (!isValidCode) {
                setError(t('error_invalid_referral_code'));
                return;
            }
        }
        try {
            setLoading(true);
            const response = await fetch(`${API_URL}/api/auth/register`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(formData)
            });
            const data = await response.json();
            if (!response.ok) {
                throw new Error(data.message || 'Registration failed');
            }
            navigate('/login', { state: { verificationRequired: true, email: formData.email } });
        } catch (err) {
            console.error('Registration error:', err);
            setError(err.message || 'Registration failed');
        } finally {
            setLoading(false);
        }
    };

    return (
        <Container component="main" maxWidth="sm">
            <StyledPaper elevation={3}>
                <Typography component="h1" variant="h4" gutterBottom sx={{textAlign: 'center', mb: 3}}>
                    {t('patient_registration_title', 'Patient Registration')}
                </Typography>
                {error && (
                    <Typography color="error" align="center" sx={{ mt: 1, mb: 2, whiteSpace: 'pre-line' }}>
                        {error}
                    </Typography>
                )}
                <StyledForm onSubmit={handleSubmit}>
                    <Stack spacing={4}>
                        <Box sx={{ border: theme => `1px solid ${theme.palette.divider}`, p: 3, borderRadius: 1 }}>
                            <Typography variant="h6" component="div" gutterBottom sx={{ mb: 2 }}>
                                {t('referral_code_section_title', 'Referral Code')}
                            </Typography>
                            <TextField
                                fullWidth
                                id="referralCode"
                                label={t('referral_code_label', 'Referral Code (Optional)')}
                                name="referralCode"
                                autoComplete="off"
                                value={formData.referralCode}
                                onChange={handleChange}
                                onBlur={() => {
                                    if (formData.referralCode.trim()) {
                                        validateReferralCode();
                                    }
                                }}
                                error={referralCodeValid === false}
                                helperText={
                                    referralCodeValid === false ? t('error_invalid_referral_code', 'Invalid referral code.') :
                                    referralCodeValid === true ? t('referral_code_validated', 'Referral code validated successfully!') :
                                    t('referral_code_helper_text', 'Enter the referral code from an existing member to access premium features.')
                                }
                                InputProps={{
                                    endAdornment: (
                                        <InputAdornment position="end">
                                            {isValidatingCode ? (
                                                <CircularProgress size={20} />
                                            ) : referralCodeValid === true ? (
                                                <CheckCircleIcon color="success" />
                                            ) : referralCodeValid === false && formData.referralCode ? (
                                                <ErrorIcon color="error" />
                                            ) : null}
                                        </InputAdornment>
                                    ),
                                }}
                            />
                        </Box>

                        <Box sx={{ border: theme => `1px solid ${theme.palette.divider}`, p: 3, borderRadius: 1 }}>
                            <Typography variant="h6" component="div" gutterBottom sx={{ mb: 2 }}>
                                {t('personal_information_section_title', 'Personal Information')}
                            </Typography>
                            <Stack spacing={2}>
                                <TextField
                                    select
                                    required
                                    fullWidth
                                    id="title"
                                    name="title"
                                    label={t('title_label', 'Title')}
                                    value={formData.title}
                                    onChange={handleChange}
                                >
                                    {titles.map((option) => (
                                        <MenuItem key={option.value} value={option.value}>
                                            {t(option.label, option.label)}
                                        </MenuItem>
                                    ))}
                                </TextField>
                                <TextField
                                    required
                                    fullWidth
                                    id="firstName"
                                    label={t('first_name_label', 'First Name')}
                                    name="firstName"
                                    autoComplete="given-name"
                                    value={formData.firstName}
                                    onChange={handleChange}
                                />
                                <TextField
                                    required
                                    fullWidth
                                    id="lastName"
                                    label={t('last_name_label', 'Last Name')}
                                    name="lastName"
                                    autoComplete="family-name"
                                    value={formData.lastName}
                                    onChange={handleChange}
                                />
                                <TextField
                                    required
                                    fullWidth
                                    id="dateOfBirth"
                                    label={t('date_of_birth_label', 'Date of Birth')}
                                    name="dateOfBirth"
                                    type="date"
                                    InputLabelProps={{ shrink: true }}
                                    value={formData.dateOfBirth}
                                    onChange={handleChange}
                                />
                                <TextField
                                    select
                                    required
                                    fullWidth
                                    id="sex"
                                    name="sex"
                                    label={t('sex_label', 'Gender')}
                                    value={formData.sex}
                                    onChange={handleChange}
                                >
                                    <MenuItem value="Male">{t('gender_male', 'Male')}</MenuItem>
                                    <MenuItem value="Female">{t('gender_female', 'Female')}</MenuItem>
                                    <MenuItem value="Other">{t('gender_other', 'Other')}</MenuItem>
                                </TextField>
                            </Stack>
                        </Box>

                        <Box sx={{ border: theme => `1px solid ${theme.palette.divider}`, p: 3, borderRadius: 1 }}>
                            <Typography variant="h6" component="div" gutterBottom sx={{ mb: 2 }}>
                                {t('contact_information_section_title', 'Contact Information')}
                            </Typography>
                            <Stack spacing={2}>
                                <TextField
                                    required
                                    fullWidth
                                    id="email"
                                    label={t('email_label', 'Email Address')}
                                    name="email"
                                    autoComplete="email"
                                    value={formData.email}
                                    onChange={handleChange}
                                    disabled={emailVerified}
                                    InputProps={{
                                        endAdornment: (
                                            <InputAdornment position="end">
                                                {emailVerified ? (
                                                    <CheckCircleIcon color="success" />
                                                ) : (
                                                    <Button
                                                        size="small"
                                                        onClick={sendEmailVerification}
                                                        disabled={!formData.email || isVerifyingEmail || emailVerificationSent}
                                                        variant="outlined"
                                                    >
                                                        {isVerifyingEmail ? <CircularProgress size={16} /> : 
                                                         emailVerificationSent ? 'Sent' : 'Verify'}
                                                    </Button>
                                                )}
                                            </InputAdornment>
                                        ),
                                    }}
                                    helperText={
                                        emailVerified ? 'Email verified successfully!' :
                                        emailVerificationSent ? 'Verification email sent. Please check your inbox.' :
                                        'Click "Verify" to send a verification code to your email'
                                    }
                                />
                                {emailVerificationSent && !emailVerified && (
                                    <TextField
                                        fullWidth
                                        id="verificationCode"
                                        label="Email Verification Code"
                                        value={verificationCode}
                                        onChange={(e) => setVerificationCode(e.target.value)}
                                        placeholder="Enter the 6-digit code from your email"
                                        InputProps={{
                                            endAdornment: (
                                                <InputAdornment position="end">
                                                    <Button
                                                        size="small"
                                                        onClick={verifyEmailCode}
                                                        disabled={!verificationCode.trim() || isVerifyingEmail}
                                                        variant="contained"
                                                    >
                                                        {isVerifyingEmail ? <CircularProgress size={16} /> : 'Verify Code'}
                                                    </Button>
                                                </InputAdornment>
                                            ),
                                        }}
                                        sx={{ mt: 2 }}
                                    />
                                )}
                                <TextField
                                    required
                                    fullWidth
                                    id="phoneNumber"
                                    label={t('phone_number_label', 'Phone Number')}
                                    name="phoneNumber"
                                    autoComplete="tel"
                                    value={formData.phoneNumber}
                                    onChange={handleChange}
                                />
                            </Stack>
                        </Box>

                        <Box sx={{ border: theme => `1px solid ${theme.palette.divider}`, p: 3, borderRadius: 1 }}>
                            <Typography variant="h6" component="div" gutterBottom sx={{ mb: 2 }}>
                                {t('address_information_section_title', 'Address Information')}
                            </Typography>
                            <Stack spacing={2}>
                                <TextField
                                    required
                                    fullWidth
                                    id="address"
                                    label={t('address_label', 'Street Address')}
                                    name="address"
                                    autoComplete="street-address"
                                    value={formData.address}
                                    onChange={handleChange}
                                />
                                <TextField
                                    required
                                    fullWidth
                                    id="city"
                                    label={t('city_label', 'City')}
                                    name="city"
                                    autoComplete="address-level2"
                                    value={formData.city}
                                    onChange={handleChange}
                                />
                                <TextField
                                    select
                                    required
                                    fullWidth
                                    id="province"
                                    name="province"
                                    label={t('province_label', 'Province/Territory')}
                                    value={formData.province}
                                    onChange={handleChange}
                                >
                                    {provinces.map((option) => (
                                        <MenuItem key={option.value} value={option.value}>
                                            {t(option.label, option.label)}
                                        </MenuItem>
                                    ))}
                                </TextField>
                                <TextField
                                    required
                                    fullWidth
                                    id="postalCode"
                                    label={t('postal_code_label', 'Postal Code')}
                                    name="postalCode"
                                    autoComplete="postal-code"
                                    value={formData.postalCode}
                                    onChange={handleChange}
                                />
                            </Stack>
                        </Box>

                        <Box sx={{ border: theme => `1px solid ${theme.palette.divider}`, p: 3, borderRadius: 1 }}>
                            <Typography variant="h6" component="div" gutterBottom sx={{ mb: 2 }}>
                                {t('health_security_section_title', 'Health & Security')}
                            </Typography>
                            <Stack spacing={2}>
                                <TextField
                                    required
                                    fullWidth
                                    id="healthInsuranceNumber"
                                    label={t('health_card_number_label', 'Health Card Number (HIN/Care Card)')}
                                    name="healthInsuranceNumber"
                                    value={formData.healthInsuranceNumber}
                                    onChange={handleChange}
                                    inputProps={{
                                        maxLength: 10,
                                        pattern: '[0-9]{10}',
                                        inputMode: 'numeric'
                                    }}
                                    helperText={t('health_card_number_tooltip', 'Please enter exactly 10 digits for your health card number.')}
                                    error={formData.healthInsuranceNumber && (!/^\d{10}$/.test(formData.healthInsuranceNumber))}
                                    InputProps={{
                                        endAdornment: (
                                            <InputAdornment position="end">
                                                <Tooltip title={t('health_card_number_tooltip', 'Please enter exactly 10 digits for your health card number.')}>
                                                    <InfoIcon color="action" />
                                                </Tooltip>
                                            </InputAdornment>
                                        ),
                                    }}
                                />
                                <TextField
                                    required
                                    fullWidth
                                    id="password"
                                    label={t('password_label', 'Password')}
                                    name="password"
                                    type="password"
                                    autoComplete="new-password"
                                    value={formData.password}
                                    onChange={handleChange}
                                    helperText={t('password_helper_text', 'Password must be at least 8 characters.')}
                                />
                                <TextField
                                    required
                                    fullWidth
                                    id="confirmPassword"
                                    label={t('confirm_password_label', 'Confirm Password')}
                                    name="confirmPassword"
                                    type="password"
                                    autoComplete="new-password"
                                    value={formData.confirmPassword}
                                    onChange={handleChange}
                                    error={formData.password !== formData.confirmPassword && formData.confirmPassword !== ''}
                                    helperText={
                                        formData.password !== formData.confirmPassword && formData.confirmPassword !== ''
                                            ? t('error_passwords_do_not_match', 'Passwords do not match.')
                                            : ''
                                    }
                                />
                            </Stack>
                        </Box>

                        <StyledButton
                            type="submit"
                            fullWidth
                            variant="contained"
                            color="primary"
                            disabled={loading || isValidatingCode || referralCodeValid === false || !emailVerified}
                        >
                            {loading ? <CircularProgress size={24} color="inherit" /> : t('register_button', 'Register')}
                        </StyledButton>
                        <Typography align="center">
                            {t('already_have_account', 'Already have an account?')} {' '}
                            <Link href="/login" variant="body2">
                                {t('login_link', 'Login here')}
                            </Link>
                        </Typography>
                    </Stack>
                </StyledForm>
            </StyledPaper>
        </Container>
    );
};

export default Register; 