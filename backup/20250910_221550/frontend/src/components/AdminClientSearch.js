import React, { useState, useEffect, useCallback } from 'react';
import {
    Box,
    Paper,
    TextField,
    IconButton,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Typography,
    Chip,
    Avatar,
    Button,
    InputAdornment,
    CircularProgress,
    Alert,
    Card,
    CardContent,
    Grid,
    Divider
} from '@mui/material';
import {
    Search as SearchIcon,
    Person as PersonIcon,
    Phone as PhoneIcon,
    Email as EmailIcon,
    CalendarToday as CalendarIcon,
    Assignment as AssignmentIcon,
    Visibility as VisibilityIcon,
    Clear as ClearIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useView } from '../context/ViewContext';

const AdminClientSearch = () => {
    const [searchTerm, setSearchTerm] = useState('');
    const [searchResults, setSearchResults] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [selectedClient, setSelectedClient] = useState(null);
    const navigate = useNavigate();
    const { setViewAs } = useView();

    // 防抖搜索
    const debounceTimeout = React.useRef(null);

    const searchClients = useCallback(async (term) => {
        if (!term || term.length < 2) {
            setSearchResults([]);
            return;
        }

        setLoading(true);
        setError(null);

        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`${process.env.REACT_APP_API_URL || 'https://app-backend.mmcwellness.ca'}/api/admin/clients/search?q=${encodeURIComponent(term)}`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`搜索失败: ${response.status}`);
            }

            const data = await response.json();
            if (data.success) {
                setSearchResults(data.data || []);
            } else {
                throw new Error(data.message || '搜索失败');
            }
        } catch (err) {
            console.error('Search error:', err);
            setError(err.message);
        } finally {
            setLoading(false);
        }
    }, []);

    const handleSearchChange = (event) => {
        const value = event.target.value;
        setSearchTerm(value);

        // 清除之前的防抖定时器
        if (debounceTimeout.current) {
            clearTimeout(debounceTimeout.current);
        }

        // 设置新的防抖定时器
        debounceTimeout.current = setTimeout(() => {
            searchClients(value);
        }, 300);
    };

    const handleViewClient = (client) => {
        console.log('AdminClientSearch: Viewing client:', client);
        console.log('AdminClientSearch: Setting view as demographic_no:', client.demographic_no);
        // 设置查看模式为该客户
        setViewAs(client.demographic_no);
        // 导航到客户档案页面
        navigate(`/profile/${client.demographic_no}`);
    };

    const handleViewAppointments = (client) => {
        setViewAs(client.demographic_no);
        navigate(`/appointments/${client.demographic_no}`);
    };

    const handleViewMembership = (client) => {
        setViewAs(client.demographic_no);
        navigate(`/membership/${client.demographic_no}`);
    };

    const formatDate = (dateString) => {
        if (!dateString) return '未知';
        try {
            return new Date(dateString).toLocaleDateString('zh-CN');
        } catch {
            return dateString;
        }
    };

    const getMembershipStatusColor = (status) => {
        if (!status) return 'default';
        if (status.isExpired) return 'error';
        return 'success';
    };

    const getMembershipStatusText = (status) => {
        if (!status) return '未知';
        if (status.isExpired) return '已过期';
        return '有效';
    };

    const clearSearch = () => {
        setSearchTerm('');
        setSearchResults([]);
        setError(null);
    };

    return (
        <Box sx={{ p: 3 }}>
            <Typography variant="h4" component="h1" gutterBottom>
                客户档案搜索
            </Typography>
            
            <Card sx={{ mb: 3 }}>
                <CardContent>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                        搜索客户档案，查看详细信息并快速访问客户数据
                    </Typography>
                    
                    <TextField
                        fullWidth
                        variant="outlined"
                        placeholder="输入客户信息进行搜索：姓名（支持 姓名/姓, 名 格式）、电话（778-777-8888）、9位健康卡号、或档案号码..."
                        value={searchTerm}
                        onChange={handleSearchChange}
                        sx={{ mt: 2 }}
                        InputProps={{
                            startAdornment: (
                                <InputAdornment position="start">
                                    <SearchIcon color="action" />
                                </InputAdornment>
                            ),
                            endAdornment: searchTerm && (
                                <InputAdornment position="end">
                                    <IconButton onClick={clearSearch} size="small">
                                        <ClearIcon />
                                    </IconButton>
                                </InputAdornment>
                            )
                        }}
                    />
                </CardContent>
            </Card>

            {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                </Alert>
            )}

            {loading && (
                <Box display="flex" justifyContent="center" alignItems="center" py={4}>
                    <CircularProgress />
                    <Typography variant="body2" sx={{ ml: 2 }}>
                        搜索中...
                    </Typography>
                </Box>
            )}

            {searchResults.length > 0 && (
                <Paper>
                    <TableContainer>
                        <Table>
                            <TableHead>
                                <TableRow>
                                    <TableCell>客户信息</TableCell>
                                    <TableCell>联系方式</TableCell>
                                    <TableCell>注册时间</TableCell>
                                    <TableCell>会员状态</TableCell>
                                    <TableCell align="center">操作</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {searchResults.map((client) => (
                                    <TableRow key={client.demographic_no} hover>
                                        <TableCell>
                                            <Box display="flex" alignItems="center" gap={2}>
                                                <Avatar sx={{ bgcolor: 'primary.main' }}>
                                                    <PersonIcon />
                                                </Avatar>
                                                <Box>
                                                    <Typography variant="subtitle2" fontWeight="bold">
                                                        {client.last_name} {client.first_name}
                                                    </Typography>
                                                    <Typography variant="caption" color="text.secondary">
                                                        档案号: {client.demographic_no}
                                                    </Typography>
                                                    {client.date_of_birth && (
                                                        <Typography variant="caption" color="text.secondary" display="block">
                                                            生日: {formatDate(client.date_of_birth)}
                                                        </Typography>
                                                    )}
                                                </Box>
                                            </Box>
                                        </TableCell>
                                        
                                        <TableCell>
                                            <Box>
                                                {client.phone && (
                                                    <Box display="flex" alignItems="center" gap={1} mb={0.5}>
                                                        <PhoneIcon fontSize="small" color="action" />
                                                        <Typography variant="body2">
                                                            {client.phone}
                                                        </Typography>
                                                    </Box>
                                                )}
                                                {client.email && (
                                                    <Box display="flex" alignItems="center" gap={1}>
                                                        <EmailIcon fontSize="small" color="action" />
                                                        <Typography variant="body2">
                                                            {client.email}
                                                        </Typography>
                                                    </Box>
                                                )}
                                            </Box>
                                        </TableCell>
                                        
                                        <TableCell>
                                            <Box display="flex" alignItems="center" gap={1}>
                                                <CalendarIcon fontSize="small" color="action" />
                                                <Typography variant="body2">
                                                    {formatDate(client.date_joined)}
                                                </Typography>
                                            </Box>
                                        </TableCell>
                                        
                                        <TableCell>
                                            <Chip
                                                label={getMembershipStatusText(client.membership)}
                                                color={getMembershipStatusColor(client.membership)}
                                                size="small"
                                                variant="outlined"
                                            />
                                        </TableCell>
                                        
                                        <TableCell align="center">
                                            <Box display="flex" gap={1} justifyContent="center">
                                                <Button
                                                    size="small"
                                                    variant="outlined"
                                                    startIcon={<VisibilityIcon />}
                                                    onClick={() => handleViewClient(client)}
                                                >
                                                    档案
                                                </Button>
                                                <Button
                                                    size="small"
                                                    variant="outlined"
                                                    startIcon={<CalendarIcon />}
                                                    onClick={() => handleViewAppointments(client)}
                                                >
                                                    预约
                                                </Button>
                                                <Button
                                                    size="small"
                                                    variant="outlined"
                                                    startIcon={<AssignmentIcon />}
                                                    onClick={() => handleViewMembership(client)}
                                                >
                                                    会员
                                                </Button>
                                            </Box>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </TableContainer>
                </Paper>
            )}

            {searchTerm.length >= 2 && !loading && searchResults.length === 0 && (
                <Paper sx={{ p: 4, textAlign: 'center' }}>
                    <Typography variant="h6" color="text.secondary" gutterBottom>
                        未找到匹配的客户
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                        请尝试使用不同的搜索关键词，或检查拼写是否正确
                    </Typography>
                </Paper>
            )}

            {searchTerm.length > 0 && searchTerm.length < 2 && (
                <Paper sx={{ p: 4, textAlign: 'center' }}>
                    <Typography variant="body2" color="text.secondary">
                        请输入至少2个字符进行搜索
                    </Typography>
                </Paper>
            )}
        </Box>
    );
};

export default AdminClientSearch; 