/**
 * Medical Term Translator Service
 * Provides Chinese translations and explanations for medical test indicators
 */

class MedicalTermTranslator {
    /**
     * Medical test indicators dictionary with Chinese translations and explanations
     */
    static medicalTerms = {
        // Blood Count (Complete Blood Count - CBC)
        'WBC': {
            chinese: '白细胞',
            explanation: '白细胞是人体免疫系统的重要组成部分，负责抵抗感染和疾病。正常值表示免疫系统功能正常。',
            category: '血常规'
        },
        'RBC': {
            chinese: '红细胞',
            explanation: '红细胞负责运输氧气到全身各个组织。正常值表示血液携氧能力良好。',
            category: '血常规'
        },
        'Hemoglobin': {
            chinese: '血红蛋白',
            explanation: '血红蛋白是红细胞中的蛋白质，负责结合和运输氧气。是评估贫血的重要指标。',
            category: '血常规'
        },
        'Hematocrit': {
            chinese: '红细胞压积',
            explanation: '表示血液中红细胞所占的体积百分比，反映血液的浓稠度。',
            category: '血常规'
        },
        'MCV': {
            chinese: '平均红细胞体积',
            explanation: '反映单个红细胞的平均大小，有助于诊断贫血类型。',
            category: '血常规'
        },
        'MCH': {
            chinese: '平均血红蛋白量',
            explanation: '每个红细胞中血红蛋白的平均含量，反映红细胞的携氧能力。',
            category: '血常规'
        },
        'MCHC': {
            chinese: '平均血红蛋白浓度',
            explanation: '红细胞中血红蛋白的浓度，反映红细胞的颜色深浅。',
            category: '血常规'
        },
        'RDW': {
            chinese: '红细胞分布宽度',
            explanation: '反映红细胞大小的一致性，异常值可能提示贫血或其他血液疾病。',
            category: '血常规'
        },
        'Platelet Count': {
            chinese: '血小板计数',
            explanation: '血小板参与血液凝固过程，防止出血。正常值表示凝血功能正常。',
            category: '血常规'
        },
        'Neutrophils': {
            chinese: '中性粒细胞',
            explanation: '最重要的白细胞类型，负责抵抗细菌感染。',
            category: '血常规'
        },
        'Lymphocytes': {
            chinese: '淋巴细胞',
            explanation: '参与免疫反应，产生抗体，抵抗病毒感染。',
            category: '血常规'
        },
        'Monocytes': {
            chinese: '单核细胞',
            explanation: '清除死亡细胞和细菌，参与免疫调节。',
            category: '血常规'
        },
        'Eosinophils': {
            chinese: '嗜酸性粒细胞',
            explanation: '参与过敏反应和寄生虫感染的防御。',
            category: '血常规'
        },
        'Basophils': {
            chinese: '嗜碱性粒细胞',
            explanation: '参与过敏反应和炎症反应。',
            category: '血常规'
        },

        // Urinalysis
        'Pathological Casts': {
            chinese: '病理管型',
            explanation: '尿液中发现的异常管型，可能提示肾脏疾病。',
            category: '尿常规'
        },
        'Crystals': {
            chinese: '结晶',
            explanation: '尿液中的矿物质结晶，可能与结石形成相关。',
            category: '尿常规'
        },
        'Urinalysis Comment': {
            chinese: '尿常规备注',
            explanation: '实验室对尿液检查结果的补充说明。',
            category: '尿常规'
        },

        // Chemistry
        'Glucose': {
            chinese: '血糖',
            explanation: '血液中的糖分含量，是诊断糖尿病的重要指标。',
            category: '生化检查'
        },
        'Creatinine': {
            chinese: '肌酐',
            explanation: '反映肾脏功能的指标，升高可能提示肾功能异常。',
            category: '生化检查'
        },
        'BUN': {
            chinese: '血尿素氮',
            explanation: '反映肾脏排泄功能的指标，与肌酐一起评估肾功能。',
            category: '生化检查'
        },
        'Sodium': {
            chinese: '钠',
            explanation: '重要的电解质，维持体内水分平衡和神经肌肉功能。',
            category: '电解质'
        },
        'Potassium': {
            chinese: '钾',
            explanation: '重要的电解质，维持心脏和肌肉正常功能。',
            category: '电解质'
        },
        'Chloride': {
            chinese: '氯',
            explanation: '重要的电解质，维持体内酸碱平衡。',
            category: '电解质'
        },
        'CO2': {
            chinese: '二氧化碳',
            explanation: '反映体内酸碱平衡状态，是重要的电解质指标。',
            category: '电解质'
        },

        // Liver Function
        'ALT': {
            chinese: '丙氨酸氨基转移酶',
            explanation: '肝脏酶，升高可能提示肝脏损伤或疾病。',
            category: '肝功能'
        },
        'AST': {
            chinese: '天冬氨酸氨基转移酶',
            explanation: '肝脏和心脏酶，升高可能提示肝脏或心脏损伤。',
            category: '肝功能'
        },
        'Alkaline Phosphatase': {
            chinese: '碱性磷酸酶',
            explanation: '肝脏和骨骼酶，升高可能提示肝脏或骨骼疾病。',
            category: '肝功能'
        },
        'Bilirubin': {
            chinese: '胆红素',
            explanation: '红细胞分解产物，升高可能提示肝脏疾病或胆道阻塞。',
            category: '肝功能'
        },
        'Albumin': {
            chinese: '白蛋白',
            explanation: '肝脏产生的主要蛋白质，反映肝脏合成功能。',
            category: '肝功能'
        },

        // Lipid Panel
        'Cholesterol': {
            chinese: '胆固醇',
            explanation: '血液中的脂质，是心血管疾病的重要风险因素。',
            category: '血脂'
        },
        'HDL': {
            chinese: '高密度脂蛋白胆固醇',
            explanation: '"好胆固醇"，有助于清除血管中的胆固醇。',
            category: '血脂'
        },
        'LDL': {
            chinese: '低密度脂蛋白胆固醇',
            explanation: '"坏胆固醇"，过多可能导致动脉粥样硬化。',
            category: '血脂'
        },
        'Triglycerides': {
            chinese: '甘油三酯',
            explanation: '血液中的脂肪，高水平可能增加心血管疾病风险。',
            category: '血脂'
        },

        // Thyroid Function
        'TSH': {
            chinese: '促甲状腺激素',
            explanation: '调节甲状腺功能的激素，是评估甲状腺功能的重要指标。',
            category: '甲状腺功能'
        },
        'T4': {
            chinese: '甲状腺素',
            explanation: '甲状腺产生的主要激素，调节新陈代谢。',
            category: '甲状腺功能'
        },
        'T3': {
            chinese: '三碘甲状腺原氨酸',
            explanation: '甲状腺激素的活性形式，调节新陈代谢。',
            category: '甲状腺功能'
        }
    };

    /**
     * Get translation and explanation for a medical term
     * @param {string} term - Medical term to translate
     * @returns {Object} Translation object with chinese name and explanation
     */
    static translate(term) {
        if (!term) return null;

        // Try exact match first
        if (this.medicalTerms[term]) {
            return this.medicalTerms[term];
        }

        // Try case-insensitive match
        const upperTerm = term.toUpperCase();
        if (this.medicalTerms[upperTerm]) {
            return this.medicalTerms[upperTerm];
        }

        // Try partial match
        for (const [key, value] of Object.entries(this.medicalTerms)) {
            if (term.toLowerCase().includes(key.toLowerCase()) || 
                key.toLowerCase().includes(term.toLowerCase())) {
                return value;
            }
        }

        // Return default for unknown terms
        return {
            chinese: term,
            explanation: '这是一个医学检验指标，具体含义请咨询医生。',
            category: '其他'
        };
    }

    /**
     * Get category for a medical term
     * @param {string} term - Medical term
     * @returns {string} Category name
     */
    static getCategory(term) {
        const translation = this.translate(term);
        return translation ? translation.category : '其他';
    }

    /**
     * Get all terms in a specific category
     * @param {string} category - Category name
     * @returns {Array} Array of terms in the category
     */
    static getTermsByCategory(category) {
        return Object.entries(this.medicalTerms)
            .filter(([key, value]) => value.category === category)
            .map(([key, value]) => ({ term: key, ...value }));
    }
}

module.exports = MedicalTermTranslator; 