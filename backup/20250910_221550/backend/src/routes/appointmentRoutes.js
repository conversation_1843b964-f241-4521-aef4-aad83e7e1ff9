const express = require('express');
const router = express.Router();
const appointmentController = require('../controllers/appointmentController');
const appointmentNoteController = require('../controllers/appointmentNoteController');
const pool = require('../config/database');
const mainServerPool = pool.mainServerPool;
const auth = require('../middleware/auth');
const { requireUser, requireMember } = require('../middleware/userTypeAuth');
const multer = require('multer');

// Helper function to clean and format provider names
function formatProviderName(firstName, lastName, providerType = null) {
    // Clean the first name and last name by removing _number pattern
    const cleanFirstName = firstName ? firstName.replace(/_\d+/g, '').trim() : '';
    const cleanLastName = lastName ? lastName.replace(/_\d+/g, '').trim() : '';
    
    // Filter out Ken Sun completely
    if ((cleanFirstName.toLowerCase() === 'ken' && cleanLastName.toLowerCase() === 'sun') ||
        (cleanFirstName.toLowerCase().includes('ken') && cleanLastName.toLowerCase() === 'sun')) {
        return null; // Return null to indicate this provider should not be displayed
    }
    
    // Format the name
    let formattedName = `${cleanFirstName} ${cleanLastName}`.trim();
    
    // Add Dr. prefix if it's a doctor
    if (providerType === 'doctor' && formattedName) {
        formattedName = `Dr. ${formattedName}`;
    }
    
    return formattedName || 'Unknown Provider';
}
// const path = require('path'); // path is not used in this file after MinIO removal. Can be removed if not used by other logic.

// Configure multer for memory storage, to be used by controllers for Supabase uploads
const uploadToMemory = multer({
    storage: multer.memoryStorage(),
    fileFilter: (req, file, cb) => {
        if (file.mimetype.startsWith('image/')) {
            cb(null, true);
        } else {
            cb(new Error('Not an image! Please upload an image file (jpeg, png, gif).'), false);
        }
    },
    limits: { fileSize: 10 * 1024 * 1024 } // 10MB file size limit (adjust as needed)
});

/**
 * @route   GET /api/appointments
 * @desc    Get user appointments from Oscar database
 * @access  Private
 */
router.get('/', auth, appointmentController.getUserAppointments);

/**
 * @route   GET /api/appointments/guest/:demographicNo
 * @desc    Get appointment history for a guest by demographicNo
 * @access  Public
 */
router.get('/guest/:demographicNo', appointmentController.getGuestAppointmentHistory);

/**
 * @route   GET /api/appointments/debug/check-tables
 * @desc    Debug endpoint to check database tables
 * @access  Public (Temporary, for debugging only)
 */
router.get('/debug/check-tables', appointmentController.checkDatabaseTables);

/**
 * @route   GET /api/appointments/:demographicNo/history-summary
 * @desc    Generate a historical summary of all past appointments for a patient
 * @access  Private
 */
router.get('/:demographicNo/history-summary', auth, appointmentController.getPatientHistorySummary);

/**
 * @route   GET /api/appointments/:demographicNo/half-year-summaries
 * @desc    Get half-year summaries of past appointments for a patient
 * @access  Private
 */
router.get('/:demographicNo/half-year-summaries', auth, appointmentController.getPatientHalfYearSummaries);

/**
 * @route   GET /api/appointments/:demographicNo/summary-for-chatbot
 * @desc    Get the latest cached historical summary for a patient (intended for internal service use)
 * @access  Private (Requires user auth token, performs permission check)
 */
// 注释掉导致错误的路由 - 函数未在appointmentController中定义
// router.get('/:demographicNo/summary-for-chatbot', auth, appointmentController.getPatientSummaryForChatbot);
// 替代方案：使用已经存在的getPatientHistorySummary函数
router.get('/:demographicNo/summary-for-chatbot', auth, appointmentController.getPatientHistorySummary);

/**
 * @route   GET /api/appointments/:demographicNo
 * @desc    Get appointments for a specific (authorized) demographic number
 * @access  Private
 */
router.get('/:demographicNo', auth, appointmentController.getViewedAppointments);

/**
 * @route   POST /api/appointments/lookup
 * @desc    Lookup appointments by last name and phone number
 * @access  Public
 */
router.post('/lookup', appointmentController.getAppointmentByNameAndPhone);

/**
 * @route   POST /api/appointments/cancel/:id
 * @desc    Cancel an appointment
 * @access  Private
 */
router.post('/cancel/:id', auth, async (req, res) => {
  try {
    const appointmentId = req.params.id;
    const userId = req.user.id;
    const { sendNotification, notificationEmail, reason, appointmentDetails } = req.body || {};

    // Verify the appointment belongs to the user
    const userQuery = `
      SELECT user_auth.demographic_no, first_name, last_name FROM user_auth
      LEFT JOIN demographic ON user_auth.demographic_no = demographic.demographic_no
      WHERE user_auth.id = ?
    `;

    const [userResult] = await pool.query(userQuery, [userId]);

    if (!userResult || userResult.length === 0) {
      return res.status(404).json({ message: 'User not found' });
    }

    const demographicNo = userResult[0].demographic_no;
    const patientName = `${userResult[0].first_name || ''} ${userResult[0].last_name || ''}`.trim() || 'Patient';

    // Check if the appointment exists and belongs to the user
    const appointmentCheck = `
      SELECT a.appointment_no, a.appointment_date, a.start_time, a.reason,
             p.first_name as provider_first_name, p.last_name as provider_last_name
      FROM appointment a
      LEFT JOIN provider p ON a.provider_no = p.provider_no
      WHERE a.appointment_no = ? AND a.demographic_no = ?
    `;

    const [checkResult] = await pool.query(appointmentCheck, [appointmentId, demographicNo]);

    if (!checkResult || checkResult.length === 0) {
      return res.status(404).json({
        message: 'Appointment not found or does not belong to this user'
      });
    }

    // Update the appointment status to cancelled
    const updateQuery = `
      UPDATE appointment
      SET
        status = 'C',
        notes = CONCAT(IFNULL(notes, ''), '\n- Cancelled online by patient on ', NOW())
      WHERE appointment_no = ?
    `;

    // 取消预约
    const conn = await pool.getConnection();
    try {
      await conn.query(updateQuery, [appointmentId]);
    } finally {
      conn.release(); // 确保释放连接
    }

    // Send email notification if requested
    if (sendNotification) {
      try {
        const nodemailer = require('nodemailer');

        // 邮件配置
        const EMAIL_USER = '<EMAIL>';
        const EMAIL_PASS = 'cmba qinv nrll rqqp';
        const EMAIL_FROM = 'MMC Wellness <<EMAIL>>';

        // 创建邮件传输器
        const transporter = nodemailer.createTransport({
          host: 'smtp.gmail.com',
          port: 587,
          secure: false,
          auth: {
            user: EMAIL_USER,
            pass: EMAIL_PASS,
          },
        });

        // 预约信息
        const appt = checkResult[0];
        const apptDate = new Date(appt.appointment_date).toLocaleDateString();
        const apptTime = appt.start_time ? appt.start_time.toString().substring(0, 5) : 'N/A';
        const doctorName = formatProviderName(appt.provider_first_name, appt.provider_last_name) || 'N/A';

        // 1. Send notification email to clinic (<EMAIL>)
        await transporter.sendMail({
          from: EMAIL_FROM,
          to: '<EMAIL>',
          subject: `[MMC Wellness] Appointment Cancelled - ${patientName}`,
          html: `
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                      <h2 style="color: #3f51b5;">Appointment Cancellation Notice</h2>
                      <p>The following appointment has been cancelled through the patient portal:</p>
                      <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                        <tr>
                          <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Patient:</td>
                          <td style="padding: 8px; border: 1px solid #ddd;">${patientName}</td>
                        </tr>
                        <tr>
                          <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Date:</td>
                          <td style="padding: 8px; border: 1px solid #ddd;">${apptDate}</td>
                        </tr>
                        <tr>
                          <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Time:</td>
                          <td style="padding: 8px; border: 1px solid #ddd;">${apptTime}</td>
                        </tr>
                        <tr>
                          <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Doctor:</td>
                          <td style="padding: 8px; border: 1px solid #ddd;">${doctorName}</td>
                        </tr>
                        <tr>
                          <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Location:</td>
                          <td style="padding: 8px; border: 1px solid #ddd;">${appt.location === 'online/phone' ? 'Phone/Online Consultation' : '#130 8780 Blundell Rd, Richmond, BC. (Midtown Medical Clinic)'}</td>
                        </tr>
                        <tr>
                          <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Reason:</td>
                          <td style="padding: 8px; border: 1px solid #ddd;">${appt.reason || 'N/A'}</td>
                        </tr>
                        <tr>
                          <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Cancellation Reason:</td>
                          <td style="padding: 8px; border: 1px solid #ddd;">${reason || 'Cancelled through patient portal'}</td>
                        </tr>
                      </table>
                      <p>This is an automated notification. Please do not reply to this email.</p>
                      <p>Best regards,<br>MMC Wellness System</p>
                    </div>
                  `
        });

        console.log('Cancellation notification email <NAME_EMAIL>');

        // 2. Send confirmation email to patient
        const userEmailQuery = `SELECT email FROM user_auth WHERE demographic_no = ?`;
        const [userEmailResult] = await pool.query(userEmailQuery, [userResult[0].demographic_no]);

        if (userEmailResult && userEmailResult.length > 0) {
          const patientEmail = userEmailResult[0].email;

          await transporter.sendMail({
            from: EMAIL_FROM,
            to: patientEmail,
            subject: 'Appointment Cancellation Confirmation - MMC Wellness',
            html: `
                      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <h2 style="color: #d32f2f;">Appointment Cancellation Confirmation</h2>
                        <p>Dear ${patientName},</p>
                        <p>Your appointment has been successfully cancelled. Here are the details of the cancelled appointment:</p>
                        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                          <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Date:</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${apptDate}</td>
                          </tr>
                          <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Time:</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${apptTime}</td>
                          </tr>
                          <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Provider:</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${doctorName}</td>
                          </tr>
                          <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Location:</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${appt.location === 'online/phone' ? 'Phone/Online Consultation' : '#130 8780 Blundell Rd, Richmond, BC. (Midtown Medical Clinic)'}</td>
                          </tr>
                          <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Reason:</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${appt.reason || 'N/A'}</td>
                          </tr>
                        </table>
                        <p><strong>Cancellation Policy:</strong></p>
                        <p>Please note: To cancel appointments, at least 48 hours notice is required, otherwise a $75 fee will be charged. Thank you for your understanding and cooperation.</p>
                        <p>If you need to book a new appointment, please log into your patient portal or contact us directly.</p>
                        <p>Thank you for choosing MMC Wellness!</p>
                        <p>Best regards,<br>MMC Wellness Team</p>
                      </div>
                    `
          });

          console.log(`Cancellation confirmation email sent to patient: ${patientEmail}`);
        }

      } catch (emailError) {
        console.error('Error sending cancellation emails:', emailError);
        // Continue with the process even if email fails
      }
    }

    return res.json({
      success: true,
      message: 'Appointment cancelled successfully'
    });

  } catch (error) {
    console.error('Error cancelling appointment:', error);
    return res.status(500).json({ message: 'Server error' });
  }
});

/**
 * Helper function to determine appointment status
 * @param {string} dbStatus - Status from database (A: active, C: cancelled, etc.)
 * @param {Date} appointmentDate - Date of the appointment
 * @param {Date} currentDate - Current date
 * @returns {string} - Standardized status (confirmed, cancelled, completed, pending)
 */
function getAppointmentStatus(dbStatus, appointmentDate, currentDate) {
  if (!dbStatus) return 'pending';

  // Convert database status to standardized status
  switch (dbStatus.toUpperCase()) {
    case 'A':
      return appointmentDate < currentDate ? 'completed' : 'confirmed';
    case 'C':
      return 'cancelled';
    case 'N': // No show
      return 'cancelled';
    case 'P': // Pending
      return 'pending';
    default:
      return 'pending';
  }
}

/**
 * @route   GET /api/appointments/doctor/:doctorId/:startDate/:endDate?
 * @desc    Get available appointment slots for a doctor
 * @access  Private
 */
router.get('/doctor/:doctorId/:startDate/:endDate?', auth, appointmentController.getDoctorAvailability);

// Create new appointment request
router.post('/', auth, appointmentController.createAppointmentRequest);

// Update appointment status
router.put('/:appointmentId', auth, appointmentController.updateAppointmentStatus);

// Delete appointment request
router.delete('/:appointmentId', auth, appointmentController.deleteAppointmentRequest);

/**
 * @route   POST /api/appointments/book
 * @desc    Book an appointment online through the portal
 * @access  Private (requires authentication)
 */
router.post('/book', auth, uploadToMemory.array('reasonImages', 3), appointmentController.bookAppointment);

/**
 * @route   GET /api/appointments/note/summary/:appointmentId
 * @desc    Get medical notes with AI summary for a specific appointment - Member only
 * @access  Private (Member)
 */
router.get('/note/summary/:appointmentId', auth, requireMember, appointmentNoteController.getAppointmentNoteSummary);

/**
 * @route   GET /api/appointments/note/:appointmentId
 * @desc    Get medical notes for a specific appointment - Member only
 * @access  Private (Member)
 */
router.get('/note/:appointmentId', auth, requireMember, appointmentNoteController.getAppointmentNote);



module.exports = router;