const ReferralService = require('../services/referralService');
const { createLogger } = require('../utils/logger');
const logger = createLogger('referralController');

/**
 * Get a user's referral code
 */
const getReferralCode = async (req, res) => {
    try {
        const demographicNo = parseInt(req.params.demographicNo || req.user.demographic_no);
        
        // Validate permission - user should only be able to access their own referral code
        if (req.user.demographic_no !== demographicNo && req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'You do not have permission to access this resource'
            });
        }
        
        const code = await ReferralService.getReferralCode(demographicNo);
        
        if (!code) {
            return res.status(404).json({
                success: false,
                message: 'Referral code not found'
            });
        }
        
        return res.json({
            success: true,
            referralCode: code
        });
    } catch (error) {
        logger.error('Error getting referral code:', error);
        return res.status(500).json({
            success: false,
            message: 'An error occurred while getting the referral code'
        });
    }
};

/**
 * Get a user's referrals
 */
const getUserReferrals = async (req, res) => {
    try {
        const demographicNo = parseInt(req.params.demographicNo || req.user.demographic_no);
        
        // Validate permission
        if (req.user.demographic_no !== demographicNo && req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'You do not have permission to access this resource'
            });
        }
        
        const referrals = await ReferralService.getUserReferrals(demographicNo);
        const totalPoints = await ReferralService.getTotalRewardPoints(demographicNo);
        
        return res.json({
            success: true,
            referrals,
            totalPoints
        });
    } catch (error) {
        logger.error('Error getting user referrals:', error);
        return res.status(500).json({
            success: false,
            message: 'An error occurred while getting the user referrals'
        });
    }
};

/**
 * Validate a referral code
 */
const validateReferralCode = async (req, res) => {
    try {
        const { code } = req.params;
        
        if (!code) {
            return res.status(400).json({
                success: false,
                message: 'Referral code is required'
            });
        }
        
        const referrerDemographicNo = await ReferralService.validateReferralCode(code);
        
        if (!referrerDemographicNo) {
            return res.status(404).json({
                success: false,
                message: 'Invalid referral code'
            });
        }
        
        return res.json({
            success: true,
            valid: true,
            referrerDemographicNo
        });
    } catch (error) {
        logger.error('Error validating referral code:', error);
        return res.status(500).json({
            success: false,
            message: 'An error occurred while validating the referral code'
        });
    }
};

module.exports = {
    getReferralCode,
    getUserReferrals,
    validateReferralCode
}; 