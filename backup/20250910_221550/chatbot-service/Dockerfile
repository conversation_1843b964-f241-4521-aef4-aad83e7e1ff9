# Use an official Node.js runtime as a parent image
FROM node:20-alpine

# Set the working directory in the container
WORKDIR /usr/src/app

# Copy package.json and package-lock.json (if available)
COPY package*.json ./

# Install app dependencies
RUN npm install
# If you are building your code for production
# RUN npm ci --only=production

# Create directory for health articles
RUN mkdir -p /usr/src/app/documents/health

# Bundle app source
COPY . .

# Make port 3002 available to the world outside this container
EXPOSE 3002

# Define the command to run your app
CMD [ "node", "server.js" ] 