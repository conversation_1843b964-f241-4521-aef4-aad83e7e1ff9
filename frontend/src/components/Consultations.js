import React, { useState, useEffect } from 'react';
import {
    List,
    ListItem,
    ListItemText,
    Typography,
    CircularProgress,
    Alert,
    Paper,
    Divider,
    Box,
    Chip
} from '@mui/material';
import { getConsultations } from '../services/consultationService';
import { useView } from '../context/ViewContext';
import { useLanguage } from '../context/LanguageContext';
import { useTheme } from '@mui/material/styles';

// Helper function to format date strings
const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
        // Handle potential time part (e.g., 01:00:00) if present
        const datePart = dateString.split('T')[0]; // Get YYYY-MM-DD part
        const [year, month, day] = datePart.split('-');
        if (!year || !month || !day) return 'Invalid Date';
        // UsetoLocaleDateString for locale-aware formatting
        return new Date(Date.UTC(year, month - 1, day)).toLocaleDateString(undefined, {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    } catch (e) {
        console.error("Error formatting date:", dateString, e);
        return 'Invalid Date';
    }
};

// Helper to get status display text/color (customize as needed)
const getStatusChip = (status) => {
    let label = `Status: ${status || 'Unknown'}`;
    let color = 'default';
    // Example: Map known status codes (expand this based on actual codes)
    switch (status) {
        case '2': label = 'Booked'; color = 'primary'; break;
        case '4': label = 'Received/Pending'; color = 'warning'; break;
        // Add more cases for other statuses found in the data
    }
    return <Chip label={label} color={color} size="small" sx={{ mr: 1 }} />;
};

// Helper to get urgency display text/color (customize as needed)
const getUrgencyChip = (urgency) => {
    let label = `Urgency: ${urgency || 'Unknown'}`;
    let color = 'default';
    // Example: Map known urgency codes (expand this based on actual codes)
    switch (urgency?.toLowerCase()) {
        case 'sz': label = 'Semi-Urgent'; color = 'info'; break;
        case 'nd': label = 'Routine'; color = 'success'; break; // Assuming 'nd' is routine
        case '2': label = 'Routine'; color = 'success'; break; // Assuming '2' is routine
        // Add more cases
    }
    return <Chip label={label} color={color} size="small" />;
}

const Consultations = ({ viewedDemographic }) => {
    const [consultations, setConsultations] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const { loggedInUser } = useView(); // Get logged-in user info
    const theme = useTheme();

    useEffect(() => {
        const fetchConsultations = async () => {
            if (!viewedDemographic?.demographic_no) {
                setError('Cannot load consultations without a patient selected.');
                setLoading(false);
                return;
            }

            setLoading(true);
            setError(null);
            try {
                const data = await getConsultations(viewedDemographic.demographic_no);
                setConsultations(data || []); // Ensure it's always an array
            } catch (err) {
                setError(err.response?.data?.message || err.message || 'An error occurred while fetching consultations.');
            } finally {
                setLoading(false);
            }
        };

        fetchConsultations();
    }, [viewedDemographic]); // Refetch if the viewed patient changes

    if (loading) {
        return <CircularProgress />;
    }

    if (error) {
        return <Alert severity="error">{error}</Alert>;
    }

    return (
        <Paper elevation={3} sx={{ p: 2, mt: 2 }}>
            <Typography variant="h6" gutterBottom component="div" sx={{ color: theme.palette.primary.main }}>
                Consultation Requests
            </Typography>
            {consultations.length === 0 ? (
                <Typography>No consultation requests found.</Typography>
            ) : (
                <List disablePadding>
                    {consultations.map((consult, index) => (
                        <React.Fragment key={consult.id || index}>
                            <ListItem alignItems="flex-start" sx={{ flexDirection: 'column' }}>
                                <ListItemText
                                    primary={`To: ${consult.specialistName || 'N/A'} (${consult.specialistType || consult.serviceDescription || 'N/A'})`}
                                    secondary={`Referred: ${formatDate(consult.referralDate)} | Appt: ${consult.appointmentDate ? formatDate(consult.appointmentDate) + (consult.appointmentTime ? ` at ${consult.appointmentTime}` : '') : 'Not Booked'}`}
                                    primaryTypographyProps={{ fontWeight: 'medium' }}
                                />
                                <Box sx={{ my: 1, width: '100%' }}>
                                    {getStatusChip(consult.status)}
                                    {getUrgencyChip(consult.urgency)}
                                </Box>
                                {consult.reason && (
                                    <Typography variant="body2" sx={{ mt: 1, mb: 1, whiteSpace: 'pre-wrap' }}>
                                        <strong>Reason:</strong> {consult.reason}
                                    </Typography>
                                )}
                                {consult.statusText && (
                                    <Typography variant="body2" color="text.secondary" sx={{ whiteSpace: 'pre-wrap' }}>
                                        <strong>Notes:</strong> {consult.statusText}
                                    </Typography>
                                )}
                            </ListItem>
                            {index < consultations.length - 1 && <Divider component="li" />}
                        </React.Fragment>
                    ))}
                </List>
            )}
        </Paper>
    );
};

export default Consultations; 