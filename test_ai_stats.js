const mysql = require('mysql2/promise');

const pool = mysql.createPool({
  host: '**************',
  user: 'mmcwebapp',
  password: 'mmcwebapp2024',
  database: 'mmcwebapp',
  charset: 'utf8mb4',
  timezone: '+00:00'
});

async function checkAIStats() {
  try {
    console.log('=== Latest AI Usage Records ===');
    const [rows] = await pool.execute(
      'SELECT * FROM ai_usage_stats ORDER BY created_at DESC LIMIT 5'
    );
    console.log(JSON.stringify(rows, null, 2));
    
    console.log('\n=== AI Usage Summary ===');
    const [summary] = await pool.execute(`
      SELECT 
        provider, 
        service_type, 
        model_name, 
        COUNT(*) as total_calls,
        SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_calls,
        AVG(response_time_ms) as avg_response_time
      FROM ai_usage_stats 
      GROUP BY provider, service_type, model_name 
      ORDER BY total_calls DESC
    `);
    console.log(JSON.stringify(summary, null, 2));
    
  } catch (error) {
    console.error('Database Error:', error.message);
  } finally {
    await pool.end();
  }
}

checkAIStats(); 