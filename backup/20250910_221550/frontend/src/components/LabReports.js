import React, { useState, useEffect } from 'react';
import { API_URL } from '../utils/env';
import { useNavigate, useParams } from 'react-router-dom';
import {
    Container,
    Paper,
    Typography,
    List,
    ListItem,
    ListItemText,
    Divider,
    Box,
    Alert,
    CircularProgress,
    Button,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    IconButton,
    Chip,
    Tabs,
    Tab,
    Tooltip
} from '@mui/material';
import { styled } from '@mui/material/styles';
import SwitchAccountIcon from '@mui/icons-material/SwitchAccount';
import DownloadIcon from '@mui/icons-material/Download';
import VisibilityIcon from '@mui/icons-material/Visibility';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import ScienceIcon from '@mui/icons-material/Science';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { format } from 'date-fns';
import { useView } from '../context/ViewContext';
import { useLanguage } from '../context/LanguageContext';
import { Accordion, AccordionSummary, AccordionDetails } from '@mui/material';

const StyledPaper = styled(Paper)(({ theme }) => ({
    marginTop: theme.spacing(3),
    padding: theme.spacing(3),
}));

const TabPanel = (props) => {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`doc-tabpanel-${index}`}
            aria-labelledby={`doc-tab-${index}`}
            {...other}
        >
            {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
        </div>
    );
};

const Documents = () => {
    const { t } = useLanguage();
    const {
        loggedInUser,
        viewingDemographicNo,
        setViewAs,
        isViewingOwnProfile,
        isLoading: contextLoading,
        error: contextError
    } = useView();

    const navigate = useNavigate();
    const { demographicNo: urlDemographicNo } = useParams();

    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [documents, setDocuments] = useState([]);
    const [documentTypes, setDocumentTypes] = useState([]);
    const [tabValue, setTabValue] = useState(0);
    const [viewingDocument, setViewingDocument] = useState(null);
    const [documentUrl, setDocumentUrl] = useState('');

    const [viewingHl7Message, setViewingHl7Message] = useState(null);
    const [hl7Results, setHl7Results] = useState([]);

    const handleTabChange = (event, newValue) => {
        setTabValue(newValue);
    };

    // 根据文档类型获取标签页颜色
    const getTabColor = (type) => {
        switch (type) {
            case 'eform':
                return '#3498db'; // 蓝色
            case 'lab':
                return '#2ecc71'; // 绿色
            case 'medical imaging':
                return '#9b59b6'; // 紫色
            case 'consult':
                return '#e67e22'; // 橙色
            case 'referral':
                return '#e74c3c'; // 红色
            case 'lifelab':
                return '#f1c40f'; // 黄色
            case 'others':
            default:
                return '#7f8c8d'; // 灰色
        }
    };

    // 定义类型归一化映射
    const typeMap = {
        'eform': ['eform', 'e-form'],
        'lab': ['lab', 'lab result', 'labs', 'laboratory'],
        'medical imaging': ['medical imaging', 'imaging', 'radiology', 'xray', 'x-ray'],
        'consult': ['consult', 'consultation', 'pathology'],
        'referral': ['referral', 'refer']
    };

    const getNormalizedType = (docType) => {
        if (!docType) return '';
        const typeStr = docType.toLowerCase().replace(/[_\-\s]+/g, ' ');
        for (const [key, aliases] of Object.entries(typeMap)) {
            if (aliases.some(alias => typeStr.includes(alias))) {
                return key;
            }
        }
        return '';
    };

    // Handle URL parameter for admin viewing other profiles
    useEffect(() => {
        if (urlDemographicNo && loggedInUser) {
            const targetDemographicNo = parseInt(urlDemographicNo, 10);
            
            // Check if user is admin or if it's their own profile
            if (loggedInUser.role === 'admin' || targetDemographicNo === loggedInUser.demographic_no) {
                console.log(`Admin or self viewing lab-reports: ${targetDemographicNo}`);
                // Only set if different from current viewing demographic
                if (viewingDemographicNo !== targetDemographicNo) {
                    setViewAs(targetDemographicNo);
                }
            } else {
                console.log('Non-admin user trying to view other lab-reports, redirecting to own lab-reports');
                navigate('/lab-reports');
            }
        } else if (!urlDemographicNo && loggedInUser?.demographic_no) {
            // No URL parameter, ensure viewing own profile only if not already set
            if (viewingDemographicNo !== loggedInUser.demographic_no) {
                setViewAs(loggedInUser.demographic_no);
            }
        }
    }, [urlDemographicNo, loggedInUser, setViewAs, navigate, viewingDemographicNo]);

    useEffect(() => {
        console.log('LabReports: Effect running with viewingDemographicNo:', viewingDemographicNo,
            'isViewingOwnProfile:', isViewingOwnProfile,
            'REACT_APP_API_URL:', API_URL);

        const fetchDocuments = async () => {
            try {
                if (!viewingDemographicNo) {
                    console.log('LabReports: Waiting for viewingDemographicNo...');
                    setLoading(contextLoading);
                    return;
                }

                console.log(`LabReports: Fetching data for demographic_no: ${viewingDemographicNo}`);
                setLoading(true);
                setError('');
                setDocuments([]);
                setHl7Results([]);
                setDocumentTypes([]);

                // 获取文档列表
                const token = localStorage.getItem('token');
                if (!token) {
                    setError(t('not_authenticated'));
                    setLoading(false);
                    return;
                }

                // 同时获取文档和HL7数据
                const [documentsResponse, hl7Response] = await Promise.all([
                    // 获取文档
                    fetch(isViewingOwnProfile
                        ? `${API_URL}/api/lab-results/reports`
                        : `${API_URL}/api/lab-results/reports/demographic/${viewingDemographicNo}`, {
                        headers: { Authorization: `Bearer ${token}` }
                    }),
                    // 获取HL7数据
                    fetch(isViewingOwnProfile
                        ? `${API_URL}/api/lab-results/hl7`
                        : `${API_URL}/api/lab-results/hl7/demographic/${viewingDemographicNo}`, {
                        headers: { Authorization: `Bearer ${token}` }
                    })
                ]);

                // 处理文档响应
                if (documentsResponse.ok) {
                    const documentsData = await documentsResponse.json();
                    const allDocuments = documentsData.reports || [];
                    setDocuments(allDocuments);

                    // 归一化类型
                    allDocuments.forEach(doc => {
                        doc.type = getNormalizedType(doc.type);
                    });
                } else {
                    console.error('Failed to fetch documents:', documentsResponse.status);
                }

                // 处理HL7响应
                if (hl7Response.ok) {
                    const hl7Data = await hl7Response.json();
                    setHl7Results(hl7Data.results || []);
                } else {
                    console.error('Failed to fetch HL7 results:', hl7Response.status);
                }

                // 设置文档类型，包括Lifelab
                const allPossibleTypes = ['eform', 'lab', 'medical imaging', 'consult', 'lifelab'];
                setDocumentTypes(allPossibleTypes);
                setTabValue(0);

            } catch (err) {
                console.error('Error fetching data:', err);
                setError(err.message || t('fetch_error'));

                if (err.message && err.message.includes('Permission denied') && loggedInUser) {
                    setViewAs(loggedInUser.demographic_no);
                }
            } finally {
                setLoading(false);
            }
        };

        fetchDocuments();
    }, [viewingDemographicNo, contextLoading, loggedInUser, setViewAs, isViewingOwnProfile, t]);

    const handleSwitchBack = () => {
        if (loggedInUser?.demographic_no) {
            console.log(`LabReports: Switching back to own profile: ${loggedInUser.demographic_no}`);
            setViewAs(loggedInUser.demographic_no);
        }
    }

    const handleViewDocument = (documentId) => {
        const token = localStorage.getItem('token');
        const documentUrlWithToken = `${API_URL}/api/lab-results/report/${documentId}?token=${token}`;
        console.log('LabReports: View document URL:', documentUrlWithToken);
        setDocumentUrl(documentUrlWithToken);
        setViewingDocument(documentId);
    };

    const handleDownloadDocument = (documentId, filename) => {
        const token = localStorage.getItem('token');
        const documentUrl = `${API_URL}/api/lab-results/report/${documentId}?token=${token}`;
        console.log('LabReports: Download document URL:', documentUrl);

        // 创建一个隐藏的a标签用于下载
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = documentUrl;
        a.download = filename || `document-${documentId}.pdf`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    };

    const handleCloseDocumentViewer = () => {
        setViewingDocument(null);
        setDocumentUrl('');
    };

    const handleCloseHl7Viewer = () => {
        setViewingHl7Message(null);
    };

    const handleViewHl7Message = async (labId) => {
        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`${API_URL}/api/lab-results/hl7/parsed/${labId}`, {
                headers: { Authorization: `Bearer ${token}` }
            });

            if (response.ok) {
                const data = await response.json();
                setViewingHl7Message(data.data);
            } else {
                throw new Error('Failed to fetch HL7 message');
            }
        } catch (error) {
            console.error('Error fetching HL7 message:', error);
            setError(`Failed to fetch HL7 message: ${error.message}`);
        }
    };



    // 将日期格式化为易读的形式
    const formatDate = (dateString) => {
        if (!dateString) return t('unknown_date');
        try {
            return format(new Date(dateString), 'yyyy-MM-dd');
        } catch (e) {
            return dateString;
        }
    };

    // 获取文档类型标签的颜色
    const getDocTypeColor = (type) => {
        // 使用与标签页相同的颜色方案
        return 'default'; // 我们将在Chip的sx属性中直接设置颜色
    };

    // Combine loading and error states
    const isLoading = loading || contextLoading;
    const displayError = error || contextError;

    return (
        <Container maxWidth="md">
            {!isViewingOwnProfile && loggedInUser && (
                <Alert
                    severity="info"
                    sx={{ mt: 2, mb: 1 }}
                    action={
                        <Button
                            color="inherit"
                            size="small"
                            onClick={handleSwitchBack}
                            startIcon={<SwitchAccountIcon />}
                        >
                            {t('switch_back')}
                        </Button>
                    }
                >
                    {t('viewing_family_lab_reports')}
                </Alert>
            )}

            <Typography variant="h4" component="h1" gutterBottom>
                {isViewingOwnProfile ? t('your_medical_documents') : t('family_medical_documents')}
            </Typography>

            {displayError && (
                <Alert severity="error" sx={{ mb: 2 }}>
                    {displayError}
                </Alert>
            )}

            <StyledPaper elevation={3}>
                {isLoading ? (
                    <Box display="flex" justifyContent="center" my={4}>
                        <CircularProgress />
                    </Box>
                ) : documents.length === 0 && hl7Results.length === 0 ? (
                    <Alert severity="info">
                        {t('no_documents')}
                    </Alert>
                ) : (
                    <>
                        <Tabs
                            value={tabValue}
                            onChange={handleTabChange}
                            aria-label="document type tabs"
                            variant="scrollable"
                            scrollButtons="auto"
                        >
                            {documentTypes.map((type, index) => (
                                <Tab
                                    key={type}
                                    label={
                                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                            <Chip
                                                label={t(type.toLowerCase())}
                                                size="small"
                                                sx={{
                                                    mr: 0.5,
                                                    backgroundColor: getTabColor(type),
                                                    color: 'white',
                                                    fontWeight: 'bold'
                                                }}
                                            />
                                        </Box>
                                    }
                                    id={`doc-tab-${index}`}
                                    aria-controls={`doc-tabpanel-${index}`}
                                />
                            ))}
                        </Tabs>

                        {documentTypes.map((type, index) => {
                            if (type === 'lifelab') {
                                // Lifelab Results Tab
                                return (
                                    <TabPanel key={type} value={tabValue} index={index}>
                                        {hl7Results.length === 0 ? (
                                            <Box sx={{ p: 2, textAlign: 'center' }}>
                                                <Typography variant="body1" color="text.secondary">
                                                    No Lifelab reports available
                                                </Typography>
                                            </Box>
                                        ) : (
                                            <List>
                                                {hl7Results.map((result) => (
                                                    <React.Fragment key={result.id}>
                                                        <ListItem>
                                                            <ListItemText
                                                                primary={
                                                                                                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        <ScienceIcon sx={{ mr: 1, color: getTabColor('lifelab') }} />
                                        <span>{result.discipline || 'Lab Report'}</span>
                                                                        <Chip
                                                                            label="Lifelab"
                                                                            size="small"
                                                                            sx={{ 
                                                                                ml: 1, 
                                                                                backgroundColor: getTabColor('lifelab'),
                                                                                color: 'white',
                                                                                fontSize: '0.7rem' 
                                                                            }}
                                                                        />
                                                                    </Box>
                                                                }
                                                                secondary={
                                                                    <Box>
                                                                        <Typography variant="body2" color="text.secondary">
                                                                            Patient: {result.patientName}
                                                                        </Typography>
                                                                        <Typography variant="body2" color="text.secondary">
                                                                            Requesting Client: {result.requestingClient}
                                                                        </Typography>
                                                                        <Typography variant="body2" color="text.secondary">
                                                                            Date: {formatDate(result.created)}
                                                                        </Typography>
                                                                    </Box>
                                                                }
                                                            />
                                                            <IconButton
                                                                edge="end"
                                                                aria-label="view hl7 message"
                                                                onClick={() => handleViewHl7Message(result.id)}
                                                                title="View Lifelab Report"
                                                            >
                                                                <VisibilityIcon />
                                                            </IconButton>
                                                        </ListItem>
                                                        <Divider />
                                                    </React.Fragment>
                                                ))}
                                            </List>
                                        )}
                                    </TabPanel>
                                );
                            } else {
                                // 现有的文档处理逻辑
                                const filteredDocs = documents.filter(doc => doc.type === type && doc.fileExists !== false);

                                return (
                                    <TabPanel key={type} value={tabValue} index={index}>
                                        {filteredDocs.length === 0 ? (
                                            <Box sx={{ p: 2, textAlign: 'center' }}>
                                                <Typography variant="body1" color="text.secondary">
                                                    {t('no_documents_of_type', { type: t(type.toLowerCase()) })}
                                                </Typography>
                                            </Box>
                                        ) : (
                                            <List>
                                                {filteredDocs.map((doc) => (
                                                    <React.Fragment key={doc.id}>
                                                        <ListItem>
                                                            <ListItemText
                                                                primary={
                                                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                                        <span>{doc.title || t('document')}</span>
                                                                        <Chip
                                                                            label={doc.type || 'unknown'}
                                                                            size="small"
                                                                            color={getDocTypeColor(doc.type)}
                                                                            sx={{ ml: 1, fontSize: '0.7rem' }}
                                                                        />
                                                                    </Box>
                                                                }
                                                                secondary={`${t('date')}: ${formatDate(doc.observationDate || doc.updatedAt)}`}
                                                            />
                                                            <IconButton
                                                                edge="end"
                                                                aria-label={t('view')}
                                                                onClick={() => handleViewDocument(doc.id)}
                                                                title="查看报告"
                                                            >
                                                                <VisibilityIcon />
                                                            </IconButton>
                                                            <IconButton
                                                                edge="end"
                                                                aria-label={t('download')}
                                                                onClick={() => handleDownloadDocument(doc.id, doc.filename)}
                                                                title="下载报告"
                                                            >
                                                                <DownloadIcon />
                                                            </IconButton>
                                                        </ListItem>
                                                        <Divider />
                                                    </React.Fragment>
                                                ))}
                                            </List>
                                        )}
                                    </TabPanel>
                                );
                            }
                        })}
                    </>
                )}
            </StyledPaper>

            {/* 文档查看对话框 */}
            <Dialog
                open={!!viewingDocument}
                onClose={handleCloseDocumentViewer}
                maxWidth="xl"
                fullWidth
            >
                <DialogTitle>
                    {t('document_viewer')}
                    <IconButton
                        aria-label={t('close')}
                        onClick={handleCloseDocumentViewer}
                        sx={{ position: 'absolute', right: 8, top: 8 }}
                    >
                        &times;
                    </IconButton>
                </DialogTitle>
                <DialogContent dividers sx={{ height: '80vh', display: 'flex' }}>
                    <Box sx={{ width: '100%' }}>
                        {documentUrl && (
                            <iframe
                                src={documentUrl}
                                title={t('document_viewer')}
                                width="100%"
                                height="100%"
                                style={{ border: 'none' }}
                            />
                        )}
                    </Box>
                    {false && (
                        <Box sx={{ width: '50%', pl: 1, borderLeft: '1px solid #e0e0e0', overflowY: 'auto' }}>
                            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                                <SmartToyIcon sx={{ mr: 1, color: 'primary.main' }} />
                                AI智能分析
                            </Typography>
                            {aiAnalyzing ? (
                                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 4 }}>
                                    <CircularProgress />
                                    <Typography variant="body2" sx={{ mt: 2 }}>
                                        正在分析报告，请稍候...
                                    </Typography>
                                </Box>
                            ) : aiAnalysis ? (
                                <Box>
                                    {/* 风险评估 */}
                                    <Paper sx={{ p: 2, mb: 2, bgcolor: 'background.default' }}>
                                        <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold' }}>
                                            🎯 风险评估
                                        </Typography>
                                        <Chip
                                            label={aiAnalysis.analysis.riskAssessment.description}
                                            color={
                                                aiAnalysis.analysis.riskAssessment.level === 'low' ? 'success' :
                                                aiAnalysis.analysis.riskAssessment.level === 'moderate' ? 'warning' : 'error'
                                            }
                                            size="small"
                                        />
                                    </Paper>

                                    {/* 指标统计 */}
                                    <Paper sx={{ p: 2, mb: 2, bgcolor: 'background.default' }}>
                                        <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold' }}>
                                            📊 指标统计
                                        </Typography>
                                        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                                            <Chip label={`正常: ${aiAnalysis.analysis.healthIndicators.normal.length}`} color="success" size="small" />
                                            <Chip label={`边界: ${aiAnalysis.analysis.healthIndicators.borderline.length}`} color="warning" size="small" />
                                            <Chip label={`异常: ${aiAnalysis.analysis.healthIndicators.abnormal.length}`} color="error" size="small" />
                                        </Box>
                                    </Paper>

                                    {/* AI分析 */}
                                    <Paper sx={{ p: 2, mb: 2, bgcolor: 'background.default' }}>
                                        <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold' }}>
                                            🤖 AI详细分析
                                        </Typography>
                                        <Typography variant="body2" sx={{ whiteSpace: 'pre-line', fontSize: '0.875rem' }}>
                                            {aiAnalysis.analysis.aiAnalysis}
                                        </Typography>
                                    </Paper>

                                    {/* 健康建议 */}
                                    <Paper sx={{ p: 2, bgcolor: 'background.default' }}>
                                        <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold' }}>
                                            💡 健康建议
                                        </Typography>
                                        <List dense>
                                            {aiAnalysis.analysis.recommendations.map((recommendation, index) => (
                                                <ListItem key={index} sx={{ py: 0.5 }}>
                                                    <ListItemText 
                                                        primary={recommendation}
                                                        primaryTypographyProps={{ fontSize: '0.875rem' }}
                                                    />
                                                </ListItem>
                                            ))}
                                        </List>
                                    </Paper>

                                    <Alert severity="warning" sx={{ mt: 2, fontSize: '0.75rem' }}>
                                        ⚠️ AI分析仅供参考，不能替代专业医疗诊断。如有异常，请及时咨询医生。
                                    </Alert>
                                </Box>
                            ) : null}
                        </Box>
                    )}
                </DialogContent>
                <DialogActions>
                    {/* <Button
                        onClick={handleAiAnalysis}
                        color="primary"
                        startIcon={aiAnalyzing ? <CircularProgress size={20} /> : <SmartToyIcon />}
                        disabled={aiAnalyzing}
                    >
                        {aiAnalyzing ? '分析中...' : 'AI解释'}
                    </Button> */}
                    <Button
                        onClick={() => handleDownloadDocument(viewingDocument, documents.find(d => d.id === viewingDocument)?.filename)}
                        color="primary"
                        startIcon={<DownloadIcon />}
                    >
                        {t('download')}
                    </Button>
                    <Button onClick={handleCloseDocumentViewer} color="secondary">
                        {t('close')}
                    </Button>
                </DialogActions>
            </Dialog>

            {/* HL7 Message Viewer Dialog */}
            <Dialog
                open={!!viewingHl7Message}
                onClose={handleCloseHl7Viewer}
                maxWidth="lg"
                fullWidth
            >
                <DialogTitle>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <ScienceIcon sx={{ mr: 1, color: getTabColor('lifelab') }} />
                        Lifelab Report - {viewingHl7Message?.discipline}
                    </Box>
                    <IconButton
                        aria-label="close"
                        onClick={handleCloseHl7Viewer}
                        sx={{ position: 'absolute', right: 8, top: 8 }}
                    >
                        &times;
                    </IconButton>
                </DialogTitle>
                <DialogContent dividers>
                    {viewingHl7Message && (
                        <Box>
                            {/* Patient Information */}
                            <Paper sx={{ p: 2, mb: 2, bgcolor: 'background.default' }}>
                                <Typography variant="h6" gutterBottom>
                                    Patient Information
                                </Typography>
                                <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                                    <Typography variant="body2">
                                        <strong>Name:</strong> {viewingHl7Message.patientName}
                                    </Typography>
                                    <Typography variant="body2">
                                        <strong>HIN:</strong> {viewingHl7Message.hin}
                                    </Typography>
                                    <Typography variant="body2">
                                        <strong>Discipline:</strong> {viewingHl7Message.discipline}
                                    </Typography>
                                    <Typography variant="body2">
                                        <strong>Requesting Client:</strong> {viewingHl7Message.requestingClient}
                                    </Typography>
                                    <Typography variant="body2">
                                        <strong>Date:</strong> {formatDate(viewingHl7Message.created)}
                                    </Typography>
                                </Box>
                            </Paper>

                            {/* Lab Results Table */}
                            {viewingHl7Message.humanReadable?.readable && viewingHl7Message.humanReadable.results.length > 0 && (
                                <Paper sx={{ p: 2, mb: 2, bgcolor: 'background.default' }}>
                                    <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                                        <span style={{ marginRight: '8px' }}>📊</span>
                                        血液化验指标
                                    </Typography>
                                    <Box sx={{ maxHeight: '400px', overflowY: 'auto' }}>
                                        <Box sx={{ 
                                            display: { xs: 'none', sm: 'grid' }, 
                                            gridTemplateColumns: '2fr 1fr 1fr 1.5fr 1fr',
                                            gap: 1,
                                            p: 1.2,
                                            bgcolor: '#263238',
                                            color: '#fff',
                                            fontWeight: 700,
                                            fontSize: '0.9rem',
                                            borderRadius: 1,
                                            mb: 1
                                        }}>
                                            <Box>项目</Box>
                                            <Box>数值</Box>
                                            <Box>单位</Box>
                                            <Box>参考范围</Box>
                                            <Box>状态</Box>
                                        </Box>
                                        {viewingHl7Message.humanReadable.results.map((result, index) => (
                                            <Box key={index} sx={{ 
                                                display: 'grid', 
                                                gridTemplateColumns: { xs: '1fr', sm: '2fr 1fr 1fr 1.5fr 1fr' },
                                                gap: { xs: 0.5, sm: 1 },
                                                p: { xs: 1.2, sm: 1 },
                                                border: { xs: '1px solid #eee', sm: 'none' },
                                                borderRadius: { xs: 1, sm: 0 },
                                                mb: { xs: 1, sm: 0 },
                                                borderBottom: { xs: 'none', sm: '1px solid #e0e0e0' },
                                                bgcolor: result.abnormalFlag === 'Abnormal' ? '#fdecea' : ((result.abnormalFlag === 'High' || result.abnormalFlag === 'Low') ? '#fff8e1' : '#ffffff'),
                                                '&:hover': {
                                                    bgcolor: result.abnormalFlag === 'Abnormal' ? '#fbd5d0' : ((result.abnormalFlag === 'High' || result.abnormalFlag === 'Low') ? '#ffefc1' : '#f7f7f7')
                                                }
                                            }}>
                                                <Tooltip 
                                                    title={result.explanation || '暂无详细解释'} 
                                                    placement="top-start"
                                                    arrow
                                                >
                                                    <Box sx={{ fontWeight: 'bold', fontSize: { xs: '1rem', sm: '0.875rem' }, cursor: 'help' }}>
                                                        <Box>{result.testName}</Box>
                                                        {result.testNameChinese && result.testNameChinese !== result.testName && (
                                                            <Box sx={{ fontSize: '0.75rem', color: 'text.secondary', mt: 0.5 }}>
                                                                {result.testNameChinese}
                                                            </Box>
                                                        )}
                                                    </Box>
                                                </Tooltip>
                                                <Box sx={{ fontSize: { xs: '1rem', sm: '0.875rem' }, whiteSpace: 'pre-line' }}>
                                                    {result.value}
                                                    <Box component="span" sx={{ display: { xs: 'inline', sm: 'none' }, color: 'text.secondary', ml: 0.5 }}>
                                                        {result.unit}
                                                    </Box>
                                                </Box>
                                                <Box sx={{ fontSize: '0.875rem', color: 'text.secondary', display: { xs: 'none', sm: 'block' } }}>
                                                    {result.unit}
                                                </Box>
                                                <Box sx={{ fontSize: '0.875rem', color: 'text.secondary', display: { xs: 'none', sm: 'block' } }}>
                                                    {result.referenceRange}
                                                </Box>
                                                <Box sx={{ display: { xs: 'block', sm: 'none' }, fontSize: '0.75rem', color: 'text.secondary', mt: 0.5 }}>
                                                    参考范围: {result.referenceRange}
                                                </Box>
                                                <Box sx={{ justifySelf: { xs: 'start', sm: 'unset' } }}>
                                                    <Chip 
                                                        label={result.abnormalFlag} 
                                                        size="small" 
                                                        color={
                                                            result.abnormalFlag === 'Normal' ? 'success' :
                                                            (result.abnormalFlag === 'High' || result.abnormalFlag === 'Low') ? 'warning' : 'error'
                                                        }
                                                        sx={{ fontSize: '0.75rem' }}
                                                    />
                                                </Box>
                                            </Box>
                                        ))}
                                    </Box>
                                </Paper>
                            )}


                        </Box>
                    )}
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseHl7Viewer} color="secondary">
                        {t('close')}
                    </Button>
                </DialogActions>
            </Dialog>
        </Container>
    );
};

export default Documents;