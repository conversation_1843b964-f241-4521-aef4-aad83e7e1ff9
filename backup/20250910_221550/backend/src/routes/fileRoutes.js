const express = require('express');
const router = express.Router();
const multer = require('multer');
const fileController = require('../controllers/fileController');
const auth = require('../middleware/auth');

// 配置文件上传中间件
const upload = multer({
    storage: multer.memoryStorage(),
    limits: {
        fileSize: 10 * 1024 * 1024 // 限制10MB
    }
});

// 需要认证的路由
router.use(auth);

// 获取用户文件列表
router.get('/', fileController.getUserFiles);

// 获取共享给用户的文件列表
router.get('/shared', fileController.getSharedFiles);

// 上传文件
router.post('/upload', upload.single('file'), fileController.uploadFile);

// 下载文件
router.get('/:fileId/download', fileController.downloadFile);

// 删除文件
router.delete('/:fileId', fileController.deleteFile);

// 共享文件
router.post('/:fileId/share', fileController.shareFile);

// 获取文件共享情况
router.get('/:fileId/shares', fileController.getFileShares);

// 商品图片上传路由
router.post('/upload/product-image', 
    fileController.uploadProductImage.single('image'), 
    fileController.uploadProductImageHandler
);

// 删除商品图片
router.delete('/product-image/:filename', fileController.deleteProductImage);

module.exports = router; 