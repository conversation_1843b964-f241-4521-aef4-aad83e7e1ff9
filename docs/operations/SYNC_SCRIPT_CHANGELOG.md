# PDF同步脚本变更日志

## 版本 2.0 - 增强版 (2025-05-28)

### 🚀 新增功能

#### 📝 完整日志系统
- **彩色日志输出**: 不同级别使用不同颜色 (INFO/蓝色, SUCCESS/绿色, WARNING/黄色, ERROR/红色, DEBUG/紫色)
- **文件日志记录**: 所有操作自动记录到 `logs/sync-pdfs.log`
- **自动日志轮转**: 超过10MB自动轮转，保留最近5个文件
- **时间戳记录**: 每条日志包含详细的时间戳信息

#### 🔧 增强的错误处理
- **连接状态检查**: 自动检测网络连接和SSH配置
- **远程目录验证**: 确认远程目录存在并获取统计信息
- **优雅错误退出**: 错误时提供详细信息并安全退出
- **临时文件清理**: 自动清理rsync临时文件

#### 📊 详细统计报告
- **同步前统计**: 显示本地文件数量和目录大小
- **传输统计**: rsync传输文件数量、总大小等信息
- **同步后对比**: 显示同步后的文件统计变化
- **远程目录统计**: 显示远程服务器文件数量

#### 🎛️ 多种运行模式
- **正常模式**: 执行完整同步操作
- **模拟模式** (`--dry-run`): 模拟运行，不实际传输文件
- **统计模式** (`--stats-only`): 仅显示统计信息
- **详细模式** (`-v`): 显示更详细的操作信息
- **保留模式** (`--no-delete`): 不删除本地多余文件

#### 🛠️ 系统优化
- **权限自动修正**: 自动设置正确的文件和目录权限
- **rsync优化**: 排除临时文件、隐藏文件，启用进度显示
- **错误恢复**: 提供多种错误恢复选项
- **帮助系统**: 完整的命令行帮助信息

### 📋 技术改进

#### 脚本结构
```bash
sync-pdfs.sh
├── 配置变量定义
├── 日志系统函数
├── 错误处理函数
├── 连接检查函数
├── 统计报告函数
├── 同步执行函数
├── 权限修正函数
├── 命令行参数解析
└── 主执行流程
```

#### 关键函数
- `log_message()`: 统一日志记录
- `check_connection()`: 网络连接检查
- `perform_sync()`: 执行同步操作
- `rotate_logs()`: 日志文件轮转
- `fix_permissions()`: 权限修正

### 🔄 使用对比

#### 原版本 (v1.0)
```bash
#!/bin/bash
rsync -avz $REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR/ $LOCAL_DIR/
# 可选权限修正（注释掉）
```

#### 新版本 (v2.0)
```bash
# 完整的功能调用
./sync-pdfs.sh                    # 正常同步
./sync-pdfs.sh --stats-only       # 查看统计
./sync-pdfs.sh --dry-run          # 模拟运行
./sync-pdfs.sh -v                 # 详细模式
./sync-pdfs.sh --help             # 查看帮助
```

### 📁 新增文件

- `SYNC_PDFS_README.md`: 详细使用说明文档
- `logs/sync-pdfs.log`: 自动创建的日志文件
- 增强的 `sync-pdfs.sh`: 主同步脚本

### 🔧 配置参数

#### 服务器配置
```bash
REMOTE_USER=mmc
REMOTE_HOST=**************
REMOTE_DIR=/home/<USER>/open-osp/volumes/OscarDocument/oscar
LOCAL_DIR=/home/<USER>/apps/mmcwebapp/local-documents/oscar
```

#### 日志配置
```bash
LOG_DIR="/home/<USER>/apps/mmcwebapp/logs"
LOG_FILE="$LOG_DIR/sync-pdfs.log"
MAX_LOG_SIZE=10485760  # 10MB
MAX_LOG_FILES=5
```

### 📈 性能数据

根据测试结果：
- **远程文件数量**: 31,479个文件
- **本地目录大小**: 2.9GB
- **连接检查时间**: < 1秒
- **统计获取时间**: < 2秒

### 🛡️ 安全增强

- SSH批处理模式连接验证
- 超时保护 (10秒连接超时)
- 文件权限自动修正 (644/755)
- 临时文件安全清理

### 📚 文档更新

- 创建详细的使用说明文档
- 更新主README文件
- 添加故障排除指南
- 提供性能优化建议

### 🔮 未来规划

- 支持配置文件
- 邮件通知功能
- Web界面监控
- 并行传输支持
- 增量备份验证

## 兼容性

### 系统要求
- Bash 4.0+
- rsync 3.0+
- SSH客户端
- 基本Unix工具 (find, grep, awk, etc.)

### 测试环境
- Ubuntu 20.04/22.04
- CentOS 8+
- macOS 10.15+

## 迁移指南

从旧版本迁移到新版本：

1. **备份现有脚本**:
   ```bash
   cp sync-pdfs.sh sync-pdfs.sh.backup
   ```

2. **替换脚本**:
   ```bash
   # 新脚本已自动替换
   chmod +x sync-pdfs.sh
   ```

3. **测试功能**:
   ```bash
   ./sync-pdfs.sh --stats-only
   ```

4. **查看日志**:
   ```bash
   tail -f logs/sync-pdfs.log
   ```

## 支持

如有问题或建议，请参考：
- [使用说明文档](./SYNC_PDFS_README.md)
- [项目主README](./README.md)
- 联系系统管理员

---

**注意**: 新版本完全兼容旧版本的基本功能，添加了大量增强特性。 