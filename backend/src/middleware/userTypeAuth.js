/**
 * 用户类型权限控制中间件
 * 用于限制不同用户类型对特定功能的访问
 */

/**
 * 要求用户为member类型才能访问
 */
const requireMember = (req, res, next) => {
    try {
        if (!req.user) {
            return res.status(401).json({ 
                success: false,
                message: 'Authentication required',
                userType: 'unauthenticated'
            });
        }

        // 管理员可以访问所有功能
        if (req.user.role === 'admin') {
            return next();
        }

        // 检查是否为member
        if (req.user.role !== 'member') {
            return res.status(403).json({ 
                success: false,
                message: 'This feature requires an active membership. Please upgrade to access this content.',
                userType: req.user.role,
                requiresUpgrade: true
            });
        }

        next();
    } catch (error) {
        console.error('Error in requireMember middleware:', error);
        return res.status(500).json({ 
            success: false,
            message: 'Server error checking membership status' 
        });
    }
};

/**
 * 要求用户为user或更高级别才能访问（基础功能）
 */
const requireUser = (req, res, next) => {
    try {
        if (!req.user) {
            return res.status(401).json({ 
                success: false,
                message: 'Authentication required',
                userType: 'unauthenticated'
            });
        }

        // user, member, admin 都可以访问基础功能
        if (['user', 'member', 'admin'].includes(req.user.role)) {
            return next();
        }

        return res.status(403).json({ 
            success: false,
            message: 'Access denied',
            userType: req.user.role
        });
    } catch (error) {
        console.error('Error in requireUser middleware:', error);
        return res.status(500).json({ 
            success: false,
            message: 'Server error checking user permissions' 
        });
    }
};

/**
 * 要求用户为admin才能访问
 */
const requireAdmin = (req, res, next) => {
    try {
        if (!req.user) {
            return res.status(401).json({ 
                success: false,
                message: 'Authentication required',
                userType: 'unauthenticated'
            });
        }

        if (req.user.role !== 'admin') {
            return res.status(403).json({ 
                success: false,
                message: 'Admin access required',
                userType: req.user.role
            });
        }

        next();
    } catch (error) {
        console.error('Error in requireAdmin middleware:', error);
        return res.status(500).json({ 
            success: false,
            message: 'Server error checking admin permissions' 
        });
    }
};

/**
 * 获取用户权限信息（不阻止访问，只添加权限信息）
 */
const addUserTypeInfo = (req, res, next) => {
    try {
        if (req.user) {
            req.userTypeInfo = {
                userType: req.user.role,
                isUser: ['user', 'member', 'admin'].includes(req.user.role),
                isMember: ['member', 'admin'].includes(req.user.role),
                isAdmin: req.user.role === 'admin',
                demographicNo: req.user.demographic_no
            };
        } else {
            req.userTypeInfo = {
                userType: 'unauthenticated',
                isUser: false,
                isMember: false,
                isAdmin: false,
                demographicNo: null
            };
        }
        next();
    } catch (error) {
        console.error('Error in addUserTypeInfo middleware:', error);
        req.userTypeInfo = {
            userType: 'error',
            isUser: false,
            isMember: false,
            isAdmin: false,
            demographicNo: null
        };
        next();
    }
};

module.exports = {
    requireMember,
    requireUser, 
    requireAdmin,
    addUserTypeInfo
}; 