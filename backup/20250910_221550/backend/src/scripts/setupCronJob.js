const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

console.log('设置每日健康小贴士生成任务...');

// 获取当前用户的crontab内容
exec('crontab -l', (error, stdout, stderr) => {
    if (error && error.code !== 0 && error.code !== 1) {
        console.error(`获取当前crontab失败: ${error.message}`);
        return;
    }

    let crontabContent = stdout;

    // 定义每天早上6点运行的cron任务
    const scriptPath = path.resolve(__dirname, 'generateDailyTip.js');
    const cronJob = `0 6 * * * cd ${path.dirname(scriptPath)} && node ${scriptPath} >> ${path.join(__dirname, '../../../logs/daily-tip.log')} 2>&1\n`;

    // 检查是否已存在相同的任务
    if (crontabContent.includes('generateDailyTip.js')) {
        console.log('每日健康小贴士生成任务已存在，无需重复添加');
        return;
    }

    // 添加新的cron任务
    crontabContent += cronJob;

    // 确保logs目录存在
    const logsDir = path.join(__dirname, '../../../logs');
    if (!fs.existsSync(logsDir)) {
        fs.mkdirSync(logsDir, { recursive: true });
    }

    // 更新crontab
    fs.writeFileSync('/tmp/new-crontab', crontabContent);
    exec('crontab /tmp/new-crontab', (error, stdout, stderr) => {
        if (error) {
            console.error(`设置crontab失败: ${error.message}`);
            return;
        }
        console.log('每日健康小贴士生成任务已成功设置');

        // 清理临时文件
        fs.unlinkSync('/tmp/new-crontab');
    });
}); 