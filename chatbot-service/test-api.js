require('dotenv').config();
const fetch = require('node-fetch');

async function testOpenRouterAPI() {
    const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;

    if (!OPENROUTER_API_KEY) {
        console.error('OPENROUTER_API_KEY not found in environment variables');
        return;
    }

    console.log('Testing OpenRouter API connection...');
    console.log(`API Key starts with: ${OPENROUTER_API_KEY.substring(0, 10)}...`);

    try {
        const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': 'app.mmcwellness.ca',
                'X-Title': 'MMC Wellness Chat Test'
            },
            body: JSON.stringify({
                model: 'deepseek/deepseek-chat',
                messages: [
                    { role: 'system', content: 'You are a helpful assistant.' },
                    { role: 'user', content: 'Hello, this is a test.' }
                ]
            })
        });

        const data = await response.json();
        console.log('API Response Status:', response.status);
        console.log('API Response Headers:', response.headers.raw());
        console.log('API Response Body:', JSON.stringify(data, null, 2));

        if (response.ok) {
            console.log('SUCCESS: OpenRouter API connection is working properly');
        } else {
            console.error('ERROR: OpenRouter API returned an error:', data);
        }
    } catch (error) {
        console.error('ERROR: Failed to connect to OpenRouter API:', error);
    }
}

testOpenRouterAPI(); 