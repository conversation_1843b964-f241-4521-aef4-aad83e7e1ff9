services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    env_file:
      - ./.env
    ports:
      - "${BACKEND_PORT:-3000}:3000"
    volumes:
      # - /home/<USER>/open-osp/volumes/OscarDocument:/OscarDocument
      - ./backend/documents:/app/documents
      - ./local-documents:/OscarDocument:ro

      # - ./remote-documents:/OscarDocument:ro
    environment:
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_NAME=${DB_NAME}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN}
      - FRONTEND_URL=${FRONTEND_URL}
      - EMAIL_HOST=${EMAIL_HOST}
      - EMAIL_PORT=${EMAIL_PORT}
      - EMAIL_SECURE=${EMAIL_SECURE}
      - EMAIL_USER=${EMAIL_USER}
      - EMAIL_PASS=${EMAIL_PASS}
      - EMAIL_FROM=${EMAIL_FROM}
      - TZ=${TZ}
      - LOCAL_TIPS_PATH=${LOCAL_TIPS_PATH}
      - LOCAL_ARTICLES_PATH=${LOCAL_ARTICLES_PATH}
      - LOCAL_ASSETS_PATH=${LOCAL_ASSETS_PATH}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - MAIN_HOST=${MAIN_HOST}
      - MAIN_USER=${MAIN_USER}
      - MAIN_PASSWORD=${MAIN_PASSWORD}
      - MAIN_DB_HOST=**************
      - MAIN_DB_PORT=3306
      - MAIN_DB_USER=root
      - MAIN_DB_PASSWORD=Z2Rh6VGr7DE=
      - MAIN_DB_NAME=oscar
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - GEMINI_MODEL=${GEMINI_MODEL}
      # Supabase Configuration (sourced from .env via env_file)
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      - SUPABASE_BUCKET_NAME=${SUPABASE_BUCKET_NAME} # Optional: Define if not relying on controller default or if different from root .env
      # MinIO Configuration - Removed as Supabase is now used
      # - MINIO_ENDPOINT=${MINIO_ENDPOINT}
      # - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      # - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      # - MINIO_BUCKET_NAME=${MINIO_BUCKET_NAME} # This one is distinct from SUPABASE_BUCKET_NAME
      # - MINIO_REGION=${MINIO_REGION}
      # - MINIO_USE_SSL=${MINIO_USE_SSL}
      # - BOOKING_IMAGE_BASE_URL=${BOOKING_IMAGE_BASE_URL}
      # PDF Converter enabled in backend
      - PDF_CONVERTER_ENABLED=true
    networks:
      - webapp-network
    restart: always
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:3000/health" ]
      interval: 30s
      timeout: 10s
      retries: 3

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    env_file:
      - ./.env
    ports:
      - "${FRONTEND_PORT:-3001}:3001"
    environment:
      - REACT_APP_API_URL=${REACT_APP_API_URL}
      - TZ=${TZ}
      - VITE_CLERK_PUBLISHABLE_KEY=${VITE_CLERK_PUBLISHABLE_KEY}
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - webapp-network
    restart: always

  chatbot-service:
    build:
      context: ./chatbot-service
      dockerfile: Dockerfile
    container_name: mmcwebapp-chatbot-service
    ports:
      - "${CHATBOT_PORT:-3002}:3002"
    restart: always
    env_file:
      - ./.env
    environment:
      - PORT=${CHATBOT_PORT:-3002}
      # 使用远程数据库配置
      - MAIN_DB_HOST=**************
      - MAIN_DB_PORT=3306
      - MAIN_DB_USER=root
      - MAIN_DB_PASSWORD=Z2Rh6VGr7DE=
      - MAIN_DB_NAME=oscar
      # Gemini API configuration
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - GEMINI_MODEL=${GEMINI_MODEL}
      # 远程文档路径
      - REMOTE_DOCUMENTS_PATH=/home/<USER>/open-osp/volumes/OscarDocument
    volumes:
      - ./local-documents:/OscarDocument:ro
    dns:
      - *******
      - *******
    networks:
      - webapp-network



networks:
  webapp-network:
    driver: bridge
  open-osp_back-tier:
    external: true
