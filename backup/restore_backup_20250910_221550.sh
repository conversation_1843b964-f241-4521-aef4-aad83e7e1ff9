#!/bin/bash

# <PERSON>MC WebApp Docker Backup Restore Script
# Created: 2025-09-10 22:15:50
# This script restores the Docker containers and application files from backup

BACKUP_DATE="20250910_221550"
BACKUP_DIR="backup/${BACKUP_DATE}"

echo "🔄 Starting MMC WebApp restore from backup ${BAC<PERSON>UP_DATE}..."

# Stop current containers
echo "⏹️  Stopping current containers..."
docker compose down

# Remove current application files (optional - uncomment if needed)
# echo "🗑️  Removing current application files..."
# rm -rf backend frontend chatbot-service docker-compose.yml

# Restore application files
echo "📁 Restoring application files..."
cp -r ${BACKUP_DIR}/backend ./
cp -r ${BACKUP_DIR}/frontend ./
cp -r ${BACKUP_DIR}/chatbot-service ./
cp ${BACKUP_DIR}/docker-compose.yml ./

# Restore Docker images
echo "🐳 Restoring Docker images..."
docker tag mmcwebapp-backend-backup:${<PERSON><PERSON><PERSON><PERSON>_DATE} mmcwebapp-backend:latest
docker tag mmcwebapp-frontend-backup:${BACKUP_DATE} mmcwebapp-frontend:latest
docker tag mmcwebapp-chatbot-backup:${BACKUP_DATE} mmcwebapp-chatbot-service:latest

# Start containers
echo "🚀 Starting restored containers..."
docker compose up -d

echo "✅ Restore completed!"
echo "📊 Check container status:"
docker compose ps

echo ""
echo "🌐 Application URLs:"
echo "   Frontend: http://localhost:3001"
echo "   Backend:  http://localhost:3000"
echo "   Chatbot:  http://localhost:3002"
