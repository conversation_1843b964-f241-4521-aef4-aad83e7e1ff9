const axios = require('axios');
const cheerio = require('cheerio');
const cron = require('node-cron');
const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
require('dotenv').config();

// Import AI usage tracker
let aiUsageTracker;
try {
    aiUsageTracker = require('./aiUsageTracker');
    console.log('AI usage tracker loaded for health news crawler-gemini');
} catch (error) {
    console.warn('AI usage tracker not available:', error.message);
    aiUsageTracker = {
        recordUsage: async () => {},
        recordError: async () => {}
    };
}

// Import AI Service Manager for intelligent provider switching
let aiServiceManager;
try {
    aiServiceManager = require('./aiServiceManager');
    console.log('Gemini Health News Crawler AI Service Manager loaded successfully');
} catch (error) {
    console.warn('AI Service Manager not available for Gemini health news crawler:', error.message);
    aiServiceManager = null;
}

// 从环境变量加载配置
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
const HEALTH_TOPICS = [
    'latest health research',
    'medical breakthrough news',
    'nutrition health research',
    'preventive healthcare news',
    'mental health tips',
    'healthy lifestyle research',
    'health news today',
    'diabetes research',
    'cancer treatment news',
    'heart health tips',
    'weight loss research',
    '健康研究',
    '健康饮食',
    '健康生活方式',
    '疾病预防'
];

// 创建数据库连接池
const pool = mysql.createPool({
    host: process.env.DB_HOST || 'database',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME || 'oscar',
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
});

// 保存文章到文件系统
async function saveArticle(title, content, sources) {
    try {
        const date = new Date();
        const formattedDate = `${date.getFullYear()}${String(date.getMonth() + 1).padStart(2, '0')}${String(date.getDate()).padStart(2, '0')}`;
        const filename = `health-summary-${formattedDate}.md`;
        const filePath = path.join(__dirname, '../documents/health', filename);

        // 确保目录存在
        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }

        // 创建Markdown格式内容
        const markdown = `---
title: ${title}
category: health
tags: health, news, research
featured: true
date: ${date.toISOString()}
---

# ${title}

${content}

## Sources
${sources.map(source => `- [${source.title}](${source.url})`).join('\n')}
`;

        fs.writeFileSync(filePath, markdown);
        console.log(`Article saved to ${filePath}`);

        // 同时存储到数据库中
        await saveArticleToDatabase(title, content, sources);

        return filePath;
    } catch (error) {
        console.error('Error saving article:', error);
    }
}

// 保存文章到数据库
async function saveArticleToDatabase(title, content, sources) {
    try {
        // 检查health_articles表是否存在，如果不存在则创建
        await pool.query(`
      CREATE TABLE IF NOT EXISTS health_articles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        sources TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

        // 插入文章
        await pool.query(
            'INSERT INTO health_articles (title, content, sources) VALUES (?, ?, ?)',
            [title, content, JSON.stringify(sources)]
        );

        console.log('Article saved to database');
    } catch (error) {
        console.error('Error saving article to database:', error);
    }
}

// 使用Gemini搜索健康新闻
async function fetchHealthNews() {
    const startTime = Date.now();
    let geminiUrl;
    
    try {
        // 随机选择一个健康主题
        const randomTopic = HEALTH_TOPICS[Math.floor(Math.random() * HEALTH_TOPICS.length)];
        console.log(`Searching for health news about: ${randomTopic}`);

        // 构建Gemini搜索请求
        const searchPrompt = `Please search for the latest health news about "${randomTopic}". 
Find 3-5 recent news articles from reputable health sources. 
For each article, provide:
1. The title of the article
2. The URL/link to the article
3. A brief summary of the article's content

Format each article as:
Title: [article title]
URL: [article URL]
Summary: [brief summary]

Please ensure these are actual recent news articles from real sources, not fictional or made-up content.`;

        // Try AI Service Manager first for intelligent provider switching
        if (aiServiceManager) {
            console.log('Using AI Service Manager for health news search');
            
            try {
                const messages = [
                    { role: 'user', content: searchPrompt }
                ];
                
                const result = await aiServiceManager.smartCall(messages, {
                    maxTokens: 2048,
                    temperature: 0.2
                });
                
                const responseTime = Date.now() - startTime;
                
                if (result && result.content) {
                    const searchResults = result.content;
                    console.log('AI Service Manager search results:', searchResults);
                    
                    // 解析搜索结果，提取文章信息
                    const articlePattern = /Title:\s*(.*?)\s*URL:\s*(https?:\/\/[^\s]+)\s*Summary:\s*([\s\S]*?)(?=Title:|$)/gi;
                    const articles = [];
                    let match;

                    while ((match = articlePattern.exec(searchResults)) !== null) {
                        articles.push({
                            title: match[1].trim(),
                            url: match[2].trim(),
                            snippet: match[3].trim(),
                            content: match[3].trim() // 使用摘要作为内容
                        });
                    }

                    console.log(`Health news search completed via AI Service Manager in ${responseTime}ms, found ${articles.length} articles`);
                    return articles;
                } else {
                    throw new Error('No content returned from AI Service Manager');
                }
            } catch (serviceManagerError) {
                console.warn('AI Service Manager failed for health news search, falling back to direct Gemini:', serviceManagerError.message);
                // Fall through to direct Gemini implementation
            }
        }
        
        // Fallback: Direct Gemini API call
        console.log('Using direct Gemini API for health news search');
        geminiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${process.env.GEMINI_MODEL || 'gemini-2.0-flash'}:generateContent?key=${GEMINI_API_KEY}`;
        
        const response = await axios.post(
            geminiUrl,
            {
                contents: [{
                    parts: [{
                        text: searchPrompt
                    }]
                }],
                generationConfig: {
                    temperature: 0.2,
                    maxOutputTokens: 2048
                }
            },
            {
                headers: {
                    'Content-Type': 'application/json'
                }
            }
        );

        const responseTime = Date.now() - startTime;

        if (response.data && response.data.candidates && response.data.candidates.length > 0) {
            const content = response.data.candidates[0].content;
            if (content && content.parts && content.parts.length > 0) {
                const searchResults = content.parts[0].text;
                console.log('Gemini search results:', searchResults);

                // Record successful AI usage
                await aiUsageTracker.recordUsage({
                    provider: 'gemini',
                    serviceType: 'health_news_crawling',
                    endpoint: geminiUrl,
                    modelName: process.env.GEMINI_MODEL || 'gemini-2.0-flash',
                    success: true,
                    responseTimeMs: responseTime,
                    cacheHit: false,
                    estimatedCostUsd: 0
                });

                // 解析搜索结果，提取文章信息
                const articlePattern = /Title:\s*(.*?)\s*URL:\s*(https?:\/\/[^\s]+)\s*Summary:\s*([\s\S]*?)(?=Title:|$)/gi;
                const articles = [];
                let match;

                while ((match = articlePattern.exec(searchResults)) !== null) {
                    articles.push({
                        title: match[1].trim(),
                        url: match[2].trim(),
                        snippet: match[3].trim(),
                        content: match[3].trim() // 使用摘要作为内容
                    });
                }

                if (articles.length > 0) {
                    console.log(`Found ${articles.length} articles in ${responseTime}ms`);
                    articles.forEach((article, index) => {
                        console.log(`${index + 1}. ${article.title} - ${article.url}`);
                    });
                    return articles;
                } else {
                    console.log('Failed to parse articles from Gemini response');
                    return [];
                }
            }
        }

        // Record failed AI usage
        await aiUsageTracker.recordUsage({
            provider: 'gemini',
            serviceType: 'health_news_crawling',
            endpoint: geminiUrl,
            modelName: process.env.GEMINI_MODEL || 'gemini-2.0-flash',
            success: false,
            responseTimeMs: responseTime,
            cacheHit: false
        });

        console.log('No valid response from Gemini API');
        return [];
    } catch (error) {
        const responseTime = Date.now() - startTime;
        console.error('Error searching health news with Gemini:', error);

        // Record failed AI usage
        await aiUsageTracker.recordUsage({
            provider: 'gemini',
            serviceType: 'health_news_crawling',
            endpoint: geminiUrl || 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent',
            modelName: process.env.GEMINI_MODEL || 'gemini-2.0-flash',
            success: false,
            responseTimeMs: responseTime,
            cacheHit: false
        });

        // Record error details
        await aiUsageTracker.recordError({
            provider: 'gemini',
            serviceType: 'health_news_crawling',
            errorType: 'API_ERROR',
            errorMessage: error.message,
            errorCode: error.response?.status?.toString(),
            requestSummary: `Health news search for topic: ${randomTopic}`,
            responseSummary: error.stack
        });

        return [];
    }
}

// 使用Gemini生成文章摘要
async function generateArticleSummary(newsItems) {
    const startTime = Date.now();
    let geminiUrl;
    
    try {
        if (!newsItems || newsItems.length === 0) {
            console.log('No news items to summarize');
            return null;
        }

        const newsText = newsItems.map(item =>
            `Title: ${item.title}\nURL: ${item.url}\nContent: ${item.content || item.snippet}`
        ).join('\n\n---\n\n');

        const prompt = `I'm sharing with you some recent health news articles. Please analyze them and create a comprehensive summary article. 
    
The summary should:
1. Have an engaging title related to the main theme of the articles
2. Start with a brief introduction about the topic's importance
3. Include key findings, research, or advice from all sources
4. Organize information logically with clear sections and headers
5. Conclude with practical takeaways for readers
6. Keep a professional yet accessible tone suitable for general audience
7. Use Markdown formatting
8. Be around 500-800 words in length

CRITICAL INSTRUCTIONS:
1. ONLY include information that is explicitly mentioned in the source articles
2. DO NOT fabricate any research findings, statistics, or medical claims
3. DO NOT add any fictional examples, case studies, or patient stories
4. If information from different sources conflicts, acknowledge this rather than inventing a resolution
5. Clearly attribute findings to their specific sources
6. Use factual, evidence-based language without exaggeration or speculation
7. If the source articles lack specific details on a topic, acknowledge this gap rather than filling in with assumptions

Here are the articles to summarize:

${newsText}`;

        // Try AI Service Manager first for intelligent provider switching
        if (aiServiceManager) {
            console.log('Using AI Service Manager for health news summary generation');
            
            try {
                const messages = [
                    { role: 'user', content: prompt }
                ];
                
                const result = await aiServiceManager.smartCall(messages, {
                    maxTokens: 4096,
                    temperature: 0.2
                });
                
                const responseTime = Date.now() - startTime;
                
                if (result && result.content) {
                    const summary = result.content;
                    
                    // 从摘要中提取标题
                    const titleMatch = summary.match(/# (.+?)(\n|$)/);
                    const title = titleMatch ? titleMatch[1] : `Health News Summary - ${new Date().toLocaleDateString()}`;

                    console.log(`Article summary generated successfully via AI Service Manager in ${responseTime}ms`);

                    return {
                        title,
                        content: summary,
                        sources: newsItems.map(item => ({ title: item.title, url: item.url }))
                    };
                } else {
                    throw new Error('No content returned from AI Service Manager');
                }
            } catch (serviceManagerError) {
                console.warn('AI Service Manager failed for health summary, falling back to direct Gemini:', serviceManagerError.message);
                // Fall through to direct Gemini implementation
            }
        }
        
        // Fallback: Direct Gemini API call
        console.log('Using direct Gemini API for health news summary generation');
        geminiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${process.env.GEMINI_MODEL || 'gemini-2.0-flash'}:generateContent?key=${GEMINI_API_KEY}`;
        
        const response = await axios.post(
            geminiUrl,
            {
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                generationConfig: {
                    temperature: 0.2,
                    maxOutputTokens: 4096
                }
            },
            {
                headers: {
                    'Content-Type': 'application/json'
                }
            }
        );

        const responseTime = Date.now() - startTime;

        if (response.data && response.data.candidates && response.data.candidates.length > 0) {
            const content = response.data.candidates[0].content;
            if (content && content.parts && content.parts.length > 0) {
                const summary = content.parts[0].text;

                // Record successful AI usage
                await aiUsageTracker.recordUsage({
                    provider: 'gemini',
                    serviceType: 'health_news_summary',
                    endpoint: geminiUrl,
                    modelName: process.env.GEMINI_MODEL || 'gemini-2.0-flash',
                    success: true,
                    responseTimeMs: responseTime,
                    cacheHit: false,
                    estimatedCostUsd: 0
                });

                // 从摘要中提取标题
                const titleMatch = summary.match(/# (.+?)(\n|$)/);
                const title = titleMatch ? titleMatch[1] : `Health News Summary - ${new Date().toLocaleDateString()}`;

                console.log(`Article summary generated successfully in ${responseTime}ms`);

                return {
                    title,
                    content: summary,
                    sources: newsItems.map(item => ({ title: item.title, url: item.url }))
                };
            }
        }

        // Record failed AI usage
        await aiUsageTracker.recordUsage({
            provider: 'gemini',
            serviceType: 'health_news_summary',
            endpoint: geminiUrl,
            modelName: process.env.GEMINI_MODEL || 'gemini-2.0-flash',
            success: false,
            responseTimeMs: responseTime,
            cacheHit: false
        });

        console.error('No valid response from Gemini API for article generation');
        return null;
    } catch (error) {
        const responseTime = Date.now() - startTime;
        console.error('Error generating article summary with Gemini:', error);

        // Record failed AI usage
        await aiUsageTracker.recordUsage({
            provider: 'gemini',
            serviceType: 'health_news_summary',
            endpoint: geminiUrl || 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent',
            modelName: process.env.GEMINI_MODEL || 'gemini-2.0-flash',
            success: false,
            responseTimeMs: responseTime,
            cacheHit: false
        });

        // Record error details
        await aiUsageTracker.recordError({
            provider: 'gemini',
            serviceType: 'health_news_summary',
            errorType: 'API_ERROR',
            errorMessage: error.message,
            errorCode: error.response?.status?.toString(),
            requestSummary: `Generate summary for ${newsItems.length} news items`,
            responseSummary: error.stack
        });

        return null;
    }
}

// 主函数：获取新闻，生成摘要，保存文章
async function processHealthNews() {
    try {
        console.log('Starting health news process with Gemini...');
        const newsItems = await fetchHealthNews();

        if (newsItems && newsItems.length > 0) {
            console.log(`Processing ${newsItems.length} news items...`);
            const article = await generateArticleSummary(newsItems);

            if (article) {
                await saveArticle(article.title, article.content, article.sources);
                console.log('Health news article created successfully');
            } else {
                console.log('Failed to generate article');
            }
        } else {
            console.log('No news items found to process');
        }
    } catch (error) {
        console.error('Error in processHealthNews:', error);
    }
}

// 每天早上7点执行爬取任务（服务器时区）
cron.schedule('0 7 * * *', () => {
    console.log('Running scheduled health news crawler with Gemini...');
    processHealthNews().catch(err => console.error('Error in scheduled task:', err));
});

// 导出函数以便手动调用
module.exports = {
    processHealthNews
};

// 如果直接执行此文件，立即运行一次
if (require.main === module) {
    processHealthNews().catch(err => console.error('Error running process:', err));
} 