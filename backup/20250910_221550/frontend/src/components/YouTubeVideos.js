import React, { useState, useEffect, useRef } from 'react';
import { API_URL } from '../utils/env';
import {
    Box,
    Typography,
    Card,
    CardContent,
    CardMedia,
    Grid,
    Button,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    IconButton,
    Divider,
    CircularProgress,
    Alert,
    Chip,
    Stack,
    Paper,
    useTheme,
    Container,
    CardActionArea,
    Skeleton,
    useMediaQuery,
    Tooltip,
    ToggleButtonGroup,
    ToggleButton
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import VideocamIcon from '@mui/icons-material/Videocam';
import PlayCircleOutlineIcon from '@mui/icons-material/PlayCircleOutline';
import FavoriteIcon from '@mui/icons-material/Favorite';
import ShareIcon from '@mui/icons-material/Share';
import VisibilityIcon from '@mui/icons-material/Visibility';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import YouTubeIcon from '@mui/icons-material/YouTube';
import RestaurantIcon from '@mui/icons-material/Restaurant';
import FitnessCenterIcon from '@mui/icons-material/FitnessCenter';
import PsychologyIcon from '@mui/icons-material/Psychology';
import NightsStayIcon from '@mui/icons-material/NightsStay';
import HealthAndSafetyIcon from '@mui/icons-material/HealthAndSafety';
import ViewAgendaIcon from '@mui/icons-material/ViewAgenda';
import FullscreenIcon from '@mui/icons-material/Fullscreen';
import { useLanguage } from '../context/LanguageContext';
import axios from 'axios';
import SwipeableViews from 'react-swipeable-views';
import { useGesture } from '@use-gesture/react';
import PauseIcon from '@mui/icons-material/Pause';

// 模拟数据 - 实际应用中应从后端API获取
const MOCK_VIDEOS = [
    {
        id: 1,
        title: '中医调理月经不调',
        description: '中医师分享如何自然调理月经不调，无需药物干预',
        thumbnailUrl: 'https://i.ytimg.com/vi/ABC123xyz/maxresdefault.jpg',
        youtubeLink: 'https://www.youtube.com/watch?v=ABC123xyz',
        category: 'women-health',
        likes: 245,
        views: 1289,
        date: '2023-12-15'
    },
    {
        id: 2,
        title: '每日5分钟颈椎放松',
        description: '办公室工作人员必备的颈椎放松操，预防颈椎病',
        thumbnailUrl: 'https://i.ytimg.com/vi/DEF456uvw/maxresdefault.jpg',
        youtubeLink: 'https://www.youtube.com/watch?v=DEF456uvw',
        category: 'fitness',
        likes: 189,
        views: 856,
        date: '2024-01-10'
    },
    {
        id: 3,
        title: '健康减脂餐制作教程',
        description: '简单美味的减脂餐制作，高蛋白低脂肪，适合减重人群',
        thumbnailUrl: 'https://i.ytimg.com/vi/GHI789jkl/maxresdefault.jpg',
        youtubeLink: 'https://www.youtube.com/watch?v=GHI789jkl',
        category: 'nutrition',
        likes: 312,
        views: 1567,
        date: '2024-02-20'
    },
    {
        id: 4,
        title: '如何预防和治疗感冒',
        description: '常见感冒预防和治疗方法，增强免疫力的小技巧',
        thumbnailUrl: 'https://i.ytimg.com/vi/MNO123pqr/maxresdefault.jpg',
        youtubeLink: 'https://www.youtube.com/watch?v=MNO123pqr',
        category: 'preventive-care',
        likes: 178,
        views: 923,
        date: '2024-03-05'
    },
    {
        id: 5,
        title: '睡眠质量提升指南',
        description: '如何改善睡眠质量，解决入睡困难和夜间醒来问题',
        thumbnailUrl: 'https://i.ytimg.com/vi/STU456vwx/maxresdefault.jpg',
        youtubeLink: 'https://www.youtube.com/watch?v=STU456vwx',
        category: 'sleep',
        likes: 267,
        views: 1102,
        date: '2024-03-18'
    },
    {
        id: 6,
        title: '居家缓解焦虑方法',
        description: '5分钟快速缓解焦虑的呼吸法和冥想技巧',
        thumbnailUrl: 'https://i.ytimg.com/vi/YZA789bcd/maxresdefault.jpg',
        youtubeLink: 'https://www.youtube.com/watch?v=YZA789bcd',
        category: 'mental-health',
        likes: 298,
        views: 1423,
        date: '2024-04-02'
    }
];

// 获取YouTube视频ID
const getYoutubeVideoId = (url) => {
    if (!url) return null;

    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);

    return (match && match[2].length === 11) ? match[2] : null;
};

// 类别定义
const CATEGORIES = [
    { slug: 'all', name: '全部', enName: 'All' },
    { slug: 'nutrition', name: '营养健康', enName: 'Nutrition' },
    { slug: 'fitness', name: '运动健身', enName: 'Fitness' },
    { slug: 'women-health', name: '女性健康', enName: 'Women\'s Health' },
    { slug: 'mental-health', name: '心理健康', enName: 'Mental Health' },
    { slug: 'sleep', name: '睡眠', enName: 'Sleep' },
    { slug: 'preventive-care', name: '疾病预防', enName: 'Preventive Care' }
];

// 类别图标和颜色配置
const CATEGORY_CONFIGS = {
    'nutrition': { color: '#2E7D32', bgColor: 'rgba(46, 125, 50, 0.1)', icon: <RestaurantIcon /> },
    'fitness': { color: '#1976D2', bgColor: 'rgba(25, 118, 210, 0.1)', icon: <FitnessCenterIcon /> },
    'women-health': { color: '#E91E63', bgColor: 'rgba(233, 30, 99, 0.1)', icon: <FavoriteIcon /> },
    'mental-health': { color: '#7B1FA2', bgColor: 'rgba(123, 31, 162, 0.1)', icon: <PsychologyIcon /> },
    'sleep': { color: '#512DA8', bgColor: 'rgba(81, 45, 168, 0.1)', icon: <NightsStayIcon /> },
    'preventive-care': { color: '#0097A7', bgColor: 'rgba(0, 151, 167, 0.1)', icon: <HealthAndSafetyIcon /> },
    'all': { color: '#1565C0', bgColor: 'rgba(21, 101, 192, 0.1)', icon: <VideocamIcon /> }
};

const YouTubeVideos = () => {
    const { t, language } = useLanguage();
    const theme = useTheme();
    const [videos, setVideos] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [activeCategory, setActiveCategory] = useState('all');
    const [dialogOpen, setDialogOpen] = useState(false);
    const [selectedVideo, setSelectedVideo] = useState(null);
    const [embedLoading, setEmbedLoading] = useState(false);
    const [embedError, setEmbedError] = useState(false);
    const [mobileMode, setMobileMode] = useState(false);
    const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
    const [isPlaying, setIsPlaying] = useState(false);
    const [videoViewMode, setVideoViewMode] = useState('card'); // 'card' or 'swipe'
    const videoRefs = useRef({});

    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const isTablet = useMediaQuery(theme.breakpoints.down('md'));

    // 检测是否为移动设备，并设置浏览模式
    useEffect(() => {
        if (isMobile) {
            // 默认移动设备使用卡片模式，但提供切换选项
            setMobileMode(true);
        } else {
            setMobileMode(false);
            setVideoViewMode('card');
        }
    }, [isMobile]);

    // 加载视频数据
    useEffect(() => {
        const fetchVideos = async () => {
            setLoading(true);
            setError('');

            try {
                // 使用实际的API端点
                const response = await axios.get(`${API_URL}/api/youtube/videos`);
                if (response.data && Array.isArray(response.data)) {
                    setVideos(response.data);
                } else {
                    console.warn('API返回的数据不是数组格式', response.data);
                    setVideos([]);
                }
                setLoading(false);
            } catch (err) {
                console.error('获取YouTube视频失败:', err);
                setError(t('error_loading_videos') || '无法加载视频数据，请稍后再试');
                setVideos([]);
                setLoading(false);
            }
        };

        fetchVideos();
    }, [t]);

    // 根据选择的分类筛选视频
    const filteredVideos = activeCategory === 'all'
        ? videos
        : videos.filter(video => video.category === activeCategory);

    // 处理打开视频详情
    const handleOpenVideo = (video) => {
        setSelectedVideo(video);
        setEmbedLoading(true);
        setEmbedError(false);
        setDialogOpen(true);
    };

    // 处理嵌入视频加载事件
    const handleEmbedLoad = () => {
        setEmbedLoading(false);
    };

    // 处理嵌入视频错误事件
    const handleEmbedError = () => {
        setEmbedLoading(false);
        setEmbedError(true);
    };

    // 处理关闭视频详情
    const handleCloseDialog = () => {
        setDialogOpen(false);
        // 延迟清除选中的视频，防止关闭动画时UI闪烁
        setTimeout(() => {
            setSelectedVideo(null);
            setEmbedLoading(false);
            setEmbedError(false);
        }, 300);
    };

    // 处理分类变更
    const handleCategoryChange = (category) => {
        setActiveCategory(category);
    };

    // 打开YouTube链接
    const openYoutubeLink = (url, event) => {
        if (event) {
            event.stopPropagation();
        }
        window.open(url, '_blank');
    };

    // 获取分类名称，根据当前语言
    const getCategoryName = (slug) => {
        const category = CATEGORIES.find(c => c.slug === slug);
        if (!category) return slug;

        return language === 'en' ? category.enName : category.name;
    };

    // 渲染类别过滤器
    const renderCategoryFilter = () => {
        return (
            <Paper elevation={0} sx={{
                p: 2,
                mb: 3,
                borderRadius: 2,
                backgroundColor: theme.palette.background.paper
            }}>
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 500 }}>
                    {t('video_categories')}
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 2 }}>
                    {CATEGORIES.map((category) => {
                        const isActive = activeCategory === category.slug;
                        const config = CATEGORY_CONFIGS[category.slug] || CATEGORY_CONFIGS['all'];
                        const categoryLabel = language === 'en' ? category.enName : category.name;

                        return (
                            <Chip
                                key={category.slug}
                                label={categoryLabel}
                                icon={React.cloneElement(config.icon, {
                                    sx: { color: isActive ? 'white' : config.color }
                                })}
                                onClick={() => handleCategoryChange(category.slug)}
                                sx={{
                                    bgcolor: isActive ? config.color : config.bgColor,
                                    color: isActive ? 'white' : config.color,
                                    fontWeight: isActive ? 600 : 400,
                                    '&:hover': {
                                        bgcolor: isActive
                                            ? config.color
                                            : theme.palette.mode === 'dark'
                                                ? `rgba(255, 255, 255, 0.08)`
                                                : `rgba(0, 0, 0, 0.08)`,
                                    }
                                }}
                            />
                        );
                    })}
                </Box>
            </Paper>
        );
    };

    // 渲染加载骨架屏
    const renderSkeletons = () => {
        return Array(6).fill(0).map((_, index) => (
            <Grid item xs={12} sm={6} md={4} key={`skeleton-${index}`}>
                <Card sx={{ height: '100%' }}>
                    <Skeleton variant="rectangular" height={200} />
                    <CardContent>
                        <Skeleton variant="text" height={30} sx={{ mb: 1 }} />
                        <Skeleton variant="text" height={60} />
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                            <Skeleton variant="rectangular" width={80} height={30} />
                            <Skeleton variant="rectangular" width={100} height={30} />
                        </Box>
                    </CardContent>
                </Card>
            </Grid>
        ));
    };

    // 渲染视频列表
    const renderVideosList = () => {
        if (loading) {
            return (
                <Grid container spacing={3}>
                    {renderSkeletons()}
                </Grid>
            );
        }

        if (error) {
            return (
                <Alert severity="error" sx={{ my: 2 }}>
                    {error}
                </Alert>
            );
        }

        if (filteredVideos.length === 0) {
            return (
                <Alert severity="info" sx={{ my: 2 }}>
                    {t('no_videos_found')}
                </Alert>
            );
        }

        return (
            <Grid container spacing={3}>
                {filteredVideos.map((video) => {
                    const videoId = getYoutubeVideoId(video.youtubeLink);
                    const thumbnailUrl = video.thumbnailUrl || (videoId ? `https://img.youtube.com/vi/${videoId}/hqdefault.jpg` : null);

                    return (
                        <Grid item xs={12} sm={6} md={4} key={video.id}>
                            <Card
                                elevation={2}
                                sx={{
                                    height: '100%',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    transition: 'transform 0.2s, box-shadow 0.2s',
                                    '&:hover': {
                                        transform: 'translateY(-4px)',
                                        boxShadow: 6,
                                    },
                                    overflow: 'hidden',
                                    borderRadius: 2
                                }}
                            >
                                <CardActionArea onClick={() => handleOpenVideo(video)}>
                                    <Box sx={{ position: 'relative' }}>
                                        <CardMedia
                                            component="img"
                                            height={200}
                                            image={thumbnailUrl}
                                            alt={video.title}
                                            sx={{ objectFit: 'cover' }}
                                        />
                                        <Box sx={{
                                            position: 'absolute',
                                            top: 0,
                                            left: 0,
                                            width: '100%',
                                            height: '100%',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            backgroundColor: 'rgba(0,0,0,0.3)',
                                            opacity: 0,
                                            transition: 'opacity 0.2s',
                                            '&:hover': {
                                                opacity: 1
                                            }
                                        }}>
                                            <Box sx={{
                                                width: 70,
                                                height: 70,
                                                borderRadius: '50%',
                                                backgroundColor: 'rgba(255,255,255,0.9)',
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center'
                                            }}>
                                                <YouTubeIcon sx={{ fontSize: 40, color: 'red' }} />
                                            </Box>
                                        </Box>

                                        {/* 视频分类标签 */}
                                        <Chip
                                            size="small"
                                            label={getCategoryName(video.category)}
                                            sx={{
                                                position: 'absolute',
                                                top: 8,
                                                right: 8,
                                                backgroundColor: 'rgba(0,0,0,0.7)',
                                                color: 'white',
                                                fontWeight: 500
                                            }}
                                        />

                                        {/* YouTube徽标 */}
                                        <Box sx={{
                                            position: 'absolute',
                                            left: 8,
                                            bottom: 8,
                                            backgroundColor: 'rgba(255, 0, 0, 0.8)',
                                            color: 'white',
                                            display: 'flex',
                                            alignItems: 'center',
                                            px: 1,
                                            py: 0.5,
                                            borderRadius: 1
                                        }}>
                                            <YouTubeIcon fontSize="small" sx={{ mr: 0.5 }} />
                                            <Typography variant="caption" fontWeight="bold">
                                                YouTube
                                            </Typography>
                                        </Box>
                                    </Box>
                                </CardActionArea>

                                <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                                    <Typography
                                        variant="h6"
                                        component="h2"
                                        gutterBottom
                                        sx={{
                                            fontSize: '1rem',
                                            fontWeight: 600,
                                            display: '-webkit-box',
                                            WebkitLineClamp: 2,
                                            WebkitBoxOrient: 'vertical',
                                            overflow: 'hidden',
                                            lineHeight: 1.3,
                                            height: '2.6em'
                                        }}
                                    >
                                        {video.title}
                                    </Typography>

                                    <Typography
                                        variant="body2"
                                        color="text.secondary"
                                        sx={{
                                            mb: 'auto',
                                            display: '-webkit-box',
                                            WebkitLineClamp: 3,
                                            WebkitBoxOrient: 'vertical',
                                            overflow: 'hidden',
                                            flex: 1
                                        }}
                                    >
                                        {video.description}
                                    </Typography>

                                    <Box sx={{
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                        alignItems: 'center',
                                        mt: 2,
                                        pt: 1,
                                        borderTop: `1px solid ${theme.palette.divider}`
                                    }}>
                                        <Stack direction="row" spacing={1} alignItems="center">
                                            {video.views && (
                                                <Tooltip title={`${video.views} ${t('views')}`}>
                                                    <Chip
                                                        icon={<VisibilityIcon fontSize="small" />}
                                                        label={video.views.toLocaleString()}
                                                        size="small"
                                                        variant="outlined"
                                                        sx={{ height: 24 }}
                                                    />
                                                </Tooltip>
                                            )}
                                            {video.likes && (
                                                <Tooltip title={`${video.likes} ${t('likes')}`}>
                                                    <Chip
                                                        icon={<FavoriteIcon fontSize="small" />}
                                                        label={video.likes.toLocaleString()}
                                                        size="small"
                                                        variant="outlined"
                                                        sx={{ height: 24 }}
                                                    />
                                                </Tooltip>
                                            )}
                                        </Stack>

                                        <Tooltip title={t('open_youtube')}>
                                            <IconButton
                                                size="small"
                                                color="primary"
                                                onClick={(e) => openYoutubeLink(video.youtubeLink, e)}
                                            >
                                                <YouTubeIcon />
                                            </IconButton>
                                        </Tooltip>
                                    </Box>
                                </CardContent>
                            </Card>
                        </Grid>
                    );
                })}
            </Grid>
        );
    };

    // 处理视频播放/暂停
    const togglePlayPause = (videoId) => {
        if (videoRefs.current[videoId]) {
            const video = videoRefs.current[videoId];
            if (video.paused) {
                video.play().catch(e => console.log('Play prevented:', e));
                setIsPlaying(true);
            } else {
                video.pause();
                setIsPlaying(false);
            }
        }
    };

    // 当视频滑动切换时
    const handleVideoChange = (index) => {
        setCurrentVideoIndex(index);
        setIsPlaying(false);

        // 暂停所有视频
        Object.values(videoRefs.current).forEach(video => {
            if (video) video.pause();
        });
    };

    // 处理视图模式变更
    const handleViewModeChange = (event, newMode) => {
        if (newMode !== null) {
            setVideoViewMode(newMode);
        }
    };

    // 渲染视图模式切换器
    const renderViewModeToggle = () => {
        if (!isMobile) return null;

        return (
            <Box sx={{
                display: 'flex',
                justifyContent: 'center',
                mb: 2,
                mt: 1
            }}>
                <ToggleButtonGroup
                    value={videoViewMode}
                    exclusive
                    onChange={handleViewModeChange}
                    aria-label={t('video_mode')}
                    size="small"
                >
                    <ToggleButton value="card" aria-label={t('card_mode')}>
                        <ViewAgendaIcon sx={{ mr: 1 }} />
                        {t('card_mode')}
                    </ToggleButton>
                    <ToggleButton value="swipe" aria-label={t('swipe_mode')}>
                        <FullscreenIcon sx={{ mr: 1 }} />
                        {t('swipe_mode')}
                    </ToggleButton>
                </ToggleButtonGroup>
            </Box>
        );
    };

    // 渲染移动端视频浏览界面 - 滑动模式
    const renderMobileVideoView = () => {
        if (loading) {
            return (
                <Box sx={{
                    height: 'calc(100vh - 240px)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                }}>
                    <CircularProgress />
                </Box>
            );
        }

        if (error) {
            return (
                <Alert severity="error" sx={{ my: 2 }}>
                    {error}
                </Alert>
            );
        }

        if (filteredVideos.length === 0) {
            return (
                <Alert severity="info" sx={{ my: 2 }}>
                    {t('no_videos_found')}
                </Alert>
            );
        }

        return (
            <Box sx={{
                height: 'calc(100vh - 240px)',
                overflow: 'hidden',
                position: 'relative',
                bgcolor: 'black',
                borderRadius: 2
            }}>
                <SwipeableViews
                    axis="y"
                    index={currentVideoIndex}
                    onChangeIndex={handleVideoChange}
                    enableMouseEvents
                    resistance
                    style={{ height: '100%' }}
                    containerStyle={{ height: '100%' }}
                    slideStyle={{ height: '100%' }}
                >
                    {filteredVideos.map((video, index) => {
                        const videoId = getYoutubeVideoId(video.youtubeLink);

                        return (
                            <Box
                                key={video.id}
                                sx={{
                                    height: '100%',
                                    position: 'relative',
                                    bgcolor: 'black',
                                    overflow: 'hidden'
                                }}
                                onClick={() => togglePlayPause(video.id)}
                            >
                                {videoId ? (
                                    <>
                                        <Box
                                            component="iframe"
                                            src={`https://www.youtube.com/embed/${videoId}?enablejsapi=1&controls=0&showinfo=0&rel=0&loop=1&playlist=${videoId}&autoplay=0&mute=0`}
                                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                            frameBorder="0"
                                            title={video.title}
                                            ref={el => videoRefs.current[video.id] = el}
                                            sx={{
                                                width: '100%',
                                                height: '100%',
                                                objectFit: 'contain',
                                                backgroundColor: '#000'
                                            }}
                                        />

                                        <Box sx={{
                                            position: 'absolute',
                                            top: '50%',
                                            left: '50%',
                                            transform: 'translate(-50%, -50%)',
                                            backgroundColor: 'rgba(0,0,0,0.3)',
                                            borderRadius: '50%',
                                            width: 60,
                                            height: 60,
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            opacity: 0.8,
                                            transition: 'opacity 0.3s',
                                            pointerEvents: 'none'
                                        }}>
                                            {isPlaying && currentVideoIndex === index ? (
                                                <PauseIcon sx={{ fontSize: 30, color: 'white' }} />
                                            ) : (
                                                <PlayCircleOutlineIcon sx={{ fontSize: 40, color: 'white' }} />
                                            )}
                                        </Box>
                                    </>
                                ) : (
                                    <Box sx={{
                                        width: '100%',
                                        height: '100%',
                                        bgcolor: 'black',
                                        display: 'flex',
                                        flexDirection: 'column',
                                        alignItems: 'center',
                                        justifyContent: 'center'
                                    }}>
                                        <Box
                                            component="img"
                                            src={video.thumbnailUrl || (videoId ? `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg` : null)}
                                            alt={video.title}
                                            sx={{
                                                maxWidth: '100%',
                                                maxHeight: '100%',
                                                objectFit: 'contain'
                                            }}
                                        />
                                        <Button
                                            variant="contained"
                                            color="error"
                                            startIcon={<YouTubeIcon />}
                                            sx={{ mt: 2, position: 'absolute', bottom: 80 }}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                openYoutubeLink(video.youtubeLink);
                                            }}
                                        >
                                            {t('view_on_youtube')}
                                        </Button>
                                    </Box>
                                )}

                                {/* 视频信息覆盖层 */}
                                <Box sx={{
                                    position: 'absolute',
                                    bottom: 0,
                                    left: 0,
                                    right: 0,
                                    bgcolor: 'rgba(0,0,0,0.6)',
                                    color: 'white',
                                    p: 2,
                                    display: 'flex',
                                    flexDirection: 'column'
                                }}>
                                    <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold' }}>
                                        {video.title}
                                    </Typography>
                                    <Typography
                                        variant="body2"
                                        sx={{
                                            mb: 1,
                                            display: '-webkit-box',
                                            WebkitLineClamp: 2,
                                            WebkitBoxOrient: 'vertical',
                                            overflow: 'hidden'
                                        }}
                                    >
                                        {video.description}
                                    </Typography>
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                        <Chip
                                            label={getCategoryName(video.category)}
                                            size="small"
                                            sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}
                                        />
                                        {video.views && (
                                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                                <VisibilityIcon fontSize="small" />
                                                <Typography variant="caption">
                                                    {video.views.toLocaleString()}
                                                </Typography>
                                            </Box>
                                        )}
                                        {video.likes && (
                                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                                <FavoriteIcon fontSize="small" />
                                                <Typography variant="caption">
                                                    {video.likes.toLocaleString()}
                                                </Typography>
                                            </Box>
                                        )}
                                    </Box>
                                </Box>

                                {/* 右侧操作按钮 */}
                                <Box sx={{
                                    position: 'absolute',
                                    right: 10,
                                    bottom: 100,
                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'center',
                                    gap: 2
                                }}>
                                    <IconButton
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            openYoutubeLink(video.youtubeLink);
                                        }}
                                        sx={{ color: 'white', bgcolor: 'rgba(255,0,0,0.7)' }}
                                    >
                                        <YouTubeIcon />
                                    </IconButton>
                                    <IconButton
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            // 检查navigator.share API是否可用
                                            if (navigator.share) {
                                                navigator.share({
                                                    title: video.title,
                                                    text: video.description,
                                                    url: video.youtubeLink
                                                }).catch(err => console.log('Share failed:', err));
                                            }
                                        }}
                                        sx={{ color: 'white', bgcolor: 'rgba(0,0,0,0.5)' }}
                                    >
                                        <ShareIcon />
                                    </IconButton>
                                </Box>

                                {/* 滑动提示信息 */}
                                <Box sx={{
                                    position: 'absolute',
                                    top: 10,
                                    width: '100%',
                                    textAlign: 'center'
                                }}>
                                    <Typography
                                        variant="caption"
                                        sx={{ color: 'white', bgcolor: 'rgba(0,0,0,0.5)', px: 1, py: 0.5, borderRadius: 1 }}
                                    >
                                        {t('swipe_up_down')}
                                    </Typography>
                                </Box>

                                {/* 点击提示 */}
                                <Box sx={{
                                    position: 'absolute',
                                    top: 40,
                                    width: '100%',
                                    textAlign: 'center'
                                }}>
                                    <Typography
                                        variant="caption"
                                        sx={{ color: 'white', bgcolor: 'rgba(0,0,0,0.5)', px: 1, py: 0.5, borderRadius: 1 }}
                                    >
                                        {t('tap_to_play_pause')}
                                    </Typography>
                                </Box>
                            </Box>
                        );
                    })}
                </SwipeableViews>
            </Box>
        );
    };

    // 渲染视频详情对话框
    const renderVideoDialog = () => {
        if (!selectedVideo) return null;

        const videoId = getYoutubeVideoId(selectedVideo.youtubeLink);
        const isPhone = isMobile; // 手机设备
        const dialogMaxWidth = isPhone ? 'sm' : 'lg';
        const embedHeight = isPhone ? '240px' : isTablet ? '360px' : '480px';

        return (
            <Dialog
                open={dialogOpen}
                onClose={handleCloseDialog}
                maxWidth={dialogMaxWidth}
                fullWidth
                sx={{
                    '& .MuiDialog-paper': {
                        borderRadius: 2,
                        overflow: 'hidden'
                    }
                }}
            >
                <DialogTitle
                    sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        bgcolor: theme.palette.primary.main,
                        color: 'white',
                        py: 1.5
                    }}
                >
                    <Stack direction="row" spacing={1} alignItems="center">
                        <YouTubeIcon />
                        <Typography variant="h6" component="div" sx={{ fontWeight: 500 }}>
                            {isPhone ? '' : 'YouTube'}
                        </Typography>
                    </Stack>
                    <IconButton
                        onClick={handleCloseDialog}
                        sx={{ color: 'white' }}
                        size="small"
                    >
                        <CloseIcon />
                    </IconButton>
                </DialogTitle>

                <DialogContent sx={{ p: 0, overflow: 'hidden' }}>
                    <Box sx={{ position: 'relative', width: '100%', height: embedHeight }}>
                        {embedLoading && (
                            <Box sx={{
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                width: '100%',
                                height: '100%',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                backgroundColor: theme.palette.background.paper,
                                zIndex: 1
                            }}>
                                <CircularProgress />
                            </Box>
                        )}

                        {videoId && !embedError ? (
                            <Box
                                component="iframe"
                                src={`https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0`}
                                frameBorder="0"
                                allowFullScreen
                                title={selectedVideo.title}
                                onLoad={handleEmbedLoad}
                                onError={handleEmbedError}
                                sx={{
                                    position: 'absolute',
                                    top: 0,
                                    left: 0,
                                    width: '100%',
                                    height: '100%',
                                    border: 'none'
                                }}
                            />
                        ) : (
                            <Box sx={{
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                width: '100%',
                                height: '100%',
                                display: 'flex',
                                flexDirection: 'column',
                                justifyContent: 'center',
                                alignItems: 'center',
                                backgroundColor: 'rgba(0,0,0,0.8)',
                                color: 'white'
                            }}>
                                <YouTubeIcon sx={{ fontSize: 60, mb: 2, color: 'red' }} />
                                <Typography variant="body1" gutterBottom>
                                    {t('youtube_embed_failed')}
                                </Typography>
                                <Button
                                    variant="contained"
                                    color="error"
                                    startIcon={<YouTubeIcon />}
                                    sx={{ mt: 2 }}
                                    onClick={() => openYoutubeLink(selectedVideo.youtubeLink)}
                                >
                                    {t('view_on_youtube')}
                                </Button>
                            </Box>
                        )}
                    </Box>

                    <Box sx={{ p: 3 }}>
                        <Typography variant="h5" gutterBottom sx={{ fontWeight: 600 }}>
                            {selectedVideo.title}
                        </Typography>

                        <Box sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            flexWrap: 'wrap',
                            mb: 2
                        }}>
                            <Stack direction="row" spacing={1} alignItems="center" sx={{ my: 1 }}>
                                <Chip
                                    label={getCategoryName(selectedVideo.category)}
                                    color="primary"
                                    variant="outlined"
                                />
                                {selectedVideo.date && (
                                    <Typography variant="body2" color="text.secondary">
                                        {`${t('published_on')} ${selectedVideo.date}`}
                                    </Typography>
                                )}
                            </Stack>

                            <Stack direction="row" spacing={1} alignItems="center" sx={{ my: 1 }}>
                                {selectedVideo.views && (
                                    <Chip
                                        icon={<VisibilityIcon />}
                                        label={`${selectedVideo.views.toLocaleString()} ${t('views')}`}
                                        variant="outlined"
                                        size="small"
                                    />
                                )}
                                {selectedVideo.likes && (
                                    <Chip
                                        icon={<FavoriteIcon />}
                                        label={`${selectedVideo.likes.toLocaleString()} ${t('likes')}`}
                                        variant="outlined"
                                        size="small"
                                    />
                                )}
                            </Stack>
                        </Box>

                        <Divider sx={{ my: 2 }} />

                        <Typography variant="body1" paragraph>
                            {selectedVideo.description}
                        </Typography>
                    </Box>
                </DialogContent>

                <DialogActions sx={{ p: 2, bgcolor: theme.palette.grey[100] }}>
                    <Button
                        startIcon={<CloseIcon />}
                        onClick={handleCloseDialog}
                    >
                        {t('close')}
                    </Button>
                    <Button
                        variant="contained"
                        color="error"
                        startIcon={<YouTubeIcon />}
                        onClick={() => openYoutubeLink(selectedVideo.youtubeLink)}
                    >
                        {t('view_on_youtube')}
                    </Button>
                </DialogActions>
            </Dialog>
        );
    };

    return (
        <Container component="main" maxWidth="lg" sx={{ mt: 2, mb: 4 }}>
            <Box sx={{ mb: 4 }}>
                <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 700 }}>
                    {t('youtube_videos')}
                </Typography>
                <Typography variant="subtitle1" color="text.secondary" paragraph>
                    {t('youtube_videos_description')}
                </Typography>
            </Box>

            {renderCategoryFilter()}

            {mobileMode && renderViewModeToggle()}

            {mobileMode && videoViewMode === 'swipe'
                ? renderMobileVideoView()
                : renderVideosList()}

            {renderVideoDialog()}
        </Container>
    );
};

export default YouTubeVideos; 