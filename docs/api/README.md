# 📡 API文档

系统API接口和集成相关文档

## 📋 文档列表

### API接口规范
- [**API接口文档**](./API.md) - RESTful API接口详细规范
  - 认证机制
  - 接口列表
  - 请求/响应格式
  - 错误处理

### 功能模块API
- [**推荐系统文档**](./REFERRAL_SYSTEM.md) - 推荐奖励系统API说明
  - 推荐码管理
  - 奖励机制
  - 用户关系
  - 积分系统

## 🔌 API概览

### 基础信息
- **Base URL**: `http://localhost:3000/api`
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8

### 主要API模块

#### 用户认证
```
POST /auth/login          # 用户登录
POST /auth/register       # 用户注册
POST /auth/logout         # 用户登出
GET  /auth/verify         # 验证Token
```

#### 患者管理
```
GET  /patients/:id        # 获取患者信息
PUT  /patients/:id        # 更新患者信息
GET  /patients/:id/records # 获取医疗记录
```

#### 预约管理
```
GET  /appointments        # 获取预约列表
POST /appointments        # 创建预约
PUT  /appointments/:id    # 更新预约
DELETE /appointments/:id  # 取消预约
```

#### AI功能
```
POST /ai/prescription-explain  # 处方解释
POST /ai/immunization-explain # 免疫接种说明
POST /ai/summary              # 生成摘要
POST /ai/chat                 # 聊天对话
```

#### 文件管理
```
GET  /files               # 获取文件列表
POST /files/upload        # 上传文件
GET  /files/:id/download  # 下载文件
DELETE /files/:id         # 删除文件
```

## 🔑 认证说明

### JWT Token
```javascript
// 请求头格式
Authorization: Bearer <token>

// Token结构
{
  "userId": "12345",
  "role": "patient",
  "exp": **********
}
```

### 权限级别
- **patient**: 普通患者权限
- **admin**: 管理员权限
- **provider**: 医疗提供者权限

## 📝 使用示例

### 登录获取Token
```bash
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### 使用Token访问API
```bash
curl -X GET http://localhost:3000/api/patients/123 \
  -H "Authorization: Bearer your-jwt-token"
```

### AI功能调用
```bash
curl -X POST http://localhost:3000/api/ai/prescription-explain \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "prescriptionId": "456",
    "language": "zh"
  }'
```

## 📊 响应格式

### 成功响应
```json
{
  "success": true,
  "data": {
    // 响应数据
  },
  "message": "操作成功"
}
```

### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "参数验证失败",
    "details": {}
  }
}
```

## 🚨 错误代码

| 错误码 | HTTP状态 | 描述 |
|--------|----------|------|
| AUTH_REQUIRED | 401 | 需要认证 |
| FORBIDDEN | 403 | 权限不足 |
| NOT_FOUND | 404 | 资源不存在 |
| VALIDATION_ERROR | 400 | 参数验证失败 |
| INTERNAL_ERROR | 500 | 服务器内部错误 |

## 🔧 开发工具

### API测试
- **Postman**: 推荐使用Postman进行API测试
- **cURL**: 命令行工具测试
- **Swagger**: 在线API文档（待实现）

### SDK支持
- **JavaScript**: 前端直接调用
- **Node.js**: 后端服务调用
- **Python**: 第三方集成（待开发）

## 📈 性能指标

### 响应时间
- 认证接口: < 200ms
- 数据查询: < 500ms
- AI处理: < 5s
- 文件上传: 取决于文件大小

### 限流策略
- 每用户每分钟: 100次请求
- AI接口每分钟: 10次请求
- 文件上传每小时: 50MB

## 🔗 相关链接

- [返回文档中心](../)
- [开发文档](../development/) - 开发指南
- [部署文档](../deployment/) - 系统部署
- [运维文档](../operations/) - 运维维护

## 📞 API支持

- API问题请查阅详细文档
- 集成支持请联系开发团队
- Bug报告请提交GitHub Issue

---

*最后更新: 2025-05-28* 