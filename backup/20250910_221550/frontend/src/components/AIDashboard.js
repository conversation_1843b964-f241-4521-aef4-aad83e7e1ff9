import React, { useState, useEffect } from 'react';
import {
    Card,
    Card<PERSON>ontent,
    CardHeader,
    Grid,
    Typo<PERSON>,
    Box,
    Chip,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    LinearProgress,
    Alert,
    IconButton,
    Tooltip,
    CircularProgress,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    TextField,
    Button,
    Tabs,
    Tab,
    Switch,
    FormControlLabel,
    Container,
    Badge,
    Avatar,
    ListItem,
    ListItemAvatar,
    ListItemText,
    List,
    Divider
} from '@mui/material';
import {
    Refresh as RefreshIcon,
    TrendingUp as TrendingUpIcon,
    Assessment as AssessmentIcon,
    Error as ErrorIcon,
    CheckCircle as CheckCircleIcon,
    Speed as SpeedIcon,
    MonetizationOn as MonetizationOnIcon,
    Memory as MemoryIcon,
    Timeline as TimelineIcon,
    SmartToy as SmartToyIcon,
    Chat as ChatIcon,
    Api as ApiIcon,
    TrendingDown as TrendingDownIcon,
    AutoMode as AutoModeIcon,
    Psychology as PsychologyIcon,
    CloudQueue as CloudQueueIcon,
    Storage as StorageIcon,
    Analytics as AnalyticsIcon
} from '@mui/icons-material';

// Chart.js imports
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip as ChartTooltip,
    Legend,
    BarElement,
    ArcElement,
    Filler
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';

// Import AI usage types for detailed classification
import {
    getServiceTypeName,
    getServiceCategoryName,
    getServiceTypeColor,
    calculateCategoryStats,
    groupByServiceCategory,
    SERVICE_CATEGORY_NAMES
} from '../constants/aiUsageTypes';

// Register Chart.js components
ChartJS.register(
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    BarElement,
    ArcElement,
    Title,
    ChartTooltip,
    Legend,
    Filler
);

const AIDashboard = () => {
    const [monitoringData, setMonitoringData] = useState(null);
    const [usageStats, setUsageStats] = useState(null);
    const [chatbotMetrics, setChatbotMetrics] = useState(null);
    const [historicalData, setHistoricalData] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [lastRefresh, setLastRefresh] = useState(null);
    const [currentTab, setCurrentTab] = useState(0);
    const [autoRefresh, setAutoRefresh] = useState(true);
    
    // 查询参数
    const [startDate, setStartDate] = useState(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]);
    const [endDate, setEndDate] = useState(new Date().toISOString().split('T')[0]);
    const [groupBy, setGroupBy] = useState('day');

    const fetchDashboardData = async () => {
        try {
            const token = localStorage.getItem('token');
            
            // 并行获取所有数据
            const [dashboardResponse, metricsResponse, usageResponse] = await Promise.allSettled([
                fetch('/api/admin/ai-dashboard', {
                    headers: { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json' }
                }),
                fetch('/api/admin/ai-metrics', {
                    headers: { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json' }
                }),
                fetch('/api/admin/ai-usage-stats', {
                    headers: { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json' }
                })
            ]);

            // 处理主要仪表板数据
            if (dashboardResponse.status === 'fulfilled' && dashboardResponse.value.ok) {
                const dashboardData = await dashboardResponse.value.json();
                if (dashboardData.success) {
                    const data = dashboardData.data;
                    
                    setMonitoringData({
                        totalCalls: data.today?.summary?.totalCalls || 0,
                        successfulCalls: data.today?.summary?.successfulCalls || 0,
                        failedCalls: data.today?.summary?.failedCalls || 0,
                        averageResponseTime: data.today?.byProvider?.reduce((acc, p) => acc + (p.avg_response_time || 0), 0) / Math.max(1, data.today?.byProvider?.length || 1),
                        totalCost: data.today?.summary?.estimatedCost || 0,
                        providers: data.today?.byProvider?.map(p => ({
                            name: p.provider,
                            model: p.model_name,
                            status: 'available',
                            totalCalls: p.total_calls || 0,
                            successfulCalls: p.successful_calls || 0,
                            averageResponseTime: p.avg_response_time || 0,
                            serviceType: p.service_type
                        })) || [],
                        recentErrors: data.errors?.recent?.slice(0, 5).map(e => ({
                            provider: e.provider,
                            message: e.error_message,
                            timestamp: e.error_timestamp,
                            serviceType: e.service_type
                        })) || []
                    });

                    // 设置历史数据用于图表
                    const weeklyData = data.week?.byProvider || [];
                    setHistoricalData(weeklyData.map(d => ({
                        date: d.date_recorded,
                        provider: d.provider,
                        totalCalls: d.total_calls,
                        successfulCalls: d.successful_calls,
                        failedCalls: d.failed_calls,
                        avgResponseTime: d.avg_response_time_ms,
                        serviceType: d.service_type,
                        modelName: d.model_name
                    })));
                }
            }

            // 处理usage response来获取chatbot数据
            if (usageResponse.status === 'fulfilled' && usageResponse.value.ok) {
                const usageData = await usageResponse.value.json();
                if (usageData.success && usageData.data) {
                    // 从使用统计中过滤chatbot相关数据
                    const allStats = usageData.data.timeline || [];
                    const chatbotStats = allStats.filter(d => 
                        d.service_type && (
                            d.service_type.includes('chatbot') || 
                            d.service_type === 'chatbot' ||
                            d.service_type.startsWith('chatbot_')
                        )
                    );
                    
                    const totalChatbotCalls = chatbotStats.reduce((acc, stat) => acc + (stat.total_calls || 0), 0);
                    const successfulChatbotCalls = chatbotStats.reduce((acc, stat) => acc + (stat.successful_calls || 0), 0);
                    const avgResponseTime = chatbotStats.length > 0 ? 
                        chatbotStats.reduce((acc, stat) => acc + (stat.avg_response_time_ms || 0), 0) / chatbotStats.length : 0;

                    setChatbotMetrics({
                        totalConversations: totalChatbotCalls,
                        activeUsers: Math.floor(totalChatbotCalls * 0.7), // 估算活跃用户
                        averageResponseTime: Math.round(avgResponseTime),
                        successfulResponses: successfulChatbotCalls,
                        totalRequests: totalChatbotCalls,
                        status: totalChatbotCalls > 0 ? 'healthy' : 'unknown',
                        lastHealthCheck: new Date().toISOString(),
                        model: 'gemini-2.0-flash'
                    });
                }
            }

        } catch (err) {
            console.error('Error fetching AI dashboard data:', err);
            setError(err.message);
        }
    };

    const fetchData = async () => {
        setLoading(true);
        setError(null);
        
        try {
            await fetchDashboardData();
            setLastRefresh(new Date());
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchData();
        
        let interval;
        if (autoRefresh) {
            interval = setInterval(fetchDashboardData, 60000);
        }
        
        return () => {
            if (interval) clearInterval(interval);
        };
    }, [autoRefresh]);

    const formatNumber = (num) => {
        if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
        if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
        return num?.toString() || '0';
    };

    const formatCurrency = (amount) => {
        return `$${parseFloat(amount || 0).toFixed(4)}`;
    };

    const getSuccessRate = (successful, total) => {
        if (!total) return 0;
        return ((successful / total) * 100).toFixed(1);
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'healthy': case 'available': return 'success';
            case 'degraded': case 'warning': return 'warning';
            case 'error': case 'down': return 'error';
            default: return 'default';
        }
    };

    const getStatusIcon = (status) => {
        switch (status) {
            case 'healthy': case 'available': return <CheckCircleIcon />;
            case 'degraded': case 'warning': return <ErrorIcon />;
            case 'error': case 'down': return <ErrorIcon />;
            default: return <CircularProgress size={16} />;
        }
    };

    // 图表数据准备
    const prepareLineChartData = () => {
        const dates = [...new Set(historicalData.map(d => d.date))].sort();
        const providers = [...new Set(historicalData.map(d => d.provider))];
        
        const colors = {
            'gemini': 'rgba(75, 192, 192, 0.8)',
            'openrouter': 'rgba(255, 99, 132, 0.8)',
            'chatbot': 'rgba(54, 162, 235, 0.8)'
        };

        return {
            labels: dates,
            datasets: providers.map(provider => ({
                label: provider,
                data: dates.map(date => {
                    const dayData = historicalData.filter(d => d.date === date && d.provider === provider);
                    return dayData.reduce((acc, d) => acc + d.totalCalls, 0);
                }),
                borderColor: colors[provider] || 'rgba(153, 102, 255, 0.8)',
                backgroundColor: colors[provider] || 'rgba(153, 102, 255, 0.1)',
                fill: false,
                tension: 0.1
            }))
        };
    };

    const prepareServiceTypeData = () => {
        // 使用新的分类系统
        const categoryStats = calculateCategoryStats(historicalData);
        const categories = Object.keys(categoryStats);
        
        if (categories.length === 0) {
            return {
                labels: ['暂无数据'],
                datasets: [{
                    data: [1],
                    backgroundColor: ['rgba(200, 200, 200, 0.8)'],
                    borderWidth: 1
                }]
            };
        }

        return {
            labels: categories.map(cat => getServiceCategoryName(cat)),
            datasets: [{
                data: categories.map(cat => categoryStats[cat].totalCalls),
                backgroundColor: categories.map(cat => getServiceTypeColor(cat)),
                borderWidth: 1
            }]
        };
    };

    const prepareResponseTimeData = () => {
        const dates = [...new Set(historicalData.map(d => d.date))].sort();
        const providers = [...new Set(historicalData.map(d => d.provider))];
        
        return {
            labels: dates,
            datasets: providers.map((provider, index) => ({
                label: `${provider} 响应时间`,
                data: dates.map(date => {
                    const dayData = historicalData.filter(d => d.date === date && d.provider === provider);
                    return dayData.length > 0 ? 
                        dayData.reduce((acc, d) => acc + d.avgResponseTime, 0) / dayData.length : 0;
                }),
                backgroundColor: `hsla(${index * 60}, 70%, 50%, 0.8)`,
                borderColor: `hsla(${index * 60}, 70%, 40%, 1)`,
                borderWidth: 1
            }))
        };
    };

    const chartOptions = {
        responsive: true,
        plugins: {
            legend: {
                position: 'top',
            },
            title: {
                display: false,
            },
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    };

    if (loading && !monitoringData) {
        return (
            <Container maxWidth="xl">
                <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
                    <CircularProgress size={60} />
                </Box>
            </Container>
        );
    }

    return (
        <Container maxWidth="xl">
            <Box sx={{ flexGrow: 1, p: 3 }}>
                {/* 标题栏 */}
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                    <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                        <SmartToyIcon sx={{ mr: 1, fontSize: 40 }} />
                        AI管理中心
                    </Typography>
                    <Box display="flex" alignItems="center" gap={2}>
                        <FormControlLabel
                            control={
                                <Switch
                                    checked={autoRefresh}
                                    onChange={(e) => setAutoRefresh(e.target.checked)}
                                    color="primary"
                                />
                            }
                            label="自动刷新"
                        />
                        <Tooltip title="刷新数据">
                            <IconButton 
                                onClick={fetchData} 
                                disabled={loading}
                                color="primary"
                                sx={{ 
                                    bgcolor: 'primary.main', 
                                    color: 'white',
                                    '&:hover': { bgcolor: 'primary.dark' }
                                }}
                            >
                                <RefreshIcon />
                            </IconButton>
                        </Tooltip>
                        {lastRefresh && (
                            <Typography variant="caption" color="text.secondary">
                                最后更新: {lastRefresh.toLocaleTimeString()}
                            </Typography>
                        )}
                    </Box>
                </Box>

                {error && (
                    <Alert severity="error" sx={{ mb: 3 }}>
                        {error}
                    </Alert>
                )}

                {/* Tab导航 */}
                <Paper sx={{ mb: 3 }}>
                    <Tabs
                        value={currentTab}
                        onChange={(e, newValue) => setCurrentTab(newValue)}
                        indicatorColor="primary"
                        textColor="primary"
                        variant="fullWidth"
                    >
                        <Tab 
                            icon={<AssessmentIcon />} 
                            label="实时监控" 
                            iconPosition="start"
                        />
                        <Tab 
                            icon={<TimelineIcon />} 
                            label="使用统计" 
                            iconPosition="start"
                        />
                        <Tab 
                            icon={<ChatIcon />} 
                            label="CHATBOT监控" 
                            iconPosition="start"
                        />
                    </Tabs>
                </Paper>

                {/* Tab 0: 实时监控 */}
                {currentTab === 0 && (
                    <>
                        {/* 关键指标卡片 */}
                        <Grid container spacing={3} sx={{ mb: 4 }}>
                            <Grid item xs={12} sm={6} md={3}>
                                <Card sx={{ height: '100%', background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
                                    <CardContent>
                                        <Box display="flex" alignItems="center" justifyContent="space-between">
                                            <Box>
                                                <Typography variant="h3" component="div" sx={{ fontWeight: 'bold' }}>
                                                    {formatNumber(monitoringData?.totalCalls || 0)}
                                                </Typography>
                                                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                                                    总对话数
                                                </Typography>
                                            </Box>
                                            <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                                                <ChatIcon sx={{ fontSize: 30 }} />
                                            </Avatar>
                                        </Box>
                                    </CardContent>
                                </Card>
                            </Grid>

                            <Grid item xs={12} sm={6} md={3}>
                                <Card sx={{ height: '100%', background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', color: 'white' }}>
                                    <CardContent>
                                        <Box display="flex" alignItems="center" justifyContent="space-between">
                                            <Box>
                                                <Typography variant="h3" component="div" sx={{ fontWeight: 'bold' }}>
                                                    {formatNumber(monitoringData?.successfulCalls || 0)}
                                                </Typography>
                                                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                                                    活跃用户
                                                </Typography>
                                            </Box>
                                            <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                                                <TrendingUpIcon sx={{ fontSize: 30 }} />
                                            </Avatar>
                                        </Box>
                                    </CardContent>
                                </Card>
                            </Grid>

                            <Grid item xs={12} sm={6} md={3}>
                                <Card sx={{ height: '100%', background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', color: 'white' }}>
                                    <CardContent>
                                        <Box display="flex" alignItems="center" justifyContent="space-between">
                                            <Box>
                                                <Typography variant="h3" component="div" sx={{ fontWeight: 'bold' }}>
                                                    {Math.round(monitoringData?.averageResponseTime || 0)}ms
                                                </Typography>
                                                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                                                    平均响应时间
                                                </Typography>
                                            </Box>
                                            <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                                                <SpeedIcon sx={{ fontSize: 30 }} />
                                            </Avatar>
                                        </Box>
                                    </CardContent>
                                </Card>
                            </Grid>

                            <Grid item xs={12} sm={6} md={3}>
                                <Card sx={{ height: '100%', background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', color: 'white' }}>
                                    <CardContent>
                                        <Box display="flex" alignItems="center" justifyContent="space-between">
                                            <Box>
                                                <Typography variant="h3" component="div" sx={{ fontWeight: 'bold' }}>
                                                    {getSuccessRate(monitoringData?.successfulCalls, monitoringData?.totalCalls)}%
                                                </Typography>
                                                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                                                    响应成功率
                                                </Typography>
                                            </Box>
                                            <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                                                <CheckCircleIcon sx={{ fontSize: 30 }} />
                                            </Avatar>
                                        </Box>
                                    </CardContent>
                                </Card>
                            </Grid>
                        </Grid>

                        {/* 图表区域 */}
                        <Grid container spacing={3} sx={{ mb: 4 }}>
                            <Grid item xs={12} md={8}>
                                <Card>
                                    <CardHeader 
                                        title="AI调用趋势" 
                                        avatar={<TimelineIcon color="primary" />}
                                    />
                                    <CardContent>
                                        <Box height={350}>
                                            {historicalData.length > 0 ? (
                                                <Line data={prepareLineChartData()} options={chartOptions} />
                                            ) : (
                                                <Box display="flex" justifyContent="center" alignItems="center" height="100%">
                                                    <Typography color="text.secondary">暂无数据</Typography>
                                                </Box>
                                            )}
                                        </Box>
                                    </CardContent>
                                </Card>
                            </Grid>

                            <Grid item xs={12} md={4}>
                                <Card>
                                    <CardHeader 
                                        title="服务类型分布" 
                                        avatar={<PsychologyIcon color="primary" />}
                                    />
                                    <CardContent>
                                        <Box height={350}>
                                            {historicalData.length > 0 ? (
                                                <Doughnut 
                                                    data={prepareServiceTypeData()} 
                                                    options={{
                                                        responsive: true,
                                                        maintainAspectRatio: false,
                                                        plugins: {
                                                            legend: {
                                                                position: 'bottom',
                                                            }
                                                        }
                                                    }}
                                                />
                                            ) : (
                                                <Box display="flex" justifyContent="center" alignItems="center" height="100%">
                                                    <Typography color="text.secondary">暂无数据</Typography>
                                                </Box>
                                            )}
                                        </Box>
                                    </CardContent>
                                </Card>
                            </Grid>
                        </Grid>

                        {/* 提供商状态和错误日志 */}
                        <Grid container spacing={3}>
                            <Grid item xs={12} md={6}>
                                <Card>
                                    <CardHeader 
                                        title="AI服务状态" 
                                        avatar={<CloudQueueIcon color="primary" />}
                                    />
                                    <CardContent>
                                        <List>
                                            {monitoringData?.providers?.map((provider, index) => (
                                                <React.Fragment key={index}>
                                                    <ListItem>
                                                        <ListItemAvatar>
                                                            <Avatar sx={{ bgcolor: getStatusColor(provider.status) === 'success' ? 'success.main' : 'error.main' }}>
                                                                {getStatusIcon(provider.status)}
                                                            </Avatar>
                                                        </ListItemAvatar>
                                                        <ListItemText
                                                            primary={
                                                                <Box display="flex" alignItems="center" gap={1}>
                                                                    <Typography variant="subtitle1" fontWeight="bold">
                                                                        {provider.name}
                                                                    </Typography>
                                                                    <Chip 
                                                                        label={provider.serviceType} 
                                                                        size="small" 
                                                                        color="primary" 
                                                                        variant="outlined" 
                                                                    />
                                                                </Box>
                                                            }
                                                            secondary={
                                                                <Box>
                                                                    <Typography variant="body2" color="text.secondary">
                                                                        模型: {provider.model || 'N/A'}
                                                                    </Typography>
                                                                    <Typography variant="body2" color="text.secondary">
                                                                        调用: {formatNumber(provider.totalCalls)} | 
                                                                        成功率: {getSuccessRate(provider.successfulCalls, provider.totalCalls)}% | 
                                                                        响应: {provider.averageResponseTime}ms
                                                                    </Typography>
                                                                </Box>
                                                            }
                                                        />
                                                    </ListItem>
                                                    {index < monitoringData.providers.length - 1 && <Divider />}
                                                </React.Fragment>
                                            ))}
                                        </List>
                                    </CardContent>
                                </Card>
                            </Grid>

                            <Grid item xs={12} md={6}>
                                <Card>
                                    <CardHeader 
                                        title="最近错误日志" 
                                        avatar={<ErrorIcon color="error" />}
                                    />
                                    <CardContent>
                                        <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
                                            {monitoringData?.recentErrors?.length > 0 ? (
                                                monitoringData.recentErrors.map((error, index) => (
                                                    <Alert
                                                        key={index}
                                                        severity="error"
                                                        sx={{ mb: 2 }}
                                                        variant="outlined"
                                                    >
                                                        <Typography variant="subtitle2" fontWeight="bold">
                                                            {error.provider} - {error.serviceType}
                                                        </Typography>
                                                        <Typography variant="body2" sx={{ mt: 1 }}>
                                                            {error.message}
                                                        </Typography>
                                                        <Typography variant="caption" color="text.secondary">
                                                            {new Date(error.timestamp).toLocaleString()}
                                                        </Typography>
                                                    </Alert>
                                                ))
                                            ) : (
                                                <Box display="flex" justifyContent="center" alignItems="center" height={200}>
                                                    <Typography variant="body2" color="text.secondary">
                                                        🎉 暂无错误记录
                                                    </Typography>
                                                </Box>
                                            )}
                                        </Box>
                                    </CardContent>
                                </Card>
                            </Grid>
                        </Grid>
                    </>
                )}

                {/* Tab 1: 使用统计 */}
                {currentTab === 1 && (
                    <>
                        {/* 查询控制 */}
                        <Card sx={{ mb: 3 }}>
                            <CardContent>
                                <Grid container spacing={2} alignItems="center">
                                    <Grid item xs={12} sm={6} md={3}>
                                        <TextField
                                            label="开始日期"
                                            type="date"
                                            value={startDate}
                                            onChange={(e) => setStartDate(e.target.value)}
                                            InputLabelProps={{ shrink: true }}
                                            fullWidth
                                            size="small"
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={3}>
                                        <TextField
                                            label="结束日期"
                                            type="date"
                                            value={endDate}
                                            onChange={(e) => setEndDate(e.target.value)}
                                            InputLabelProps={{ shrink: true }}
                                            fullWidth
                                            size="small"
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={3}>
                                        <FormControl fullWidth size="small">
                                            <InputLabel>聚合维度</InputLabel>
                                            <Select
                                                value={groupBy}
                                                label="聚合维度"
                                                onChange={(e) => setGroupBy(e.target.value)}
                                            >
                                                <MenuItem value="hour">小时</MenuItem>
                                                <MenuItem value="day">天</MenuItem>
                                                <MenuItem value="week">周</MenuItem>
                                                <MenuItem value="month">月</MenuItem>
                                            </Select>
                                        </FormControl>
                                    </Grid>
                                    <Grid item xs={12} sm={6} md={3}>
                                        <Button
                                            variant="contained"
                                            onClick={fetchData}
                                            disabled={loading}
                                            fullWidth
                                            startIcon={<RefreshIcon />}
                                        >
                                            查询
                                        </Button>
                                    </Grid>
                                </Grid>
                            </CardContent>
                        </Card>

                        {/* 响应时间图表 */}
                        <Grid container spacing={3} sx={{ mb: 3 }}>
                            <Grid item xs={12}>
                                <Card>
                                    <CardHeader 
                                        title="响应时间分析" 
                                        avatar={<SpeedIcon color="primary" />}
                                    />
                                    <CardContent>
                                        <Box height={400}>
                                            {historicalData.length > 0 ? (
                                                <Bar data={prepareResponseTimeData()} options={chartOptions} />
                                            ) : (
                                                <Box display="flex" justifyContent="center" alignItems="center" height="100%">
                                                    <Typography color="text.secondary">暂无数据</Typography>
                                                </Box>
                                            )}
                                        </Box>
                                    </CardContent>
                                </Card>
                            </Grid>
                        </Grid>

                        {/* 统计数据表格 */}
                        <Card>
                            <CardHeader 
                                title="详细使用统计" 
                                avatar={<AnalyticsIcon color="primary" />}
                            />
                            <CardContent>
                                <TableContainer>
                                    <Table>
                                        <TableHead>
                                            <TableRow>
                                                <TableCell>日期</TableCell>
                                                <TableCell>Provider</TableCell>
                                                <TableCell>服务类型</TableCell>
                                                <TableCell>模型</TableCell>
                                                <TableCell>调用次数</TableCell>
                                                <TableCell>成功次数</TableCell>
                                                <TableCell>失败次数</TableCell>
                                                <TableCell>成功率</TableCell>
                                                <TableCell>平均响应时间</TableCell>
                                            </TableRow>
                                        </TableHead>
                                        <TableBody>
                                            {historicalData.map((stat, index) => (
                                                <TableRow key={index} hover>
                                                    <TableCell>{stat.date}</TableCell>
                                                    <TableCell>
                                                        <Chip 
                                                            label={stat.provider} 
                                                            size="small" 
                                                            color="primary" 
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <Chip 
                                                            label={stat.serviceType} 
                                                            size="small" 
                                                            variant="outlined"
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <Typography variant="body2" color="text.secondary">
                                                            {stat.modelName || 'N/A'}
                                                        </Typography>
                                                    </TableCell>
                                                    <TableCell>{formatNumber(stat.totalCalls)}</TableCell>
                                                    <TableCell>{formatNumber(stat.successfulCalls)}</TableCell>
                                                    <TableCell>{formatNumber(stat.failedCalls)}</TableCell>
                                                    <TableCell>
                                                        <Box display="flex" alignItems="center">
                                                            <LinearProgress 
                                                                variant="determinate" 
                                                                value={getSuccessRate(stat.successfulCalls, stat.totalCalls)} 
                                                                sx={{ width: 60, mr: 1 }}
                                                            />
                                                            <Typography variant="body2">
                                                                {getSuccessRate(stat.successfulCalls, stat.totalCalls)}%
                                                            </Typography>
                                                        </Box>
                                                    </TableCell>
                                                    <TableCell>{Math.round(stat.avgResponseTime)}ms</TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>
                                </TableContainer>
                            </CardContent>
                        </Card>
                    </>
                )}

                {/* Tab 2: Chatbot监控 */}
                {currentTab === 2 && (
                    <>
                        <Grid container spacing={3}>
                            <Grid item xs={12} sm={6} md={3}>
                                <Card sx={{ height: '100%', background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
                                    <CardContent>
                                        <Box display="flex" alignItems="center" justifyContent="space-between">
                                            <Box>
                                                <Typography variant="h4" component="div" sx={{ fontWeight: 'bold' }}>
                                                    {formatNumber(chatbotMetrics?.totalConversations || 0)}
                                                </Typography>
                                                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                                                    总对话数
                                                </Typography>
                                            </Box>
                                            <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                                                <ChatIcon sx={{ fontSize: 30 }} />
                                            </Avatar>
                                        </Box>
                                    </CardContent>
                                </Card>
                            </Grid>

                            <Grid item xs={12} sm={6} md={3}>
                                <Card sx={{ height: '100%', background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', color: 'white' }}>
                                    <CardContent>
                                        <Box display="flex" alignItems="center" justifyContent="space-between">
                                            <Box>
                                                <Typography variant="h4" component="div" sx={{ fontWeight: 'bold' }}>
                                                    {formatNumber(chatbotMetrics?.activeUsers || 0)}
                                                </Typography>
                                                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                                                    活跃用户
                                                </Typography>
                                            </Box>
                                            <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                                                <TrendingUpIcon sx={{ fontSize: 30 }} />
                                            </Avatar>
                                        </Box>
                                    </CardContent>
                                </Card>
                            </Grid>

                            <Grid item xs={12} sm={6} md={3}>
                                <Card sx={{ height: '100%', background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', color: 'white' }}>
                                    <CardContent>
                                        <Box display="flex" alignItems="center" justifyContent="space-between">
                                            <Box>
                                                <Typography variant="h4" component="div" sx={{ fontWeight: 'bold' }}>
                                                    {chatbotMetrics?.averageResponseTime || 0}ms
                                                </Typography>
                                                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                                                    平均响应时间
                                                </Typography>
                                            </Box>
                                            <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                                                <SpeedIcon sx={{ fontSize: 30 }} />
                                            </Avatar>
                                        </Box>
                                    </CardContent>
                                </Card>
                            </Grid>

                            <Grid item xs={12} sm={6} md={3}>
                                <Card sx={{ height: '100%', background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', color: 'white' }}>
                                    <CardContent>
                                        <Box display="flex" alignItems="center" justifyContent="space-between">
                                            <Box>
                                                <Typography variant="h4" component="div" sx={{ fontWeight: 'bold' }}>
                                                    {getSuccessRate(chatbotMetrics?.successfulResponses, chatbotMetrics?.totalRequests)}%
                                                </Typography>
                                                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                                                    响应成功率
                                                </Typography>
                                            </Box>
                                            <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                                                <CheckCircleIcon sx={{ fontSize: 30 }} />
                                            </Avatar>
                                        </Box>
                                    </CardContent>
                                </Card>
                            </Grid>

                            <Grid item xs={12}>
                                <Card>
                                    <CardHeader 
                                        title="Chatbot 服务详情" 
                                        avatar={<SmartToyIcon color="primary" />}
                                    />
                                    <CardContent>
                                        <Grid container spacing={3}>
                                            <Grid item xs={12} md={6}>
                                                <Typography variant="h6" gutterBottom color="primary">
                                                    服务状态
                                                </Typography>
                                                <Box display="flex" alignItems="center" gap={2} mb={3}>
                                                    <Chip
                                                        icon={chatbotMetrics?.status === 'healthy' ? <CheckCircleIcon /> : <ErrorIcon />}
                                                        label={chatbotMetrics?.status === 'healthy' ? '正常运行' : '状态未知'}
                                                        color={chatbotMetrics?.status === 'healthy' ? 'success' : 'default'}
                                                        variant="filled"
                                                        size="medium"
                                                    />
                                                </Box>
                                                
                                                <Typography variant="h6" gutterBottom color="primary">
                                                    AI模型
                                                </Typography>
                                                <Chip 
                                                    label={chatbotMetrics?.model || 'gemini-2.0-flash'} 
                                                    color="primary" 
                                                    variant="outlined"
                                                    icon={<AutoModeIcon />}
                                                />
                                            </Grid>
                                            
                                            <Grid item xs={12} md={6}>
                                                <Typography variant="h6" gutterBottom color="primary">
                                                    最后健康检查
                                                </Typography>
                                                <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                                                    {chatbotMetrics?.lastHealthCheck ? 
                                                        new Date(chatbotMetrics.lastHealthCheck).toLocaleString() : 
                                                        '未知'
                                                    }
                                                </Typography>

                                                <Typography variant="h6" gutterBottom color="primary">
                                                    服务地址
                                                </Typography>
                                                <Chip 
                                                    label="localhost:3002" 
                                                    color="info" 
                                                    variant="outlined"
                                                    icon={<ApiIcon />}
                                                />
                                            </Grid>
                                        </Grid>
                                    </CardContent>
                                </Card>
                            </Grid>
                        </Grid>
                    </>
                )}
            </Box>
        </Container>
    );
};

export default AIDashboard;
