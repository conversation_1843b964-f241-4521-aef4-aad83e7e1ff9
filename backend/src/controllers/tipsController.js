const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

let useMockData = false;
let useLocalFiles = false;

// Local files configuration
const LOCAL_TIPS_PATH = process.env.LOCAL_TIPS_PATH || path.join(__dirname, '../../documents');
const LOCAL_TIPS_ASSETS_PATH = process.env.LOCAL_TIPS_ASSETS_PATH || path.join(__dirname, '../../documents/assets');
const DOCUMENTS_BASE_PATH = path.join(__dirname, '../../documents');

// Check if local path exists
console.log(`Checking if local path exists: ${LOCAL_TIPS_PATH}`);
if (fs.existsSync(LOCAL_TIPS_PATH)) {
    console.log(`Using local files from ${LOCAL_TIPS_PATH}, local files found`);
    useLocalFiles = true;
} else {
    console.log(`Local path ${LOCAL_TIPS_PATH} not found, using mock data`);
    useMockData = true;
}

// Category Translation Mapping
const categoryTranslation = {
    'general': '综合健康',
    'nutrition': '营养膳食',
    'fitness': '运动健身',
    'wellness': '身心健康',
    'mental-health': '心理健康',
    'preventive-care': '疾病预防',
    'disease-info': '疾病信息',
    'health': '健康资讯', // Generic health folder
    'fm': '功能医学',
    'members': '会员专区',
    'tips': '健康小贴士'
    // Add more mappings as new categories appear
};

// Function to parse YAML front matter from markdown content
const parseFrontMatter = (content) => {
    const frontMatterRegex = /^---\r?\n([\s\S]*?)\r?\n---\r?\n([\s\S]*)$/;
    const match = content.match(frontMatterRegex);

    if (!match) {
        return { metadata: {}, content };
    }

    const frontMatter = match[1];
    const actualContent = match[2];

    const metadata = {};
    frontMatter.split('\n').forEach(line => {
        const [key, ...valueParts] = line.split(':');
        if (key && valueParts.length) {
            const value = valueParts.join(':').trim();
            metadata[key.trim()] = value;
        }
    });

    return { metadata, content: actualContent };
};

// Function to extract metadata from filename if no front matter is found
const extractMetadataFromFilename = (filename) => {
    const nameWithoutExt = filename.replace('.md', '');
    const parts = nameWithoutExt.split('-');

    let category = 'general';
    if (parts.length > 1) {
        const possibleCategory = parts[parts.length - 1];
        if (['nutrition', 'fitness', 'wellness', 'mental-health'].includes(possibleCategory)) {
            category = possibleCategory;
            parts.pop();
        }
    }

    const title = parts.map(part =>
        part.charAt(0).toUpperCase() + part.slice(1)
    ).join(' ');

    return { title, category };
};

// Mock data for testing or when local files are not available
const mockTips = [
    {
        filename: 'healthy-eating.md',
        title: 'Healthy Eating Guidelines',
        category: 'nutrition',
        lastModified: '2023-05-15T10:30:00Z',
        content: `---
title: Healthy Eating Guidelines
category: nutrition
tags: diet, food, nutrition
featured: true
---
# Healthy Eating Guidelines

## Daily Nutritional Goals

* Eat at least 5 servings of fruits and vegetables
* Choose whole grains over refined carbohydrates
* Include lean protein with each meal
* Stay hydrated with at least 8 glasses of water

## Meal Planning Tips

1. Prepare meals in advance
2. Use a variety of colorful foods
3. Control portion sizes
4. Limit processed foods and added sugars`
    },
    {
        filename: 'stress-management.md',
        title: 'Stress Management Techniques',
        category: 'mental-health',
        lastModified: '2023-06-20T14:45:00Z',
        content: `---
title: Stress Management Techniques
category: mental-health
tags: stress, anxiety, wellbeing
featured: true
---
# Stress Management Techniques

## Quick Stress Relievers

* Deep breathing exercises
* Progressive muscle relaxation
* Mindfulness meditation
* Short walks outdoors

## Long-term Stress Management

1. Regular exercise routine
2. Adequate sleep (7-9 hours)
3. Maintaining social connections
4. Professional counseling when needed`
    },
    {
        filename: 'exercise-basics.md',
        title: 'Exercise Fundamentals',
        category: 'fitness',
        lastModified: '2023-07-05T09:15:00Z',
        content: `---
title: Exercise Fundamentals
category: fitness
tags: workout, exercise, strength
featured: false
---
# Exercise Fundamentals

## Getting Started

* Begin with a 5-10 minute warm-up
* Aim for 150 minutes of moderate activity weekly
* Include strength training 2-3 times per week
* Always cool down and stretch

## Finding What Works For You

1. Try different activities to find enjoyable ones
2. Start slowly and gradually increase intensity
3. Consider group classes for motivation
4. Track progress to stay motivated`
    },
    {
        filename: 'sleep-improvement.md',
        title: 'Better Sleep Habits',
        category: 'wellness',
        lastModified: '2023-08-12T16:20:00Z',
        content: `---
title: Better Sleep Habits
category: wellness
tags: sleep, rest, wellness
featured: true
---
# Better Sleep Habits

## Creating an Optimal Sleep Environment

* Keep your bedroom cool, dark, and quiet
* Use comfortable mattress and pillows
* Remove electronic devices from the bedroom
* Maintain a consistent sleep schedule

## Helpful Bedtime Routines

1. Avoid caffeine and heavy meals before bed
2. Practice relaxation techniques
3. Limit screen time 1-2 hours before sleep
4. Consider journaling to clear your mind`
    }
];

// Helper function to determine content type
const determineContentType = (filename) => {
    const ext = path.extname(filename).toLowerCase();
    const contentTypes = {
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.gif': 'image/gif',
        '.pdf': 'application/pdf',
        '.doc': 'application/msword',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.xls': 'application/vnd.ms-excel',
        '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        '.ppt': 'application/vnd.ms-powerpoint',
        '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        '.txt': 'text/plain',
        '.md': 'text/markdown',
        '.csv': 'text/csv'
    };

    return contentTypes[ext] || 'application/octet-stream';
};

// Helper function to scan directories for markdown files (Updated & Recursive)
const scanDirectoriesForMarkdown = (basePath = DOCUMENTS_BASE_PATH) => {
    let allFiles = [];
    console.log(`Scanning for markdown files starting from base path: ${basePath}`);

    try {
        // Read all entries in the base path
        const entries = fs.readdirSync(basePath, { withFileTypes: true });

        // Process files directly in the base path
        const filesInBase = entries
            .filter(entry => entry.isFile() && entry.name.endsWith('.md') && !entry.name.startsWith('.'))
            .map(entry => ({
                filename: entry.name,
                filePath: path.join(basePath, entry.name),
                category: path.basename(basePath) === 'documents' ? 'general' : path.basename(basePath) // Use parent folder name as category, or 'general' for root
            }));
        if (filesInBase.length > 0) {
            console.log(`Found ${filesInBase.length} markdown files in ${basePath}`);
            allFiles = [...allFiles, ...filesInBase];
        }

        // Process subdirectories recursively
        const directories = entries.filter(entry => entry.isDirectory() && !entry.name.startsWith('.'));
        for (const dir of directories) {
            const subDirPath = path.join(basePath, dir.name);
            console.log(`Scanning subdirectory: ${subDirPath}`);
            // Recursively scan subdirectory
            const filesInSubDir = scanDirectoriesForMarkdown(subDirPath);
            allFiles = [...allFiles, ...filesInSubDir];
        }

    } catch (err) {
        // Log specific errors for base path vs subdirectories if needed
        if (basePath === DOCUMENTS_BASE_PATH) {
            console.error(`Error scanning base documents directory ${basePath}:`, err);
        } else {
            console.warn(`Warning: Could not scan directory ${basePath}:`, err.code);
        }
        // Continue scanning other directories even if one fails
    }

    // Only log total at the initial call level
    if (basePath === DOCUMENTS_BASE_PATH) {
        console.log(`Total markdown files found across all scanned directories: ${allFiles.length}`);
    }

    return allFiles;
};

// Helper function to get mock categories
const getMockCategories = () => {
    return [
        { slug: 'nutrition', name: '营养膳食' },
        { slug: 'mental-health', name: '心理健康' },
        { slug: 'fitness', name: '运动健身' },
        { slug: 'wellness', name: '身心健康' },
        { slug: 'preventive-care', name: '疾病预防' },
        { slug: 'disease-info', name: '疾病信息' },
        { slug: 'sleep', name: '睡眠健康' },
        { slug: 'general', name: '常见问题' }
    ];
};

// 函数用于从内容中提取摘要
const extractSummaryFromContent = (content) => {
    if (!content) return '';

    // 寻找摘要部分
    const summaryRegex = /## 摘要\s*\n([\s\S]*?)(?=\n## |$)/;
    const match = content.match(summaryRegex);

    if (match && match[1]) {
        return match[1].trim();
    }

    // 如果没有找到摘要部分，提取内容的前100个字符作为摘要
    const firstParagraph = content.split('\n\n')[1] || '';
    return firstParagraph.substring(0, 100) + (firstParagraph.length > 100 ? '...' : '');
};

const tipsController = {
    // Get available categories with translation
    listCategories: async (req, res) => {
        try {
            console.log('API request received for /api/tips/categories');
            if (useLocalFiles) {
                console.log(`Using LOCAL_TIPS_PATH: ${LOCAL_TIPS_PATH}`);
                console.log(`useLocalFiles is set to: ${useLocalFiles}`);

                const allMarkdownFiles = scanDirectoriesForMarkdown(DOCUMENTS_BASE_PATH);
                console.log(`Found ${allMarkdownFiles.length} markdown files for category extraction`);

                if (allMarkdownFiles.length === 0) {
                    console.log('No markdown files found. Returning mock categories instead.');
                    const mockCategories = getMockCategories();
                    return res.json(mockCategories);
                }

                console.log('Sample of markdown files found:', allMarkdownFiles.slice(0, 3));

                // Extract categories with verbose logging and error handling
                const categories = [];
                const errors = [];

                allMarkdownFiles.forEach((file, index) => {
                    try {
                        console.log(`Processing file [${index}]: ${file.filePath}`);
                        const content = fs.readFileSync(file.filePath, 'utf8');
                        const { metadata } = parseFrontMatter(content);

                        const category = metadata.category || file.category || 'general';
                        console.log(`Extracted category: "${category}" from file: ${file.filename}`);
                        categories.push(category);
                    } catch (err) {
                        console.error(`Error processing file ${file.filePath}:`, err);
                        errors.push({ file: file.filePath, error: err.message });
                        // Still add the fallback category
                        categories.push(file.category || 'general');
                    }
                });

                if (errors.length > 0) {
                    console.warn(`Encountered ${errors.length} errors while processing files`);
                }

                console.log('All categories extracted:', categories);

                const uniqueCategories = [...new Set(categories)];
                console.log('Unique category slugs extracted:', uniqueCategories);

                const translatedCategories = uniqueCategories.map(slug => {
                    const result = {
                        slug: slug,
                        name: categoryTranslation[slug] || slug // Provide translation or fallback to slug
                    };
                    console.log(`Translating category "${slug}" to "${result.name}"`);
                    return result;
                });

                console.log('Returning translated categories:', JSON.stringify(translatedCategories));
                res.json(translatedCategories);
            } else {
                // Mock categories if needed
                console.log('useLocalFiles is false, returning mock categories');
                const mockCategories = getMockCategories();
                console.log('Returning mock categories:', mockCategories);
                res.json(mockCategories);
            }
        } catch (error) {
            console.error('Error fetching categories:', error);
            // Return mock categories as fallback on error
            const mockCategories = getMockCategories();
            console.log('Error occurred, returning mock categories as fallback');
            res.json(mockCategories);
        }
    },

    // List all tips (Add abstract and categoryName)
    listTips: async (req, res) => {
        try {
            if (useLocalFiles) {
                const allMarkdownFiles = scanDirectoriesForMarkdown(DOCUMENTS_BASE_PATH);
                const tips = allMarkdownFiles.map(({ filename, filePath, category: directoryCategory }) => {
                    try {
                        const content = fs.readFileSync(filePath, 'utf8');
                        const { metadata } = parseFrontMatter(content);
                        const stats = fs.statSync(filePath);
                        const finalCategorySlug = metadata.category || directoryCategory || 'general';

                        // 提取摘要
                        const abstract = metadata.abstract || extractSummaryFromContent(content);

                        return {
                            filename,
                            title: metadata.title || extractMetadataFromFilename(filename).title,
                            category: finalCategorySlug,
                            categoryName: categoryTranslation[finalCategorySlug] || finalCategorySlug, // Add translated name
                            tags: metadata.tags ? metadata.tags.split(',').map(tag => tag.trim()) : [],
                            abstract: abstract, // 使用提取的摘要
                            featured: metadata.featured === 'true',
                            lastModified: stats.mtime.toISOString(),
                            // path: filePath // Maybe remove path from final response
                        };
                    } catch (err) {
                        console.warn(`Error processing file ${filePath}:`, err);
                        return null;
                    }
                }).filter(Boolean);

                // --- DEBUG LOGGING START ---
                if (tips.length > 0) {
                    console.log(`[listTips] First article data being sent: title='${tips[0].title}', abstract='${tips[0].abstract}', categoryName='${tips[0].categoryName}'`);
                } else {
                    console.log('[listTips] No tips found to send.');
                }
                // --- DEBUG LOGGING END ---

                return res.json(tips);
            } else {
                // Mock data logic needs update for abstract and categoryName
                const tips = mockTips.map(tip => {
                    const { metadata } = parseFrontMatter(tip.content);
                    const slug = metadata.category || tip.category || 'general';

                    // 提取摘要
                    const abstract = metadata.abstract || extractSummaryFromContent(tip.content);

                    return {
                        filename: tip.filename,
                        title: metadata.title || tip.title,
                        category: slug,
                        categoryName: categoryTranslation[slug] || slug,
                        tags: metadata.tags ? metadata.tags.split(',').map(tag => tag.trim()) : [],
                        abstract: abstract, // 使用提取的摘要
                        featured: metadata.featured === 'true',
                        lastModified: tip.lastModified
                    };
                });
                return res.json(tips);
            }
        } catch (error) {
            console.error('Error fetching tips:', error);
            res.status(500).json({ message: 'Failed to fetch tips', error: error.message });
        }
    },

    // List tips by category (Add abstract and categoryName)
    listTipsByCategory: async (req, res) => {
        try {
            const { category } = req.params;
            if (useLocalFiles) {
                const allMarkdownFiles = scanDirectoriesForMarkdown(DOCUMENTS_BASE_PATH);
                const filteredTips = allMarkdownFiles.map(({ filename, filePath, category: directoryCategory }) => {
                    try {
                        const content = fs.readFileSync(filePath, 'utf8');
                        const { metadata } = parseFrontMatter(content);
                        const stats = fs.statSync(filePath);
                        const finalCategorySlug = metadata.category || directoryCategory || 'general';

                        if (finalCategorySlug !== category) {
                            return null;
                        }

                        // 提取摘要
                        const abstract = metadata.abstract || extractSummaryFromContent(content);

                        return {
                            filename,
                            title: metadata.title || extractMetadataFromFilename(filename).title,
                            category: finalCategorySlug,
                            categoryName: categoryTranslation[finalCategorySlug] || finalCategorySlug,
                            tags: metadata.tags ? metadata.tags.split(',').map(tag => tag.trim()) : [],
                            abstract: abstract, // 使用提取的摘要
                            featured: metadata.featured === 'true',
                            lastModified: stats.mtime.toISOString(),
                            // path: filePath
                        };
                    } catch (err) {
                        console.warn(`Error processing file ${filePath} for category listing:`, err);
                        return null;
                    }
                }).filter(Boolean);

                // --- DEBUG LOGGING START ---
                if (filteredTips.length > 0) {
                    console.log(`[listTipsByCategory: ${category}] First article data being sent: title='${filteredTips[0].title}', abstract='${filteredTips[0].abstract}', categoryName='${filteredTips[0].categoryName}'`);
                } else {
                    console.log(`[listTipsByCategory: ${category}] No tips found for this category.`);
                }
                // --- DEBUG LOGGING END ---

                return res.json(filteredTips);
            } else {
                // Mock data logic needs update
                const tips = mockTips.map(tip => {
                    const { metadata } = parseFrontMatter(tip.content);
                    const slug = metadata.category || tip.category || 'general';
                    return {
                        filename: tip.filename,
                        title: metadata.title || tip.title,
                        category: slug,
                        categoryName: categoryTranslation[slug] || slug,
                        tags: metadata.tags ? metadata.tags.split(',').map(tag => tag.trim()) : [],
                        abstract: metadata.abstract || '',
                        featured: metadata.featured === 'true',
                        lastModified: tip.lastModified
                    };
                });
                const filteredTips = tips.filter(tip => tip.category === category);
                return res.json(filteredTips);
            }
        } catch (error) {
            console.error('Error fetching tips by category:', error);
            res.status(500).json({ message: `Failed to fetch tips for category: ${req.params.category}`, error: error.message });
        }
    },

    // Get a specific tip by filename (Add abstract and categoryName)
    getTip: async (req, res) => {
        try {
            const { filename } = req.params;
            if (useLocalFiles) {
                const allMarkdownFiles = scanDirectoriesForMarkdown(DOCUMENTS_BASE_PATH);
                const fileInfo = allMarkdownFiles.find(file => file.filename === filename);
                if (!fileInfo) {
                    return res.status(404).json({ message: 'Tip not found' });
                }
                const filePath = fileInfo.filePath;
                const directoryCategory = fileInfo.category;
                const fileContent = fs.readFileSync(filePath, 'utf8');
                const { metadata, content } = parseFrontMatter(fileContent);
                const stats = fs.statSync(filePath);
                const finalCategorySlug = metadata.category || directoryCategory || 'general';

                // 提取摘要
                const abstract = metadata.abstract || extractSummaryFromContent(fileContent);

                res.json({
                    filename,
                    title: metadata.title || extractMetadataFromFilename(filename).title,
                    category: finalCategorySlug,
                    categoryName: categoryTranslation[finalCategorySlug] || finalCategorySlug,
                    tags: metadata.tags ? metadata.tags.split(',').map(tag => tag.trim()) : [],
                    abstract: abstract, // 使用提取的摘要
                    featured: metadata.featured === 'true',
                    lastModified: stats.mtime.toISOString(),
                    content
                });
            } else {
                // Mock data needs update
                const tip = mockTips.find(t => t.filename === filename);
                if (!tip) {
                    return res.status(404).json({ message: 'Tip not found' });
                }
                const { metadata, content } = parseFrontMatter(tip.content);
                const slug = metadata.category || tip.category || 'general';

                // 提取摘要
                const abstract = metadata.abstract || extractSummaryFromContent(tip.content);

                return res.json({
                    filename: tip.filename,
                    title: metadata.title || tip.title,
                    category: slug,
                    categoryName: categoryTranslation[slug] || slug,
                    tags: metadata.tags ? metadata.tags.split(',').map(tag => tag.trim()) : [],
                    abstract: abstract, // 使用提取的摘要
                    featured: metadata.featured === 'true',
                    lastModified: tip.lastModified,
                    content
                });
            }
        } catch (error) {
            console.error('Error fetching tip:', error);
            res.status(500).json({ message: `Failed to fetch tip: ${req.params.filename}`, error: error.message });
        }
    },

    // Get asset related to tips (images, etc.)
    getAsset: async (req, res) => {
        try {
            if (useLocalFiles) {
                const { assetPath } = req.params;
                const sanitizedPath = assetPath.replace(/\.\.\//g, '');
                const fullAssetPath = path.join(LOCAL_TIPS_ASSETS_PATH, sanitizedPath);
                if (!fs.existsSync(fullAssetPath)) {
                    return res.status(404).json({ message: 'Asset not found' });
                }
                const content = fs.readFileSync(fullAssetPath);
                const contentType = determineContentType(sanitizedPath);
                res.setHeader('Content-Type', contentType);
                res.send(content);
            } else {
                return res.status(404).json({ message: 'Assets not available in mock mode' });
            }
        } catch (error) {
            console.error('Error fetching asset:', error);
            res.status(500).json({ message: `Failed to fetch asset: ${req.params.assetPath}`, error: error.message });
        }
    }
};

module.exports = tipsController; 