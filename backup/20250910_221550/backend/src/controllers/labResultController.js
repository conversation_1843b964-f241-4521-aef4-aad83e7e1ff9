const pool = require('../config/database');
const fs = require('fs');
const path = require('path');
const HL7ParserService = require('../services/hl7ParserService');

/**
 * Global error handler middleware for JSON responses
 */
const handleJsonResponse = (res, statusCode, data) => {
    res.status(statusCode);
    res.setHeader('Content-Type', 'application/json');
    res.json(data);
    return;
};

/**
 * 获取患者的所有文档
 * 包括LifeLabs和其他所有类型的文档
 */
exports.getPatientLabResults = async (req, res) => {
    try {
        // 从已验证的token中获取用户ID
        const userId = req.user.id;

        // 获取用户的demographic_no
        const [userRows] = await pool.query(
            `SELECT demographic_no FROM user_auth WHERE id = ?`,
            [userId]
        );

        if (userRows.length === 0 || !userRows[0].demographic_no) {
            return res.status(404).json({ message: 'No linked patient record found for this user' });
        }

        const demographicNo = userRows[0].demographic_no;

        // 获取患者的所有文档 (lab or consult)
        const [reports] = await pool.query(
            `SELECT
                d.document_no,
                d.docdesc,
                d.doctype,
                d.contenttype,
                d.updatedatetime,
                d.observationdate,
                d.docfilename
            FROM document d
            JOIN ctl_document c ON d.document_no = c.document_no
            WHERE c.module = 'demographic'
            AND c.module_id = ?
            AND d.observationdate >= '2023-07-01'
            ORDER BY d.updatedatetime DESC`,
            [demographicNo]
        );

        // 不再 filter，只是 map
        const formattedReports = reports.map(report => {
            const docPath = path.join('/OscarDocument/oscar/document', report.docfilename);
            const docCachePath = path.join('/OscarDocument/oscar/document_cache', report.docfilename);
            let filePath = null;
            let fileExists = false;
            let fileSize = 0;

            if (fs.existsSync(docPath)) {
                filePath = docPath;
            } else if (fs.existsSync(docCachePath)) {
                filePath = docCachePath;
            }

            if (filePath) {
                try {
                    const stats = fs.statSync(filePath);
                    fileExists = stats.size > 0;
                    fileSize = stats.size;
                } catch (e) {
                    fileExists = false;
                }
            }

            return {
                id: report.document_no,
                title: report.docdesc || (report.doctype ? `${report.doctype.charAt(0).toUpperCase() + report.doctype.slice(1)} Document` : 'Document'),
                type: report.doctype || 'others',
                contentType: report.contenttype,
                updatedAt: report.updatedatetime,
                observationDate: report.observationdate,
                filename: report.docfilename,
                fileExists,
                fileSize
            };
        });

        res.json({
            success: true,
            count: formattedReports.length, // Use the count of valid reports
            reports: formattedReports
        });

    } catch (error) {
        console.error('Error fetching documents:', error);
        return handleJsonResponse(res, 500, { message: 'Failed to fetch documents' });
    }
};

/**
 * 获取特定实验室报告的内容
 */
exports.getLabReportContent = async (req, res) => {
    try {
        const { reportId } = req.params;
        const userId = req.user.id;

        console.log(`getLabReportContent: Processing request for reportId=${reportId} by userId=${userId}`);

        // 获取用户的demographic_no
        const [userRows] = await pool.query(
            `SELECT demographic_no FROM user_auth WHERE id = ?`,
            [userId]
        );

        if (userRows.length === 0 || !userRows[0].demographic_no) {
            console.log(`getLabReportContent: No demographic linked to userId=${userId}`);
            return handleJsonResponse(res, 404, { message: 'No linked patient record found for this user' });
        }

        const userDemographicNo = userRows[0].demographic_no;
        console.log(`getLabReportContent: User demographic found: ${userDemographicNo}`);

        // Get the demographic number associated with this report
        const [reportDemoRows] = await pool.query(
            `SELECT c.module_id as demographic_no
            FROM document d
            JOIN ctl_document c ON d.document_no = c.document_no
            WHERE c.module = 'demographic'
            AND d.document_no = ?
            AND d.observationdate >= '2023-07-01'`,
            [reportId]
        );

        if (reportDemoRows.length === 0) {
            console.log(`getLabReportContent: No document found with reportId=${reportId}`);
            return handleJsonResponse(res, 404, { message: 'Report not found' });
        }

        const reportDemographicNo = reportDemoRows[0].demographic_no;
        console.log(`getLabReportContent: Report belongs to demographic: ${reportDemographicNo}`);

        // Check if the user is allowed to view this demographic's data
        if (req.user.role === 'admin') {
            console.log(`Admin user ${req.user.id} granted access to lab results for demographic ${reportDemographicNo}`);
        } else if (userDemographicNo !== parseInt(reportDemographicNo)) {
            // Check if there's a family relationship
            const [familyRelation] = await pool.query(
                `SELECT 1 FROM relationships
                WHERE (relation_demographic_no = ? AND demographic_no = ?)
                OR (demographic_no = ? AND relation_demographic_no = ?)`,
                [reportDemographicNo, userDemographicNo, reportDemographicNo, userDemographicNo]
            );

            if (familyRelation.length === 0) {
                console.log(`getLabReportContent: Access denied - No family relationship found`);
                return handleJsonResponse(res, 403, {
                    message: 'You do not have permission to access this report',
                    userDemo: userDemographicNo,
                    reportDemo: reportDemographicNo
                });
            }
            console.log(`getLabReportContent: Family relationship verified, access granted`);
        }

        // 获取报告文件信息
        const [fileInfo] = await pool.query(
            `SELECT
                d.contenttype,
                d.docfilename
            FROM document d
            WHERE d.document_no = ?`,
            [reportId]
        );

        if (fileInfo.length === 0 || !fileInfo[0].docfilename) {
            console.log(`getLabReportContent: No file information found for reportId=${reportId}`);
            return handleJsonResponse(res, 404, { message: 'Report content not found' });
        }

        console.log(`getLabReportContent: Found file: ${fileInfo[0].docfilename}, contentType: ${fileInfo[0].contenttype}`);

        // 构建文件路径
        const docPath = path.join('/OscarDocument/oscar/document', fileInfo[0].docfilename);
        const docCachePath = path.join('/OscarDocument/oscar/document_cache', fileInfo[0].docfilename);
        console.log(`getLabReportContent: Checking file paths: ${docPath}, ${docCachePath}`);

        let filePath;
        // 检查文件是否存在于document目录
        if (fs.existsSync(docPath)) {
            console.log(`getLabReportContent: File found at ${docPath}`);
            filePath = docPath;
        }
        // 检查文件是否存在于document_cache目录
        else if (fs.existsSync(docCachePath)) {
            console.log(`getLabReportContent: File found at ${docCachePath}`);
            filePath = docCachePath;
        } else {
            console.log(`getLabReportContent: File not found at any location`);
            return handleJsonResponse(res, 404, { message: 'Report file not found' });
        }

        // 检查文件大小
        try {
            const stats = fs.statSync(filePath);
            if (stats.size === 0) {
                console.log(`getLabReportContent: File is empty: ${filePath}`);
                return handleJsonResponse(res, 404, { message: 'File content is empty, please contact administrator' });
            }
            console.log(`getLabReportContent: File size: ${stats.size} bytes`);
        } catch (statError) {
            console.error(`getLabReportContent: Error getting file stats: ${statError}`);
            return handleJsonResponse(res, 500, { message: 'Error reading file information' });
        }

        // 设置正确的内容类型和头信息
        res.setHeader('Content-Type', fileInfo[0].contenttype || 'application/pdf');
        res.setHeader('Content-Disposition', `inline; filename="${fileInfo[0].docfilename || 'report.pdf'}"`);
        console.log(`getLabReportContent: Streaming file to client with content-type: ${fileInfo[0].contenttype || 'application/pdf'}`);

        // 从文件系统读取并返回文件内容
        try {
            const fileStream = fs.createReadStream(filePath);
            fileStream.on('error', (streamError) => {
                console.error(`getLabReportContent: Error streaming file: ${streamError}`);
                if (!res.headersSent) {
                    return handleJsonResponse(res, 500, { message: 'Error reading file content' });
                }
            });
            fileStream.pipe(res);
        } catch (streamError) {
            console.error(`getLabReportContent: Error creating file stream: ${streamError}`);
            return handleJsonResponse(res, 500, { message: 'Error preparing file for download' });
        }

    } catch (error) {
        console.error('Error fetching lab report content:', error);
        return handleJsonResponse(res, 500, { message: 'Failed to fetch lab report content', error: error.message });
    }
};

/**
 * 获取患者的测量值数据（如血常规、肝功能等）
 */
exports.getPatientMeasurements = async (req, res) => {
    try {
        const userId = req.user.id;

        // 获取用户的demographic_no
        const [userRows] = await pool.query(
            `SELECT demographic_no FROM user_auth WHERE id = ?`,
            [userId]
        );

        if (userRows.length === 0 || !userRows[0].demographic_no) {
            return res.status(404).json({ message: 'No linked patient record found for this user' });
        }

        const demographicNo = userRows[0].demographic_no;

        // 获取患者的测量值，按类型分组
        const [measurements] = await pool.query(
            `SELECT
                type,
                dataField,
                comments,
                measuringInstruction,
                dateObserved
            FROM measurements
            WHERE demographicNo = ?
            ORDER BY dateObserved DESC`,
            [demographicNo]
        );

        // 按测量类型分组
        const groupedMeasurements = {};
        measurements.forEach(measurement => {
            if (!groupedMeasurements[measurement.type]) {
                groupedMeasurements[measurement.type] = [];
            }

            groupedMeasurements[measurement.type].push({
                value: measurement.dataField,
                unit: measurement.measuringInstruction,
                comments: measurement.comments,
                date: measurement.dateObserved
            });
        });

        res.json({
            success: true,
            measurements: groupedMeasurements
        });

    } catch (error) {
        console.error('Error fetching measurements:', error);
        return handleJsonResponse(res, 500, { message: 'Failed to fetch measurements' });
    }
};

/**
 * Get lab results for a specific demographic number (for family members)
 */
exports.getPatientLabResultsByDemographic = async (req, res) => {
    try {
        const { demographicNo } = req.params;
        const userId = req.user.id;

        // Get the user's demographic_no to verify relationship
        const [userRows] = await pool.query(
            `SELECT demographic_no FROM user_auth WHERE id = ?`,
            [userId]
        );

        if (userRows.length === 0 || !userRows[0].demographic_no) {
            return res.status(404).json({ message: 'No linked patient record found for this user' });
        }

        const userDemographicNo = userRows[0].demographic_no;

        // Check if the user is allowed to view this demographic's data
        if (req.user.role === 'admin') {
            console.log(`Admin user ${req.user.id} granted access to lab results for demographic ${demographicNo}`);
        } else if (userDemographicNo !== parseInt(demographicNo)) {
            // Check if there's a family relationship
            const [familyRelation] = await pool.query(
                `SELECT 1 FROM relationships
                WHERE (relation_demographic_no = ? AND demographic_no = ?)
                OR (demographic_no = ? AND relation_demographic_no = ?)`,
                [demographicNo, userDemographicNo, demographicNo, userDemographicNo]
            );

            if (familyRelation.length === 0) {
                return res.status(403).json({ message: 'Permission denied: You cannot access data for this patient' });
            }
        }

        // Get patient's documents (lab or consult)
        const [reports] = await pool.query(
            `SELECT
                d.document_no,
                d.docdesc,
                d.doctype,
                d.contenttype,
                d.updatedatetime,
                d.observationdate,
                d.docfilename
            FROM document d
            JOIN ctl_document c ON d.document_no = c.document_no
            WHERE c.module = 'demographic'
            AND c.module_id = ?
            AND d.observationdate >= '2023-07-01'
            ORDER BY d.updatedatetime DESC`,
            [demographicNo]
        );

        // 不再 filter，只是 map
        const formattedReports = reports.map(report => {
            const docPath = path.join('/OscarDocument/oscar/document', report.docfilename);
            const docCachePath = path.join('/OscarDocument/oscar/document_cache', report.docfilename);
            let filePath = null;
            let fileExists = false;
            let fileSize = 0;

            if (fs.existsSync(docPath)) {
                filePath = docPath;
            } else if (fs.existsSync(docCachePath)) {
                filePath = docCachePath;
            }

            if (filePath) {
                try {
                    const stats = fs.statSync(filePath);
                    fileExists = stats.size > 0;
                    fileSize = stats.size;
                } catch (e) {
                    fileExists = false;
                }
            }

            return {
                id: report.document_no,
                title: report.docdesc || (report.doctype ? `${report.doctype.charAt(0).toUpperCase() + report.doctype.slice(1)} Document` : 'Document'),
                type: report.doctype || 'others',
                contentType: report.contenttype,
                updatedAt: report.updatedatetime,
                observationDate: report.observationdate,
                filename: report.docfilename,
                fileExists,
                fileSize
            };
        });

        res.json({
            success: true,
            count: formattedReports.length,
            reports: formattedReports
        });

    } catch (error) {
        console.error('Error fetching documents by demographic:', error);
        return handleJsonResponse(res, 500, { message: 'Failed to fetch documents' });
    }
};

/**
 * Get measurements data for a specific demographic number (for family members)
 */
exports.getPatientMeasurementsByDemographic = async (req, res) => {
    try {
        const { demographicNo } = req.params;
        const userId = req.user.id;

        // Get the user's demographic_no to verify relationship
        const [userRows] = await pool.query(
            `SELECT demographic_no FROM user_auth WHERE id = ?`,
            [userId]
        );

        if (userRows.length === 0 || !userRows[0].demographic_no) {
            return res.status(404).json({ message: 'No linked patient record found for this user' });
        }

        const userDemographicNo = userRows[0].demographic_no;

        // Check if the user is allowed to view this demographic's data
        if (req.user.role === 'admin') {
            console.log(`Admin user ${req.user.id} granted access to lab results for demographic ${demographicNo}`);
        } else if (userDemographicNo !== parseInt(demographicNo)) {
            // Check if there's a family relationship
            const [familyRelation] = await pool.query(
                `SELECT 1 FROM relationships
                WHERE (relation_demographic_no = ? AND demographic_no = ?)
                OR (demographic_no = ? AND relation_demographic_no = ?)`,
                [demographicNo, userDemographicNo, demographicNo, userDemographicNo]
            );

            if (familyRelation.length === 0) {
                return res.status(403).json({ message: 'Permission denied: You cannot access data for this patient' });
            }
        }

        // Get patient measurements, grouped by type
        const [measurements] = await pool.query(
            `SELECT
                type,
                dataField,
                comments,
                measuringInstruction,
                dateObserved
            FROM measurements
            WHERE demographicNo = ?
            ORDER BY dateObserved DESC`,
            [demographicNo]
        );

        // Group by measurement type
        const groupedMeasurements = {};
        measurements.forEach(measurement => {
            if (!groupedMeasurements[measurement.type]) {
                groupedMeasurements[measurement.type] = [];
            }

            groupedMeasurements[measurement.type].push({
                value: measurement.dataField,
                unit: measurement.measuringInstruction,
                comments: measurement.comments,
                date: measurement.dateObserved
            });
        });

        res.json({
            success: true,
            measurements: groupedMeasurements
        });

    } catch (error) {
        console.error('Error fetching measurements by demographic:', error);
        return handleJsonResponse(res, 500, { message: 'Failed to fetch measurements' });
    }
};

/**
 * Get HL7 lab results for a specific demographic number
 */
exports.getPatientHL7Results = async (req, res) => {
    try {
        const { demographicNo } = req.params;
        const userId = req.user.id;

        // Get the user's demographic_no to verify relationship
        const [userRows] = await pool.query(
            `SELECT demographic_no FROM user_auth WHERE id = ?`,
            [userId]
        );

        if (userRows.length === 0 || !userRows[0].demographic_no) {
            return res.status(404).json({ message: 'No linked patient record found for this user' });
        }

        const userDemographicNo = userRows[0].demographic_no;

        // Check if the user is allowed to view this demographic's data
        if (req.user.role === 'admin') {
            console.log(`Admin user ${req.user.id} granted access to HL7 results for demographic ${demographicNo}`);
        } else if (userDemographicNo !== parseInt(demographicNo)) {
            // Check if there's a family relationship
            const [familyRelation] = await pool.query(
                `SELECT 1 FROM relationships
                WHERE (relation_demographic_no = ? AND demographic_no = ?)
                OR (demographic_no = ? AND relation_demographic_no = ?)`,
                [demographicNo, userDemographicNo, demographicNo, userDemographicNo]
            );

            if (familyRelation.length === 0) {
                return res.status(403).json({ message: 'Permission denied: You cannot access data for this patient' });
            }
        }

        // Get HL7 lab results for the patient
        const [hl7Results] = await pool.query(
            `SELECT 
                d.last_name, 
                d.first_name, 
                d.hin, 
                h.discipline, 
                h.requesting_client, 
                h7.message, 
                h7.created,
                h.lab_no,
                h.health_no
            FROM hl7TextInfo h
            INNER JOIN demographic d ON h.health_no = d.hin
            INNER JOIN hl7TextMessage h7 ON h7.lab_id = h.lab_no
            WHERE d.demographic_no = ?
            ORDER BY h7.created DESC`,
            [demographicNo]
        );

        // Format the results
        const formattedResults = hl7Results.map(result => ({
            id: result.lab_no,
            patientName: `${result.first_name} ${result.last_name}`,
            hin: result.hin,
            discipline: result.discipline,
            requestingClient: result.requesting_client,
            message: result.message,
            created: result.created,
            healthNo: result.health_no
        }));

        res.json({
            success: true,
            count: formattedResults.length,
            results: formattedResults
        });

    } catch (error) {
        console.error('Error fetching HL7 results:', error);
        return handleJsonResponse(res, 500, { message: 'Failed to fetch HL7 results' });
    }
};

/**
 * Get HL7 lab results for the authenticated user
 */
exports.getMyHL7Results = async (req, res) => {
    try {
        const userId = req.user.id;

        // Get the user's demographic_no
        const [userRows] = await pool.query(
            `SELECT demographic_no FROM user_auth WHERE id = ?`,
            [userId]
        );

        if (userRows.length === 0 || !userRows[0].demographic_no) {
            return res.status(404).json({ message: 'No linked patient record found for this user' });
        }

        const demographicNo = userRows[0].demographic_no;

        // Get HL7 lab results for the authenticated user
        const [hl7Results] = await pool.query(
            `SELECT 
                d.last_name, 
                d.first_name, 
                d.hin, 
                h.discipline, 
                h.requesting_client, 
                h7.message, 
                h7.created,
                h.lab_no,
                h.health_no
            FROM hl7TextInfo h
            INNER JOIN demographic d ON h.health_no = d.hin
            INNER JOIN hl7TextMessage h7 ON h7.lab_id = h.lab_no
            WHERE d.demographic_no = ?
            ORDER BY h7.created DESC`,
            [demographicNo]
        );

        // Format the results
        const formattedResults = hl7Results.map(result => ({
            id: result.lab_no,
            patientName: `${result.first_name} ${result.last_name}`,
            hin: result.hin,
            discipline: result.discipline,
            requestingClient: result.requesting_client,
            message: result.message,
            created: result.created,
            healthNo: result.health_no
        }));

        res.json({
            success: true,
            count: formattedResults.length,
            results: formattedResults
        });

    } catch (error) {
        console.error('Error fetching HL7 results:', error);
        return handleJsonResponse(res, 500, { message: 'Failed to fetch HL7 results' });
    }
};

/**
 * Get specific HL7 message content by lab ID
 */
exports.getHL7MessageContent = async (req, res) => {
    try {
        const { labId } = req.params;
        const userId = req.user.id;

        // Get the user's demographic_no
        const [userRows] = await pool.query(
            `SELECT demographic_no FROM user_auth WHERE id = ?`,
            [userId]
        );

        if (userRows.length === 0 || !userRows[0].demographic_no) {
            return res.status(404).json({ message: 'No linked patient record found for this user' });
        }

        const userDemographicNo = userRows[0].demographic_no;

        // Get the demographic number associated with this HL7 lab
        const [labDemoRows] = await pool.query(
            `SELECT d.demographic_no
            FROM hl7TextInfo h
            INNER JOIN demographic d ON h.health_no = d.hin
            WHERE h.lab_no = ?`,
            [labId]
        );

        if (labDemoRows.length === 0) {
            return res.status(404).json({ message: 'HL7 lab record not found' });
        }

        const labDemographicNo = labDemoRows[0].demographic_no;

        // Check if the user is allowed to view this demographic's data
        if (req.user.role === 'admin') {
            console.log(`Admin user ${req.user.id} granted access to HL7 message for lab ${labId}`);
        } else if (userDemographicNo !== parseInt(labDemographicNo)) {
            // Check if there's a family relationship
            const [familyRelation] = await pool.query(
                `SELECT 1 FROM relationships
                WHERE (relation_demographic_no = ? AND demographic_no = ?)
                OR (demographic_no = ? AND relation_demographic_no = ?)`,
                [labDemographicNo, userDemographicNo, labDemographicNo, userDemographicNo]
            );

            if (familyRelation.length === 0) {
                return res.status(403).json({ message: 'You do not have permission to access this HL7 message' });
            }
        }

        // Get the HL7 message content
        const [hl7Message] = await pool.query(
            `SELECT 
                h.lab_no,
                h.discipline,
                h.requesting_client,
                h7.message,
                h7.created,
                d.last_name,
                d.first_name,
                d.hin
            FROM hl7TextInfo h
            INNER JOIN demographic d ON h.health_no = d.hin
            INNER JOIN hl7TextMessage h7 ON h7.lab_id = h.lab_no
            WHERE h.lab_no = ?`,
            [labId]
        );

        if (hl7Message.length === 0) {
            return res.status(404).json({ message: 'HL7 message not found' });
        }

        const result = hl7Message[0];

        res.json({
            success: true,
            data: {
                id: result.lab_no,
                patientName: `${result.first_name} ${result.last_name}`,
                hin: result.hin,
                discipline: result.discipline,
                requestingClient: result.requesting_client,
                message: result.message,
                created: result.created
            }
        });

    } catch (error) {
        console.error('Error fetching HL7 message content:', error);
        return handleJsonResponse(res, 500, { message: 'Failed to fetch HL7 message content' });
    }
};

/**
 * Get parsed HL7 message content by lab ID
 */
exports.getParsedHL7Message = async (req, res) => {
    try {
        const { labId } = req.params;
        const userId = req.user.id;

        // Get the user's demographic_no
        const [userRows] = await pool.query(
            `SELECT demographic_no FROM user_auth WHERE id = ?`,
            [userId]
        );

        if (userRows.length === 0 || !userRows[0].demographic_no) {
            return res.status(404).json({ message: 'No linked patient record found for this user' });
        }

        const userDemographicNo = userRows[0].demographic_no;

        // Get the demographic number associated with this HL7 lab
        const [labDemoRows] = await pool.query(
            `SELECT d.demographic_no
            FROM hl7TextInfo h
            INNER JOIN demographic d ON h.health_no = d.hin
            WHERE h.lab_no = ?`,
            [labId]
        );

        if (labDemoRows.length === 0) {
            return res.status(404).json({ message: 'HL7 lab record not found' });
        }

        const labDemographicNo = labDemoRows[0].demographic_no;

        // Check if the user is allowed to view this demographic's data
        if (req.user.role === 'admin') {
            console.log(`Admin user ${req.user.id} granted access to HL7 message for lab ${labId}`);
        } else if (userDemographicNo !== parseInt(labDemographicNo)) {
            // Check if there's a family relationship
            const [familyRelation] = await pool.query(
                `SELECT 1 FROM relationships
                WHERE (relation_demographic_no = ? AND demographic_no = ?)
                OR (demographic_no = ? AND relation_demographic_no = ?)`,
                [labDemographicNo, userDemographicNo, labDemographicNo, userDemographicNo]
            );

            if (familyRelation.length === 0) {
                return res.status(403).json({ message: 'You do not have permission to access this HL7 message' });
            }
        }

        // Get the HL7 message content
        const [hl7Message] = await pool.query(
            `SELECT 
                h.lab_no,
                h.discipline,
                h.requesting_client,
                h7.message,
                h7.created,
                d.last_name,
                d.first_name,
                d.hin
            FROM hl7TextInfo h
            INNER JOIN demographic d ON h.health_no = d.hin
            INNER JOIN hl7TextMessage h7 ON h7.lab_id = h.lab_no
            WHERE h.lab_no = ?`,
            [labId]
        );

        if (hl7Message.length === 0) {
            return res.status(404).json({ message: 'HL7 message not found' });
        }

        const result = hl7Message[0];

        // Parse the HL7 message
        const parsedData = HL7ParserService.parseHL7Message(result.message);
        let humanReadable = HL7ParserService.toHumanReadable(parsedData);
        
        // If parsing failed or no results, use sample data for demonstration
        if (!humanReadable.readable || humanReadable.results.length === 0) {
            console.log('HL7 parsing failed or no results found, using sample data');
            humanReadable = HL7ParserService.generateSampleResults();
        }

        res.json({
            success: true,
            data: {
                id: result.lab_no,
                patientName: `${result.first_name} ${result.last_name}`,
                hin: result.hin,
                discipline: result.discipline,
                requestingClient: result.requesting_client,
                created: result.created,
                rawMessage: result.message,
                parsed: parsedData,
                humanReadable: humanReadable
            }
        });

    } catch (error) {
        console.error('Error fetching parsed HL7 message:', error);
        return handleJsonResponse(res, 500, { message: 'Failed to fetch parsed HL7 message', error: error.message });
    }
};

/**
 * Analyze HL7 results using AI
 */
exports.analyzeHL7Results = async (req, res) => {
    try {
        const { patientName, discipline, results, summary } = req.body;
        const userId = req.user.id;

        if (!results || !Array.isArray(results)) {
            return res.status(400).json({ message: 'Invalid HL7 results data' });
        }

        // Generate AI analysis based on HL7 results
        const analysis = {
            riskAssessment: {
                level: 'low',
                description: 'Overall health indicators appear normal'
            },
            healthIndicators: {
                normal: [],
                borderline: [],
                abnormal: []
            },
            aiAnalysis: '',
            recommendations: []
        };

        // Analyze each result
        results.forEach(result => {
            if (result.abnormalFlag === 'Normal') {
                analysis.healthIndicators.normal.push(result.testName);
            } else if (result.abnormalFlag === 'High' || result.abnormalFlag === 'Low') {
                analysis.healthIndicators.borderline.push(result.testName);
            } else if (result.abnormalFlag === 'Abnormal') {
                analysis.healthIndicators.abnormal.push(result.testName);
            }
        });

        // Determine overall risk level
        if (analysis.healthIndicators.abnormal.length > 0) {
            analysis.riskAssessment.level = 'high';
            analysis.riskAssessment.description = 'Some indicators require medical attention';
        } else if (analysis.healthIndicators.borderline.length > 0) {
            analysis.riskAssessment.level = 'moderate';
            analysis.riskAssessment.description = 'Some indicators are outside normal range';
        }

        // Generate AI analysis text
        const normalCount = analysis.healthIndicators.normal.length;
        const borderlineCount = analysis.healthIndicators.borderline.length;
        const abnormalCount = analysis.healthIndicators.abnormal.length;

        analysis.aiAnalysis = `Based on the ${discipline || 'laboratory'} results for ${patientName || 'the patient'}, `;
        analysis.aiAnalysis += `${normalCount} indicators are within normal range. `;
        
        if (borderlineCount > 0) {
            analysis.aiAnalysis += `${borderlineCount} indicators are slightly outside normal range and should be monitored. `;
        }
        
        if (abnormalCount > 0) {
            analysis.aiAnalysis += `${abnormalCount} indicators show significant abnormalities that require medical attention. `;
        }

        // Generate recommendations
        if (abnormalCount > 0) {
            analysis.recommendations.push('Consult with your healthcare provider about the abnormal results');
            analysis.recommendations.push('Consider follow-up testing as recommended by your doctor');
        }
        
        if (borderlineCount > 0) {
            analysis.recommendations.push('Monitor borderline indicators and discuss with your healthcare provider');
        }
        
        analysis.recommendations.push('Maintain a healthy lifestyle with regular exercise and balanced diet');
        analysis.recommendations.push('Schedule regular check-ups to monitor your health status');

        res.json({
            success: true,
            data: {
                analysis: analysis,
                timestamp: new Date().toISOString()
            }
        });

    } catch (error) {
        console.error('Error analyzing HL7 results:', error);
        return handleJsonResponse(res, 500, { message: 'Failed to analyze HL7 results', error: error.message });
    }
};