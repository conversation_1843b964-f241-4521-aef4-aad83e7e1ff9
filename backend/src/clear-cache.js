const pool = require('./config/database');

async function clearCache() {
  try {
    console.log('Clearing cache for demographic_no 54897...');
    
    const [result] = await pool.query(
      'DELETE FROM patient_summaries WHERE demographic_no = ?',
      [54897]
    );
    
    console.log(`Deleted ${result.affectedRows} rows from patient_summaries table.`);
    process.exit(0);
  } catch (error) {
    console.error('Error clearing cache:', error);
    process.exit(1);
  }
}

clearCache();
