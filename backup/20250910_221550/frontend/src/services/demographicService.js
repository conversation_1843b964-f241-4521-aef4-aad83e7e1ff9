import axios from 'axios';

const API_BASE_URL = '/api/demographics'; // Base path for demographic-related APIs

/**
 * Fetches the pre-signed URL for a user's avatar.
 * @param {string} demographicNo - The demographic number of the user.
 * @returns {Promise<string|null>} The pre-signed URL for the avatar, or null if an error occurs or no avatar exists.
 */
export const getAvatarUrl = async (demographicNo) => {
    if (!demographicNo) {
        console.warn('getAvatarUrl: demographicNo is required.');
        return null;
    }
    try {
        const response = await axios.get(`${API_BASE_URL}/${demographicNo}/avatar`);
        if (response.data && response.data.success && response.data.signedUrl) {
            return response.data.signedUrl;
        }
        // It's not an error if a user simply doesn't have an avatar (backend returns 404 handled as error by axios by default)
        // So, we check for specific success response, otherwise assume no avatar or issue.
        if (response.status === 404) {
             console.log(`No avatar found for demographic_no: ${demographicNo}`);
             return null;
        }
        return null;
    } catch (error) {
        if (error.response && error.response.status === 404) {
            console.log(`No avatar found for demographic_no: ${demographicNo}`);
        } else {
            console.error('Error fetching avatar URL:', error.response?.data?.message || error.message);
        }
        return null;
    }
};

/**
 * Uploads an avatar image for a user.
 * @param {string} demographicNo - The demographic number of the user.
 * @param {File} file - The avatar image file to upload.
 * @returns {Promise<object|null>} The response data from the server, or null if an error occurs.
 */
export const uploadAvatar = async (demographicNo, file) => {
    if (!demographicNo || !file) {
        console.error('uploadAvatar: demographicNo and file are required.');
        return null;
    }

    const formData = new FormData();
    formData.append('avatar', file);

    try {
        const response = await axios.post(`${API_BASE_URL}/${demographicNo}/avatar`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
        return response.data; // Expected: { success: true, message: '...', avatarKey: '...' }
    } catch (error) {
        console.error('Error uploading avatar:', error.response?.data?.message || error.message);
        // Rethrow to allow components to handle it, e.g., show a notification
        throw error.response?.data || new Error('Avatar upload failed'); 
    }
};

/**
 * Deletes a user's avatar.
 * @param {string} demographicNo - The demographic number of the user.
 * @returns {Promise<object|null>} The response data from the server, or null if an error occurs.
 */
export const deleteAvatar = async (demographicNo) => {
    if (!demographicNo) {
        console.error('deleteAvatar: demographicNo is required.');
        return null;
    }
    try {
        const response = await axios.delete(`${API_BASE_URL}/${demographicNo}/avatar`);
        return response.data; // Expected: { success: true, message: '...' }
    } catch (error) {
        console.error('Error deleting avatar:', error.response?.data?.message || error.message);
        throw error.response?.data || new Error('Avatar deletion failed');
    }
}; 