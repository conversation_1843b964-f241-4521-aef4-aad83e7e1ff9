import React, { useEffect, useRef } from 'react';
import { useLanguage } from '../context/LanguageContext';
import { useView } from '../context/ViewContext';

// 自动生成房间名：日期+用户ID+随机码
function generateRoomName(userId) {
  const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
  const rand = Math.random().toString(36).substring(2, 8);
  return `MMC_${date}_${userId || 'guest'}_${rand}`;
}

const JITSI_DOMAIN = 'meeting.mmc-group.ca';

const VideoConference = () => {
  const jitsiContainerRef = useRef(null);
  const apiRef = useRef(null);
  const { t, language } = useLanguage();
  const { loggedInUser } = useView();

  // 国际化标题
  const i18n = {
    zh: {
      title: '视频会议',
      join: '加入会议',
      leave: '离开会议',
      joined: '已加入会议',
      left: '已离开会议',
    },
    en: {
      title: 'Video Conference',
      join: 'Join Conference',
      leave: 'Leave Conference',
      joined: 'Joined Conference',
      left: 'Left Conference',
    }
  };
  const lang = language === 'zh' ? 'zh' : 'en';
  const labels = i18n[lang];

  // 自动生成房间名和昵称
  const demographicNo = loggedInUser?.demographic_no || 'guest';
  const demographicInfo = loggedInUser?.demographicInfo;
  const displayName =
    demographicInfo?.first_name && demographicInfo?.last_name
      ? `${demographicInfo.first_name} ${demographicInfo.last_name}`
      : demographicInfo?.first_name ||
        demographicInfo?.last_name ||
        loggedInUser?.name ||
        loggedInUser?.username ||
        t('guest') ||
        '访客';
  const roomName = `${demographicNo}`;

  useEffect(() => {
    // 动态加载 Jitsi Meet external_api.js
    const scriptId = 'jitsi-api-script';
    if (!document.getElementById(scriptId)) {
      const script = document.createElement('script');
      script.id = scriptId;
      script.src = 'https://meeting.mmc-group.ca/external_api.js';
      script.async = true;
      script.onload = () => {
        createJitsiMeeting();
      };
      document.body.appendChild(script);
    } else {
      createJitsiMeeting();
    }

    function createJitsiMeeting() {
      if (!window.JitsiMeetExternalAPI) return;
      if (apiRef.current) {
        apiRef.current.dispose();
      }
      apiRef.current = new window.JitsiMeetExternalAPI(JITSI_DOMAIN, {
        roomName,
        parentNode: jitsiContainerRef.current,
        width: '100%',
        height: 700,
        userInfo: { displayName },
        configOverwrite: {},
        interfaceConfigOverwrite: {
          TOOLBAR_BUTTONS: [
            'microphone', 'camera', 'desktop', 'fullscreen', 'fodeviceselection',
            'hangup', 'chat', 'settings', 'raisehand', 'videoquality', 'tileview'
          ]
        },
        lang: lang
      });

      // 事件处理示例
      apiRef.current.addListener('participantJoined', (participant) => {
        console.log(labels.joined + ':', participant.displayName);
      });
      apiRef.current.addListener('participantLeft', (participant) => {
        console.log(labels.left + ':', participant.displayName);
      });
      apiRef.current.addListener('videoConferenceJoined', (obj) => {
        console.log(labels.joined + ':', obj.displayName);
      });
      apiRef.current.addListener('videoConferenceLeft', (obj) => {
        console.log(labels.left + ':', obj.displayName);
      });
    }

    // 组件卸载时清理
    return () => {
      if (apiRef.current) {
        apiRef.current.dispose();
      }
    };
    // eslint-disable-next-line
  }, [roomName, displayName, lang]);

  return (
    <div>
      <h2>{labels.title}</h2>
      <div
        id="jitsi-container"
        ref={jitsiContainerRef}
        style={{ height: 700, width: '100%', minHeight: 400, background: '#000' }}
      />
    </div>
  );
};

export default VideoConference;
