const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

// 测试配置
const BASE_URL = 'https://app-backend.mmcwellness.ca';
const TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MywiaWF0IjoxNzQ4NDcxNzYxLCJleHAiOjE3NDg1NTgxNjF9.-9w0ylINA5NuYW5rRlWv5CoFLk8RlHvQ1vMrVkLxaNA';

async function testRecordingAPI() {
    try {
        console.log('🎤 Testing Meeting Recording API...\n');

        // 1. 测试开始录制
        console.log('1. Starting meeting recording...');
        const startResponse = await axios.post(`${BASE_URL}/api/meeting-records/start`, {
            roomName: 'test-room-' + Date.now(),
            recordingQuality: 'medium'
        }, {
            headers: {
                'Authorization': `Bearer ${TOKEN}`,
                'Content-Type': 'application/json'
            }
        });

        console.log('✅ Meeting started:', startResponse.data);
        const meetingId = startResponse.data.meetingId;

        // 2. 测试获取会议状态
        console.log('\n2. Getting meeting status...');
        const statusResponse = await axios.get(`${BASE_URL}/api/meeting-records/${meetingId}/status`, {
            headers: {
                'Authorization': `Bearer ${TOKEN}`
            }
        });

        console.log('✅ Meeting status:', statusResponse.data);

        // 3. 创建一个模拟音频文件（简单的文本文件作为测试）
        console.log('\n3. Creating mock audio file...');
        const mockAudioContent = 'This is a mock audio file for testing purposes.';
        fs.writeFileSync('test-audio.txt', mockAudioContent);

        // 4. 测试音频上传
        console.log('\n4. Testing audio upload...');
        const formData = new FormData();
        formData.append('audio', fs.createReadStream('test-audio.txt'), {
            filename: 'test-audio.webm',
            contentType: 'audio/webm'
        });
        formData.append('meetingId', meetingId);

        const uploadResponse = await axios.post(`${BASE_URL}/api/meeting-records/upload-audio`, formData, {
            headers: {
                'Authorization': `Bearer ${TOKEN}`,
                ...formData.getHeaders()
            }
        });

        console.log('✅ Audio upload:', uploadResponse.data);

        // 5. 测试停止录制
        console.log('\n5. Stopping meeting recording...');
        const stopResponse = await axios.post(`${BASE_URL}/api/meeting-records/${meetingId}/stop`, {}, {
            headers: {
                'Authorization': `Bearer ${TOKEN}`,
                'Content-Type': 'application/json'
            }
        });

        console.log('✅ Meeting stopped:', stopResponse.data);

        // 6. 等待一下处理
        console.log('\n6. Waiting for processing...');
        await new Promise(resolve => setTimeout(resolve, 3000));

        // 7. 检查最终状态
        console.log('\n7. Checking final status...');
        const finalStatusResponse = await axios.get(`${BASE_URL}/api/meeting-records/${meetingId}/status`, {
            headers: {
                'Authorization': `Bearer ${TOKEN}`
            }
        });

        console.log('✅ Final status:', finalStatusResponse.data);

        // 8. 获取会议详情
        console.log('\n8. Getting meeting details...');
        const detailsResponse = await axios.get(`${BASE_URL}/api/meeting-records/${meetingId}`, {
            headers: {
                'Authorization': `Bearer ${TOKEN}`
            }
        });

        console.log('✅ Meeting details:', detailsResponse.data);

        // 清理测试文件
        fs.unlinkSync('test-audio.txt');
        console.log('\n🎉 All tests completed successfully!');

    } catch (error) {
        console.error('❌ Test failed:', error.response?.data || error.message);
        
        // 清理测试文件
        try {
            fs.unlinkSync('test-audio.txt');
        } catch (e) {
            // 忽略清理错误
        }
    }
}

// 运行测试
testRecordingAPI(); 