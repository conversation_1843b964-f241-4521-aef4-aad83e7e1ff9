# Internal Development Guidelines (MMC Wellness Portal)

This document outlines key guidelines to follow during development and modification of the MMC Wellness Portal application to ensure consistency, accuracy, and adherence to requirements.

## 1. Understand the Goal First

- **Clarify Intent:** Before modifying code, understand the *precise* requirement and the *reason* behind it. Ask for specifics if a request is ambiguous.
- **Confirm Assumptions:** Explicitly state assumptions made (e.g., data sources, formats, user roles).

## 2. Data Source & Format Verification

- **Identify Correct Table:** Confirm the definitive source table for requested data (e.g., `billingservice` for standard price, `billingmaster` for actual paid amount).
- **Verify Column Meaning:** Confirm the meaning of columns (`service_date` vs. `createdate`).
- **Check Data Format/Units:** *Before* implementing logic, query the DB (`DESCRIBE`, sample `SELECT`) to confirm actual data format and units (cents vs. dollars, date formats). **Do not assume.**
- **Handle Data Variations:** Implement robust backend logic for `null` values or unexpected formats.

## 3. API Endpoint Discipline

- **Check Existing First:** Search (`codebase_search`, `grep_search`) for existing functionality before creating new endpoints.
- **Clear Purpose:** Ensure endpoints have distinct, well-defined purposes.
- **Consistent Naming:** Follow existing patterns (e.g., `/api/resource/:id`).

## 4. Backend Logic

- **Permissions are Paramount:** Every endpoint accessing patient-specific data via `/:demographicNo` *must* include a permission check:
    - Verify authenticated and linked user (`loggedInUser.demographic_no`).
    - Check if `targetDemoNo === loggedInDemoNo` (self).
    - If not self, query `relationships` (both directions, non-deleted) for a link.
    - Return `403 Forbidden` if denied.
- **Database Queries:** Use specific column selects. Use `LEFT JOIN` for potentially missing related data (e.g., provider name).
- **Data Transformation:** Perform necessary transformations (parsing, formatting names) in the backend controller.

## 5. Frontend Implementation

- **Match API Contract:** Ensure frontend calls handle the exact data structure returned by the backend.
- **Use Context Appropriately:** Use `ViewContext` for global state (`loggedInUser`, `viewingDemographicNo`, `setViewAs`). Fetch page-specific data within the component `useEffect`, passing `viewingDemographicNo`.
- **Conditional Rendering:** Check for data existence before rendering (`data && (...)`, `list.length > 0 ? (...) : (...)`).
- **Data Formatting:** Perform presentation formatting (dates, currency) in the frontend, respecting API data format/units.
- **Loading/Error States:** Consistently implement loading indicators and clear error messages.

## 6. Multilingual Support & Theming

- **Translation System:** All user-facing text must use the translation system with the `useLanguage` hook:
    - Access the translation function with `const { t } = useLanguage()`.
    - Use translation keys with the `t()` function (e.g., `t('profile')` instead of hardcoded text).
    - Add new translation keys to both languages in `LanguageContext.js`.
    - Group related translations by category (navigation, forms, buttons, etc.).
    - Ensure all labels, buttons, messages, and titles are translatable.
- **Theme Support:** Use the theme context to ensure dark/light mode compatibility:
    - Access theme information with `const { isDarkMode } = useThemeMode()`.
    - Avoid hardcoding colors - use theme palette values.
    - Test components in both light and dark modes.
    - Consider contrast ratios for accessibility.
    - For custom styling, use `theme.palette.*` values through the `useTheme` hook.

## 7. Code Modification Process

- **Read Before Edit:** Use `read_file` on the relevant code section *immediately before* generating an `edit_file` call.
- **Targeted Edits:** Keep edits focused. Use `// ... existing code ...` correctly.
- **Explain the "Why":** State the edit's purpose clearly.
- **Verify Application:** Check edit tool results. Consider `reapply` if needed.

## 8. Testing and Verification

- **Suggest Restarts:** Recommend backend/frontend restarts after relevant changes.
- **Provide Test Steps:** Offer `curl` commands or UI steps for verification.

## 9. Mobile Responsiveness

- **Mobile First/Friendly:** Always consider how features and UI elements will look and function on mobile devices. Use responsive design techniques (e.g., MUI Grid, `useMediaQuery`).

## 10. Code Reusability & DRY (Don't Repeat Yourself)

- **Check for Parallel Features:** Before implementing a feature (especially UI components for displaying lists or data), check if a similar feature/component already exists (e.g., Appointments list, Prescriptions list, Immunizations list).
- **Adapt, Don't Duplicate:** If a similar pattern exists, try to adapt or generalize the existing component/logic rather than creating a completely new, redundant one. Parameterize components where possible.

## 11. Caution with Modifications & Deletions

- **Prioritize Addition:** Development should primarily focus on *adding* new features or fixing bugs.
- **Exercise Extreme Caution:** Be extremely careful when modifying or deleting existing functionality. Understand the full impact across the application before proceeding.
- **Confirm Necessity:** Verify if a modification or deletion is truly necessary and won't break existing workflows relied upon by users.

## 12. Docker Development Workflow (Focus: `mmcwebapp`)

- **Working Directory:** All development and Docker commands should typically be executed from the root of the `mmcwebapp` workspace directory.
- **Backend Changes:** To apply changes made to the backend code (Node.js/Express API in `/backend`):
    - **Restart the backend container:**
      ```bash
      docker restart mmcwebapp-backend-1 
      ```
      (Verify container name if it differs from `mmcwebapp-backend-1` in your `docker-compose.yml`)
- **Frontend Changes:** To apply changes made to the frontend code (React app in `/frontend`):
    - **Rebuild and restart the frontend container:** The frontend needs to be rebuilt to incorporate code changes.
      ```bash
      # Ensure you are in the mmcwebapp directory containing docker-compose.yml
      docker-compose up -d --build frontend
      ```
      (Alternatively, use `docker restart mmcwebapp-frontend-1` *if* hot-reloading is properly configured within the container, but rebuilding is the most reliable way to ensure changes are applied).
- **Database/OSCAR:** These guidelines pertain to the `mmcwebapp` frontend and backend services. Do **not** rebuild the main OSCAR database container (`open-osp-db-1` or similar) unless specifically required for database schema changes and approved.

## 13. Instructing the AI Assistant (Especially After Refresh)

When requesting a **new feature**, especially if the conversation context might be limited (e.g., after a refresh), provide the following information explicitly:

1.  **Goal:** Clearly state the overall feature objective (e.g., "Add a page to view health reminders").
2.  **Location:** Specify if it involves Backend, Frontend, or both.
3.  **Backend Details (If applicable):**
    *   **Data Source:** Which database table(s) and column(s) are involved?
    *   **API Endpoint:** Propose the specific route path and HTTP method (e.g., `GET /api/reminders/:demographicNo`).
    *   **Logic:** Describe the necessary query logic, filtering, sorting, and data transformations.
    *   **Permissions:** Explicitly mention the need for standard permission checks (self/family).
    *   **Response Structure:** Define the expected JSON response format.
4.  **Frontend Details (If applicable):**
    *   **Placement:** Specify the component file path (e.g., `frontend/src/components/Reminders.js`).
    *   **UI/Layout:** Describe the desired layout or reference a similar existing page.
    *   **Navigation:** State where links/tabs should be added (e.g., `Header.js`, `App.js`).
    *   **API Call:** Confirm the frontend should call the new backend endpoint.
5.  **Reference Existing Patterns/Files:** **Crucially**, point to existing code files or documentation within the project to use as a template or reference for structure, logic, and style.
    *   *Examples:* "Use `Immunizations.js` layout", "Follow permission logic in `prescriptionController.js`", "Refer to `API.md` for response format examples", "Follow `DEV_GUIDELINES.md`".
6.  **Break Down Complexity:** For larger features, consider breaking the request into smaller, sequential steps (backend first, then frontend, etc.).

*This document should be reviewed periodically and updated as the application evolves.*

## Key Feature Implementations

This section documents significant features added or major refactors.

### Dual-Mode Appointment Booking (Implemented: YYYY-MM-DD - *Please update date*)

- **Goal:** Allow users to book appointments either by selecting a specific Doctor first or by selecting a general Service Type first.
- **Backend (`providerController.js`, `providerRoutes.js`):
    - Modified availability logic (`getProviderAvailability`) to recognize any non-underscore character in `scheduletemplate.timecode` as available.
    - Added `GET /api/providers/consultation-types`: Returns a list of distinct, currently bookable service types (template names) based on active future schedules and valid templates.
    - Added `GET /api/providers/availability/by-type`: Fetches available slots aggregated across all providers for a specific service type and date. Returns slots including `provider_no` and `providerName`.
- **Frontend (`BookingPage.js`):
    - Refactored the component to include a mode selection (Radio buttons: "Book by Doctor" / "Book by Service Type").
    - "Book by Doctor" mode retains the original flow.
    - "Book by Service Type" mode:
        - Step 1: Select Service Type (uses `/api/providers/consultation-types`).
        - Step 2: Select Date, then select a Time slot and the specific Doctor offering that slot (uses `/api/providers/availability/by-type`).
        - Step 3: Confirmation shows selected Service Type, Date, Time, and Doctor.
    - Conditional logic added for state management, API calls, UI rendering, validation, and submission based on the selected mode.
- **Documentation:** Updated `API.md` with the new endpoints and a note about the feature.
- **Dependencies:** Requires backend restart and potential frontend translation key additions. 


### docker exec mmcwebapp-backend-1 node /app/src/scripts/generateDailyTip.js
