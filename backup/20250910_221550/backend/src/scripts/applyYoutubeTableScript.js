/**
 * 脚本用于应用YouTube表结构
 * 
 * 此脚本读取并执行create_youtube_table.sql文件中的SQL命令
 * 用于确保YouTube视频功能所需的表结构存在
 */

const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
require('dotenv').config({ path: path.join(__dirname, '../../../.env') });

async function applyYoutubeTableScript() {
    console.log('开始应用YouTube表结构...');

    // 数据库连接配置
    const dbConfig = {
        host: process.env.DB_HOST || 'open-osp-db-1',
        port: process.env.DB_PORT || 3306,
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME || 'oscar',
        multipleStatements: true // 允许执行多条SQL语句
    };

    console.log(`连接到数据库: ${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`);

    let connection;

    try {
        // 读取SQL脚本文件
        const sqlFilePath = path.join(__dirname, 'create_youtube_table.sql');
        const sqlScript = fs.readFileSync(sqlFilePath, 'utf8');

        console.log('SQL脚本加载成功，开始执行...');

        // 连接数据库
        connection = await mysql.createConnection(dbConfig);

        // 执行SQL脚本
        const [results] = await connection.query(sqlScript);

        console.log('YouTube表结构应用成功!');
        console.log('SQL执行结果:', results);

        // 检查表是否存在
        const [tables] = await connection.query('SHOW TABLES LIKE "youtube_videos"');

        if (tables.length > 0) {
            console.log('验证: youtube_videos表存在');

            // 检查表中的记录数
            const [count] = await connection.query('SELECT COUNT(*) as count FROM youtube_videos');
            console.log(`youtube_videos表中有${count[0].count}条记录`);

            // 检查分类
            const [categories] = await connection.query('SELECT DISTINCT category FROM youtube_videos');
            console.log('表中有以下分类:', categories.map(c => c.category).join(', '));
        } else {
            console.error('警告: youtube_videos表未创建成功!');
        }

    } catch (error) {
        console.error('应用YouTube表结构时发生错误:', error);
    } finally {
        if (connection) {
            await connection.end();
            console.log('数据库连接已关闭');
        }
    }
}

// 执行脚本
applyYoutubeTableScript(); 