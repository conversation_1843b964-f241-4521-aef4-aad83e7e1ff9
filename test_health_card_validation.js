#!/usr/bin/env node

// Test script for health card number validation
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// Test cases for health card number validation
const testCases = [
    {
        name: "Valid 10-digit health card",
        data: {
            firstName: "Test",
            lastName: "User",
            email: "<EMAIL>",
            phone: "**********",
            healthInsuranceNumber: "**********",
            password: "password123"
        },
        expectedResult: "success"
    },
    {
        name: "Invalid - 9 digits",
        data: {
            firstName: "Test",
            lastName: "User",
            email: "<EMAIL>",
            phone: "**********",
            healthInsuranceNumber: "123456789",
            password: "password123"
        },
        expectedResult: "error"
    },
    {
        name: "Invalid - 11 digits",
        data: {
            firstName: "Test",
            lastName: "User",
            email: "<EMAIL>",
            phone: "**********",
            healthInsuranceNumber: "**********1",
            password: "password123"
        },
        expectedResult: "error"
    },
    {
        name: "Invalid - contains letters",
        data: {
            firstName: "Test",
            lastName: "User",
            email: "<EMAIL>",
            phone: "**********",
            healthInsuranceNumber: "123456789A",
            password: "password123"
        },
        expectedResult: "error"
    },
    {
        name: "Invalid - empty",
        data: {
            firstName: "Test",
            lastName: "User",
            email: "<EMAIL>",
            phone: "**********",
            healthInsuranceNumber: "",
            password: "password123"
        },
        expectedResult: "error"
    }
];

async function runTests() {
    console.log('🧪 Testing Health Card Number Validation...\n');

    let passedTests = 0;
    let totalTests = testCases.length;

    for (const testCase of testCases) {
        try {
            console.log(`Testing: ${testCase.name}`);
            console.log(`Health Card Number: "${testCase.data.healthInsuranceNumber}"`);

            // Create a unique email for each test run to avoid conflicts
            const testData = {
                ...testCase.data,
                email: `test${Date.now()}_${Math.random().toString(36).substr(2, 9)}@example.com`
            };

            const response = await axios.post(`${BASE_URL}/api/auth/register`, testData);
            
            if (testCase.expectedResult === "success") {
                if (response.data.success) {
                    console.log('✅ PASS - Registration successful as expected');
                    passedTests++;
                } else {
                    console.log('❌ FAIL - Expected success but got error:', response.data.message);
                }
            } else {
                console.log('❌ FAIL - Expected error but got success');
            }
            
        } catch (error) {
            if (testCase.expectedResult === "error") {
                if (error.response && error.response.data && error.response.data.message) {
                    if (error.response.data.message.includes('Health Insurance Number must be exactly 10 digits')) {
                        console.log('✅ PASS - Correct validation error message');
                        passedTests++;
                    } else {
                        console.log('⚠️  PARTIAL - Got error but wrong message:', error.response.data.message);
                        passedTests += 0.5; // Partial credit
                    }
                } else {
                    console.log('⚠️  PARTIAL - Got error but no specific message');
                    passedTests += 0.5; // Partial credit
                }
            } else {
                console.log('❌ FAIL - Expected success but got error:', error.response?.data?.message || error.message);
            }
        }
        
        console.log('---');
    }
    
    console.log(`\n📊 Test Results: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
        console.log('🎉 All tests passed! Health card validation is working correctly.');
    } else if (passedTests >= totalTests * 0.8) {
        console.log('✅ Most tests passed. Health card validation is mostly working.');
    } else {
        console.log('⚠️  Some tests failed. Health card validation needs attention.');
    }
}

// Run the tests
runTests().catch(console.error);
