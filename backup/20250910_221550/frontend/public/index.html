<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />

    <!-- 缓存控制：不缓存HTML页面 -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />

    <meta name="description" content="MMC Wellness App" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />

    <!-- 防止缓存脚本 -->
    <script>
        // 添加一个版本号到所有API请求和资源请求中
        window.appVersion = '%REACT_APP_VERSION%';

        // 阻止浏览器强制缓存
        window.addEventListener('load', function () {
            // 如果用户刷新页面，重新加载所有资源
            if (performance.navigation.type === 1) {
                localStorage.setItem('lastRefresh', Date.now());
            }
        });
    </script>

    <title>MMC Wellness</title>
</head>

<body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
</body>

</html>