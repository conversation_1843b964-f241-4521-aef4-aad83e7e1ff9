const mysql = require('mysql2/promise');

class AIUsageTracker {
    constructor() {
        this.pool = null;
        this.initializeDatabase();
    }

    async initializeDatabase() {
        try {
            this.pool = mysql.createPool({
                host: process.env.MAIN_DB_HOST || 'localhost',
                port: process.env.MAIN_DB_PORT || 3306,
                user: process.env.MAIN_DB_USER || 'root',
                password: process.env.MAIN_DB_PASSWORD || '',
                database: process.env.MAIN_DB_NAME || 'oscar',
                waitForConnections: true,
                connectionLimit: 10,
                queueLimit: 0,
                timezone: '+00:00'
            });

            // 确保表存在
            await this.createTablesIfNotExist();
            console.log('[AIUsageTracker] Database initialized successfully');
        } catch (error) {
            console.error('[AIUsageTracker] Failed to initialize database:', error);
        }
    }

    async createTablesIfNotExist() {
        const createUsageStatsTable = `
            CREATE TABLE IF NOT EXISTS ai_usage_stats (
                id INT AUTO_INCREMENT PRIMARY KEY,
                provider VARCHAR(50) NOT NULL COMMENT 'AI提供商: gemini, openrouter',
                service_type VARCHAR(50) NOT NULL COMMENT '服务类型: chatbot, medical_notes, tips_generation等',
                endpoint VARCHAR(100) COMMENT 'API端点',
                model_name VARCHAR(100) COMMENT '使用的模型名称',
                
                total_calls INT DEFAULT 0 COMMENT '总调用次数',
                successful_calls INT DEFAULT 0 COMMENT '成功调用次数',
                failed_calls INT DEFAULT 0 COMMENT '失败调用次数',
                
                input_tokens INT DEFAULT 0 COMMENT '输入令牌数',
                output_tokens INT DEFAULT 0 COMMENT '输出令牌数',
                total_tokens INT DEFAULT 0 COMMENT '总令牌数',
                
                cache_hits INT DEFAULT 0 COMMENT '缓存命中次数',
                cache_misses INT DEFAULT 0 COMMENT '缓存未命中次数',
                
                avg_response_time_ms DECIMAL(10,2) DEFAULT 0 COMMENT '平均响应时间(毫秒)',
                total_response_time_ms BIGINT DEFAULT 0 COMMENT '总响应时间(毫秒)',
                
                estimated_cost_usd DECIMAL(10,4) DEFAULT 0 COMMENT '预估成本(美元)',
                
                date_recorded DATE NOT NULL COMMENT '记录日期',
                hour_recorded TINYINT NOT NULL DEFAULT 0 COMMENT '记录小时(0-23)',
                
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                INDEX idx_provider_date (provider, date_recorded),
                INDEX idx_service_type_date (service_type, date_recorded),
                INDEX idx_date_hour (date_recorded, hour_recorded),
                INDEX idx_created_at (created_at),
                
                UNIQUE KEY uk_usage_stats (provider, service_type, model_name, date_recorded, hour_recorded)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI使用统计表'
        `;

        const createErrorLogsTable = `
            CREATE TABLE IF NOT EXISTS ai_error_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                provider VARCHAR(50) NOT NULL COMMENT 'AI提供商',
                service_type VARCHAR(50) NOT NULL COMMENT '服务类型',
                error_type VARCHAR(100) COMMENT '错误类型',
                error_message TEXT COMMENT '错误信息',
                error_code VARCHAR(50) COMMENT '错误代码',
                
                request_summary TEXT COMMENT '请求摘要',
                response_summary TEXT COMMENT '响应摘要',
                
                occurred_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '发生时间',
                
                INDEX idx_provider_occurred (provider, occurred_at),
                INDEX idx_error_type (error_type),
                INDEX idx_occurred_at (occurred_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI错误日志表'
        `;

        try {
            await this.pool.execute(createUsageStatsTable);
            await this.pool.execute(createErrorLogsTable);
            console.log('[AIUsageTracker] Tables created successfully');
        } catch (error) {
            console.error('[AIUsageTracker] Failed to create tables:', error);
        }
    }

    /**
     * 记录AI调用统计
     */
    async recordUsage(params) {
        const {
            provider,
            serviceType,
            endpoint = null,
            modelName = null,
            success = true,
            inputTokens = 0,
            outputTokens = 0,
            responseTimeMs = 0,
            cacheHit = false,
            estimatedCostUsd = 0
        } = params;

        try {
            const now = new Date();
            const dateRecorded = now.toISOString().split('T')[0]; // YYYY-MM-DD
            const hourRecorded = now.getHours();

            const query = `
                INSERT INTO ai_usage_stats (
                    provider, service_type, endpoint, model_name,
                    total_calls, successful_calls, failed_calls,
                    input_tokens, output_tokens, total_tokens,
                    cache_hits, cache_misses,
                    total_response_time_ms, estimated_cost_usd,
                    date_recorded, hour_recorded
                ) VALUES (?, ?, ?, ?, 1, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                    total_calls = total_calls + 1,
                    successful_calls = successful_calls + ?,
                    failed_calls = failed_calls + ?,
                    input_tokens = input_tokens + ?,
                    output_tokens = output_tokens + ?,
                    total_tokens = total_tokens + ?,
                    cache_hits = cache_hits + ?,
                    cache_misses = cache_misses + ?,
                    total_response_time_ms = total_response_time_ms + ?,
                    avg_response_time_ms = CASE 
                        WHEN total_calls + 1 > 0 
                        THEN (total_response_time_ms + ?) / (total_calls + 1)
                        ELSE 0 
                    END,
                    estimated_cost_usd = estimated_cost_usd + ?,
                    updated_at = CURRENT_TIMESTAMP
            `;

            const totalTokens = inputTokens + outputTokens;
            const successCount = success ? 1 : 0;
            const failCount = success ? 0 : 1;
            const cacheHitCount = cacheHit ? 1 : 0;
            const cacheMissCount = cacheHit ? 0 : 1;

            await this.pool.execute(query, [
                provider, serviceType, endpoint, modelName,
                successCount, failCount,
                inputTokens, outputTokens, totalTokens,
                cacheHitCount, cacheMissCount,
                responseTimeMs, estimatedCostUsd,
                dateRecorded, hourRecorded,
                // ON DUPLICATE KEY UPDATE values
                successCount, failCount,
                inputTokens, outputTokens, totalTokens,
                cacheHitCount, cacheMissCount,
                responseTimeMs, responseTimeMs,
                estimatedCostUsd
            ]);

        } catch (error) {
            console.error('[AIUsageTracker] Failed to record usage:', error);
        }
    }

    /**
     * 记录AI错误
     */
    async recordError(params) {
        const {
            provider,
            serviceType,
            errorType,
            errorMessage,
            errorCode = null,
            requestSummary = null,
            responseSummary = null
        } = params;

        try {
            const query = `
                INSERT INTO ai_error_logs (
                    provider, service_type, error_type, error_message, 
                    error_code, request_summary, response_summary
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            `;

            await this.pool.execute(query, [
                provider, serviceType, errorType, errorMessage,
                errorCode, requestSummary, responseSummary
            ]);

        } catch (error) {
            console.error('[AIUsageTracker] Failed to record error:', error);
        }
    }

    /**
     * 获取使用统计 - 按日期范围
     */
    async getUsageStats(startDate, endDate, groupBy = 'day') {
        try {
            let dateFormat;
            switch (groupBy) {
                case 'hour':
                    dateFormat = "CONCAT(date_recorded, ' ', LPAD(hour_recorded, 2, '0'), ':00')";
                    break;
                case 'day':
                    dateFormat = 'date_recorded';
                    break;
                case 'week':
                    dateFormat = "DATE_FORMAT(date_recorded, '%Y-%u')";
                    break;
                case 'month':
                    dateFormat = "DATE_FORMAT(date_recorded, '%Y-%m')";
                    break;
                default:
                    dateFormat = 'date_recorded';
            }

            const query = `
                SELECT 
                    provider,
                    service_type,
                    model_name,
                    ${dateFormat} as period,
                    SUM(total_calls) as total_calls,
                    SUM(successful_calls) as successful_calls,
                    SUM(failed_calls) as failed_calls,
                    SUM(input_tokens) as input_tokens,
                    SUM(output_tokens) as output_tokens,
                    SUM(total_tokens) as total_tokens,
                    SUM(cache_hits) as cache_hits,
                    SUM(cache_misses) as cache_misses,
                    AVG(avg_response_time_ms) as avg_response_time_ms,
                    SUM(estimated_cost_usd) as estimated_cost_usd
                FROM ai_usage_stats 
                WHERE date_recorded BETWEEN ? AND ?
                GROUP BY provider, service_type, model_name, ${dateFormat}
                ORDER BY period DESC, provider, service_type
            `;

            const [rows] = await this.pool.execute(query, [startDate, endDate]);
            return rows;

        } catch (error) {
            console.error('[AIUsageTracker] Failed to get usage stats:', error);
            return [];
        }
    }

    /**
     * 获取汇总统计
     */
    async getSummaryStats(startDate, endDate) {
        try {
            const query = `
                SELECT 
                    provider,
                    model_name,
                    SUM(total_calls) as total_calls,
                    SUM(successful_calls) as successful_calls,
                    SUM(failed_calls) as failed_calls,
                    SUM(total_tokens) as total_tokens,
                    SUM(estimated_cost_usd) as estimated_cost_usd,
                    AVG(avg_response_time_ms) as avg_response_time_ms,
                    (SUM(successful_calls) / SUM(total_calls) * 100) as success_rate
                FROM ai_usage_stats 
                WHERE date_recorded BETWEEN ? AND ?
                GROUP BY provider, model_name
                ORDER BY total_calls DESC
            `;

            const [rows] = await this.pool.execute(query, [startDate, endDate]);
            return rows;

        } catch (error) {
            console.error('[AIUsageTracker] Failed to get summary stats:', error);
            return [];
        }
    }

    /**
     * 获取错误统计
     */
    async getErrorStats(startDate, endDate) {
        try {
            const query = `
                SELECT 
                    provider,
                    service_type,
                    error_type,
                    COUNT(*) as error_count,
                    DATE(occurred_at) as error_date
                FROM ai_error_logs 
                WHERE DATE(occurred_at) BETWEEN ? AND ?
                GROUP BY provider, service_type, error_type, DATE(occurred_at)
                ORDER BY occurred_at DESC
            `;

            const [rows] = await this.pool.execute(query, [startDate, endDate]);
            return rows;

        } catch (error) {
            console.error('[AIUsageTracker] Failed to get error stats:', error);
            return [];
        }
    }

    /**
     * 清理旧数据
     */
    async cleanupOldData(retentionDays = 90) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - retentionDays);
            const cutoffDateStr = cutoffDate.toISOString().split('T')[0];

            // 清理使用统计
            const cleanupUsageQuery = `DELETE FROM ai_usage_stats WHERE date_recorded < ?`;
            const [usageResult] = await this.pool.execute(cleanupUsageQuery, [cutoffDateStr]);

            // 清理错误日志
            const cleanupErrorQuery = `DELETE FROM ai_error_logs WHERE DATE(occurred_at) < ?`;
            const [errorResult] = await this.pool.execute(cleanupErrorQuery, [cutoffDateStr]);

            console.log(`[AIUsageTracker] Cleaned up ${usageResult.affectedRows} usage records and ${errorResult.affectedRows} error records`);
            
            return {
                usageRecordsDeleted: usageResult.affectedRows,
                errorRecordsDeleted: errorResult.affectedRows
            };

        } catch (error) {
            console.error('[AIUsageTracker] Failed to cleanup old data:', error);
            return { usageRecordsDeleted: 0, errorRecordsDeleted: 0 };
        }
    }
}

// 创建单例实例
const aiUsageTracker = new AIUsageTracker();

module.exports = aiUsageTracker; 