import React, { useState, useEffect } from 'react';
import { API_URL } from '../utils/env';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import {
    Container,
    Paper,
    Typography,
    TextField,
    Button,
    Box,
    CircularProgress,
    Alert,
    Tabs,
    Tab,
    Divider,
    Card,
    CardContent,
    Grid,
    List,
    ListItem,
    ListItemText,
    ListItemIcon,
    Radio,
    FormControlLabel,
    FormControl,
    FormLabel,
    RadioGroup,
    InputLabel,
    OutlinedInput,
    InputAdornment,
    IconButton,
    useTheme,
    useMediaQuery
} from '@mui/material';
import { styled } from '@mui/material/styles';
import SearchIcon from '@mui/icons-material/Search';
import EventIcon from '@mui/icons-material/Event';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import PeopleIcon from '@mui/icons-material/People';
import MedicalServicesIcon from '@mui/icons-material/MedicalServices';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import NotesIcon from '@mui/icons-material/Notes';
import { useLanguage } from '../context/LanguageContext';
import CancelIcon from '@mui/icons-material/Cancel';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ScheduleIcon from '@mui/icons-material/Schedule';
import AssignmentTurnedInIcon from '@mui/icons-material/AssignmentTurnedIn';
import PersonIcon from '@mui/icons-material/Person';
import PhoneIcon from '@mui/icons-material/Phone';
import MedicalInformationIcon from '@mui/icons-material/MedicalInformation';

// Configure dayjs
dayjs.extend(utc);
dayjs.extend(timezone);

// Set the timezone to match backend
const TIMEZONE = 'America/Vancouver';

const StyledPaper = styled(Paper)(({ theme }) => ({
    marginTop: theme.spacing(3),
    marginBottom: theme.spacing(3),
    padding: theme.spacing(3),
    [theme.breakpoints.up('md')]: {
        marginTop: theme.spacing(4),
        marginBottom: theme.spacing(4),
        padding: theme.spacing(4),
    },
}));

const StyledButton = styled(Button)(({ theme }) => ({
    margin: theme.spacing(2, 0, 1),
}));

const StyledCard = styled(Card)(({ theme }) => ({
    marginBottom: theme.spacing(2),
    transition: 'transform 0.2s ease-in-out',
    '&:hover': {
        transform: 'translateY(-3px)',
        boxShadow: theme.shadows[4],
    },
}));

const TabPanel = (props) => {
    const { children, value, index, ...other } = props;
    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`appointment-tabpanel-${index}`}
            aria-labelledby={`appointment-tab-${index}`}
            {...other}
        >
            {value === index && <Box sx={{ pt: 2 }}>{children}</Box>}
        </div>
    );
};

const getStatusIcon = (status) => {
    switch (status) {
        case 'confirmed':
            return <CheckCircleIcon color="success" />;
        case 'cancelled':
            return <CancelIcon color="error" />;
        case 'completed':
            return <AssignmentTurnedInIcon color="primary" />;
        case 'pending':
            return <ScheduleIcon color="warning" />;
        default:
            return <ScheduleIcon color="default" />;
    }
};

const AppointmentLookup = () => {
    const [searchMethod, setSearchMethod] = useState('namePhone');
    const [lastName, setLastName] = useState('');
    const [phone, setPhone] = useState('');
    const [demographicNo, setDemographicNo] = useState('');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [tabValue, setTabValue] = useState(0);
    const [patientData, setPatientData] = useState(null);
    const [multiplePatients, setMultiplePatients] = useState(null);

    const handleTabChange = (event, newValue) => {
        setTabValue(newValue);
    };

    const handleSearchMethodChange = (event) => {
        setSearchMethod(event.target.value);
        // Reset fields
        setError('');
        setSuccess('');
        setPatientData(null);
        setMultiplePatients(null);
    };

    const handleSearchSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError('');
        setSuccess('');
        setPatientData(null);
        setMultiplePatients(null);

        try {
            let response;
            if (searchMethod === 'namePhone') {
                if (!lastName || !phone) {
                    setError('Last name and phone are required');
                    setLoading(false);
                    return;
                }

                response = await fetch(`${API_URL}/api/appointments/lookup`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ lastName, phone })
                });
            } else {
                if (!demographicNo) {
                    setError('Patient ID is required');
                    setLoading(false);
                    return;
                }

                response = await fetch(`${API_URL}/api/appointments/guest/${demographicNo}`);
            }

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'Failed to search for appointments');
            }

            if (data.patients && data.patients.length > 1) {
                setMultiplePatients(data.patients);
                setSuccess('Multiple patients found. Please select one.');
            } else if (data.success) {
                setPatientData(data);
                setSuccess('Appointment data retrieved successfully');
            } else {
                setError(data.message || 'No appointments found');
            }
        } catch (err) {
            console.error('Error searching for appointments:', err);
            setError(err.message || 'Error searching for appointments');
        } finally {
            setLoading(false);
        }
    };

    const handlePatientSelect = async (demographicNo) => {
        setLoading(true);
        setError('');
        setSuccess('');
        setPatientData(null);
        setMultiplePatients(null);

        try {
            const response = await fetch(`${API_URL}/api/appointments/guest/${demographicNo}`);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'Failed to fetch patient appointments');
            }

            setPatientData(data);
            setSuccess('Appointment data retrieved successfully');
        } catch (err) {
            console.error('Error fetching patient data:', err);
            setError(err.message || 'Error fetching patient data');
        } finally {
            setLoading(false);
        }
    };

    const formatTimeString = (timeStr) => {
        if (timeStr && timeStr.includes("::")) {
            return timeStr.replace("::", ":");
        }
        return timeStr;
    };

    const formatDate = (dateStr) => {
        if (!dateStr) return 'N/A';

        // Format date using dayjs with the correct timezone
        const date = dayjs.tz(dateStr, TIMEZONE);
        return date.format('dddd, MMMM D, YYYY');
    };

    return (
        <Container component="main" maxWidth="md">
            <StyledPaper elevation={3}>
                <Typography component="h1" variant="h5" gutterBottom align="center">
                    Patient Appointment History
                </Typography>
                <Typography color="textSecondary" paragraph align="center">
                    Search for a patient's appointment history by name, phone or patient ID
                </Typography>

                <FormControl component="fieldset" sx={{ mb: 3, width: '100%' }}>
                    <FormLabel component="legend">Search Method</FormLabel>
                    <RadioGroup
                        row
                        name="search-method"
                        value={searchMethod}
                        onChange={handleSearchMethodChange}
                    >
                        <FormControlLabel
                            value="namePhone"
                            control={<Radio />}
                            label="By Name & Phone"
                        />
                        <FormControlLabel
                            value="patientId"
                            control={<Radio />}
                            label="By Patient ID"
                        />
                    </RadioGroup>
                </FormControl>

                <Box component="form" onSubmit={handleSearchSubmit} noValidate>
                    {searchMethod === 'namePhone' ? (
                        <Grid container spacing={2}>
                            <Grid item xs={12} sm={6}>
                                <TextField
                                    fullWidth
                                    id="lastName"
                                    label="Last Name"
                                    name="lastName"
                                    value={lastName}
                                    onChange={(e) => setLastName(e.target.value)}
                                    required
                                />
                            </Grid>
                            <Grid item xs={12} sm={6}>
                                <TextField
                                    fullWidth
                                    id="phone"
                                    label="Phone Number"
                                    name="phone"
                                    value={phone}
                                    onChange={(e) => setPhone(e.target.value)}
                                    required
                                />
                            </Grid>
                        </Grid>
                    ) : (
                        <TextField
                            fullWidth
                            id="demographicNo"
                            label="Patient ID"
                            name="demographicNo"
                            value={demographicNo}
                            onChange={(e) => setDemographicNo(e.target.value)}
                            required
                            margin="normal"
                        />
                    )}

                    <StyledButton
                        type="submit"
                        fullWidth
                        variant="contained"
                        color="primary"
                        disabled={loading}
                        startIcon={<SearchIcon />}
                    >
                        {loading ? 'Searching...' : 'Search'}
                    </StyledButton>
                </Box>

                {loading && (
                    <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                        <CircularProgress />
                    </Box>
                )}

                {error && (
                    <Alert severity="error" sx={{ mt: 2 }}>
                        {error}
                    </Alert>
                )}

                {success && (
                    <Alert severity="success" sx={{ mt: 2 }}>
                        {success}
                    </Alert>
                )}

                {multiplePatients && (
                    <Box sx={{ mt: 3 }}>
                        <Typography variant="h6" gutterBottom>
                            Multiple patients found. Please select one:
                        </Typography>
                        <List>
                            {multiplePatients.map((patient) => (
                                <ListItem
                                    key={patient.demographicNo}
                                    button
                                    onClick={() => handlePatientSelect(patient.demographicNo)}
                                    divider
                                >
                                    <ListItemIcon>
                                        <PersonIcon />
                                    </ListItemIcon>
                                    <ListItemText
                                        primary={`${patient.firstName} ${patient.lastName}`}
                                        secondary={`Phone: ${patient.phone} | DOB: ${patient.dob}`}
                                    />
                                </ListItem>
                            ))}
                        </List>
                    </Box>
                )}

                {patientData && (
                    <Box sx={{ mt: 4 }}>
                        <Card sx={{ mb: 3, backgroundColor: '#f5f5f5' }}>
                            <CardContent>
                                <Typography variant="h6" gutterBottom>
                                    Patient Information
                                </Typography>
                                <Grid container spacing={2}>
                                    <Grid item xs={12} sm={6}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                            <PersonIcon sx={{ mr: 1 }} color="primary" />
                                            <Typography>
                                                {patientData.patient ? `${patientData.patient.firstName} ${patientData.patient.lastName}` : 'N/A'}
                                            </Typography>
                                        </Box>
                                    </Grid>
                                    <Grid item xs={12} sm={6}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                            <PhoneIcon sx={{ mr: 1 }} color="primary" />
                                            <Typography>
                                                {patientData.patient ? patientData.patient.phone : 'N/A'}
                                            </Typography>
                                        </Box>
                                    </Grid>
                                    {patientData.patient && patientData.patient.dob && (
                                        <Grid item xs={12}>
                                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                <EventIcon sx={{ mr: 1 }} color="primary" />
                                                <Typography>
                                                    DOB: {patientData.patient.dob}
                                                </Typography>
                                            </Box>
                                        </Grid>
                                    )}
                                    <Grid item xs={12}>
                                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                            <MedicalInformationIcon sx={{ mr: 1 }} color="primary" />
                                            <Typography>
                                                Patient ID: {patientData.demographicNo}
                                            </Typography>
                                        </Box>
                                    </Grid>
                                </Grid>
                            </CardContent>
                        </Card>

                        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                            <Tabs value={tabValue} onChange={handleTabChange} aria-label="appointment tabs">
                                <Tab label="Upcoming Appointments" id="appointments-tab-0" />
                                <Tab label="Past Appointments" id="appointments-tab-1" />
                            </Tabs>
                        </Box>

                        <TabPanel value={tabValue} index={0}>
                            {patientData.upcoming && patientData.upcoming.length > 0 ? (
                                patientData.upcoming.map((appointment) => (
                                    <StyledCard key={appointment.id} variant="outlined">
                                        <CardContent>
                                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                                                <Typography variant="h6" component="div">
                                                    {appointment.reason}
                                                </Typography>
                                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                    {getStatusIcon(appointment.status)}
                                                    <Typography color="textSecondary" sx={{ ml: 1 }}>
                                                        {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                                                    </Typography>
                                                </Box>
                                            </Box>

                                            <Divider sx={{ my: 1 }} />

                                            <Grid container spacing={2}>
                                                <Grid item xs={12} sm={6}>
                                                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                                        <EventIcon sx={{ mr: 1 }} fontSize="small" color="primary" />
                                                        <Typography variant="body2">
                                                            {formatDate(appointment.date)}
                                                        </Typography>
                                                    </Box>
                                                </Grid>
                                                <Grid item xs={12} sm={6}>
                                                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                                        <AccessTimeIcon sx={{ mr: 1 }} fontSize="small" color="primary" />
                                                        <Typography variant="body2">
                                                            {formatTimeString(appointment.time)}
                                                        </Typography>
                                                    </Box>
                                                </Grid>
                                                <Grid item xs={12}>
                                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                        <PersonIcon sx={{ mr: 1 }} fontSize="small" color="primary" />
                                                        <Typography variant="body2">
                                                            {appointment.doctor}
                                                        </Typography>
                                                    </Box>
                                                </Grid>
                                                {appointment.notes && (
                                                    <Grid item xs={12}>
                                                        <Typography variant="body2" color="textSecondary">
                                                            Notes: {appointment.notes}
                                                        </Typography>
                                                    </Grid>
                                                )}
                                            </Grid>
                                        </CardContent>
                                    </StyledCard>
                                ))
                            ) : (
                                <Typography color="textSecondary" align="center" sx={{ py: 4 }}>
                                    No upcoming appointments found
                                </Typography>
                            )}
                        </TabPanel>

                        <TabPanel value={tabValue} index={1}>
                            {patientData.past && patientData.past.length > 0 ? (
                                patientData.past.map((appointment) => (
                                    <StyledCard key={appointment.id} variant="outlined">
                                        <CardContent>
                                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                                                <Typography variant="h6" component="div">
                                                    {appointment.reason}
                                                </Typography>
                                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                    {getStatusIcon(appointment.status)}
                                                    <Typography color="textSecondary" sx={{ ml: 1 }}>
                                                        {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                                                    </Typography>
                                                </Box>
                                            </Box>

                                            <Divider sx={{ my: 1 }} />

                                            <Grid container spacing={2}>
                                                <Grid item xs={12} sm={6}>
                                                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                                        <EventIcon sx={{ mr: 1 }} fontSize="small" color="primary" />
                                                        <Typography variant="body2">
                                                            {formatDate(appointment.date)}
                                                        </Typography>
                                                    </Box>
                                                </Grid>
                                                <Grid item xs={12} sm={6}>
                                                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                                        <AccessTimeIcon sx={{ mr: 1 }} fontSize="small" color="primary" />
                                                        <Typography variant="body2">
                                                            {formatTimeString(appointment.time)}
                                                        </Typography>
                                                    </Box>
                                                </Grid>
                                                <Grid item xs={12}>
                                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                        <PersonIcon sx={{ mr: 1 }} fontSize="small" color="primary" />
                                                        <Typography variant="body2">
                                                            {appointment.doctor}
                                                        </Typography>
                                                    </Box>
                                                </Grid>
                                                {appointment.notes && (
                                                    <Grid item xs={12}>
                                                        <Typography variant="body2" color="textSecondary">
                                                            Notes: {appointment.notes}
                                                        </Typography>
                                                    </Grid>
                                                )}
                                            </Grid>
                                        </CardContent>
                                    </StyledCard>
                                ))
                            ) : (
                                <Typography color="textSecondary" align="center" sx={{ py: 4 }}>
                                    No past appointments found
                                </Typography>
                            )}
                        </TabPanel>
                    </Box>
                )}
            </StyledPaper>
        </Container>
    );
};

export default AppointmentLookup; 