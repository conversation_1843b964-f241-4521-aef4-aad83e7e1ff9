-- AI使用统计表
CREATE TABLE IF NOT EXISTS ai_usage_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    provider VARCHAR(50) NOT NULL COMMENT 'AI提供商: gemini, openrouter',
    service_type VARCHAR(50) NOT NULL COMMENT '服务类型: chatbot, medical_notes, tips_generation等',
    endpoint VARCHAR(100) COMMENT 'API端点',
    model_name VARCHAR(100) COMMENT '使用的模型名称',
    
    -- 调用统计
    total_calls INT DEFAULT 0 COMMENT '总调用次数',
    successful_calls INT DEFAULT 0 COMMENT '成功调用次数',
    failed_calls INT DEFAULT 0 COMMENT '失败调用次数',
    
    -- 令牌统计
    input_tokens INT DEFAULT 0 COMMENT '输入令牌数',
    output_tokens INT DEFAULT 0 COMMENT '输出令牌数',
    total_tokens INT DEFAULT 0 COMMENT '总令牌数',
    
    -- 缓存统计
    cache_hits INT DEFAULT 0 COMMENT '缓存命中次数',
    cache_misses INT DEFAULT 0 COMMENT '缓存未命中次数',
    
    -- 性能统计
    avg_response_time_ms DECIMAL(10,2) DEFAULT 0 COMMENT '平均响应时间(毫秒)',
    total_response_time_ms BIGINT DEFAULT 0 COMMENT '总响应时间(毫秒)',
    
    -- 成本统计 (如果有的话)
    estimated_cost_usd DECIMAL(10,4) DEFAULT 0 COMMENT '预估成本(美元)',
    
    -- 时间维度
    date_recorded DATE NOT NULL COMMENT '记录日期',
    hour_recorded TINYINT NOT NULL DEFAULT 0 COMMENT '记录小时(0-23)',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_provider_date (provider, date_recorded),
    INDEX idx_service_type_date (service_type, date_recorded),
    INDEX idx_date_hour (date_recorded, hour_recorded),
    INDEX idx_created_at (created_at),
    
    -- 唯一约束：每个提供商+服务类型+日期+小时的组合唯一
    UNIQUE KEY uk_usage_stats (provider, service_type, date_recorded, hour_recorded)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI使用统计表';

-- 实时AI指标表（用于存储当前会话的实时指标）
CREATE TABLE IF NOT EXISTS ai_realtime_metrics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    provider VARCHAR(50) NOT NULL COMMENT 'AI提供商',
    service_type VARCHAR(50) NOT NULL COMMENT '服务类型',
    
    -- 实时计数器
    session_calls INT DEFAULT 0 COMMENT '当前会话调用次数',
    session_successes INT DEFAULT 0 COMMENT '当前会话成功次数',
    session_failures INT DEFAULT 0 COMMENT '当前会话失败次数',
    session_cache_hits INT DEFAULT 0 COMMENT '当前会话缓存命中',
    session_cache_misses INT DEFAULT 0 COMMENT '当前会话缓存未命中',
    
    -- 会话开始时间
    session_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_provider_service (provider, service_type),
    INDEX idx_last_activity (last_activity),
    
    -- 唯一约束
    UNIQUE KEY uk_realtime_metrics (provider, service_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI实时指标表';

-- AI错误日志表
CREATE TABLE IF NOT EXISTS ai_error_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    provider VARCHAR(50) NOT NULL COMMENT 'AI提供商',
    service_type VARCHAR(50) NOT NULL COMMENT '服务类型',
    error_type VARCHAR(100) COMMENT '错误类型',
    error_message TEXT COMMENT '错误信息',
    error_code VARCHAR(50) COMMENT '错误代码',
    
    -- 请求信息
    request_payload JSON COMMENT '请求负载',
    response_payload JSON COMMENT '响应负载',
    
    -- 时间信息
    occurred_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '发生时间',
    
    -- 索引
    INDEX idx_provider_occurred (provider, occurred_at),
    INDEX idx_error_type (error_type),
    INDEX idx_occurred_at (occurred_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI错误日志表'; 