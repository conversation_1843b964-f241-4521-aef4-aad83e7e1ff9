// Video conference configuration
export const VIDEO_CONFERENCE_CONFIG = {
    JIGASI_SERVER: process.env.REACT_APP_JIGASI_URL || 'https://**************:8443',
    DEFAULT_CONFERENCE_OPTIONS: {
        videoQuality: '720p',
        enableScreenSharing: true,
        muteOnEntry: true,
        requireDisplayName: true,
        enableChat: true,
        enableRaiseHand: true,
        enableRecording: false,
        // Add any additional Jigasi-specific options here
    },
    UI_SETTINGS: {
        showParticipantCount: true,
        showChatButton: true,
        showSettingsButton: true,
        enableFullscreen: true,
        primaryColor: '#1976d2', // Match your app's theme
        // Add any additional UI customization options
    }
};
