CREATE TABLE IF NOT EXISTS dietician_comment_summaries (
    summary_id INT AUTO_INCREMENT PRIMARY KEY,
    dietician_comment_id INT NOT NULL, -- This will be the FK to formDietician.ID
    language VARCHAR(5) NOT NULL,
    generated_summary TEXT,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_dietician_comment_id_lang (dietician_comment_id, language),
    UNIQUE KEY unique_comment_lang (dietician_comment_id, language),
    FOREIGN KEY (dietician_comment_id) REFERENCES formDietician(ID) ON DELETE CASCADE
); 