{"name": "mmcwebapp-backend", "version": "1.0.0", "description": "MMC Web App Backend", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "generate-tip": "node src/scripts/generateDailyTip.js", "setup-daily-tips": "bash src/scripts/setupDailyTips.sh"}, "dependencies": {"@supabase/supabase-js": "^2.0.0", "axios": "^1.6.7", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "cron": "^3.1.6", "date-fns": "^3.0.0", "dayjs": "^1.11.11", "dotenv": "^16.0.3", "express": "^4.18.2", "form-data": "^4.0.2", "jsonwebtoken": "^9.0.0", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.0", "mysql2": "^3.2.0", "node-cache": "^5.1.2", "nodemailer": "^6.9.13", "sharp": "^0.34.2", "stripe": "^18.1.1", "uuid": "^9.0.1", "validator": "^13.9.0", "webdav": "^4.2.1", "winston": "^3.13.0", "ws": "^8.17.0"}, "devDependencies": {"jest": "^29.5.0", "nodemon": "^2.0.22"}}