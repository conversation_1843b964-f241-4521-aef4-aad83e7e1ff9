import React, { useState, useEffect } from 'react';
import { API_URL } from '../utils/env';
import {
    Box,
    Typography,
    Card,
    CardContent,
    Grid,
    Button,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    IconButton,
    Divider,
    CircularProgress,
    Alert
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import ArticleIcon from '@mui/icons-material/Article';
import { useLanguage } from '../context/LanguageContext';
import axios from 'axios';

const MemberResources = () => {
    const { t } = useLanguage();
    const [articles, setArticles] = useState([]);
    const [selectedArticle, setSelectedArticle] = useState(null);
    const [articleContent, setArticleContent] = useState('');
    const [dialogOpen, setDialogOpen] = useState(false);
    const [loading, setLoading] = useState(true);
    const [articleLoading, setArticleLoading] = useState(false);
    const [error, setError] = useState('');

    // 获取文章列表
    useEffect(() => {
        const fetchArticles = async () => {
            setLoading(true);
            setError('');

            try {
                const token = localStorage.getItem('token');
                const response = await axios.get(`${API_URL}/api/articles`, {
                    headers: { Authorization: `Bearer ${token}` }
                });

                setArticles(response.data || []);
            } catch (err) {
                console.error('Error fetching member resources:', err);
                setError(err.response?.data?.message || 'Failed to load resources');
            } finally {
                setLoading(false);
            }
        };

        fetchArticles();
    }, []);

    // 获取特定文章的内容
    const fetchArticleContent = async (filename) => {
        setArticleLoading(true);

        try {
            const token = localStorage.getItem('token');
            const response = await axios.get(`${API_URL}/api/articles/${filename}`, {
                headers: { Authorization: `Bearer ${token}` }
            });

            setArticleContent(response.data);
        } catch (err) {
            console.error(`Error fetching article content for ${filename}:`, err);
            setArticleContent(`**${t('resource_not_available')}**`);
        } finally {
            setArticleLoading(false);
        }
    };

    const handleOpenArticle = (article) => {
        setSelectedArticle(article);
        setArticleContent('');
        setDialogOpen(true);
        fetchArticleContent(article.filename);
    };

    const handleCloseDialog = () => {
        setDialogOpen(false);
        setSelectedArticle(null);
        setArticleContent('');
    };

    // 渲染文章内容 (简单实现，后续可以使用 react-markdown)
    const renderArticleContent = () => {
        if (articleLoading) {
            return (
                <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                    <CircularProgress />
                    <Typography sx={{ ml: 2 }}>{t('article_loading')}</Typography>
                </Box>
            );
        }

        // 简单的Markdown-like渲染 (只处理段落和粗体)
        return (
            <Box>
                {articleContent.split('\n\n').map((paragraph, idx) => (
                    <Typography key={idx} paragraph>
                        {paragraph.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')}
                    </Typography>
                ))}
            </Box>
        );
    };

    // 渲染文章列表
    const renderArticlesList = () => {
        if (loading) {
            return (
                <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                    <CircularProgress />
                    <Typography sx={{ ml: 2 }}>{t('loading_resources')}</Typography>
                </Box>
            );
        }

        if (error) {
            return (
                <Alert severity="error" sx={{ my: 2 }}>
                    {error}
                </Alert>
            );
        }

        if (articles.length === 0) {
            return (
                <Alert severity="info" sx={{ my: 2 }}>
                    {t('no_resources_found')}
                </Alert>
            );
        }

        return (
            <Grid container spacing={2}>
                {articles.map((article, index) => (
                    <Grid item xs={12} sm={6} md={4} key={index}>
                        <Card
                            variant="outlined"
                            sx={{
                                height: '100%',
                                display: 'flex',
                                flexDirection: 'column',
                                '&:hover': {
                                    boxShadow: 3,
                                    cursor: 'pointer'
                                }
                            }}
                            onClick={() => handleOpenArticle(article)}
                        >
                            <CardContent sx={{ flexGrow: 1 }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                    <ArticleIcon color="primary" sx={{ mr: 1 }} />
                                    <Typography variant="h6" component="h3">
                                        {article.title}
                                    </Typography>
                                </Box>
                                <Button
                                    color="primary"
                                    sx={{ mt: 2 }}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        handleOpenArticle(article);
                                    }}
                                >
                                    {t('read_more')}
                                </Button>
                            </CardContent>
                        </Card>
                    </Grid>
                ))}
            </Grid>
        );
    };

    return (
        <>
            <Box sx={{ my: 4 }}>
                <Typography variant="h5" component="h2" gutterBottom>
                    {t('member_resources')}
                </Typography>
                <Typography variant="body1" color="text.secondary" paragraph>
                    {t('member_resources_description')}
                </Typography>

                <Divider sx={{ my: 2 }} />

                {renderArticlesList()}
            </Box>

            {/* 文章内容对话框 */}
            <Dialog
                open={dialogOpen}
                onClose={handleCloseDialog}
                fullWidth
                maxWidth="md"
                aria-labelledby="article-dialog-title"
            >
                <DialogTitle id="article-dialog-title">
                    {selectedArticle?.title}
                    <IconButton
                        aria-label="close"
                        onClick={handleCloseDialog}
                        sx={{ position: 'absolute', right: 8, top: 8 }}
                    >
                        <CloseIcon />
                    </IconButton>
                </DialogTitle>
                <DialogContent dividers>
                    {renderArticleContent()}
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDialog}>{t('back_to_resources')}</Button>
                </DialogActions>
            </Dialog>
        </>
    );
};

export default MemberResources; 