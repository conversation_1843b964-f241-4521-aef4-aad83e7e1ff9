const PrescriptionService = require('../services/prescriptionService');
const PrescriptionExplanationService = require('../services/prescriptionExplanationService');
const { createLogger } = require('../utils/logger');
const { ValidationError, AuthorizationError } = require('../utils/errors');

const logger = createLogger('PrescriptionController');

/**
 * Get prescriptions for a specific patient
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} JSON response with prescriptions list
 */
exports.getPatientPrescriptions = async (req, res) => {
    try {
        const { demographicNo } = req.params;
        const targetDemoNo = parseInt(demographicNo, 10);

        if (isNaN(targetDemoNo)) {
            throw new ValidationError('Invalid demographic number provided');
        }

        const prescriptions = await PrescriptionService.getPatientPrescriptions(req.user, targetDemoNo);

        res.json({
            success: true,
            prescriptions
        });

    } catch (error) {
        logger.error('Error in getPatientPrescriptions controller:', {
            message: error.message,
            stack: error.stack,
            demographicNo: req.params.demographicNo,
            userId: req.user?.id
        });

        if (error instanceof ValidationError) {
            return res.status(400).json({
                success: false,
                message: error.message
            });
        }

        if (error instanceof AuthorizationError) {
            return res.status(403).json({
                success: false,
                message: error.message
            });
        }

        res.status(500).json({
            success: false,
            message: 'Server error fetching prescriptions'
        });
    }
};

/**
 * 获取处方的AI解释
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @returns {Object} 包含AI解释的JSON响应
 */
exports.getPrescriptionExplanation = async (req, res) => {
    try {
        const { prescriptionId } = req.params;
        const scriptId = parseInt(prescriptionId, 10);
        const lang = req.query.lang || 'zh'; // 默认中文

        if (isNaN(scriptId)) {
            throw new ValidationError('无效的处方ID');
        }

        // 获取处方详情
        const prescription = await PrescriptionService.getPrescriptionById(req.user, scriptId);

        // 如果没有找到处方
        if (!prescription) {
            return res.status(404).json({
                success: false,
                message: '未找到处方'
            });
        }

        // 获取或生成AI解释
        const explanationResult = await PrescriptionExplanationService.getOrCreateExplanation(
            prescription,
            lang
        );

        if (explanationResult.success) {
            return res.json({
                success: true,
                explanation: explanationResult.explanation
            });
        } else {
            throw new Error(explanationResult.message || '无法生成AI解释');
        }

    } catch (error) {
        logger.error('获取处方AI解释控制器出错:', {
            message: error.message,
            stack: error.stack,
            prescriptionId: req.params.prescriptionId,
            userId: req.user?.id
        });

        if (error instanceof ValidationError) {
            return res.status(400).json({
                success: false,
                message: error.message
            });
        }

        if (error instanceof AuthorizationError) {
            return res.status(403).json({
                success: false,
                message: error.message
            });
        }

        res.status(500).json({
            success: false,
            message: '服务器获取处方AI解释时出错'
        });
    }
}; 