import React, { useState, useEffect, useMemo } from 'react';
import { API_URL } from '../utils/env';
import { useNavigate } from 'react-router-dom';
import {
    Container,
    Paper,
    Typography,
    Stepper,
    Step,
    StepLabel,
    Button,
    Box,
    CircularProgress,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    TextField,
    Grid,
    Alert,
    AlertTitle,
    List,
    ListItem,
    ListItemButton,
    ListItemText,
    Chip,
    Divider,
    useTheme,
    useMediaQuery,
    FormControlLabel,
    Checkbox,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { styled } from '@mui/material/styles';
import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import MedicalServicesIcon from '@mui/icons-material/MedicalServices';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import NotesIcon from '@mui/icons-material/Notes';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import CheckIcon from '@mui/icons-material/Check';
import EventBusyIcon from '@mui/icons-material/EventBusy';
import PhotoCamera from '@mui/icons-material/PhotoCamera';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import { useView } from '../context/ViewContext';
import { useLanguage } from '../context/LanguageContext';
import { useUserType } from '../hooks/useUserType';
import { getLocationDisplay, locationOptions } from '../utils/locationUtils';
import dayjs from 'dayjs';

// --- Basic Styling ---
const StyledPaper = styled(Paper)(({ theme }) => ({
    marginTop: theme.spacing(3),
    marginBottom: theme.spacing(3),
    padding: theme.spacing(3),
    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.12)',
    borderRadius: '16px',
    [theme.breakpoints.down('sm')]: {
        marginTop: theme.spacing(2),
        marginBottom: theme.spacing(2),
        padding: theme.spacing(2),
    }
}));

// 新增服务类型描述映射
const serviceDescriptions = {
    'Regular Checkup': '常规健康检查，包括基础身体检查和健康咨询',
    'Pediatric Care': '儿科护理，专为婴幼儿和青少年提供的医疗服务',
    'Vaccination': '疫苗接种服务，包括流感疫苗和其他常规疫苗',
    'Women Health': '女性健康服务，包括妇科检查和相关咨询',
    'Men Health': '男性健康服务，包括前列腺检查和相关咨询',
    'Chronic Disease': '慢性疾病管理，针对糖尿病、高血压等慢性病患者的长期护理',
    'Mental Health': '心理健康咨询和评估',
    'Diagnostic': '诊断服务，包括各种检测和化验',
    // 可根据实际服务类型添加更多描述
};

const BookingPage = () => {
    const { t } = useLanguage();
    const { loggedInUser } = useView();
    const { isMember } = useUserType();
    const navigate = useNavigate();
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

    // 获取当前语言设置
    const { language: currentLang } = useLanguage();

    // Stepper state
    const [activeStep, setActiveStep] = useState(0);

    // Form state
    const [selectedServiceType, setSelectedServiceType] = useState('');
    const [selectedDate, setSelectedDate] = useState('');
    const [selectedTime, setSelectedTime] = useState('');
    const [bookingReason, setBookingReason] = useState('');
    const [selectedProviderByType, setSelectedProviderByType] = useState('');
    const [selectedLocation, setSelectedLocation] = useState('online/phone');
    const [reasonImages, setReasonImages] = useState([]);
    const [imagePreviews, setImagePreviews] = useState([]);

    // Data fetching state
    const [availabilityByType, setAvailabilityByType] = useState([]);
    const [serviceTypes, setServiceTypes] = useState([]);
    const [availableDates, setAvailableDates] = useState([]);
    const [loadingAvailableDates, setLoadingAvailableDates] = useState(false);

    // Force in-person location for Pap Test
    const requiresInPerson = useMemo(() => {
        if (!selectedServiceType || typeof selectedServiceType !== 'string') return false;
        return selectedServiceType.toLowerCase().includes('pap test');
    }, [selectedServiceType]);

    useEffect(() => {
        if (requiresInPerson && selectedLocation !== 'In Person') {
            setSelectedLocation('In Person');
        }
    }, [requiresInPerson, selectedLocation]);

    // Auto-fill booking reason for Pap Test
    useEffect(() => {
        if (selectedServiceType && selectedServiceType.toLowerCase().includes('pap test')) {
            if (!bookingReason || bookingReason.trim() === '') {
                setBookingReason('Pap Test 宫颈癌抹片筛查');
            }
        }
    }, [selectedServiceType, bookingReason]);

    // Loading states
    const [loadingServiceTypes, setLoadingServiceTypes] = useState(false);
    const [loadingAvailabilityByType, setLoadingAvailabilityByType] = useState(false);
    const [bookingLoading, setBookingLoading] = useState(false);

    // Status states
    const [error, setError] = useState('');
    const [bookingSuccess, setBookingSuccess] = useState(false);

    // New state for terms acceptance
    const [termsAccepted, setTermsAccepted] = useState(false);

    // Revoke object URLs on component unmount or when imagePreviews changes
    useEffect(() => {
        return () => {
            imagePreviews.forEach(preview => {
                if (preview) {
                    URL.revokeObjectURL(preview);
                }
            });
        };
    }, [imagePreviews]);

    // Steps for booking process
    const steps = useMemo(() => [
        t('select_service_type'),
        t('select_date_time_doctor'),
        t('confirm_booking')
    ], [t]);

    // Helper to get provider name for confirmation
    const finalSelectedProviderInfo = useMemo(() => {
        if (!selectedProviderByType) return null;

        const slotData = availabilityByType.find(slot => slot.provider_no === selectedProviderByType);
        if (slotData) {
            return { id: slotData.provider_no, name: slotData.providerName };
        }

        return null;
    }, [selectedProviderByType, availabilityByType]);

    // Helper function to check if provider is Dr. Miao
    const isDrMiao = (providerName) => {
        return providerName && providerName.toLowerCase().includes('miao');
    };

    // Filter slots based on membership for Dr. Miao
    const filteredAvailabilityByType = availabilityByType.filter(slot => {
        // If it's Dr. Miao and user is not a member, exclude the slot
        if (isDrMiao(slot.providerName) && !isMember) {
            return false;
        }
        return true;
    });

    // --- Fetch Bookable Providers (Only run if mode is provider) --- REMOVED
    // useEffect(() => {
    //     ...
    // }, [navigate, t, bookingMode]);

    // --- Fetch Service Types (Only run if mode is type) --- Simplified
    useEffect(() => {
        // if (bookingMode !== 'type') { // REMOVED check
        //     setServiceTypes([]);
        //     setLoadingServiceTypes(false);
        //     return;
        // }
        const fetchServiceTypes = async () => {
            setLoadingServiceTypes(true);
            setError('');
            const token = localStorage.getItem('token');
            if (!token) { navigate('/login'); return; }
            try {
                const response = await fetch(`${API_URL}/api/providers/consultation-types`, {
                    headers: { 'Authorization': `Bearer ${token}` },
                });
                if (!response.ok) throw new Error(t('error_fetching_service_types')); // New translation
                const data = await response.json();
                if (data.success && Array.isArray(data.consultationTypes)) {
                    setServiceTypes(data.consultationTypes);
                } else {
                    throw new Error(data.message || t('error_fetching_service_types'));
                }
            } catch (err) {
                console.error("Fetch service types error:", err);
                setError(err.message || t('error_fetching_service_types'));
                setServiceTypes([]);
            } finally {
                setLoadingServiceTypes(false);
            }
        };
        fetchServiceTypes();
    }, [navigate, t]);

    // 获取特定服务类型的可用日期
    useEffect(() => {
        if (!selectedServiceType) {
            setAvailableDates([]);
            return;
        }

        const fetchAvailableDates = async () => {
            setLoadingAvailableDates(true);
            setError('');
            const token = localStorage.getItem('token');
            if (!token) { navigate('/login'); return; }

            try {
                const response = await fetch(`${API_URL}/api/providers/availability/by-type/dates?type=${encodeURIComponent(selectedServiceType)}`, {
                    headers: { 'Authorization': `Bearer ${token}` },
                });

                if (!response.ok) {
                    // Attempt to parse error message from backend
                    let errorMsg = t('error_fetching_available_dates'); // Default error
                    try {
                        const errorData = await response.json();
                        errorMsg = errorData.message || errorMsg;
                    } catch (parseError) {
                        // Ignore if response is not JSON or empty
                    }
                    throw new Error(errorMsg);
                }

                const data = await response.json();

                if (data.success && Array.isArray(data.availableDates)) {
                    setAvailableDates(data.availableDates); // Store ['YYYY-MM-DD', ...]
                } else {
                    throw new Error(data.message || t('error_fetching_available_dates'));
                }
            } catch (err) {
                console.error("Fetch available dates error:", err);
                setError(err.message || t('error_fetching_available_dates'));
                setAvailableDates([]); // Ensure dates are cleared on error
            } finally {
                setLoadingAvailableDates(false);
            }
        };

        fetchAvailableDates();
    }, [selectedServiceType, navigate, t]);

    // 获取可用时间段
    useEffect(() => {
        if (!selectedServiceType || !selectedDate || !(selectedDate instanceof Date)) {
            setAvailabilityByType([]);
            setSelectedTime('');
            setSelectedProviderByType('');
            return;
        }
        // Format date to string *here* just for the availability check
        const formattedSelectedDate = selectedDate.toISOString().split('T')[0];

        // This check relies on availableDates being populated correctly first
        // availableDates contains strings, so compare with the formatted string
        if (availableDates.length > 0 && !availableDates.includes(formattedSelectedDate)) {
            console.log("Selected date not available, skipping fetchAvailabilityByType");
            setAvailabilityByType([]); // Clear availability if date is invalid
            return;
        }

        const fetchAvailabilityByType = async () => {
            setLoadingAvailabilityByType(true);
            setError('');
            const token = localStorage.getItem('token');
            if (!token) { navigate('/login'); return; }
            try {
                // Use the date part of the selectedDate state (which is a Date object)
                // Format it to string *for the API call only*
                const dateParam = selectedDate.toISOString().split('T')[0];
                const response = await fetch(`${API_URL}/api/providers/availability/by-type?type=${encodeURIComponent(selectedServiceType)}&startDate=${dateParam}`, {
                    headers: { 'Authorization': `Bearer ${token}` },
                });
                if (!response.ok) throw new Error(t('error_fetching_availability')); // New translation
                const data = await response.json();
                if (data.success && Array.isArray(data.availability)) {
                    setAvailabilityByType(data.availability);
                    setSelectedTime(''); // Reset time selection when availability changes
                    setSelectedProviderByType(''); // Reset provider selection too
                } else {
                    throw new Error(data.message || t('error_fetching_availability'));
                }
            } catch (err) {
                console.error("Fetch availability by type error:", err);
                setError(err.message || t('error_fetching_availability'));
                setAvailabilityByType([]);
            } finally {
                setLoadingAvailabilityByType(false);
            }
        };
        fetchAvailabilityByType();
    }, [selectedDate, availableDates, selectedServiceType, navigate, t]);

    // 步骤导航
    const handleNext = () => {
        setError('');
        let canProceed = true;

        if (activeStep === 0) {
            if (!selectedServiceType) {
                setError(t('select_service_type'));
                canProceed = false;
            }
        }

        if (activeStep === 1) {
            if (!selectedDate || !selectedTime) {
                setError(t('select_date_time'));
                canProceed = false;
            }
            if (!selectedProviderByType) {
                setError(t('select_doctor_for_slot'));
                canProceed = false;
            }
        }

        if (canProceed) {
            if (activeStep === steps.length - 1) {
                handleBookingSubmit();
            } else {
                setActiveStep((prevActiveStep) => prevActiveStep + 1);
            }
        }
    };


    const handleBack = () => {
        setError('');
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
    };

    const handleImageChange = (event) => {
        const files = Array.from(event.target.files);
        const maxImages = 3;
        
        if (files.length > maxImages) {
            setError(t('error_max_images', { max: maxImages }));
            return;
        }
        
        // Clean up previous previews
        imagePreviews.forEach(preview => {
            if (preview) {
                URL.revokeObjectURL(preview);
            }
        });
        
        const validFiles = [];
        const previews = [];
        
        files.forEach(file => {
            if (file && file.type.startsWith('image/')) {
                validFiles.push(file);
                previews.push(URL.createObjectURL(file));
            }
        });
        
        if (validFiles.length !== files.length) {
            setError(t('error_invalid_image_files'));
        } else {
            setError(''); // Clear previous errors
        }
        
        setReasonImages(validFiles);
        setImagePreviews(previews);
    };

    const handleRemoveImage = (index) => {
        if (imagePreviews[index]) {
            URL.revokeObjectURL(imagePreviews[index]);
        }
        
        const newImages = reasonImages.filter((_, i) => i !== index);
        const newPreviews = imagePreviews.filter((_, i) => i !== index);
        
        setReasonImages(newImages);
        setImagePreviews(newPreviews);
        
        // Clear the file input value if no images left
        if (newImages.length === 0) {
            const fileInput = document.getElementById('reason-image-input');
            if (fileInput) {
                fileInput.value = '';
            }
        }
    };

    // 提交预约
    const handleBookingSubmit = async () => {
        if (!termsAccepted) {
            setError(t('error_accept_terms'));
            return;
        }
        setBookingLoading(true);
        setError('');

        const token = localStorage.getItem('token');
        if (!token) {
            navigate('/login');
            return;
        }

        // Always use FormData
        const formData = new FormData();

        // Append all necessary fields to FormData
        // Ensure selectedProviderByType (providerNo) is correctly sourced
        // For type mode, provider is selected at step 2 from availabilityByType
        const finalProviderNo = selectedProviderByType; // This should hold the ID from selection
        if (!finalProviderNo) {
            setError(t('error_missing_provider')); // Add translation for missing provider error
            setBookingLoading(false);
            return;
        }
        formData.append('providerNo', finalProviderNo);

        const formattedDate = selectedDate ? formatDateToYMD(selectedDate) : '';
        if (!formattedDate) {
            setError(t('error_missing_date')); // Add translation for missing date error
            setBookingLoading(false);
            return;
        }
        formData.append('appointmentDate', formattedDate);

        if (!selectedTime) {
            setError(t('error_missing_time')); // Add translation for missing time error
            setBookingLoading(false);
            return;
        }
        formData.append('time', selectedTime); // Assuming selectedTime is in the correct string format expected by backend

        if (!bookingReason.trim()) {
            setError(t('error_missing_reason')); // Add translation for missing reason error
            setBookingLoading(false);
            return;
        }
        formData.append('reason', bookingReason);

        if (!loggedInUser || !loggedInUser.demographic_no) {
            setError(t('error_missing_patient_info')); // Add translation for missing patient info error
            setBookingLoading(false);
            return;
        }
        formData.append('patientDemographicNo', loggedInUser.demographic_no.toString());
        
        if (!selectedServiceType) {
            setError(t('error_missing_service_type')); // Add translation for missing service type error
            setBookingLoading(false);
            return;
        }
        formData.append('serviceType', selectedServiceType);
        
        formData.append('location', selectedLocation);

        // Append images if they exist
        if (reasonImages.length > 0) {
            reasonImages.forEach((image) => {
                formData.append('reasonImages', image);
            });
        }

        console.log('[BookingPage] Submitting booking with formData:');
        for (let pair of formData.entries()) {
            console.log(`[BookingPage] FormData: ${pair[0]} = ${pair[1]}`);
        }
        // Add specific logging for date/time related fields as they are constructed
        console.log(`[BookingPage] Explicitly logging time fields before send:`);
        console.log(`[BookingPage] selectedDate (before formatting for formData):`, selectedDate);
        console.log(`[BookingPage] appointmentDate (formatted, for formData): ${formData.get('appointmentDate')}`);
        console.log(`[BookingPage] time (for formData, becomes startTime): ${formData.get('time')}`);
        console.log(`[BookingPage] endTime (for formData, if exists): ${formData.get('endTime')}`);

        let calculatedEndTime = null;
        if (selectedTime && availabilityByType && availabilityByType.length > 0) {
            const currentSlots = availabilityByType;
            const selectedSlotIndex = currentSlots.findIndex(slot => slot.time === selectedTime);

            // Helper to parse "HH:mm" string to minutes since midnight
            const timeToMinutes = (timeStr) => {
                if (!timeStr || !timeStr.includes(':')) return NaN;
                const parts = timeStr.split(':');
                const hours = parseInt(parts[0], 10);
                const minutes = parseInt(parts[1], 10);
                if (isNaN(hours) || isNaN(minutes)) return NaN;
                return hours * 60 + minutes;
            };

            const selectedTimeInMinutes = timeToMinutes(selectedTime);
            let slotDurationInMinutes;

            // Check if this is a Pap Test appointment and force 15-minute duration
            if (selectedServiceType && selectedServiceType.toLowerCase().includes('pap test')) {
                slotDurationInMinutes = 15;
                console.log("Pap Test detected - forcing 15 minute duration");
            } else if (selectedSlotIndex !== -1 && !isNaN(selectedTimeInMinutes)) {
                if (selectedSlotIndex < currentSlots.length - 1) {
                    const nextSlotTimeStr = currentSlots[selectedSlotIndex + 1].time;
                    const nextSlotTimeInMinutes = timeToMinutes(nextSlotTimeStr);

                    if (!isNaN(nextSlotTimeInMinutes)) {
                        slotDurationInMinutes = nextSlotTimeInMinutes - selectedTimeInMinutes;
                        if (slotDurationInMinutes <= 0) {
                            console.warn("Calculated slot duration was not positive (or next slot is earlier), falling back to default 15 min.", { selectedTime, nextSlotTimeStr, slotDurationInMinutes });
                            slotDurationInMinutes = 15;
                        }
                    } else {
                        console.warn("Failed to parse next slot time for duration, falling back to default 15 min duration.", { nextSlotTimeStr });
                        slotDurationInMinutes = 15;
                    }
                } else {
                    slotDurationInMinutes = 15; // Default Oscar display duration for the last slot
                }
            } else {
                console.warn("Selected time not found in available slots or invalid, falling back to default 15 min duration.", { selectedTime, currentSlots });
                slotDurationInMinutes = 15;
            }

            const actualIntervalMinutes = Math.max(1, slotDurationInMinutes - 1);
            
            // Create Day.js object from selectedDate and parsed selectedTime components
            const [selectedHour, selectedMinute] = selectedTime.split(':').map(Number);
            const baseDateTime = dayjs(selectedDate).hour(selectedHour).minute(selectedMinute).second(0).millisecond(0);

            if (baseDateTime.isValid()) {
                calculatedEndTime = baseDateTime.add(actualIntervalMinutes, 'minute').format("HH:mm");
                console.log(`Calculated endTime for booking: ${calculatedEndTime} based on slot duration: ${slotDurationInMinutes}min (actual interval: ${actualIntervalMinutes}min)`);
            } else {
                console.error("BaseDateTime for endTime calculation is invalid. SelectedDate or selectedTime might be problematic.", {selectedDate, selectedTime});
                calculatedEndTime = "Invalid Date"; // Explicitly set to this to see if this path is hit
            }
        } else {
            console.warn("AvailabilityByType not available or selectedTime missing for endTime calculation. Backend will use default.");
            calculatedEndTime = "Invalid Date"; // To indicate frontend calculation failed
        }

        if (calculatedEndTime) {
            formData.append('endTime', calculatedEndTime);
        }

        try {
            const response = await fetch(`${API_URL}/api/appointments/book`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    // 'Content-Type' is NOT set here for FormData; browser sets it with boundary
                },
                body: formData, // Always send FormData
            });

            const responseData = await response.json();

            if (!response.ok) {
                throw new Error(responseData.message || t('error_booking_failed'));
            }

            if (responseData.success) {
                setBookingSuccess(true);
                setActiveStep(steps.length); // Move to a "Completed" step
                // Clear the image state on successful booking
                imagePreviews.forEach(preview => {
                    if (preview) {
                        URL.revokeObjectURL(preview);
                    }
                });
                setReasonImages([]);
                setImagePreviews([]);
            } else {
                setError(responseData.message || t('error_booking_failed'));
            }
        } catch (err) {
            console.error("Booking submission error:", err);
            setError(err.message || t('error_booking_failed_try_again'));
        } finally {
            setBookingLoading(false);
        }
    };

    // --- Calculate minimum date --- (No change needed)
    const getMinDate = () => {
        const minDate = new Date();
        minDate.setDate(minDate.getDate() + 3); // Require booking at least 3 days from today
        minDate.setHours(0, 0, 0, 0); // Start of day
        return minDate;
    };

    // Helper function to format Date object to 'YYYY-MM-DD' string (No change needed)
    const formatDateToYMD = (date) => {
        if (!date || !(date instanceof Date)) return '';
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    // 判断日期是否应该被禁用
    const shouldDisableDate = (date) => {
        const minDate = getMinDate();

        if (date < minDate) {
            return true;
        }

        const dateStr = formatDateToYMD(date);

        if (!loadingAvailableDates) {
            if (availableDates.length >= 0) {
                const isAvailable = availableDates.includes(dateStr);
                return !isAvailable;
            }
        }

        return false;
    };

    // 渲染步骤内容
    const getStepContent = (step) => {
        switch (step) {
            case 0: // 选择服务类型
                return (
                    <Box sx={{ mt: 2 }}>
                        <Typography variant="body1" sx={{ mb: 3, color: 'text.secondary' }}>
                            {t('service_type_description') || '请选择您需要的服务类型。不同的服务类型可能由不同的医生提供，选择后您可以查看可预约的时间。'}
                        </Typography>

                        {loadingServiceTypes ? (
                            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                                <CircularProgress />
                            </Box>
                        ) : (
                            <Grid container spacing={2}>
                                {serviceTypes.map((type) => {
                                    // 获取当前服务类型的翻译
                                    const serviceInfo = t('service_types') && t('service_types')[type];
                                    const serviceTitle = serviceInfo ?
                                        (currentLang === 'en' ? serviceInfo.en : serviceInfo.zh) :
                                        type;
                                    const serviceDesc = serviceInfo ?
                                        (currentLang === 'en' ? serviceInfo.description.en : serviceInfo.description.zh) :
                                        (serviceDescriptions[type] || t('service_no_description'));

                                    return (
                                        <Grid item xs={12} sm={6} md={4} key={type}>
                                            <Paper
                                                elevation={selectedServiceType === type ? 6 : 1}
                                                onClick={() => setSelectedServiceType(type)}
                                                sx={{
                                                    p: 3,
                                                    cursor: 'pointer',
                                                    transition: 'all 0.25s ease',
                                                    borderLeft: selectedServiceType === type ? '4px solid' : '1px solid',
                                                    borderColor: selectedServiceType === type ? 'primary.main' : 'divider',
                                                    bgcolor: selectedServiceType === type ? 'action.selected' : 'background.paper',
                                                    '&:hover': {
                                                        borderLeftColor: 'primary.main',
                                                        transform: 'translateY(-4px)',
                                                        boxShadow: 4
                                                    },
                                                    display: 'flex',
                                                    flexDirection: 'column',
                                                    height: '100%',
                                                    borderRadius: '12px'
                                                }}
                                            >
                                                <Box sx={{
                                                    mb: 1.5,
                                                    display: 'flex',
                                                    justifyContent: 'space-between',
                                                    alignItems: 'flex-start'
                                                }}>
                                                    <Typography
                                                        variant="h6"
                                                        component="h3"
                                                        gutterBottom
                                                        color={selectedServiceType === type ? 'primary.main' : 'text.primary'}
                                                        sx={{
                                                            fontWeight: 600,
                                                            lineHeight: 1.2,
                                                            mb: 0
                                                        }}
                                                    >
                                                        {serviceTitle}
                                                    </Typography>

                                                    {selectedServiceType === type && (
                                                        <Box
                                                            sx={{
                                                                width: 24,
                                                                height: 24,
                                                                borderRadius: '50%',
                                                                bgcolor: 'primary.main',
                                                                display: 'flex',
                                                                alignItems: 'center',
                                                                justifyContent: 'center',
                                                                color: 'white',
                                                                fontSize: 14
                                                            }}
                                                        >
                                                            <CheckIcon fontSize="small" />
                                                        </Box>
                                                    )}
                                                </Box>

                                                <Divider sx={{ mb: 2 }} />

                                                <Typography
                                                    variant="body2"
                                                    color="text.secondary"
                                                    sx={{
                                                        flex: 1,
                                                        lineHeight: 1.6
                                                    }}
                                                >
                                                    {serviceDesc}
                                                </Typography>
                                            </Paper>
                                        </Grid>
                                    );
                                })}
                            </Grid>
                        )}
                    </Box>
                );

            case 1: // 选择日期时间和医生
                return (
                    <Grid container spacing={4} sx={{ mt: 2 }}>
                        {/* Left Side: Date Picker */}
                        <Grid item xs={12} md={4}>
                            <Typography variant="h6" gutterBottom>
                                {t('select_visit_date')}
                            </Typography>
                            <Paper elevation={1} sx={{ p: 2, mb: 2, borderRadius: '8px' }}>
                                <Typography variant="subtitle1" fontWeight="500" sx={{ mb: 2 }}>
                                    {t('select_date')}
                                </Typography>
                                <LocalizationProvider dateAdapter={AdapterDateFns}>
                                    <DatePicker
                                        label={t('appointment_date')}
                                        value={selectedDate}
                                        onChange={(newValue) => setSelectedDate(newValue)}
                                        shouldDisableDate={shouldDisableDate}
                                        slotProps={{ textField: { fullWidth: true, sx: { mb: 1 } } }}
                                        disablePast
                                        minDate={getMinDate()}
                                    />
                                </LocalizationProvider>
                                <Typography variant="caption" color="text.secondary">
                                    {t('date_notice')}
                                </Typography>
                            </Paper>
                            {selectedDate && !loadingAvailabilityByType && (
                                <Box sx={{ p: 2, backgroundColor: 'info.lighter', borderRadius: '8px' }}>
                                    <Typography variant="body2"><strong>{t('selected_doctor')}:</strong> {filteredAvailabilityByType.find(s => s.provider_no === selectedProviderByType)?.providerName || t('not_selected')}</Typography>
                                    <Typography variant="body2"><strong>{t('selected_date')}:</strong> {formatDateToYMD(selectedDate)}</Typography>
                                    {selectedTime && <Typography variant="body2"><strong>{t('selected_time')}:</strong> {selectedTime}</Typography>}
                                </Box>
                            )}
                        </Grid>

                        {/* Right Side: Availability List (Type Mode) */}
                        <Grid item xs={12} md={8}>
                            <Typography variant="h6" gutterBottom>
                                {t('available_slots')}
                            </Typography>
                            {loadingAvailabilityByType && (
                                <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}><CircularProgress /></Box>
                            )}
                            {!loadingAvailabilityByType && filteredAvailabilityByType.length === 0 && (
                                <Box sx={{ py: 3, display: 'flex', flexDirection: 'column', alignItems: 'center', color: 'text.secondary' }}>
                                    <AccessTimeIcon sx={{ fontSize: 40, mb: 2, opacity: 0.6 }} />
                                    <Typography variant="body1" align="center">
                                        {error ? error : (selectedDate ? (availabilityByType.length > 0 ? '所选医生需要会员权限访问' : t('no_slots_available')) : t('select_date_first'))}
                                    </Typography>
                                </Box>
                            )}
                            {!loadingAvailabilityByType && filteredAvailabilityByType.length > 0 && (
                                <Box sx={{ maxHeight: 350, overflow: 'auto' }}>
                                    <Grid container spacing={1}>
                                        {/* Availability is now filtered by date already in the effect */}
                                        {filteredAvailabilityByType.map((slot) => (
                                            <Grid item xs={12} sm={6} md={4} key={slot.time + slot.provider_no}>
                                                <Button
                                                    variant={selectedTime === slot.time && selectedProviderByType === slot.provider_no ? 'contained' : 'outlined'}
                                                    size="medium"
                                                    fullWidth
                                                    onClick={() => {
                                                        setSelectedTime(slot.time);
                                                        setSelectedProviderByType(slot.provider_no);
                                                    }}
                                                    sx={{ borderRadius: '8px', py: 1.5, textTransform: 'none', display: 'flex', flexDirection: 'column', alignItems: 'center', lineHeight: 1.4 }}
                                                >
                                                    <Typography variant="body1" fontWeight="500">{slot.time}</Typography>
                                                    {slot.providerName && (
                                                        <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>{slot.providerName}</Typography>
                                                    )}
                                                </Button>
                                            </Grid>
                                        ))}
                                    </Grid>
                                </Box>
                            )}
                        </Grid>
                    </Grid>
                );
            // }
            // break; 

            case 2: // 确认预约
                const providerInfo = finalSelectedProviderInfo;
                return (
                    <Box sx={{ mt: 2 }}>
                        <Paper elevation={1} sx={{ p: 3, mb: 3, borderRadius: '8px', border: '1px solid', borderColor: 'divider' }}>
                            <Typography variant="h6" gutterBottom sx={{ pb: 1, mb: 2, borderBottom: '1px solid', borderColor: 'divider', display: 'flex', alignItems: 'center' }}>
                                <EventAvailableIcon sx={{ mr: 1 }} /> {t('appointment_details')}
                            </Typography>
                            <Grid container spacing={2}>
                                <Grid item xs={12} md={6}>
                                    <Box sx={{ mb: 2 }}>
                                        <Typography variant="subtitle2" color="text.secondary">{t('provider')}</Typography>
                                        <Typography variant="body1" fontWeight="500">{providerInfo?.name || t('not_selected')}</Typography>
                                    </Box>
                                    <Box sx={{ mb: 2 }}>
                                        <Typography variant="subtitle2" color="text.secondary">{t('service_type')}</Typography>
                                        <Typography variant="body1" fontWeight="500">{selectedServiceType || t('not_selected')}</Typography>
                                    </Box>
                                </Grid>
                                <Grid item xs={12} md={6}>
                                    <Box sx={{ mb: 2 }}>
                                        <Typography variant="subtitle2" color="text.secondary">{t('date')}</Typography>
                                        <Typography variant="body1" fontWeight="500">{selectedDate ? formatDateToYMD(selectedDate) : ''}</Typography>
                                    </Box>
                                    <Box sx={{ mb: 2 }}>
                                        <Typography variant="subtitle2" color="text.secondary">{t('time')}</Typography>
                                        <Typography variant="body1" fontWeight="500">{selectedTime || t('not_selected')}</Typography>
                                    </Box>
                                </Grid>
                            </Grid>

                            {/* 添加预约地点选择 */}
                            <Box sx={{ mt: 2, mb: 3 }}>
                                <Typography variant="subtitle1" gutterBottom>{t('location')}</Typography>
                                <FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
                                    <InputLabel>{t('select_location')}</InputLabel>
                                    <Select
                                        value={selectedLocation}
                                        onChange={(e) => setSelectedLocation(e.target.value)}
                                        label={t('select_location')}
                                        disabled={requiresInPerson}
                                    >
                                        {locationOptions.map((option) => (
                                            <MenuItem key={option.value} value={option.value}>
                                                {getLocationDisplay(option.value, t)}
                                            </MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>
                            </Box>

                            {/* Booking Notes/Reason for Visit */}
                            <Typography variant="h6" gutterBottom sx={{ mt: 3, display: 'flex', alignItems: 'center' }}>
                                <NotesIcon sx={{ mr: 1, color: 'primary.main' }} />
                                {t('reason_for_visit')}
                            </Typography>
                            <TextField
                                label={t('reason_for_visit_label')}
                                multiline
                                rows={4}
                                value={bookingReason}
                                onChange={(e) => setBookingReason(e.target.value)}
                                fullWidth
                                variant="outlined"
                                placeholder={t('reason_for_visit_placeholder')}
                                sx={{ mb: 2, borderRadius: '8px' }}
                            />

                            {/* Multiple Images Upload Section */}
                            <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
                                {t('upload_reason_images_optional')} (最多3张)
                            </Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1}}>
                                <Button
                                    variant="outlined"
                                    component="label"
                                    startIcon={<PhotoCamera />}
                                    disabled={reasonImages.length >= 3}
                                >
                                    {reasonImages.length === 0 ? t('select_images') : t('add_more_images')}
                                    <input
                                        id="reason-image-input"
                                        type="file"
                                        hidden
                                        accept="image/*"
                                        multiple
                                        onChange={handleImageChange}
                                    />
                                </Button>
                                {reasonImages.length > 0 && (
                                    <Typography variant="body2" sx={{color: theme.palette.text.secondary}}>
                                        {reasonImages.length} 张图片已选择
                                    </Typography>
                                )}
                            </Box>
                            
                            {imagePreviews.length > 0 && (
                                <Box sx={{ mt: 2, mb: 2 }}>
                                    <Grid container spacing={2}>
                                        {imagePreviews.map((preview, index) => (
                                            <Grid item xs={6} sm={4} key={index}>
                                                <Box sx={{ 
                                                    position: 'relative', 
                                                    border: '1px solid', 
                                                    borderColor: theme.palette.divider, 
                                                    borderRadius: '8px', 
                                                    overflow: 'hidden',
                                                    aspectRatio: '1',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    justifyContent: 'center'
                                                }}>
                                                    <img 
                                                        src={preview} 
                                                        alt={`${t('image_preview_alt')} ${index + 1}`} 
                                                        style={{ 
                                                            width: '100%', 
                                                            height: '100%', 
                                                            objectFit: 'cover',
                                                            borderRadius: '8px' 
                                                        }} 
                                                    />
                                                    <IconButton
                                                        aria-label={`${t('remove_image_aria')} ${index + 1}`}
                                                        onClick={() => handleRemoveImage(index)}
                                                        sx={{
                                                            position: 'absolute',
                                                            top: 4,
                                                            right: 4,
                                                            backgroundColor: 'rgba(0, 0, 0, 0.6)',
                                                            color: theme.palette.common.white,
                                                            '&:hover': {
                                                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                                            }
                                                        }}
                                                        size="small"
                                                    >
                                                        <CloseIcon fontSize="small" />
                                                    </IconButton>
                                                    <Typography 
                                                        variant="caption" 
                                                        sx={{
                                                            position: 'absolute',
                                                            bottom: 4,
                                                            left: 4,
                                                            backgroundColor: 'rgba(0, 0, 0, 0.6)',
                                                            color: 'white',
                                                            padding: '2px 6px',
                                                            borderRadius: '4px',
                                                            fontSize: '0.7rem'
                                                        }}
                                                    >
                                                        {reasonImages[index]?.name?.substring(0, 10)}...
                                                    </Typography>
                                                </Box>
                                            </Grid>
                                        ))}
                                    </Grid>
                                </Box>
                            )}
                            {/* End Image Upload Section */}

                            <Divider sx={{ my: 2 }} />
                            <Alert severity="info" sx={{ mt: 2, mb: 3 }}>
                                <AlertTitle>{t('important_notice')}</AlertTitle>
                                {t('booking_notice')}
                            </Alert>
                            {/* Terms Agreement Section */}
                            <Paper elevation={0} sx={{ p: 2, bgcolor: 'background.default', borderRadius: '8px', border: '1px solid', borderColor: 'divider' }}>
                                <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600 }}>{t('appointment_policy')}</Typography>
                                <Box component="ul" sx={{ pl: 2, mt: 1, mb: 2 }}>
                                    {selectedServiceType && selectedServiceType.toLowerCase().includes('pap test') ? (
                                        // Pap Test specific policy
                                        <>
                                            <Typography component="li" variant="body2" sx={{ mb: 1 }}>{t('pap_arrival_notice')}</Typography>
                                            <Typography component="li" variant="body2" sx={{ mb: 1 }}>{t('pap_documents_notice')}</Typography>
                                            <Typography component="li" variant="body2" sx={{ mb: 1 }}>{t('pap_clothing_notice')}</Typography>
                                            <Typography component="li" variant="body2" sx={{ mb: 1 }}>{t('pap_scope_notice')}</Typography>
                                            <Typography component="li" variant="body2" sx={{ mb: 1 }}>{t('pap_family_doctor_notice')}</Typography>
                                            <Typography component="li" variant="body2">{t('pap_cancellation_notice')}</Typography>
                                        </>
                                    ) : selectedLocation === 'online/phone' ? (
                                        // By phone policy
                                        <>
                                            <Typography component="li" variant="body2" sx={{ mb: 1 }}>{t('phone_arrival_notice')}</Typography>
                                            <Typography component="li" variant="body2" sx={{ mb: 1 }}>{t('phone_documents_notice')}</Typography>
                                            <Typography component="li" variant="body2" sx={{ mb: 1 }}>{t('phone_cancellation_notice')}</Typography>
                                            <Typography component="li" variant="body2">{t('phone_emergency_notice')}</Typography>
                                        </>
                                    ) : (
                                        // In person policy
                                        <>
                                            <Typography component="li" variant="body2" sx={{ mb: 1 }}>{t('arrival_notice')}</Typography>
                                            <Typography component="li" variant="body2" sx={{ mb: 1 }}>{t('documents_notice')}</Typography>
                                            <Typography component="li" variant="body2" sx={{ mb: 1 }}>{t('cancellation_notice')}</Typography>
                                            <Typography component="li" variant="body2">{t('emergency_notice')}</Typography>
                                        </>
                                    )}
                                </Box>
                                <FormControlLabel control={<Checkbox required onChange={(e) => setTermsAccepted(e.target.checked)} checked={termsAccepted} />} label={<><span style={{ color: 'red', marginRight: '4px' }}>*</span><Typography variant="body2" component="span">{t('terms_agreement')}</Typography></>} sx={{ mt: 1 }} />
                            </Paper>
                        </Paper>
                    </Box>
                );
            // }
            // break;

            // --- CASE 3 (Provider Mode Confirm) --- REMOVED
            // case 3:
            //    ...
            //    break;

            default:
                return 'Unknown step';
        }
    };

    // --- Add console logging for debugging --- REMOVED provider-specific log
    // console.log(`[BookingPage Render] activeStep: ${activeStep}, bookingMode: ${bookingMode}, selectedServiceByDoctor: '${selectedServiceByDoctor}'`);
    console.log(`[BookingPage Render] activeStep: ${activeStep}`);
    // -----------------------------------------

    return (
        <Container component="main" maxWidth="lg">
            <StyledPaper elevation={3}>
                <Box sx={{ mb: 4, textAlign: 'center' }}>
                    <Typography
                        component="h1"
                        variant={isMobile ? "h5" : "h4"}
                        align="center"
                        gutterBottom
                        sx={{
                            fontWeight: 700,
                            color: 'primary.main',
                            mb: 2,
                            position: 'relative',
                            display: 'inline-block',
                            '&::after': {
                                content: '""',
                                position: 'absolute',
                                bottom: -8,
                                left: '50%',
                                transform: 'translateX(-50%)',
                                width: '80px',
                                height: '3px',
                                backgroundColor: 'primary.main',
                                borderRadius: '2px'
                            }
                        }}
                    >
                        {t('book_appointment')}
                    </Typography>
                    <Typography
                        variant="body1"
                        color="text.secondary"
                        sx={{
                            maxWidth: '700px',
                            mx: 'auto',
                            lineHeight: 1.6
                        }}
                    >
                        {t('booking_intro')}
                    </Typography>
                </Box>

                {/* --- 预约模式选择 --- REMOVED */}
                {/* <Box sx={{ ... }}>
                    <RadioGroup ... >
                        <FormControlLabel value="type" ... />
                        <FormControlLabel value="provider" ... />
                    </RadioGroup>
                </Box> */}

                {/* 步骤指示器 */}
                {steps && (
                    <Stepper
                        activeStep={activeStep}
                        sx={{
                            pt: isMobile ? 1 : 3,
                            pb: isMobile ? 2 : 4,
                            px: isMobile ? 0 : 4
                        }}
                        alternativeLabel={true}
                    >
                        {steps.map((label, index) => (
                            <Step key={label}>
                                <StepLabel
                                    StepIconProps={{
                                        sx: {
                                            '&.MuiStepIcon-root': {
                                                color: activeStep >= index ? 'primary.main' : 'grey.400',
                                                fontSize: 28,
                                            },
                                            '& .MuiStepIcon-text': {
                                                fill: '#fff',
                                                fontWeight: 600,
                                                fontSize: '0.85rem'
                                            }
                                        }
                                    }}
                                >
                                    <Typography
                                        variant="body2"
                                        sx={{
                                            fontWeight: activeStep >= index ? 600 : 400,
                                            color: activeStep >= index ? 'primary.main' : 'text.secondary',
                                            mt: 0.5
                                        }}
                                    >
                                        {label}
                                    </Typography>
                                </StepLabel>
                            </Step>
                        ))}
                    </Stepper>
                )}

                {bookingSuccess ? (
                    <Box sx={{ my: isMobile ? 2 : 4, textAlign: 'center' }}>
                        <Box sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            mb: 3
                        }}>
                            <Box sx={{
                                width: 80,
                                height: 80,
                                borderRadius: '50%',
                                bgcolor: 'success.light',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                mb: 2
                            }}>
                                <CheckIcon sx={{ fontSize: 40, color: '#fff' }} />
                            </Box>
                            <Typography variant="h5" gutterBottom>
                                {t('booking_success_title')}
                            </Typography>
                            <Typography variant="body1" color="text.secondary" sx={{ maxWidth: '600px', mb: 3 }}>
                                {t('booking_success_message')}
                            </Typography>
                        </Box>

                        <Paper sx={{ p: 3, mb: 4, maxWidth: '600px', mx: 'auto', borderRadius: '12px' }}>
                            <Typography variant="h6" gutterBottom sx={{ borderBottom: '1px solid', borderColor: 'divider', pb: 1, mb: 2 }}>
                                {t('appointment_details')}
                            </Typography>
                            <Grid container spacing={2} sx={{ mb: 1 }}>
                                {/* {bookingMode === 'type' && ( // No longer needed */}
                                <Grid item xs={12} sm={6}>
                                    <Typography variant="subtitle2" color="text.secondary">
                                        {t('service_type')}
                                    </Typography>
                                    <Typography variant="body1" sx={{ mb: 2 }}>
                                        {selectedServiceType}
                                    </Typography>
                                </Grid>
                                {/* )} */}
                                <Grid item xs={12} sm={6}>
                                    <Typography variant="subtitle2" color="text.secondary">
                                        {t('provider')}
                                    </Typography>
                                    <Typography variant="body1" sx={{ mb: 2 }}>
                                        {finalSelectedProviderInfo?.name || ''}
                                    </Typography>
                                </Grid>
                                <Grid item xs={12} sm={6}>
                                    <Typography variant="subtitle2" color="text.secondary">
                                        {t('date')}
                                    </Typography>
                                    <Typography variant="body1" sx={{ mb: 2 }}>
                                        {selectedDate ? formatDateToYMD(selectedDate) : ''}
                                    </Typography>
                                </Grid>
                                <Grid item xs={12} sm={6}>
                                    <Typography variant="subtitle2" color="text.secondary">
                                        {t('time')}
                                    </Typography>
                                    <Typography variant="body1" sx={{ mb: 2 }}>
                                        {selectedTime}
                                    </Typography>
                                </Grid>
                                <Grid item xs={12} sm={6}>
                                    <Typography variant="subtitle2" color="text.secondary">
                                        {t('location')}
                                    </Typography>
                                    <Typography variant="body1" sx={{ mb: 2 }}>
                                        {getLocationDisplay(selectedLocation, t)}
                                    </Typography>
                                </Grid>
                                {bookingReason && (
                                    <Grid item xs={12}>
                                        <Typography variant="subtitle2" color="text.secondary">
                                            {t('reason')}
                                        </Typography>
                                        <Typography variant="body1" sx={{ mb: 2 }}>
                                            {bookingReason}
                                        </Typography>
                                    </Grid>
                                )}
                            </Grid>
                        </Paper>

                        <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}>
                            <Button variant="outlined" onClick={() => navigate('/booking')} sx={{ borderRadius: '50px', px: 3 }}>
                                {t('new_appointment')}
                            </Button>
                            <Button variant="contained" onClick={() => navigate('/appointments')} sx={{ borderRadius: '50px', px: 3 }}>
                                {t('view_appointments')}
                            </Button>
                        </Box>
                    </Box>
                ) : (
                    <>
                        {error && <Alert severity="error" sx={{ mb: 3 }}>{error}</Alert>}
                        {/* Ensure steps is defined before calling getStepContent */}
                        <Box sx={{
                            p: isMobile ? 1 : 3,
                            backgroundColor: theme.palette.background.default,
                            borderRadius: '12px',
                            mb: 3
                        }}>
                            {steps && getStepContent(activeStep)}
                        </Box>

                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
                            <Button
                                disabled={activeStep === 0 || bookingLoading}
                                onClick={handleBack}
                                startIcon={<ArrowBackIcon />}
                                sx={{
                                    borderRadius: '50px',
                                    px: 3
                                }}
                            >
                                {t('previous')}
                            </Button>
                            <Button
                                variant="contained"
                                onClick={handleNext}
                                endIcon={steps && activeStep === steps.length - 1 ? null : <ArrowForwardIcon />}
                                disabled={(() => { // Add IIFE for logging - Simplified
                                    const step0Invalid = activeStep === 0 && !selectedServiceType;
                                    // const step1ProviderInvalid = bookingMode === 'provider' && activeStep === 1 && !selectedServiceByDoctor; // REMOVED
                                    const step1TypeInvalid = activeStep === 1 && (!selectedTime || !selectedProviderByType);
                                    // const step2ProviderInvalid = bookingMode === 'provider' && activeStep === 2 && !selectedTime; // REMOVED
                                    // const isDisabled = bookingLoading || step0Invalid || step1ProviderInvalid || step1TypeInvalid || step2ProviderInvalid; // Simplified
                                    const isDisabled = bookingLoading || step0Invalid || step1TypeInvalid;
                                    console.log(`[Button Disabled Check] bookingLoading: ${bookingLoading}, step0Invalid: ${step0Invalid}, step1TypeInvalid: ${step1TypeInvalid} => isDisabled: ${isDisabled}`);
                                    return isDisabled;
                                })()}
                                sx={{
                                    borderRadius: '50px',
                                    px: 3
                                }}
                            >
                                {/* Ensure steps is defined before checking length */}
                                {steps && activeStep === steps.length - 1 ? (
                                    bookingLoading ? <CircularProgress size={24} color="inherit" /> : t('confirm_booking')
                                ) : t('next')}
                            </Button>
                        </Box>
                    </>
                )}
            </StyledPaper>
        </Container>
    );
};

export default BookingPage; 