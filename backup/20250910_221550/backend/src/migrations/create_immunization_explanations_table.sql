-- Create immunization explanations table
CREATE TABLE IF NOT EXISTS immunization_explanations (
    id INT(11) NOT NULL AUTO_INCREMENT,
    immunization_id INT(11) NOT NULL,
    explanation TEXT NOT NULL,
    language VARCHAR(10) NOT NULL DEFAULT 'zh',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY unique_immunization_language (immunization_id, language)
) ENGINE=Aria DEFAULT CHARSET=utf8mb4; 