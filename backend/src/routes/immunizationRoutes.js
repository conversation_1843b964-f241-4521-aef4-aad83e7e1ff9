const express = require('express');
const router = express.Router();
const immunizationController = require('../controllers/immunizationController');
const authenticate = require('../middleware/authenticate');
const { requireMember } = require('../middleware/userTypeAuth');

/**
 * @route   GET /api/immunizations/:demographicNo
 * @desc    Get immunizations for a specific patient (self or family) - Member only
 * @access  Private (Member)
 */
router.get('/:demographicNo', authenticate, requireMember, immunizationController.getPatientImmunizations);

/**
 * @route   GET /api/immunizations/explanation/:immunizationId
 * @desc    Get AI-generated explanation for a specific immunization - Member only
 * @access  Private (Member)
 */
router.get('/explanation/:immunizationId', authenticate, requireMember, immunizationController.getImmunizationExplanation);

module.exports = router; 