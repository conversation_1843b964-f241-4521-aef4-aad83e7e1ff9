console.log('>>> Loading backend/src/utils/errors.js...'); // <<< DEBUG LOG

class ValidationError extends Error {
    constructor(message) {
        super(message);
        this.name = 'ValidationError';
    }
}

class AuthorizationError extends Error {
    constructor(message) {
        super(message);
        this.name = 'AuthorizationError';
    }
}

module.exports = {
    ValidationError,
    AuthorizationError
}; 