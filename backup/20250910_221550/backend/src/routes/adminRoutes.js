const express = require('express');
const router = express.Router();
const adminController = require('../controllers/adminController');
const authMiddleware = require('../middleware/authMiddleware');
const roleMiddleware = require('../middleware/roleMiddleware');

/**
 * @swagger
 * tags:
 *   name: Admin
 *   description: 管理员API
 */

/**
 * @swagger
 * /api/admin/ai-metrics:
 *   get:
 *     summary: 获取AI服务指标
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功获取AI指标
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalCalls:
 *                       type: integer
 *                       description: 总调用次数
 *                     successfulCalls:
 *                       type: integer
 *                       description: 成功调用次数
 *                     failedCalls:
 *                       type: integer
 *                       description: 失败调用次数
 *                     cacheHits:
 *                       type: integer
 *                       description: 缓存命中次数
 *                     cacheMisses:
 *                       type: integer
 *                       description: 缓存未命中次数
 *                     providers:
 *                       type: array
 *                       description: 提供商状态列表
 *                     providerUsage:
 *                       type: object
 *                       description: 按提供商的使用统计
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 *       500:
 *         description: 服务器错误
 */
router.get('/ai-metrics', authMiddleware, roleMiddleware('admin'), adminController.getAIMetrics);

// 临时测试端点 - 无认证
router.get('/ai-usage-stats-test', adminController.getAIUsageStats);
router.get('/ai-dashboard-test', adminController.getAIDashboard);

/**
 * @swagger
 * /api/admin/ai-metrics/reset:
 *   post:
 *     summary: 重置AI服务指标
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功重置AI指标
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 *       500:
 *         description: 服务器错误
 */
router.post('/ai-metrics/reset', authMiddleware, roleMiddleware('admin'), adminController.resetAIMetrics);

/**
 * @swagger
 * /api/admin/ai-health:
 *   get:
 *     summary: 获取AI服务健康状态
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功获取AI健康状态
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     status:
 *                       type: string
 *                       enum: [healthy, warning, critical]
 *                       description: 健康状态
 *                     message:
 *                       type: string
 *                       description: 状态描述
 *                     metrics:
 *                       type: object
 *                       properties:
 *                         totalCalls:
 *                           type: integer
 *                         successRate:
 *                           type: number
 *                         failureRate:
 *                           type: number
 *                         availableProviders:
 *                           type: integer
 *                         totalProviders:
 *                           type: integer
 *                     issues:
 *                       type: array
 *                       items:
 *                         type: string
 *                       description: 问题列表
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 *       500:
 *         description: 服务器错误
 */
router.get('/ai-health', authMiddleware, roleMiddleware('admin'), adminController.getAIHealthStatus);

/**
 * @swagger
 * /api/admin/system-info:
 *   get:
 *     summary: 获取系统信息
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功获取系统信息
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     nodeVersion:
 *                       type: string
 *                     platform:
 *                       type: string
 *                     architecture:
 *                       type: string
 *                     uptime:
 *                       type: number
 *                     memoryUsage:
 *                       type: object
 *                     environment:
 *                       type: string
 *                     timestamp:
 *                       type: string
 *                       format: date-time
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 *       500:
 *         description: 服务器错误
 */
router.get('/system-info', authMiddleware, roleMiddleware('admin'), adminController.getSystemInfo);

/**
 * @swagger
 * /api/admin/ai-usage-stats:
 *   get:
 *     summary: 获取AI使用统计
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 开始日期 (YYYY-MM-DD)
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 结束日期 (YYYY-MM-DD)
 *       - in: query
 *         name: groupBy
 *         schema:
 *           type: string
 *           enum: [hour, day, week, month]
 *         description: 聚合维度
 *     responses:
 *       200:
 *         description: 成功获取AI使用统计
 */
router.get('/ai-usage-stats', authMiddleware, roleMiddleware('admin'), adminController.getAIUsageStats);

/**
 * @swagger
 * /api/admin/ai-dashboard:
 *   get:
 *     summary: 获取AI监控看板数据
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功获取AI看板数据
 */
router.get('/ai-dashboard', authMiddleware, roleMiddleware('admin'), adminController.getAIDashboard);

/**
 * @swagger
 * /api/admin/ai-stats/cleanup:
 *   post:
 *     summary: 清理AI统计数据
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               retentionDays:
 *                 type: integer
 *                 default: 90
 *                 description: 保留天数
 *     responses:
 *       200:
 *         description: 成功清理数据
 */
router.post('/ai-stats/cleanup', authMiddleware, roleMiddleware('admin'), adminController.cleanupAIStats);

/**
 * @swagger
 * /api/admin/ai-providers/status:
 *   get:
 *     summary: 获取AI提供商状态
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功获取AI提供商状态
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     providers:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           name:
 *                             type: string
 *                           available:
 *                             type: boolean
 *                           errorCount:
 *                             type: integer
 *                           lastError:
 *                             type: string
 *                           model:
 *                             type: string
 *                           priority:
 *                             type: integer
 *                     availableCount:
 *                       type: integer
 *                     totalCount:
 *                       type: integer
 *                     lastUpdated:
 *                       type: string
 *                       format: date-time
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 *       500:
 *         description: 服务器错误
 */
router.get('/ai-providers/status', authMiddleware, roleMiddleware('admin'), adminController.getAIProvidersStatus);

/**
 * @swagger
 * /api/admin/ai-providers/availability:
 *   post:
 *     summary: 设置AI提供商可用性
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - provider
 *               - available
 *             properties:
 *               provider:
 *                 type: string
 *                 description: 提供商名称 (gemini, openrouter)
 *               available:
 *                 type: boolean
 *                 description: 是否可用
 *     responses:
 *       200:
 *         description: 成功设置提供商可用性
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     provider:
 *                       type: string
 *                     available:
 *                       type: boolean
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 *       500:
 *         description: 服务器错误
 */
router.post('/ai-providers/availability', authMiddleware, roleMiddleware('admin'), adminController.setAIProviderAvailability);

/**
 * @swagger
 * /api/admin/clients/search:
 *   get:
 *     summary: 搜索客户档案
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: q
 *         schema:
 *           type: string
 *         description: 搜索关键词 (姓名、电话、邮箱或档案号)
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: 每页数量
 *     responses:
 *       200:
 *         description: 成功获取搜索结果
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       demographic_no:
 *                         type: integer
 *                       first_name:
 *                         type: string
 *                       last_name:
 *                         type: string
 *                       email:
 *                         type: string
 *                       phone:
 *                         type: string
 *                       date_of_birth:
 *                         type: string
 *                       date_joined:
 *                         type: string
 *                       membership:
 *                         type: object
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     total:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 *       500:
 *         description: 服务器错误
 */
/**
 * @swagger
 * /api/admin/clients/{demographicNo}:
 *   get:
 *     summary: 获取客户档案详情
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: demographicNo
 *         required: true
 *         schema:
 *           type: string
 *         description: 客户档案号码
 *     responses:
 *       200:
 *         description: 成功获取客户详情
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 demographicInfo:
 *                   type: object
 *                   properties:
 *                     demographic_no:
 *                       type: integer
 *                     first_name:
 *                       type: string
 *                     last_name:
 *                       type: string
 *                     email:
 *                       type: string
 *                     phone:
 *                       type: string
 *                     address:
 *                       type: string
 *                     city:
 *                       type: string
 *                     province:
 *                       type: string
 *                     postal:
 *                       type: string
 *                     hin:
 *                       type: string
 *                     sex:
 *                       type: string
 *                     date_of_birth:
 *                       type: string
 *                 email:
 *                   type: string
 *                 message:
 *                   type: string
 *       404:
 *         description: 客户档案不存在
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 *       500:
 *         description: 服务器错误
 */
router.get('/clients/search', authMiddleware, roleMiddleware('admin'), adminController.searchClients);

router.get('/clients/:demographicNo', authMiddleware, roleMiddleware('admin'), adminController.getClientDetails);

// Test route
router.get('/test-client/:demographicNo', (req, res) => {
    res.json({
        success: true,
        message: 'Test route works',
        demographicNo: req.params.demographicNo
    });
});

module.exports = router; 