const express = require('express');
const router = express.Router();
const labResultController = require('../controllers/labResultController');
const auth = require('../middleware/auth');
const { requireMember } = require('../middleware/userTypeAuth');
const pool = require('../config/database');

// Member-only routes - 需要有效membership才能访问实验室结果
router.get('/reports', auth, requireMember, labResultController.getPatientLabResults);

// Get lab reports for a specific demographic (family member)
router.get('/reports/demographic/:demographicNo', auth, requireMember, labResultController.getPatientLabResultsByDemographic);

// Get measurements data for the authenticated user
router.get('/measurements', auth, requireMember, labResultController.getPatientMeasurements);

// Get measurements data for a specific demographic (family member)
router.get('/measurements/demographic/:demographicNo', auth, requireMember, labResultController.getPatientMeasurementsByDemographic);

// Get specific report content by report ID
// This needs to be AFTER the demographic routes to avoid route conflicts
router.get('/report/:reportId', auth, requireMember, labResultController.getLabReportContent);

// Get HL7 lab results for the authenticated user
router.get('/hl7', auth, requireMember, labResultController.getMyHL7Results);

// Get HL7 lab results for a specific demographic (family member)
router.get('/hl7/demographic/:demographicNo', auth, requireMember, labResultController.getPatientHL7Results);

// Get specific HL7 message content by lab ID
router.get('/hl7/message/:labId', auth, requireMember, labResultController.getHL7MessageContent);

// Get parsed HL7 message content by lab ID
router.get('/hl7/parsed/:labId', auth, requireMember, labResultController.getParsedHL7Message);
router.post('/hl7/analyze', auth, requireMember, labResultController.analyzeHL7Results);

// 调试端点：查询特定患者的文档信息（无需身份验证）
router.get('/debug/documents/:demographicNo', async (req, res) => {
    try {
        const { demographicNo } = req.params;

        // 查询文档
        const [documents] = await pool.query(`
            SELECT
                d.document_no,
                d.docdesc,
                d.doctype,
                d.contenttype,
                d.docfilename,
                d.updatedatetime,
                d.observationdate
            FROM document d
            JOIN ctl_document c ON d.document_no = c.document_no
            WHERE c.module = 'demographic'
            AND c.module_id = ?
            AND d.observationdate >= '2023-07-01'
            ORDER BY d.updatedatetime DESC
        `, [demographicNo]);

        // 查询特定的文档类型统计
        const [doctypes] = await pool.query(`
            SELECT
                doctype,
                COUNT(*) as count
            FROM document d
            JOIN ctl_document c ON d.document_no = c.document_no
            WHERE c.module = 'demographic'
            AND c.module_id = ?
            AND d.observationdate >= '2023-07-01'
            GROUP BY doctype
            ORDER BY count DESC
        `, [demographicNo]);

        // 查找特定的XRAY文档
        const [xrayDocs] = await pool.query(`
            SELECT
                d.document_no,
                d.docdesc,
                d.doctype,
                d.docfilename
            FROM document d
            JOIN ctl_document c ON d.document_no = c.document_no
            WHERE c.module = 'demographic'
            AND c.module_id = ?
            AND d.observationdate >= '2023-07-01'
            AND (d.docfilename LIKE '%XRAY%' OR d.docfilename LIKE '%FM_%' OR d.docdesc LIKE '%X-Ray%')
        `, [demographicNo]);

        res.json({
            success: true,
            documentCount: documents.length,
            documents: documents.slice(0, 20), // 只返回前20条记录
            doctypes,
            xrayDocs
        });
    } catch (error) {
        console.error('查询文档出错:', error);
        res.status(500).json({
            success: false,
            message: '查询文档出错',
            error: error.message
        });
    }
});

module.exports = router;