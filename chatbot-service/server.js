require('dotenv').config();
const express = require('express');
const fetch = require('node-fetch');
const axios = require('axios');
const cors = require('cors');
const mysql = require('mysql2/promise');

// Import AI usage tracker for recording AI calls
let aiUsageTracker;
try {
    aiUsageTracker = require('./aiUsageTracker');
    console.log('AI usage tracker loaded successfully');
} catch (error) {
    console.warn('AI usage tracker not available:', error.message);
    // Create a mock tracker if not available
    aiUsageTracker = {
        recordUsage: async () => {},
        recordError: async () => {}
    };
}

// Import AI usage types for detailed classification
const aiUsageTypes = require('./aiUsageTypes');

// Import AI Service Manager for intelligent provider switching
let aiServiceManager;
try {
    aiServiceManager = require('./aiServiceManager');
    console.log('Chatbot AI Service Manager loaded successfully');
} catch (error) {
    console.warn('AI Service Manager not available:', error.message);
    aiServiceManager = null;
}

const app = express();
const PORT = process.env.PORT || 3002;
// Remove OpenRouter specific variables
// const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;
// const MODEL_NAME = process.env.MODEL_NAME || 'deepseek/deepseek-chat-v3-0324:free';
// const SITE_URL = process.env.SITE_URL || 'app.mmcwellness.ca';
// const SITE_NAME = process.env.SITE_NAME || 'MMC Wellness Chat';

// Add Gemini specific variables
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
const GEMINI_MODEL = process.env.GEMINI_MODEL || 'gemini-2.0-flash'; // Use a Gemini model suitable for chat

// 创建数据库连接池 - 连接到主服务器数据库
const pool = mysql.createPool({
    host: process.env.MAIN_DB_HOST || '**************',  // 远程服务器 IP
    port: process.env.MAIN_DB_PORT || 3306,
    user: process.env.MAIN_DB_USER || 'root',
    password: process.env.MAIN_DB_PASSWORD || 'Z2Rh6VGr7DE=',  // 使用加密后的密码
    database: process.env.MAIN_DB_NAME || 'oscar',
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
});

// 在启动时创建聊天记录表（如果不存在）
async function setupDatabase() {
    try {
        const connection = await pool.getConnection();
        // Add abstract column to health_articles if not exists
        try {
            await connection.query('ALTER TABLE health_articles ADD COLUMN abstract TEXT');
            console.log("Checked/Added 'abstract' column to health_articles table.");
        } catch (alterError) {
            if (alterError.code !== 'ER_DUP_FIELDNAME') { // Ignore if column already exists
                console.warn("Could not add 'abstract' column to health_articles (might already exist):", alterError.code);
            }
        }

        // 创建聊天会话表
        await connection.query(`
            CREATE TABLE IF NOT EXISTS chat_conversations (
                id INT AUTO_INCREMENT PRIMARY KEY,
                conversation_id VARCHAR(50) NOT NULL,
                user_id INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX (conversation_id),
                INDEX (user_id)
            )
        `);

        // 创建聊天消息表
        await connection.query(`
            CREATE TABLE IF NOT EXISTS chat_messages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                conversation_id VARCHAR(50) NOT NULL,
                role ENUM('user', 'assistant', 'model') NOT NULL,
                content TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX (conversation_id)
            )
        `);

        connection.release();
        console.log('Database tables for chat functionality have been set up');
    } catch (error) {
        console.error('Error setting up database tables:', error);
    }
}

// 启动时初始化数据库
setupDatabase();

// Check for Gemini API Key
if (!GEMINI_API_KEY) {
    console.error('FATAL ERROR: GEMINI_API_KEY environment variable is not set.');
    process.exit(1);
}

app.use(cors());
app.use(express.json());

// 保存消息到数据库的函数
// Note: Gemini uses 'model' role instead of 'assistant'
async function saveMessageToDatabase(conversationId, role, content, userId = null) {
    // Map 'assistant' role from older calls (if any) to 'model' for consistency
    const dbRole = (role === 'assistant' || role === 'model') ? 'model' : 'user';

    try {
        const [existingConversation] = await pool.query(
            'SELECT id FROM chat_conversations WHERE conversation_id = ?',
            [conversationId]
        );

        if (existingConversation.length === 0) {
            await pool.query(
                'INSERT INTO chat_conversations (conversation_id, user_id) VALUES (?, ?)',
                [conversationId, userId]
            );
        }

        await pool.query(
            'INSERT INTO chat_messages (conversation_id, role, content) VALUES (?, ?, ?)',
            [conversationId, dbRole, content] // Use dbRole
        );

        console.log(`Message saved to database: ${dbRole} in conversation ${conversationId}`);
    } catch (error) {
        console.error('Error saving message to database:', error);
    }
}

// 获取用户聊天历史
app.get('/api/chat/history/:userId', async (req, res) => {
    try {
        const userId = req.params.userId;

        // 获取该用户的所有会话
        const [conversations] = await pool.query(
            'SELECT conversation_id, created_at FROM chat_conversations WHERE user_id = ? ORDER BY created_at DESC',
            [userId]
        );

        // 对于每个会话，获取最后一条用户消息作为摘要
        const conversationsWithSummary = await Promise.all(
            conversations.map(async (conv) => {
                const [messages] = await pool.query(
                    'SELECT content FROM chat_messages WHERE conversation_id = ? AND role = "user" ORDER BY created_at DESC LIMIT 1',
                    [conv.conversation_id]
                );

                return {
                    id: conv.conversation_id,
                    date: conv.created_at,
                    summary: messages.length > 0 ? messages[0].content.substring(0, 50) + (messages[0].content.length > 50 ? '...' : '') : 'Empty conversation'
                };
            })
        );

        res.json({ conversations: conversationsWithSummary });
    } catch (error) {
        console.error('Error fetching chat history:', error);
        res.status(500).json({ error: 'Failed to fetch chat history' });
    }
});

// 获取特定会话的所有消息
app.get('/api/chat/conversation/:conversationId', async (req, res) => {
    try {
        const conversationId = req.params.conversationId;

        const [messages] = await pool.query(
            'SELECT role, content, created_at FROM chat_messages WHERE conversation_id = ? ORDER BY created_at',
            [conversationId]
        );

        // Map 'model' back to 'assistant' for frontend if necessary, or keep as is
        const frontendMessages = messages.map(m => ({ ...m, role: m.role === 'model' ? 'assistant' : m.role }));
        res.json({ messages: frontendMessages });
    } catch (error) {
        console.error('Error fetching conversation messages:', error);
        res.status(500).json({ error: 'Failed to fetch conversation messages' });
    }
});

app.post('/api/chat', async (req, res) => {
    // Extract token and viewingDemographicNo, potentially passed from frontend
    const { messages, userId, conversationId = Date.now().toString(), token, viewingDemographicNo } = req.body;

    // Analyze user message content for intelligent service type classification
    let detailedServiceType = 'chatbot_general';
    let userContent = '';
    
    if (messages && messages.length > 0) {
        // Get the latest user message content
        const userMessages = messages.filter(msg => msg.role === 'user');
        if (userMessages.length > 0) {
            userContent = userMessages[userMessages.length - 1].content || '';
            detailedServiceType = aiUsageTypes.inferServiceType(userContent, {
                isAutomated: false,
                hasContext: messages.length > 1
            });
        }
    }

    // Validate required fields
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
        return res.status(400).json({ error: 'Invalid messages format' });
    }
    if (!token) {
        console.warn('[Chatbot API] Token not provided in request body. Cannot fetch summary.');
        // Proceed without summary if no token
    }
    if (!viewingDemographicNo) {
        console.warn('[Chatbot API] viewingDemographicNo not provided in request body. Cannot fetch summary.');
        // Proceed without summary
    }

    // Save user message to database
    const lastUserMessage = messages[messages.length - 1];
    if (lastUserMessage.role === 'user') {
        await saveMessageToDatabase(conversationId, 'user', lastUserMessage.content, userId); // userId might be different from viewingDemographicNo
    }

    // --- Fetch Patient Summary (New Logic) --- 
    let patientSummary = null;
    if (token && viewingDemographicNo) {
        try {
            const backendApiUrl = process.env.BACKEND_API_URL || 'http://mmcwebapp-backend-1:3001'; // Use internal service name
            const summaryUrl = `${backendApiUrl}/api/appointments/${viewingDemographicNo}/summary-for-chatbot`;

            console.log(`[Chatbot API] Fetching summary from: ${summaryUrl}`);

            const summaryResponse = await axios.get(summaryUrl, {
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                timeout: 5000 // Add a timeout (5 seconds)
            });

            if (summaryResponse.data.success && summaryResponse.data.summary) {
                patientSummary = summaryResponse.data.summary;
                console.log(`[Chatbot API] Successfully fetched patient summary for demographic ${viewingDemographicNo}.`);
            } else {
                console.log(`[Chatbot API] No summary available or failed fetch for demographic ${viewingDemographicNo}. Message: ${summaryResponse.data.message}`);
            }
        } catch (summaryError) {
            console.error(`[Chatbot API] Error fetching patient summary for demographic ${viewingDemographicNo}:`,
                summaryError.response ? summaryError.response.data : summaryError.message
            );
            // Don't fail the chat if summary fetch fails, just proceed without it
        }
    }
    // --- End Fetch Patient Summary --- 

    // Define the base system prompt
    const baseSystemText = `You are a specialized AI assistant for MMC Wellness (mmcwellness.ca). You provide helpful information to users about health-related topics, with a focus on guiding patients to appropriate care at MMC Wellness.

**Allowed Topics & Actions:**

1. **General Health Information:**
   * Provide general information about common health conditions and symptoms.
   * Offer general wellness advice, preventative care tips, and seasonal health reminders.
   * Explain basic medical terminology and procedures.

2. **Common Diseases Information:**
   * Provide general, educational information about common diseases and conditions.
   * Explain symptoms, risk factors, and general management approaches for common conditions.
   * **IMPORTANT:** Always clarify that this information is educational only, not diagnostic or a substitute for professional medical advice.

3. **Seasonal Health Reminders:**
   * Provide information about seasonal health concerns (flu season, allergy season, heat-related illnesses in summer, etc.).
   * Share preventative measures for seasonal health issues.
   * Remind about seasonal vaccinations and health precautions.

4. **MMC Wellness Information:**
   * Answer questions about MMC Wellness services, programs, location, and contact information based on the following:
     * "MMC Wellness Group is a comprehensive medical institution in Richmond, BC, offering Family Medicine, a Functional Wellness Program (including Weight Loss with Semaglutide, HRT, TRT, Peptide Therapy, NAD+ IV Therapy), Medical Exams (like Genetic Testing for cancer risk, Private Imaging - CT/MRI), and official Canadian Immigration Medical Exams. They emphasize personalized care and have multilingual staff (English, Mandarin, Cantonese). Their location is #130-8780 Blundell Road, Richmond, BC, phone ************, open Mon-Fri 9 AM - 5 PM."
   * Explain services offered at MMC Wellness that may help with the user's concerns.

5. **Appointment Guidance:**
   * Encourage users to seek professional medical advice for specific health concerns.
   * Guide users on how to book appointments at MMC Wellness.

**Response Guidelines:**

* Always include a clear disclaimer that you're providing general information, not medical advice.
* For any specific medical concerns, recommend consulting a physician at MMC Wellness.
* When discussing health conditions, be informative but never diagnostic.
* Encourage appropriate medical care for symptoms that require professional attention.
* Always maintain a professional, helpful, and compassionate tone.
* For any emergency symptoms, advise the user to seek immediate medical attention.

**Safety Measures:**

* Refuse to diagnose specific conditions for individual users.
* Do not recommend specific treatment plans, medications or dosages.
* For complex medical questions, suggest consulting with a doctor at MMC Wellness.

MMC Wellness values patient education and preventative care. Your role is to provide helpful general information while guiding users to appropriate professional care when needed.`;

    // Inject summary if available
    let finalSystemText = baseSystemText;
    if (patientSummary) {
        finalSystemText = `You are a specialized AI assistant for MMC Wellness (mmcwellness.ca). Below is a summary of the patient's recent health history. Use this information to provide more relevant initial advice, but always remind the user to consult a real doctor for diagnosis and treatment. Do not mention the summary directly unless relevant to the user's query.

**Patient Health Summary:**
${patientSummary}
---

**CRITICAL MEDICAL INFORMATION GUIDELINES:**
1. NEVER invent or fabricate any medical information not included in the patient summary above
2. DO NOT create fictional diagnoses, treatments, or test results 
3. DO NOT reference specific dates, appointments, or treatments not mentioned in the summary
4. If asked about something not in the summary, say "I don't have that information" rather than making it up
5. NEVER provide future medical predictions or assume information not explicitly stated

**Your Role & Guidelines:**
${baseSystemText}`;
        console.log("[Chatbot API] Injected patient summary into system prompt with strict non-fabrication guidelines.");
    } else {
        console.log("[Chatbot API] Proceeding without patient summary in system prompt.");
    }

    const systemInstruction = {
        parts: [{ text: finalSystemText }]
    };

    // Format messages for Gemini API (user/model roles)
    const geminiMessages = messages.map(msg => ({
        role: msg.role === 'assistant' ? 'model' : 'user',
        parts: [{ text: msg.content }]
    }));

    const startTime = Date.now();
    
    try {
        // Use AI Service Manager for intelligent provider switching if available
        if (aiServiceManager) {
            console.log('Using Chatbot AI Service Manager for intelligent provider switching');
            
            // Convert system instruction and messages for service manager
            const convertedMessages = [];
            
            // Add system message if exists
            if (systemInstruction && systemInstruction.parts && systemInstruction.parts[0].text) {
                convertedMessages.push({
                    role: 'system',
                    content: systemInstruction.parts[0].text
                });
            }
            
            // Convert Gemini format messages to standard format
            geminiMessages.forEach(msg => {
                if (msg.role === 'user' && msg.parts && msg.parts[0].text) {
                    convertedMessages.push({
                        role: 'user',
                        content: msg.parts[0].text
                    });
                } else if (msg.role === 'model' && msg.parts && msg.parts[0].text) {
                    convertedMessages.push({
                        role: 'assistant',
                        content: msg.parts[0].text
                    });
                }
            });
            
            console.log(`Request messages converted for service manager: ${convertedMessages.length} messages`);
            
            try {
                // Use smart call for automatic provider switching
                const result = await aiServiceManager.smartCall(convertedMessages, {
                    stream: false, // For now, disable streaming with service manager
                    maxTokens: 4000,
                    temperature: 0.7,
                    serviceType: detailedServiceType // 传递正确的服务类型
                });
                
                const responseTime = Date.now() - startTime;
                
                if (result && result.content) {
                    // Save the response
                    await saveMessageToDatabase(conversationId, 'model', result.content, userId);
                    console.log('AI Service Manager response saved to database');
                    
                    // Send non-streaming response (simulate streaming for frontend compatibility)
                    res.setHeader('Content-Type', 'text/event-stream');
                    res.setHeader('Cache-Control', 'no-cache');
                    res.setHeader('Connection', 'keep-alive');
                    res.flushHeaders();
                    
                    // Split response into chunks for streaming effect
                    const words = result.content.split(' ');
                    const chunkSize = 3; // Send 3 words at a time
                    
                    for (let i = 0; i < words.length; i += chunkSize) {
                        const chunk = words.slice(i, i + chunkSize).join(' ') + (i + chunkSize < words.length ? ' ' : '');
                        const sseChunk = {
                            id: `chatcmpl-${Date.now()}`,
                            object: "chat.completion.chunk",
                            created: Math.floor(Date.now() / 1000),
                            model: result.modelUsed || 'ai-service-manager',
                            choices: [{
                                index: 0,
                                delta: { content: chunk },
                                finish_reason: null
                            }]
                        };
                        res.write(`data: ${JSON.stringify(sseChunk)}\n\n`);
                        
                        // Small delay for streaming effect
                        await new Promise(resolve => setTimeout(resolve, 50));
                    }
                    
                    res.write('data: [DONE]\n\n');
                    res.end();
                    return;
                } else {
                    throw new Error('No response content from AI Service Manager');
                }
            } catch (serviceManagerError) {
                console.warn('AI Service Manager failed, falling back to direct Gemini:', serviceManagerError.message);
                // Fall through to direct Gemini implementation
            }
        }
        
        // Fallback: Direct Gemini API call
        console.log(`Falling back to direct Gemini API with model: ${GEMINI_MODEL}`);
        // Log only roles and snippet for brevity
        console.log(`Request messages (snippet): ${JSON.stringify(geminiMessages.map(m => ({ role: m.role, content: m.parts[0].text.substring(0, 30) + '...' })))}`);

        // Construct Gemini API request body (using the potentially modified systemInstruction)
        const requestBody = {
            systemInstruction: systemInstruction,
            contents: geminiMessages
        };

        const geminiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_MODEL}:streamGenerateContent?key=${GEMINI_API_KEY}&alt=sse`;

        const response = await fetch(geminiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            const errorBody = await response.text();
            console.error(`Gemini API error: ${response.status} ${response.statusText}`, errorBody);
            try {
                const parsedError = JSON.parse(errorBody);
                throw new Error(`API request failed: ${parsedError.error?.message || errorBody}`);
            } catch (e) {
                throw new Error(`API request failed with status ${response.status}: ${errorBody}`);
            }
        }

        // Set headers for Server-Sent Events (SSE)
        res.setHeader('Content-Type', 'text/event-stream');
        res.setHeader('Cache-Control', 'no-cache');
        res.setHeader('Connection', 'keep-alive');
        res.flushHeaders();

        let fullAssistantResponse = '';

        // Process the stream from Gemini
        response.body.on('data', (chunk) => {
            const chunkText = chunk.toString();
            // Gemini SSE format might send multiple JSON objects in one chunk or across chunks
            // Each data line should be processed individually
            const lines = chunkText.split('\n');
            lines.forEach(line => {
                if (line.startsWith('data: ')) {
                    try {
                        const jsonData = line.substring(6);
                        const parsed = JSON.parse(jsonData);
                        // Check for text content within the Gemini structure
                        if (parsed.candidates && parsed.candidates[0]?.content?.parts?.[0]?.text) {
                            const textChunk = parsed.candidates[0].content.parts[0].text;
                            fullAssistantResponse += textChunk;
                            // Forward the text chunk to the client
                            // We need to wrap it back into the OpenAI-like SSE format the frontend expects
                            const sseChunk = {
                                id: `chatcmpl-${Date.now()}`,
                                object: "chat.completion.chunk",
                                created: Math.floor(Date.now() / 1000),
                                model: GEMINI_MODEL,
                                choices: [{
                                    index: 0,
                                    delta: { content: textChunk },
                                    finish_reason: null
                                }]
                            };
                            res.write(`data: ${JSON.stringify(sseChunk)}\n\n`);
                        } else if (parsed.candidates && parsed.candidates[0]?.finishReason) {
                            console.log(`Gemini finish reason: ${parsed.candidates[0].finishReason}`);
                        }
                    } catch (e) {
                        console.error('Error parsing Gemini chunk:', e, 'Chunk:', line);
                    }
                }
            });
        });

        response.body.on('end', async () => {
            const responseTime = Date.now() - startTime;
            
            if (fullAssistantResponse) {
                // Save the complete response from the 'model' role
                await saveMessageToDatabase(conversationId, 'model', fullAssistantResponse, userId);
                console.log('Gemini stream completed, saved model response to database');
                
                // Record successful AI usage with detailed classification
                const serviceCategory = aiUsageTypes.getServiceCategory(detailedServiceType);
                await aiUsageTracker.recordUsage({
                    provider: 'gemini',
                    serviceType: detailedServiceType,
                    serviceCategory: serviceCategory,
                    endpoint: geminiUrl,
                    modelName: GEMINI_MODEL,
                    success: true,
                    responseTimeMs: responseTime,
                    cacheHit: false,
                    estimatedCostUsd: 0, // Gemini API可能不提供成本信息
                    userType: userId ? 'patient' : 'anonymous',
                    useCase: 'patient_inquiry',
                    sessionId: conversationId,
                    contentType: 'text',
                    language: 'zh'
                });
            } else {
                console.log('Gemini stream completed but no model response was captured');
                
                // Record failed AI usage (no response captured) with detailed classification
                const serviceCategory = aiUsageTypes.getServiceCategory(detailedServiceType);
                await aiUsageTracker.recordUsage({
                    provider: 'gemini',
                    serviceType: detailedServiceType,
                    serviceCategory: serviceCategory,
                    endpoint: geminiUrl,
                    modelName: GEMINI_MODEL,
                    success: false,
                    responseTimeMs: responseTime,
                    cacheHit: false,
                    userType: userId ? 'patient' : 'anonymous',
                    useCase: 'patient_inquiry',
                    sessionId: conversationId,
                    contentType: 'text',
                    language: 'zh'
                });
            }
            
            // Send the final [DONE] message in OpenAI format for frontend compatibility
            res.write('data: [DONE]\n\n');
            res.end();
        });

        response.body.on('error', (err) => {
            console.error('Error in Gemini response stream:', err);
            if (!res.headersSent) {
                res.status(500).json({ error: 'Error in response stream' });
            } else {
                res.end();
            }
        });

    } catch (error) {
        const responseTime = Date.now() - startTime;
        console.error('Error calling Gemini API:', error);
        console.error('Error details:', error.stack);
        
        // Record failed AI usage with detailed classification
        const serviceCategory = aiUsageTypes.getServiceCategory(detailedServiceType);
        await aiUsageTracker.recordUsage({
            provider: 'gemini',
            serviceType: detailedServiceType,
            serviceCategory: serviceCategory,
            endpoint: `https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_MODEL}:streamGenerateContent`,
            modelName: GEMINI_MODEL,
            success: false,
            responseTimeMs: responseTime,
            cacheHit: false,
            userType: userId ? 'patient' : 'anonymous',
            useCase: 'patient_inquiry',
            sessionId: conversationId,
            contentType: 'text',
            language: 'zh'
        });
        
        // Record error details with detailed classification
        await aiUsageTracker.recordError({
            provider: 'gemini',
            serviceType: detailedServiceType,
            errorType: 'API_ERROR',
            errorMessage: error.message,
            errorCode: error.status?.toString(),
            requestSummary: `Model: ${GEMINI_MODEL}, Messages: ${geminiMessages.length}, Type: ${detailedServiceType}`,
            responseSummary: error.stack
        });
        
        if (!res.headersSent) {
            res.status(500).json({ error: error.message || 'Internal Server Error calling Gemini' });
        } else {
            res.end();
        }
    }
});

// 删除会话及其消息
app.delete('/api/chat/conversation/:conversationId', async (req, res) => {
    try {
        const conversationId = req.params.conversationId;

        // 首先删除所有消息
        await pool.query('DELETE FROM chat_messages WHERE conversation_id = ?', [conversationId]);

        // 然后删除会话
        await pool.query('DELETE FROM chat_conversations WHERE conversation_id = ?', [conversationId]);

        res.json({ success: true, message: 'Conversation deleted successfully' });
    } catch (error) {
        console.error('Error deleting conversation:', error);
        res.status(500).json({ error: 'Failed to delete conversation' });
    }
});

// Basic health check endpoint
app.get('/health', (req, res) => {
    res.status(200).send('OK');
});

// AI metrics endpoint for backend to collect chatbot statistics
app.get('/api/ai-metrics', async (req, res) => {
    try {
        console.log('[Chatbot] Getting AI metrics...');
        
        // 使用aiUsageTracker获取今天的统计数据
        const today = new Date().toISOString().split('T')[0];
        
        // 模拟指标数据，基于今天的数据库记录
        const metrics = {
            totalCalls: 0,
            successfulCalls: 0,
            failedCalls: 0,
            cacheHits: 0,
            cacheMisses: 0,
            providerUsage: {
                gemini: {
                    calls: 0,
                    successes: 0,
                    failures: 0
                }
            },
            lastUpdated: new Date().toISOString(),
            service: 'chatbot'
        };
        
        res.json({
            success: true,
            data: metrics,
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        console.error('[Chatbot] Failed to get AI metrics:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get AI metrics',
            error: error.message
        });
    }
});

// Health news crawler initialization (Keep as is)
try {
    const healthNewsCrawler = require('./health-news-crawler');
    console.log('Health news crawler loaded successfully');
} catch (error) {
    console.error('Failed to load health news crawler:', error);
}

app.listen(PORT, () => {
    console.log(`Chatbot service listening on port ${PORT} (using Gemini API)`); // Updated log message
}); 