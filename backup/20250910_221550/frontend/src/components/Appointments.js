import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import {
    Container,
    Paper,
    Typography,
    Tabs,
    Tab,
    Box,
    Card,
    CardContent,
    Grid,
    Divider,
    Chip,
    Button,
    CircularProgress,
    useTheme,
    useMediaQuery,
    Alert,
    Dialog,
    DialogActions,
    DialogContent,
    DialogContentText,
    DialogTitle,
    Snackbar,
    IconButton
} from '@mui/material';
import { styled } from '@mui/material/styles';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import PersonIcon from '@mui/icons-material/Person';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import AssignmentIcon from '@mui/icons-material/Assignment';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import PendingIcon from '@mui/icons-material/Pending';
import EventBusyIcon from '@mui/icons-material/EventBusy';
import CancelIcon from '@mui/icons-material/Cancel';
import EventIcon from '@mui/icons-material/Event';
import HourglassEmptyIcon from '@mui/icons-material/HourglassEmpty';
import SwitchAccountIcon from '@mui/icons-material/SwitchAccount';
import EventNoteIcon from '@mui/icons-material/EventNote';
import CardContainer from './CardContainer';
import AppointmentNoteDialog from './AppointmentNoteDialog';
import { useView } from '../context/ViewContext';
import { useLanguage } from '../context/LanguageContext';
import { getLocationDisplay } from '../utils/locationUtils';
import Pagination from '@mui/material/Pagination';

// Configure dayjs
dayjs.extend(utc);
dayjs.extend(timezone);

// Set default timezone to Vancouver (same as backend)
const TIMEZONE = 'America/Vancouver';

const StyledPaper = styled(Paper)(({ theme }) => ({
    marginBottom: theme.spacing(4),
    padding: theme.spacing(3),
    [theme.breakpoints.up('md')]: {
        padding: theme.spacing(4),
    },
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
}));

function TabPanel(props) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`appointment-tabpanel-${index}`}
            aria-labelledby={`appointment-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: { xs: 2, md: 3 } }}>
                    {children}
                </Box>
            )}
        </div>
    );
}

const InfoItem = ({ icon, label, value }) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

    return (
        <Box sx={{
            display: 'flex',
            alignItems: 'flex-start',
            mb: 1.5
        }}>
            <Box sx={{
                mr: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '50%',
                bgcolor: theme.palette.primary.main + '14', // 8% opacity
                p: 1,
                minWidth: 40,
                height: 40
            }}>
                {React.cloneElement(icon, { color: "primary" })}
            </Box>
            <Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                    {label}
                </Typography>
                <Typography
                    variant="body1"
                    sx={{
                        fontWeight: 500,
                        fontSize: isMobile ? '0.9rem' : '1rem',
                        wordBreak: 'break-word'
                    }}
                >
                    {value}
                </Typography>
            </Box>
        </Box>
    );
};

function Appointments() {
    const { t } = useLanguage();
    const {
        loggedInUser,
        viewingDemographicNo,
        isViewingOwnProfile,
        setViewAs,
        isLoading: contextLoading,
        error: contextError
    } = useView();

    const [value, setValue] = useState(0);
    const [upcomingAppointments, setUpcomingAppointments] = useState([]);
    const [pastAppointments, setPastAppointments] = useState([]);
    const [cancelledAppointments, setCancelledAppointments] = useState([]);
    const [pastCurrentPage, setPastCurrentPage] = useState(1);
    const [pastTotalPages, setPastTotalPages] = useState(0);
    const itemsPerPage = 10;

    // Re-add apptsLoading and apptsError state
    const [apptsLoading, setApptsLoading] = useState(true);
    const [apptsError, setApptsError] = useState(null);

    // 取消预约相关状态
    const [cancelDialogOpen, setCancelDialogOpen] = useState(false);
    const [appointmentToCancel, setAppointmentToCancel] = useState(null);
    const [cancelLoading, setCancelLoading] = useState(false);
    const [cancelSuccess, setCancelSuccess] = useState(false);
    const [cancelError, setCancelError] = useState('');

    // 笔记对话框相关状态
    const [noteDialogOpen, setNoteDialogOpen] = useState(false);
    const [selectedAppointment, setSelectedAppointment] = useState(null);

    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const navigate = useNavigate();

    const handleTabChange = (event, newValue) => {
        setValue(newValue);
        if (newValue === 1) {
            if (pastCurrentPage !== 1) {
                setPastCurrentPage(1);
            } else {
                fetchAppointmentsForView(newValue, 1);
            }
        } else {
            fetchAppointmentsForView(newValue);
        }
    };

    const fetchAppointmentsForView = async (tabIndex = value, page = 1) => {
        if (!viewingDemographicNo) {
            console.log('Appointments: No demographic number, waiting...');
            setApptsLoading(contextLoading);
            return;
        }
        const demoNo = parseInt(viewingDemographicNo, 10);
        if (isNaN(demoNo) || demoNo <= 0) {
            setApptsError(t('invalid_patient_id'));
            setApptsLoading(false);
            return;
        }

        const token = localStorage.getItem('token');
        if (!token) {
            navigate('/login?error=auth_required');
            return;
        }

        setApptsLoading(true);
        setApptsError(null);

        const tabMap = { 0: 'upcoming', 1: 'past', 2: 'cancelled' };
        const currentTab = tabMap[tabIndex];

        let appointmentsUrl = `${process.env.REACT_APP_API_URL}/api/appointments/${demoNo}?tab=${currentTab}`;
        if (currentTab === 'past') {
            appointmentsUrl += `&page=${page}&limit=${itemsPerPage}`;
        }

        console.log(`Appointments: Fetching tab '${currentTab}', page ${page} from URL: ${appointmentsUrl}`);

        try {
            const response = await fetch(appointmentsUrl, {
                headers: { 'Authorization': `Bearer ${token}` }
            });

            if (response.status === 403) {
                throw new Error(t('no_permission_appointments'));
            }
            const data = await response.json();
            if (!response.ok || !data.success) {
                throw new Error(data.message || t('fetch_appointments_failed'));
            }

            console.log(`Appointments: Data received for tab '${currentTab}', page ${page}:`, data);

            if (currentTab === 'upcoming') {
                setUpcomingAppointments(data.appointments || []);
            } else if (currentTab === 'past') {
                setPastAppointments(data.appointments || []);
                setPastTotalPages(data.pagination?.totalPages || 0);
                setPastCurrentPage(data.pagination?.currentPage || 1);
            } else if (currentTab === 'cancelled') {
                setCancelledAppointments(data.appointments || []);
            }

        } catch (err) {
            console.error(`Appointments fetch error for tab '${currentTab}', page ${page}:`, err);
            setApptsError(err.message || t('fetch_error'));
            if (err.message.includes('Permission denied') || err.message.includes('没有权限')) {
                if (loggedInUser && loggedInUser.demographic_no) {
                    console.log('Switching back to own profile due to permission error');
                    setViewAs(loggedInUser.demographic_no);
                }
            }
        } finally {
            setApptsLoading(false);
        }
    };

    useEffect(() => {
        if (viewingDemographicNo) {
            if (value === 1) {
                fetchAppointmentsForView(1, pastCurrentPage);
            } else {
                fetchAppointmentsForView(value);
            }
        }
    }, [viewingDemographicNo, pastCurrentPage]);

    const handlePastPageChange = (event, newPage) => {
        if (newPage !== pastCurrentPage) {
            setPastCurrentPage(newPage);
        }
    };

    const handleSwitchBack = () => {
        if (loggedInUser?.demographic_no) {
            console.log(`Appointments: Switching back to own profile: ${loggedInUser.demographic_no}`);
            setViewAs(loggedInUser.demographic_no);
        }
    }

    const getStatusChip = (status) => {
        switch (status) {
            case 'confirmed':
                return (
                    <Chip
                        icon={<CheckCircleIcon />}
                        label={t('confirmed')}
                        color="success"
                        size={isMobile ? "small" : "medium"}
                    />
                );
            case 'pending':
                return (
                    <Chip
                        icon={<PendingIcon />}
                        label={t('pending')}
                        color="warning"
                        size={isMobile ? "small" : "medium"}
                    />
                );
            case 'completed':
                return (
                    <Chip
                        icon={<CheckCircleIcon />}
                        label={t('completed')}
                        color="primary"
                        size={isMobile ? "small" : "medium"}
                    />
                );
            case 'cancelled':
                return (
                    <Chip
                        icon={<EventBusyIcon />}
                        label={t('cancelled')}
                        color="error"
                        size={isMobile ? "small" : "medium"}
                    />
                );
            default:
                return (
                    <Chip
                        label={status}
                        size={isMobile ? "small" : "medium"}
                    />
                );
        }
    };

    // 处理预约取消
    const handleCancelClick = (appointment) => {
        // 检查预约是否至少提前2天（48小时）取消
        const appointmentDate = dayjs.tz(appointment.date, TIMEZONE);
        const currentDate = dayjs().tz(TIMEZONE);
        const timeDiff = appointmentDate.diff(currentDate, 'hour');

        // 如果距离预约时间不足48小时，则不允许取消
        if (timeDiff < 48) {
            // 显示错误消息而不是打开对话框
            setCancelError(t('cannot_cancel_late'));
            setCancelSuccess(false); // 重置成功状态
            setCancelDialogOpen(false); // 确保对话框关闭

            // 使用Snackbar显示错误信息
            setApptsError(t('cannot_cancel_late'));
            return;
        }

        // 如果可以取消，则继续正常流程
        setAppointmentToCancel(appointment);
        setCancelDialogOpen(true);
        setCancelError(''); // 清除任何之前的错误
    };

    const handleCancelDialogClose = () => {
        setCancelDialogOpen(false);
        setAppointmentToCancel(null);
    };

    const handleCancelAppointment = async () => {
        if (!appointmentToCancel) return;

        setCancelLoading(true);
        setCancelError('');

        try {
            const token = localStorage.getItem('token');
            if (!token) {
                throw new Error(t('auth_required'));
            }

            // 构建包含额外信息的请求体
            const cancelData = {
                sendNotification: true,
                notificationEmail: '<EMAIL>',
                reason: 'Cancelled through patient portal',
                appointmentDetails: {
                    date: appointmentToCancel.date,
                    time: appointmentToCancel.time,
                    doctor: appointmentToCancel.doctor,
                    patientName: loggedInUser?.firstName + ' ' + loggedInUser?.lastName || 'Patient'
                }
            };

            const response = await fetch(`${process.env.REACT_APP_API_URL}/api/appointments/cancel/${appointmentToCancel.id}`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(cancelData)
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || t('cancel_failed'));
            }

            // 更新本地预约列表
            const updatedUpcoming = upcomingAppointments.filter(appt => appt.id !== appointmentToCancel.id);
            const updatedCancelled = [...cancelledAppointments, { ...appointmentToCancel, status: 'cancelled' }].sort((a, b) => dayjs.tz(b.date, TIMEZONE).diff(dayjs.tz(a.date, TIMEZONE)));

            setUpcomingAppointments(updatedUpcoming);
            setCancelledAppointments(updatedCancelled);

            setCancelSuccess(true);
            handleCancelDialogClose();

        } catch (error) {
            console.error('Error cancelling appointment:', error);
            setCancelError(error.message || t('cancel_failed'));
        } finally {
            setCancelLoading(false);
        }
    };

    const handleCancelSuccessClose = () => {
        setCancelSuccess(false);
        setApptsError(null); // 同时清除可能存在的错误信息
    };

    // 处理查看笔记按钮点击
    const handleViewNoteClick = (appointment) => {
        setSelectedAppointment(appointment);
        setNoteDialogOpen(true);
    };

    const handleNoteDialogClose = () => {
        setNoteDialogOpen(false);
    };

    const renderAppointmentCards = (appointmentList) => {
        if (isLoading) {
            return null;
        }
        
        // 调试信息
        console.log('Rendering appointments with timezone:', TIMEZONE);
        if (appointmentList && appointmentList.length > 0) {
            console.log('First appointment date string:', appointmentList[0].date);
            console.log('Parsed date:', dayjs(appointmentList[0].date, 'YYYY-MM-DD', true).format('YYYY-MM-DD'));
        }
        if (!appointmentList || appointmentList.length === 0) {
            return (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                    <Typography variant="body1" color="text.secondary">
                        {t('no_appointments')}
                    </Typography>
                </Box>
            );
        }
        return appointmentList.map((appointment) => (
            <Card
                key={appointment.id}
                sx={{
                    mb: 2,
                    borderLeft: '4px solid',
                    borderLeftColor: appointment.status === 'confirmed' ? 'success.main' :
                        appointment.status === 'cancelled' ? 'error.main' :
                            appointment.status === 'completed' ? 'info.main' : 'warning.main',
                    boxShadow: 2,
                    '&:hover': {
                        boxShadow: 4,
                    },
                    transition: 'box-shadow 0.3s ease',
                }}
            >
                <CardContent>
                    <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                            <Box>
                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
                                    {getStatusChip(appointment.status)}
                                </Box>

                                <InfoItem
                                    icon={<CalendarMonthIcon />}
                                    label={t('date')}
                                    value={
                                        (() => {
                                            try {
                                                const dateStr = appointment.date;
                                                if (!dateStr) return t('no_date_provided');
                                                
                                                // 尝试解析日期
                                                const dateObj = dayjs.tz(dateStr, 'YYYY-MM-DD', TIMEZONE);
                                                if (!dateObj.isValid()) {
                                                    console.warn('Invalid date format:', dateStr);
                                                    return t('invalid_date_format', 'Invalid Date');
                                                }
                                                
                                                // 返回格式化后的日期
                                                return dateObj.format(t('date_format_short', 'MM/DD/YYYY'));
                                            } catch (error) {
                                                console.error('Error formatting date:', error);
                                                return t('date_format_error');
                                            }
                                        })()
                                    }
                                />

                                <InfoItem
                                    icon={<AccessTimeIcon />}
                                    label={t('time')}
                                    value={
                                        (() => {
                                            try {
                                                const timeStr = appointment.time;
                                                if (!timeStr) return t('no_time_provided');
                                                
                                                // 尝试解析时间（支持多种格式：HH:mm, H:mm, HH:mm:ss, H:mm:ss, 或带有AM/PM的格式）
                                                let hours, minutes, period;
                                                
                                                // 1. 尝试匹配 HH:MM 或 H:MM 格式（24小时制）
                                                let timeMatch = timeStr.match(/^(\d{1,2}):(\d{2})(?::\d{2})?\s*([AaPp][Mm])?$/);
                                                
                                                if (timeMatch) {
                                                    hours = parseInt(timeMatch[1], 10);
                                                    minutes = timeMatch[2];
                                                    period = timeMatch[3] ? timeMatch[3].toUpperCase() : null;
                                                    
                                                    // 如果没有指定AM/PM，则根据小时数判断
                                                    if (!period) {
                                                        period = hours >= 12 ? 'PM' : 'AM';
                                                        hours = hours % 12;
                                                        if (hours === 0) hours = 12; // 0点转为12 AM
                                                    }
                                                } else {
                                                    // 2. 尝试其他可能的时间格式
                                                    console.warn('Time format not matched, using original:', timeStr);
                                                    return timeStr.trim();
                                                }
                                                
                                                // 确保分钟数是两位数
                                                minutes = minutes.padStart(2, '0');
                                                
                                                // 返回格式化后的时间
                                                return `${hours}:${minutes} ${period}`;
                                            } catch (error) {
                                                console.error('Error formatting time:', error);
                                                return appointment.time || t('time_format_error');
                                            }
                                        })()
                                    }
                                />

                                <InfoItem
                                    icon={<PersonIcon />}
                                    label={t('doctor')}
                                    value={appointment.doctor}
                                />
                            </Box>
                        </Grid>

                        <Grid item xs={12} md={6}>
                            <Box>
                                <InfoItem
                                    icon={<LocationOnIcon />}
                                    label={t('location')}
                                    value={getLocationDisplay(appointment.location, t)}
                                />

                                <InfoItem
                                    icon={<AssignmentIcon />}
                                    label={t('reason')}
                                    value={appointment.reason}
                                />
                            </Box>
                        </Grid>
                    </Grid>

                    <Divider sx={{ my: 2 }} />

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        {/* 按钮组 */}
                        <Box sx={{ display: 'flex', gap: 1 }}>
                            {/* 查看笔记按钮 - 仅对已完成的预约显示 */}
                            {appointment.status === 'completed' && (
                                <Button
                                    variant="outlined"
                                    color="primary"
                                    startIcon={<EventNoteIcon />}
                                    onClick={() => handleViewNoteClick(appointment)}
                                    size={isMobile ? "small" : "medium"}
                                >
                                    {t('view_note')}
                                </Button>
                            )}
                        </Box>

                        {/* 取消预约按钮 - 仅对确认状态的预约显示 */}
                        {appointment.status === 'confirmed' && (
                            <Button
                                variant="outlined"
                                color="error"
                                startIcon={<CancelIcon />}
                                onClick={() => handleCancelClick(appointment)}
                                size={isMobile ? "small" : "medium"}
                            >
                                {t('cancel_appointment')}
                            </Button>
                        )}
                    </Box>
                </CardContent>
            </Card>
        ));
    };

    const isLoading = contextLoading || apptsLoading;
    const error = contextError || apptsError;

    // 检测严重网络错误
    const isNetworkError = error && (
        error.includes('Failed to fetch') ||
        error.includes('Network Error') ||
        error.includes('timeout') ||
        error.includes('超时')
    );

    return (
        <Container component="main" sx={{ pb: 4 }}>
            {isNetworkError && (
                <Paper
                    sx={{
                        mt: 2,
                        p: 2,
                        border: '1px solid #ffcc80',
                        bgcolor: '#fff8e1',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexDirection: 'column',
                        textAlign: 'center'
                    }}
                >
                    <Typography variant="h6" color="error" gutterBottom>
                        {t('network_error')}
                    </Typography>
                    <Typography variant="body1" gutterBottom>
                        {t('network_error_message')}
                    </Typography>
                    <Button
                        variant="contained"
                        color="primary"
                        onClick={() => window.location.reload()}
                        sx={{ mt: 2 }}
                    >
                        {t('refresh')}
                    </Button>
                </Paper>
            )}

            {!isViewingOwnProfile && loggedInUser && (
                <Alert
                    severity="info"
                    sx={{ mt: 2, mb: 1 }}
                    action={
                        <Button
                            color="inherit"
                            size="small"
                            onClick={handleSwitchBack}
                            startIcon={<SwitchAccountIcon />}
                        >
                            {t('switch_back')}
                        </Button>
                    }
                >
                    {t('viewing_family_appointments')}
                </Alert>
            )}

            <StyledPaper elevation={3}>
                <Typography
                    component="h1"
                    variant={isMobile ? "h5" : "h4"}
                    gutterBottom
                >
                    {isViewingOwnProfile ? t('your_appointments') : t('family_appointments')}
                </Typography>

                {/* Use the derived error constant */}
                {error && !isNetworkError && (
                    <Alert severity="error" sx={{ mb: 2, width: '100%' }}>
                        {t('error_loading_data')}: {error}
                    </Alert>
                )}

                <Box sx={{ width: '100%' }}>
                    <Box sx={{
                        borderBottom: 1,
                        borderColor: 'divider',
                        mb: 2
                    }}>
                        <Tabs
                            value={value}
                            onChange={handleTabChange}
                            aria-label="appointment tabs"
                            centered
                            variant={isMobile ? "fullWidth" : "standard"}
                            TabIndicatorProps={{
                                style: {
                                    height: 3,
                                    borderRadius: 3
                                }
                            }}
                        >
                            <Tab
                                label={isMobile ? t('upcoming_short') : t('upcoming_appointments')}
                                id="appointment-tab-0"
                                icon={isMobile ? <CalendarMonthIcon /> : null}
                                iconPosition="start"
                            />
                            <Tab
                                label={isMobile ? t('past_short') : t('past_appointments')}
                                id="appointment-tab-1"
                                icon={isMobile ? <EventIcon /> : null}
                                iconPosition="start"
                            />
                            <Tab
                                label={isMobile ? t('cancelled_short') : t('cancelled_appointments')}
                                id="appointment-tab-2"
                                icon={isMobile ? <EventBusyIcon /> : null}
                                iconPosition="start"
                            />
                        </Tabs>
                    </Box>

                    {/* Use the derived isLoading constant */}
                    {isLoading ? (
                        <Box sx={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            flexDirection: 'column',
                            py: 8
                        }}>
                            <CircularProgress size={48} />
                            <Typography variant="body1" sx={{ mt: 2, color: 'text.secondary' }}>
                                {t('loading_appointments')}
                            </Typography>
                        </Box>
                    ) : (
                        <>
                            <TabPanel value={value} index={0}>
                                {renderAppointmentCards(upcomingAppointments)}
                            </TabPanel>
                            <TabPanel value={value} index={1}>
                                {renderAppointmentCards(pastAppointments)}
                                {pastTotalPages > 1 && (
                                    <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                                        <Pagination
                                            count={pastTotalPages}
                                            page={pastCurrentPage}
                                            onChange={handlePastPageChange}
                                            color="primary"
                                            shape="rounded"
                                            showFirstButton
                                            showLastButton
                                        />
                                    </Box>
                                )}
                            </TabPanel>
                            <TabPanel value={value} index={2}>
                                {renderAppointmentCards(cancelledAppointments)}
                            </TabPanel>
                        </>
                    )}
                </Box>
            </StyledPaper>

            {/* 取消预约确认对话框 */}
            <Dialog
                open={cancelDialogOpen}
                onClose={handleCancelDialogClose}
                aria-labelledby="cancel-appointment-dialog-title"
            >
                <DialogTitle id="cancel-appointment-dialog-title">
                    {t('cancel_appointment_confirmation')}
                </DialogTitle>
                <DialogContent>
                    <Alert severity="warning" sx={{ mb: 2 }}>
                        {t('cancel_policy')}
                    </Alert>
                    <Alert severity="info" sx={{ mb: 2 }}>
                        {t('cancellation_email_notice')}
                    </Alert>
                    {appointmentToCancel && (
                        <Box sx={{ mt: 2 }}>
                            <Typography variant="subtitle2" gutterBottom>
                                {t('appointment_details')}:
                            </Typography>
                            <Typography variant="body2">
                                <strong>{t('date')}:</strong> {appointmentToCancel.date}
                            </Typography>
                            <Typography variant="body2">
                                <strong>{t('time')}:</strong> {appointmentToCancel.time}
                            </Typography>
                            <Typography variant="body2">
                                <strong>{t('doctor')}:</strong> {appointmentToCancel.doctor}
                            </Typography>
                            <Typography variant="body2">
                                <strong>{t('reason')}:</strong> {appointmentToCancel.reason}
                            </Typography>
                        </Box>
                    )}
                    {cancelError && (
                        <Alert severity="error" sx={{ mt: 2 }}>
                            {cancelError}
                        </Alert>
                    )}
                </DialogContent>
                <DialogActions>
                    <Button
                        onClick={handleCancelDialogClose}
                        disabled={cancelLoading}
                    >
                        {t('back')}
                    </Button>
                    <Button
                        onClick={handleCancelAppointment}
                        color="error"
                        variant="contained"
                        disabled={cancelLoading}
                        startIcon={cancelLoading ? <CircularProgress size={20} color="inherit" /> : <CancelIcon />}
                    >
                        {cancelLoading ? t('cancelling') : t('confirm_cancel')}
                    </Button>
                </DialogActions>
            </Dialog>

            {/* 取消成功提示 */}
            <Snackbar
                open={cancelSuccess || apptsError != null}
                autoHideDuration={6000}
                onClose={handleCancelSuccessClose}
                message={cancelSuccess ? t('appointment_cancelled_successfully') : apptsError}
                action={
                    <Button color="inherit" size="small" onClick={handleCancelSuccessClose}>
                        {t('close')}
                    </Button>
                }
            />

            {/* 笔记查看对话框 */}
            <AppointmentNoteDialog
                open={noteDialogOpen}
                onClose={handleNoteDialogClose}
                appointmentId={selectedAppointment?.id}
                appointmentData={selectedAppointment}
            />
        </Container>
    );
}

export default Appointments; 