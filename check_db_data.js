const mysql = require('mysql2/promise');

async function checkData() {
  const pool = mysql.createPool({
    host: '**************',
    user: 'mmcwebapp', 
    password: 'mmcwebapp2024',
    database: 'mmcwebapp',
    charset: 'utf8mb4'
  });
  
  try {
    console.log('=== AI使用统计数据检查 ===');
    const [rows] = await pool.execute('SELECT * FROM ai_usage_stats ORDER BY created_at DESC LIMIT 5');
    console.log('最近5条记录:');
    rows.forEach((row, index) => {
      console.log(`${index + 1}. ${row.provider}/${row.service_type}/${row.model_name} - 调用:${row.total_calls} 成功:${row.successful_calls} 时间:${row.created_at}`);
    });
    
    console.log('\n=== 统计汇总 ===');
    const [summary] = await pool.execute(`
      SELECT 
        provider, 
        service_type, 
        COUNT(*) as record_count, 
        SUM(total_calls) as total_calls, 
        SUM(successful_calls) as successful_calls,
        DATE(created_at) as date_created
      FROM ai_usage_stats 
      GROUP BY provider, service_type, DATE(created_at)
      ORDER BY date_created DESC
    `);
    
    console.log('按服务类型汇总:');
    summary.forEach(row => {
      console.log(`${row.provider}/${row.service_type} - 记录数:${row.record_count} 总调用:${row.total_calls} 成功:${row.successful_calls} 日期:${row.date_created}`);
    });

    console.log('\n=== Chatbot特定统计 ===');
    const [chatbotStats] = await pool.execute(`
      SELECT 
        service_type,
        COUNT(*) as record_count,
        SUM(total_calls) as total_calls,
        SUM(successful_calls) as successful_calls,
        AVG(avg_response_time_ms) as avg_response_time
      FROM ai_usage_stats 
      WHERE service_type LIKE '%chatbot%' OR service_type = 'chatbot'
      GROUP BY service_type
    `);
    
    if (chatbotStats.length > 0) {
      console.log('Chatbot相关统计:');
      chatbotStats.forEach(row => {
        console.log(`${row.service_type} - 记录:${row.record_count} 调用:${row.total_calls} 成功:${row.successful_calls} 响应时间:${row.avg_response_time}ms`);
      });
    } else {
      console.log('❌ 没有找到Chatbot相关的统计数据');
    }

  } catch (error) {
    console.error('数据库查询错误:', error.message);
  } finally {
    await pool.end();
  }
}

checkData().catch(console.error); 