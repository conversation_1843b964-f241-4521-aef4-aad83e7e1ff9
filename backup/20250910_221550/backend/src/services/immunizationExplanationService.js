const aiService = require('../utils/aiService');
const MedicalPromptTemplates = require('../prompts/medicalPrompts');
const ImmunizationExplanationDAO = require('../dao/immunizationExplanationDAO');
const { createLogger } = require('../utils/logger');
const { ValidationError, AuthorizationError } = require('../utils/errors');

const logger = createLogger('ImmunizationExplanationService');

class ImmunizationExplanationService {
    /**
     * 使用AI解释疫苗接种内容 - 重构版本
     * @param {Object} immunization - 疫苗接种对象
     * @param {string} language - 语言 ('zh' 或 'en')
     * @returns {Promise<Object>} 包含AI解释的对象
     */
    static async explainWithAI(immunization, language = 'zh') {
        try {
            logger.info(`[AI Service] Starting immunization explanation for immunization ${immunization.id}`);

            // 构建提示数据
            const promptData = {
                id: immunization.id,
                type: immunization.type || '未知类型',
                immunizationDate: immunization.immunizationDate || '未知日期',
                providerName: immunization.providerName || '未知医生',
                nextDueDate: immunization.nextDueDate || '无下次接种日期'
            };

            // 使用模板构建提示词
            const prompt = MedicalPromptTemplates.buildPrompt('immunization', language, promptData);

            // 调用统一AI服务
            const result = await aiService.generateImmunizationExplanation(prompt, immunization.id, language);

            if (result.success) {
                logger.info(`[AI Service] Successfully generated explanation for immunization ${immunization.id}`);
                
                // 保存到数据库
                await ImmunizationExplanationDAO.createOrUpdate(
                    immunization.id,
                    result.content,
                    language
                );

                return { success: true, explanation: result.content };
            } else {
                logger.error(`[AI Service] Failed to generate explanation for immunization ${immunization.id}:`, result.error);
                return { success: false, message: result.error };
            }

        } catch (error) {
            logger.error('[AI Service] Error in explainWithAI:', error);
            return { success: false, message: 'Failed to generate AI explanation', error: error.message };
        }
    }

    /**
     * 获取疫苗接种解释，如果不存在则生成
     * @param {Object} immunization - 疫苗接种对象
     * @param {string} language - 语言 ('zh' 或 'en')
     * @returns {Promise<Object>} 疫苗接种解释对象
     */
    static async getOrCreateExplanation(immunization, language = 'zh') {
        try {
            // 先查找是否有已有的解释
            const existingExplanation = await ImmunizationExplanationDAO.getByImmunizationId(immunization.id, language);

            if (existingExplanation) {
                logger.info(`Found existing explanation for immunization ${immunization.id} in ${language}`);
                return { success: true, explanation: existingExplanation.explanation };
            }

            // 如果没有，生成新的解释
            logger.info(`No existing explanation found, generating new one for immunization ${immunization.id}`);
            return await this.explainWithAI(immunization, language);
        } catch (error) {
            logger.error('Error in getOrCreateExplanation:', error);
            return { success: false, message: 'Failed to get or create explanation', error: error.message };
        }
    }
}

module.exports = ImmunizationExplanationService; 