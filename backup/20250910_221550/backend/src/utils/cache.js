console.log('>>> Loading backend/src/utils/cache.js...'); // <<< DEBUG LOG
const NodeCache = require('node-cache');
const { createLogger } = require('./logger');

const logger = createLogger('Cache');
const cache = new NodeCache({
    stdTTL: 300, // 5 minutes default TTL
    checkperiod: 60 // Check for expired keys every 60 seconds
});

module.exports = {
    async get(key) {
        try {
            const value = cache.get(key);
            if (value) {
                logger.debug('Cache hit', { key });
            } else {
                logger.debug('Cache miss', { key });
            }
            return value;
        } catch (error) {
            logger.error('Cache get error:', { error, key });
            return null;
        }
    },

    async set(key, value, ttl = 300) {
        try {
            cache.set(key, value, ttl);
            logger.debug('Cache set', { key, ttl });
        } catch (error) {
            logger.error('Cache set error:', { error, key });
        }
    },

    async del(key) {
        try {
            cache.del(key);
            logger.debug('Cache delete', { key });
        } catch (error) {
            logger.error('Cache delete error:', { error, key });
        }
    },

    async flush() {
        try {
            cache.flushAll();
            logger.info('Cache flushed');
        } catch (error) {
            logger.error('Cache flush error:', { error });
        }
    }
}; 