const formDieticianDAO = require('../dao/formDieticianDAO');
const dieticianService = require('../services/dieticianService');
const pool = require('../config/database');

async function getDieticianCommentsByDemographicNo(req, res) {
    const demographic_no = req.params.demographic_no;
    try {
        const comments = await formDieticianDAO.getCommentsByDemographicNo(demographic_no);
        res.json({ success: true, data: comments });
    } catch (error) {
        res.status(500).json({ success: false, message: error.message });
    }
}

/**
 * 获取特定营养师评论的AI总结 (带缓存)
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @returns {Object} JSON响应包含评论内容和AI总结
 */
async function getDieticianCommentSummary(req, res) {
    console.log('<<<<< ENTERING getDieticianCommentSummary CONTROLLER >>>>>');
    try {
        const { commentId } = req.params;
        const userId = req.user.id;
        const language = req.query.lang || req.headers['accept-language']?.substring(0, 2) || 'en';

        console.log(`[Dietician Summary Cache] Getting summary for commentId: ${commentId}, userId: ${userId}, language: ${language}`);

        // --- 1. 获取评论数据 ---
        const [commentResult] = await pool.query(
            'SELECT ID as dietician_comment_id, comments, demographic_no, consultTime as entry_date FROM formDietician WHERE ID = ?',
            [commentId]
        );

        if (!commentResult || commentResult.length === 0) {
            console.log(`[Dietician Summary Cache] Comment ${commentId} not found`);
            return res.status(404).json({
                success: false,
                message: 'Dietician comment not found'
            });
        }

        const commentData = commentResult[0];
        console.log(`[Dietician Summary Cache] Got comment data with ID: ${commentId}`);

        // --- 2. 权限检查 ---
        // 查询用户的demographic_no
        const [userResult] = await pool.query(
            'SELECT demographic_no FROM user_auth WHERE id = ?',
            [userId]
        );

        if (!userResult || userResult.length === 0 || !userResult[0].demographic_no) {
            console.log('User not linked to an Oscar account');
            return res.status(404).json({
                success: false,
                message: 'User not linked to an Oscar account'
            });
        }

        const userDemographicNo = userResult[0].demographic_no;
        console.log(`User demographic_no: ${userDemographicNo}`);

        // 检查是否是用户自己的评论或有家庭关系
        if (parseInt(userDemographicNo) !== parseInt(commentData.demographic_no)) {
            // 检查家庭关系
            const [familyRelation] = await pool.query(
                `SELECT 1 FROM relationships
                WHERE (relation_demographic_no = ? AND demographic_no = ?)
                OR (demographic_no = ? AND relation_demographic_no = ?)
                AND deleted != '1'`,
                [commentData.demographic_no, userDemographicNo, commentData.demographic_no, userDemographicNo]
            );

            if (familyRelation.length === 0) {
                console.log(`User ${userId} not authorized to view comment for patient ${commentData.demographic_no}`);
                return res.status(403).json({
                    success: false,
                    message: 'Not authorized to view this dietician comment'
                });
            }
        }

        // --- 3. 检查缓存 --- 
        const cacheQuery = `
            SELECT generated_summary 
            FROM dietician_comment_summaries 
            WHERE dietician_comment_id = ? AND language = ?
        `;
        try {
            const [cacheResult] = await pool.query(cacheQuery, [commentId, language]);

            if (cacheResult.length > 0) {
                const cachedSummary = cacheResult[0].generated_summary;
                console.log(`[Dietician Summary Cache] Cache hit for commentId ${commentId}, language ${language}. Returning cached summary.`);
                return res.json({
                    success: true,
                    comment: commentData,
                    summary: cachedSummary,
                    source: 'cache',
                    language: language
                });
            }
            console.log(`[Dietician Summary Cache] Cache miss for commentId ${commentId}, language ${language}. Generating...`);

        } catch (cacheReadError) {
            console.error(`[Dietician Summary Cache] Error reading cache for commentId ${commentId}:`, cacheReadError);
            // 不中断流程，继续尝试生成
        }

        // --- 4. 生成 AI 摘要 (如果缓存未命中) ---
        const summaryResult = await dieticianService.summarizeCommentWithAI(commentData, language);

        // --- 5. 存储到缓存 (如果生成成功) ---
        if (summaryResult.success && summaryResult.summary) {
            const upsertCacheQuery = `
                INSERT INTO dietician_comment_summaries (dietician_comment_id, language, generated_summary) 
                VALUES (?, ?, ?) 
                ON DUPLICATE KEY UPDATE 
                generated_summary = VALUES(generated_summary),
                last_updated = NOW()
            `;
            try {
                console.log(`[Dietician Summary Cache] Attempting to write to cache. SQL: ${upsertCacheQuery.replace(/\s+/g, ' ')}`);
                console.log(`[Dietician Summary Cache] Parameters: [commentId: ${commentId} (${typeof commentId}), language: ${language} (${typeof language}), summary: ${summaryResult.summary.substring(0, 50)}... (${typeof summaryResult.summary}, length: ${summaryResult.summary.length})]`);

                const [result] = await pool.query(upsertCacheQuery, [commentId, language, summaryResult.summary]);

                console.log(`[Dietician Summary Cache] DB write result: ${JSON.stringify(result)}`);
                if (result.affectedRows > 0 || result.warningStatus === 0) {
                    console.log(`[Dietician Summary Cache] Successfully wrote/updated cache for commentId ${commentId}, language ${language}.`);
                } else {
                    console.warn(`[Dietician Summary Cache] DB write for commentId ${commentId} completed but reported 0 affected rows. Result: ${JSON.stringify(result)}`);
                }

            } catch (cacheWriteError) {
                console.error(`[Dietician Summary Cache] FATAL: Error writing cache for commentId ${commentId}, lang ${language}. Error Code: ${cacheWriteError.code}, SQL State: ${cacheWriteError.sqlState}, Message: ${cacheWriteError.message}`);
                console.error(`[Dietician Summary Cache] Failing SQL parameters: [commentId: ${commentId}, language: ${language}, summary length: ${summaryResult.summary?.length}]`);
            }
        } else {
            console.log(`[Dietician Summary Cache] Skipped caching for commentId ${commentId}: success=${summaryResult.success}, summary exists=${!!summaryResult.summary}`);
        }

        // --- 6. 返回结果 --- 
        return res.json({
            success: true,
            comment: commentData,
            summary: summaryResult.success ? summaryResult.summary : null,
            summaryError: !summaryResult.success ? summaryResult.message : null,
            source: 'generated',
            language: language
        });

    } catch (error) {
        console.error('Error in getDieticianCommentSummary controller:', error);
        return res.status(500).json({
            success: false,
            message: 'Server error processing dietician comment summary.',
            error: error.message
        });
    }
}

module.exports = {
    getDieticianCommentsByDemographicNo,
    getDieticianCommentSummary,
}; 