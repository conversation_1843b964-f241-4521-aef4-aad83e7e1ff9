console.log('>>> Loading backend/src/controllers/consultationController.js...'); // <<< DEBUG LOG

const ConsultationService = require('../services/consultationService');
const { createLogger } = require('../utils/logger');
const { ValidationError, AuthorizationError } = require('../utils/errors');

const logger = createLogger('ConsultationController');

// Using 'exports.' consistent with other working controllers
exports.fetchPatientConsultations = async (req, res) => {
    const { demographicNo } = req.params;
    const userId = req.user?.id;
    const loggedInDemoNo = req.user?.demographic_no;

    try {
        const targetDemoNo = parseInt(demographicNo, 10);
        if (isNaN(targetDemoNo)) {
            throw new ValidationError('Invalid demographic number provided in URL parameter');
        }

        logger.info(`Fetching consultations for patient ${targetDemoNo}, requested by user ${userId}`);

        const consultations = await ConsultationService.getPatientConsultations(req.user, targetDemoNo);

        res.json({
            success: true,
            consultations
        });

    } catch (error) {
        logger.error('Error in fetchPatientConsultations controller:', {
            message: error.message,
            stack: error.stack,
            targetDemographicNo: demographicNo,
            requestingUserId: userId,
            requestingUserDemoNo: loggedInDemoNo
        });

        if (error instanceof ValidationError) {
            return res.status(400).json({
                success: false,
                message: error.message
            });
        }

        if (error instanceof AuthorizationError) {
            return res.status(403).json({
                success: false,
                message: error.message
            });
        }

        res.status(500).json({
            success: false,
            message: 'Server error fetching consultation requests'
        });
    }
};

console.log('!!! consultationController.js loaded, exports set !!!'); 