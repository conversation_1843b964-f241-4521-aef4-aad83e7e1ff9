import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
    Container,
    Paper,
    Typography,
    Button,
    Grid,
    Divider,
    Card,
    CardContent,
    Box,
    Chip,
    Avatar,
    CircularProgress,
    useTheme,
    useMediaQuery,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogContentText,
    DialogActions,
    Alert,
    Tabs,
    Tab,
    Tooltip,
    Snackbar,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import PersonIcon from '@mui/icons-material/Person';
import HomeIcon from '@mui/icons-material/Home';
import PhoneIcon from '@mui/icons-material/Phone';
import EmailIcon from '@mui/icons-material/Email';
import MedicalInformationIcon from '@mui/icons-material/MedicalInformation';
import WcIcon from '@mui/icons-material/Wc';
import HistoryEduIcon from '@mui/icons-material/HistoryEdu';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import RefreshIcon from '@mui/icons-material/Refresh';
import UpdateIcon from '@mui/icons-material/Update';
import CreditCardIcon from '@mui/icons-material/CreditCard';
import CardMembershipIcon from '@mui/icons-material/CardMembership';
import CardContainer from './CardContainer';
import { useView } from '../context/ViewContext';
import { useLanguage } from '../context/LanguageContext';
import axios from 'axios';
import { API_URL } from '../utils/env';
import { alpha } from '@mui/material/styles';
import PhotoCamera from '@mui/icons-material/PhotoCamera';
import DeleteIcon from '@mui/icons-material/Delete';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import { getAvatarUrl, uploadAvatar, deleteAvatar } from '../services/demographicService';
import AIDisclaimer from './AIDisclaimer';

const StyledPaper = styled(Paper)(({ theme }) => ({
    marginBottom: theme.spacing(3),
    padding: theme.spacing(2), // 16px for xs, sm
    [theme.breakpoints.up('md')]: {
        padding: theme.spacing(2.5), // 20px for md+
    },
    width: '100%',
    minWidth: 0, 
    boxSizing: 'border-box',
}));

const StyledButton = styled(Button)(({ theme }) => ({
    margin: theme.spacing(2, 0, 1),
}));

// 增强的Markdown渲染
const renderMarkdown = (text) => {
    if (!text) return '';

    // 清理markdown代码块标记
    let cleanText = text.replace(/```markdown\s+/g, '').replace(/```\s*$/g, '');

    // 替换标题
    let formatted = cleanText.replace(/^# (.*$)/gm, '<h1>$1</h1>');
    formatted = formatted.replace(/^## (.*$)/gm, '<h2>$1</h2>');
    formatted = formatted.replace(/^### (.*$)/gm, '<h3>$1</h3>');

    // 替换列表
    formatted = formatted.replace(/^\* (.*$)/gm, '<li>$1</li>');
    formatted = formatted.replace(/^(\d+)\. (.*$)/gm, '<li>$2</li>');
    formatted = formatted.replace(/<\/li>\n<li>/g, '</li><li>');
    formatted = formatted.replace(/(?:<li>.*<\/li>\n)+/gs, function (match) {
        return match.includes('<li>1') || match.includes('<li>2')
            ? '<ol>' + match + '</ol>'
            : '<ul>' + match + '</ul>';
    });

    // 替换粗体和斜体
    formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>');

    // 替换段落
    formatted = formatted.replace(/(?:^|\n)(?!\<h|\<ul|\<ol|\<li)(.+)/g, '<p>$1</p>');

    return formatted;
};

const InfoItem = ({ icon, primary, secondary }) => {
    const theme = useTheme();

    return (
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box sx={{
                mr: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '50%',
                bgcolor: theme.palette.primary.main + '14', // 8% opacity
                p: 1,
                minWidth: 40,
                height: 40
            }}>
                {React.cloneElement(icon, { color: "primary" })}
            </Box>
            <Box sx={{ width: '100%' }}>
                <Typography variant="body2" color="text.secondary">{primary}</Typography>
                <Typography variant="body1" sx={{ fontWeight: 500, wordBreak: 'break-all', overflowWrap: 'break-word', whiteSpace: 'pre-line' }}>{secondary}</Typography>
            </Box>
        </Box>
    );
};

// 修改健康摘要的显示样式
const SummaryContent = styled(Box)(({ theme }) => ({
    whiteSpace: 'pre-wrap',
    fontFamily: theme.typography.fontFamily,
    padding: theme.spacing(2),
    backgroundColor: theme.palette.mode === 'dark'
        ? alpha(theme.palette.primary.main, 0.08)
        : alpha(theme.palette.primary.main, 0.05),
    borderRadius: theme.shape.borderRadius,
    border: `1px solid ${theme.palette.mode === 'dark'
        ? alpha(theme.palette.primary.main, 0.2)
        : alpha(theme.palette.primary.main, 0.15)}`,
    maxHeight: '400px', // Slightly increased max-height
    overflow: 'auto',
    width: '100%',

    // Base typography for markdown content
    fontSize: theme.typography.pxToRem(16),
    lineHeight: 1.7,
    [theme.breakpoints.down('sm')]: {
        fontSize: theme.typography.pxToRem(14.5),
        lineHeight: 1.6,
        padding: theme.spacing(1.5),
    },

    '& h1': {
        fontSize: theme.typography.pxToRem(26),
        fontWeight: 600,
        marginTop: theme.spacing(1),
        marginBottom: theme.spacing(1.5),
        paddingBottom: theme.spacing(0.5),
        color: theme.palette.mode === 'dark' ? theme.palette.primary.light : theme.palette.primary.dark,
        borderBottom: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(144, 202, 249, 0.2)' : 'rgba(21, 101, 192, 0.2)'}`,
        [theme.breakpoints.down('sm')]: {
            fontSize: theme.typography.pxToRem(21),
            marginBottom: theme.spacing(1),
        },
    },
    '& h2': {
        fontSize: theme.typography.pxToRem(22),
        fontWeight: 600,
        marginTop: theme.spacing(2.5),
        marginBottom: theme.spacing(1),
        paddingBottom: theme.spacing(0.5),
        color: theme.palette.mode === 'dark' ? theme.palette.primary.light : theme.palette.primary.dark,
        borderBottom: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(100, 181, 246, 0.1)' : 'rgba(25, 118, 210, 0.1)'}`,
        [theme.breakpoints.down('sm')]: {
            fontSize: theme.typography.pxToRem(19),
            marginTop: theme.spacing(2),
            marginBottom: theme.spacing(1),
        },
    },
    '& h3': {
        fontSize: theme.typography.pxToRem(19),
        fontWeight: 600,
        marginTop: theme.spacing(2),
        marginBottom: theme.spacing(1),
        color: theme.palette.mode === 'dark' ? theme.palette.primary.light : theme.palette.primary.dark,
        [theme.breakpoints.down('sm')]: {
            fontSize: theme.typography.pxToRem(17),
            marginTop: theme.spacing(1.5),
            marginBottom: theme.spacing(0.75),
        },
    },
    '& p': {
        marginBottom: theme.spacing(1.5),
        [theme.breakpoints.down('sm')]: {
            marginBottom: theme.spacing(1),
        },
    },
    '& ul, & ol': {
        marginBottom: theme.spacing(1.5),
        paddingLeft: theme.spacing(2.5),
        [theme.breakpoints.down('sm')]: {
            marginBottom: theme.spacing(1),
            paddingLeft: theme.spacing(2),
        },
    },
    '& li': {
        marginBottom: theme.spacing(0.75),
        [theme.breakpoints.down('sm')]: {
            marginBottom: theme.spacing(0.5),
        },
    },
    '& strong': { fontWeight: 600 },
    '& a': {
        color: theme.palette.primary.main,
        textDecoration: 'underline',
        '&:hover': {
            color: theme.palette.primary.dark,
        }
    }
}));

const Profile = () => {
    const { t, language } = useLanguage();
    const {
        loggedInUser,
        viewingDemographicNo,
        setViewAs,
        isViewingOwnProfile,
    } = useView();
    
    const { demographicNo } = useParams();
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const navigate = useNavigate();
    const token = localStorage.getItem('token');

    // Update viewingDemographicNo when URL parameter changes
    useEffect(() => {
        if (demographicNo && demographicNo !== viewingDemographicNo?.toString()) {
            console.log(`Profile: URL parameter demographicNo changed to ${demographicNo}, updating view context`);
            setViewAs(demographicNo);
        }
    }, [demographicNo, viewingDemographicNo, setViewAs]);

    // State for the demographic info of the profile being displayed
    const [currentDisplayProfile, setCurrentDisplayProfile] = useState(null);
    const [currentDisplayEmail, setCurrentDisplayEmail] = useState(null);
    const [isProfileDataLoading, setIsProfileDataLoading] = useState(true);
    const [profileDataError, setProfileDataError] = useState('');

    // Avatar States
    const [avatarUrl, setAvatarUrl] = useState(null);
    const [avatarFile, setAvatarFile] = useState(null);
    const [avatarPreview, setAvatarPreview] = useState(null);
    const [isAvatarLoading, setIsAvatarLoading] = useState(false);
    const [avatarError, setAvatarError] = useState('');
    const [snackbarOpen, setSnackbarOpen] = useState(false);
    const [snackbarMessage, setSnackbarMessage] = useState('');
    const [snackbarSeverity, setSnackbarSeverity] = useState('success');
    const fileInputRef = useRef(null);
    const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);

    const [historicalSummary, setHistoricalSummary] = useState('');
    const [isSummaryLoading, setIsSummaryLoading] = useState(false);
    const [summaryError, setSummaryError] = useState('');
    const [halfYearSummaries, setHalfYearSummaries] = useState({});
    const [periods, setPeriods] = useState([]);
    const [activeTab, setActiveTab] = useState(0);
    const [isHalfYearLoading, setIsHalfYearLoading] = useState(false);
    const [halfYearError, setHalfYearError] = useState('');
    const [lastUpdated, setLastUpdated] = useState(null);

    // Membership states
    const [membershipData, setMembershipData] = useState(null);
    const [isMembershipLoading, setIsMembershipLoading] = useState(false);
    const [membershipError, setMembershipError] = useState('');

    // Handle tab change
    const handleTabChange = (event, newValue) => {
        setActiveTab(newValue);
    };

    // Format period for display (e.g., "2023-H1" -> "Jan-Jun 2023")
    const formatPeriod = (period) => {
        if (!period) return '';
        const [year, half] = period.split('-');
        return half === 'H1' ? `${t('jan_jun')} ${year}` : `${t('jul_dec')} ${year}`;
    };

    // 格式化上次更新时间
    const formatLastUpdated = (dateString) => {
        if (!dateString) return '';

        const date = new Date(dateString);

        // 检查日期是否有效
        if (isNaN(date.getTime())) return '';

        // 格式化日期和时间
        const options = {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        };

        return new Intl.DateTimeFormat(language === 'zh' ? 'zh-CN' : 'en-US', options).format(date);
    };

    // 检查是否需要自动刷新
    const checkAutoRefresh = (dateString) => {
        if (!dateString) return true;

        const lastUpdate = new Date(dateString);
        const now = new Date();

        // 检查日期是否有效
        if (isNaN(lastUpdate.getTime())) return true;

        // 计算天数差异
        const diffTime = Math.abs(now - lastUpdate);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        // 如果超过90天没有更新，自动刷新（主要作为安全措施）
        // 后端会自动检查是否有新预约，只有新预约时才会重新生成摘要
        if (diffDays > 90) {
            console.log("Auto refreshing summary due to age (>90 days):", dateString);
            return true;
        }
        return false;
    };

    // 定义一个可以在组件内部调用的函数，用于获取半年摘要数据
    const fetchHalfYearSummaries = async (forceRefresh = false) => {
        if (!viewingDemographicNo || !token) {
            console.log("Skipping half-year summaries fetch: No viewingDemographicNo or token");
            return;
        }

        setIsHalfYearLoading(true);
        setHalfYearError('');

        // 检查上次更新时间，如果超过90天则自动刷新（主要作为安全措施）
        // 后端会自动检查是否有新预约，只有新预约时才会重新生成摘要
        if (lastUpdated && !forceRefresh) {
            forceRefresh = checkAutoRefresh(lastUpdated);
            if (forceRefresh) {
                console.log("Auto refreshing summary due to age (>90 days):", lastUpdated);
            }
        }

        console.log(`Fetching half-year summaries for ${viewingDemographicNo} with lang=${language}${forceRefresh ? ' (force refresh)' : ''}`);

        try {
            const url = `${API_URL}/api/appointments/${viewingDemographicNo}/half-year-summaries?lang=${language}${forceRefresh ? '&refresh=true' : ''}`;
            console.log("Request URL:", url);

            // 设置较长的超时时间以防止AI生成时超时
            const response = await axios.get(
                url,
                {
                    headers: { Authorization: `Bearer ${token}` },
                    timeout: 120000, // 2分钟超时，给AI足够的生成时间
                }
            );

            if (response.data.success && response.data.summaries) {
                console.log("Half-year summaries API response:", response.data);
                setHalfYearSummaries(response.data.summaries);
                setPeriods(response.data.periods || []);

                // 设置最后更新时间
                if (response.data.lastUpdated) {
                    setLastUpdated(response.data.lastUpdated);
                    console.log("Last updated:", response.data.lastUpdated);
                }

                // 如果有数据，设置当前期间为默认历史摘要
                if (response.data.periods && response.data.periods.length > 0) {
                    const currentPeriod = response.data.periods[0]; // Most recent period
                    setHistoricalSummary(response.data.summaries[currentPeriod] || '');
                }
            } else {
                console.error("Failed to fetch half-year summaries:", response.data);
                setHalfYearError(response.data.message || t('error_fetching_summary'));
            }
        } catch (err) {
            console.error('Error fetching half-year summaries:', err);
            let errorMsg = t('error_fetching_summary');
            if (err.response && err.response.data && err.response.data.message) {
                errorMsg = err.response.data.message;
            }
            setHalfYearError(errorMsg);
        } finally {
            setIsHalfYearLoading(false);
        }
    };

    // 获取会员信息
    const fetchMembershipInfo = async () => {
        if (!viewingDemographicNo || !token) {
            console.log("Skipping membership fetch: No viewingDemographicNo or token");
            return;
        }

        setIsMembershipLoading(true);
        setMembershipError('');

        try {
            const response = await axios.get(
                `${API_URL}/api/auth/membership/${viewingDemographicNo}`,
                {
                    headers: { Authorization: `Bearer ${token}` },
                    timeout: 10000,
                }
            );

            if (response.data.success) {
                setMembershipData(response.data.membershipInfo);
            } else {
                setMembershipError(response.data.message || t('fetch_membership_failed'));
            }
        } catch (err) {
            console.error('Error fetching membership info:', err);
            // 静默失败，不显示错误，让用户体验更好
            setMembershipData(null);
        } finally {
            setIsMembershipLoading(false);
        }
    };

    // 格式化会员日期
    const formatMembershipDate = (dateString) => {
        if (!dateString) return t('data_not_available');
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString(language === 'zh' ? 'zh-CN' : 'en-US', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
        } catch (error) {
            return t('data_not_available');
        }
    };

    // 获取会员类型标签
    const getMembershipTypeLabel = (code) => {
        const types = {
            'AHM_EM': language === 'zh' ? '个人会员' : 'Individual Membership',
            'AHM_EM_KD': language === 'zh' ? '儿童会员' : 'Child Membership',
            'AHM_SIG': language === 'zh' ? '家庭会员' : 'Family Membership',
            'AHM_SIG_KD': language === 'zh' ? '家庭儿童会员' : 'Family Child Membership',
        };
        return types[code] || (language === 'zh' ? '未知类型' : 'Unknown Type');
    };

    // 格式化价格
    const formatPrice = (price) => {
        return `CA$${parseFloat(price || 0).toFixed(2)}`;
    };

    useEffect(() => {
        const loadData = async () => {
            // Clear previous summary states
            setHalfYearSummaries({});
            setPeriods([]);
            setHistoricalSummary('');
            setLastUpdated(null);
            setSummaryError('');
            setHalfYearError('');
            // Reset profile specific data
            setIsProfileDataLoading(true);
            setProfileDataError('');
            // Reset avatar states
            setAvatarUrl(null);
            setAvatarFile(null);
            setAvatarPreview(null);
            setAvatarError('');
            // Reset membership states
            setMembershipData(null);
            setIsMembershipLoading(false);
            setMembershipError('');

            if (!viewingDemographicNo || !token) {
                setIsProfileDataLoading(false);
                setProfileDataError(t('error_missing_profile_id'));
                return;
            }

            // Fetch avatar URL
            const fetchedAvatarUrl = await getAvatarUrl(viewingDemographicNo);
            if (fetchedAvatarUrl) {
                setAvatarUrl(fetchedAvatarUrl);
            }

            // Fetch membership info
            fetchMembershipInfo();

            // Fetch or set demographic data
            if (isViewingOwnProfile) {
                if (loggedInUser && loggedInUser.demographicInfo) {
                    setCurrentDisplayProfile(loggedInUser.demographicInfo);
                    setCurrentDisplayEmail(loggedInUser.email);
                    setIsProfileDataLoading(false);
                } else {
                    setProfileDataError(t('error_loading_own_profile'));
                    setIsProfileDataLoading(false);
                }
            } else {
                // Fetch profile data for the viewingDemographicNo
                try {
                    console.log(`Fetching profile for demographic_no: ${viewingDemographicNo}`);
                    const response = await axios.get(
                        `${API_URL}/api/auth/profile/${viewingDemographicNo}`,
                        {
                            headers: { Authorization: `Bearer ${token}` },
                        }
                    );

                    if (response.data.success && response.data.demographicInfo) {
                        setCurrentDisplayProfile(response.data.demographicInfo);
                        setCurrentDisplayEmail(response.data.demographicInfo.email || t('email_not_available'));
                        setProfileDataError('');
                    } else {
                        // Fallback to placeholder if API fails or data is incomplete
                        console.error("Failed to fetch viewing profile or data incomplete:", response.data);
                        setCurrentDisplayProfile({
                            first_name: t('data_not_available_short'),
                            last_name: '',
                            title: '', address: t('data_not_available_short'), city: '', province: '', postal: '',
                            phone: t('data_not_available_short'), hin: t('data_not_available_short'), sex: t('data_not_available_short')
                        });
                        setCurrentDisplayEmail(t('data_not_available_short'));
                        setProfileDataError(response.data.message || t('error_fetching_profile_details'));
                    }
                } catch (err) {
                    console.error('Error fetching viewing profile details:', err);
                    // Fallback to placeholder on error
                    setCurrentDisplayProfile({
                        first_name: t('error_generic_short'),
                        last_name: '',
                        title: '', address: t('error_generic_short'), city: '', province: '', postal: '',
                        phone: t('error_generic_short'), hin: t('error_generic_short'), sex: t('error_generic_short')
                    });
                    setCurrentDisplayEmail(t('error_generic_short'));
                    setProfileDataError(t('error_fetching_profile_details_network'));
                } finally {
                    setIsProfileDataLoading(false);
                }
            }

            // Fetch summary data (existing logic)
            if (viewingDemographicNo && token) { // Ensure viewingDemographicNo is valid before fetching summaries
                try {
                    await fetchHalfYearSummaries(false);

                    if (Object.keys(halfYearSummaries).length === 0 && !halfYearError && periods.length === 0) { // ensure not to overwrite if already populated by halfyear
                        setIsSummaryLoading(true);
                        const summaryResponse = await axios.get(
                            `${API_URL}/api/appointments/${viewingDemographicNo}/history-summary?lang=${language}`,
                            {
                                headers: { Authorization: `Bearer ${token}` },
                            }
                        );

                        if (summaryResponse.data.success && summaryResponse.data.summary) {
                            setHistoricalSummary(summaryResponse.data.summary);
                            if ((summaryResponse.data.summaries || summaryResponse.data.halfYearSummaries) && summaryResponse.data.periods) {
                                const summariesData = summaryResponse.data.summaries || summaryResponse.data.halfYearSummaries;
                                setHalfYearSummaries(summariesData);
                                setPeriods(summaryResponse.data.periods);
                                if (summaryResponse.data.lastUpdated) setLastUpdated(summaryResponse.data.lastUpdated);
                                setHalfYearError('');
                            }
                        } else {
                            setSummaryError(summaryResponse.data.message || t('error_fetching_summary'));
                        }
                    }
                } catch (err) {
                    console.error('Error loading health summaries in loadData:', err);
                    if (!halfYearError) { // Only set general summaryError if halfYearError isn't already specific
                        let errorMsg = t('error_fetching_summary');
                        if (err.response?.data?.message) {
                            errorMsg = err.response.data.message;
                        }
                        setSummaryError(errorMsg);
                    }
                } finally {
                    setIsSummaryLoading(false);
                }
            }
        };

        loadData();
    }, [viewingDemographicNo, token, language, isViewingOwnProfile, loggedInUser]); // Added isViewingOwnProfile and loggedInUser

    const handleSwitchBack = () => {
        if (loggedInUser?.demographic_no) {
            setViewAs(loggedInUser.demographic_no);
        }
    }

    // Avatar handling functions
    const handleFileSelect = (event) => {
        const file = event.target.files[0];
        if (file) {
            setAvatarFile(file);
            const reader = new FileReader();
            reader.onloadend = () => {
                setAvatarPreview(reader.result);
            };
            reader.readAsDataURL(file);
            setAvatarError(''); // Clear previous error
        }
    };

    const handleUploadAvatar = async () => {
        if (!avatarFile || !viewingDemographicNo) return;
        setIsAvatarLoading(true);
        setAvatarError('');
        try {
            await uploadAvatar(viewingDemographicNo, avatarFile);
            const newAvatarUrl = await getAvatarUrl(viewingDemographicNo); // Refetch to get fresh pre-signed URL
            setAvatarUrl(newAvatarUrl);
            setAvatarFile(null);
            setAvatarPreview(null);
            showSnackbar(t('avatar_upload_success'), 'success');
            // Potentially notify Header to update avatar if a more global state/event system is in place
        } catch (error) {
            console.error('Failed to upload avatar:', error);
            setAvatarError(error.message || t('avatar_upload_failed'));
            showSnackbar(error.message || t('avatar_upload_failed'), 'error');
        } finally {
            setIsAvatarLoading(false);
        }
    };

    const handleDeleteAvatar = async () => {
        if (!viewingDemographicNo) return;
        setIsAvatarLoading(true);
        setAvatarError('');
        try {
            await deleteAvatar(viewingDemographicNo);
            setAvatarUrl(null);
            setAvatarFile(null); // Clear any pending uploads
            setAvatarPreview(null);
            showSnackbar(t('avatar_delete_success'), 'success');
        } catch (error) {
            console.error('Failed to delete avatar:', error);
            setAvatarError(error.message || t('avatar_delete_failed'));
            showSnackbar(error.message || t('avatar_delete_failed'), 'error');
        } finally {
            setIsAvatarLoading(false);
            setDeleteConfirmOpen(false);
        }
    };

    const showSnackbar = (message, severity) => {
        setSnackbarMessage(message);
        setSnackbarSeverity(severity);
        setSnackbarOpen(true);
    };

    const handleCloseSnackbar = (event, reason) => {
        if (reason === 'clickaway') {
            return;
        }
        setSnackbarOpen(false);
    };

    if (!loggedInUser || !loggedInUser.demographicInfo || !loggedInUser.demographicInfo.first_name) {
        console.error("Profile Component Error: loggedInUser from context is missing or incomplete.", loggedInUser);
        return (
            <Container component="main" maxWidth="lg">
                <StyledPaper elevation={3}>
                    <Typography color="error" variant="h6">Error Loading User Profile</Typography>
                    <Alert severity="error" sx={{ mt: 2, width: '100%' }}>
                        Could not load your primary profile information. Please try logging out and back in, or contact support.
                    </Alert>
                </StyledPaper>
            </Container>
        );
    }

    // New: Use state for displayed profile
    const displayDemographicInfo = currentDisplayProfile;
    const displayEmail = currentDisplayEmail;

    if (isProfileDataLoading && !displayDemographicInfo) { // Show full page loader only if there's no stale data to show
        return (
            <Container component="main" sx={{ pb: 4, display: 'flex', justifyContent: 'center', alignItems: 'center', height: 'calc(100vh - 200px)' }}>
                <CircularProgress size={60} />
            </Container>
        );
    }

    if (profileDataError && !displayDemographicInfo) { // Show error if loading failed and no stale data
        return (
            <Container component="main" maxWidth="lg">
                <StyledPaper elevation={3}>
                    <Typography color="error" variant="h6">{t('error_loading_profile_data')}</Typography>
                    <Alert severity="error" sx={{ mt: 2, width: '100%' }}>
                        {profileDataError}
                    </Alert>
                </StyledPaper>
            </Container>
        );
    }

    // If displayDemographicInfo is still null after attempting to load, something is wrong.
    // This might happen if viewing another profile and the fetch fails AND the fallback wasn't set.
    // Or if own profile is loading and loggedInUser.demographicInfo is not yet ready.
    // However, the above checks should handle loading/error states.
    // For safety, if we reach here and displayDemographicInfo is null, show a generic error.
    if (!displayDemographicInfo) {
        return (
            <Container component="main" sx={{ pb: 4, display: 'flex', justifyContent: 'center', alignItems: 'center', flexDirection: 'column' }}>
                <Typography variant="h6" color="error" gutterBottom>
                    {t('profile_unavailable')}
                </Typography>
                <Alert severity="warning">
                    {t('profile_data_could_not_be_loaded')}
                </Alert>
                {isProfileDataLoading && <CircularProgress sx={{ mt: 2 }} />}
            </Container>
        );
    }

    return (
        <Container component="main" maxWidth={false} sx={{ pb: 4, px: isMobile ? 0.5 : 2, width: '100%', minWidth: 0 }}>
            {!isViewingOwnProfile && (
                <Alert
                    severity="info"
                    sx={{ mt: 2, mb: 2 }}
                    action={
                        <Button
                            color="inherit"
                            size="small"
                            onClick={handleSwitchBack}
                            startIcon={<PersonIcon />}
                        >
                            {t('return_to_profile')}
                        </Button>
                    }
                >
                    {t('viewing_family_member')}
                </Alert>
            )}

            <StyledPaper elevation={3} sx={{ p: isMobile ? 1 : 3, width: '100%', minWidth: 0 }}>
                <Box sx={{ display: 'flex', alignItems: isMobile ? 'center' : 'center', mb: 2, flexDirection: isMobile ? 'column' : 'row', width: '100%', minWidth: 0 }}>
                    <Avatar 
                        src={avatarPreview || avatarUrl} // Show preview if available, else current URL
                        sx={{ 
                            width: isMobile ? 80 : 100, 
                            height: isMobile ? 80 : 100, 
                            mb: isMobile ? 2 : 0, 
                            mr: isMobile ? 0 : 3,
                            alignSelf: isMobile ? 'center' : 'flex-start',
                            bgcolor: (avatarPreview || avatarUrl) ? 'transparent' : theme.palette.primary.light,
                            border: `3px solid ${(avatarPreview || avatarUrl) ? theme.palette.primary.main : 'transparent'}`
                        }}
                    >
                        {!(avatarPreview || avatarUrl) && displayDemographicInfo.first_name ? displayDemographicInfo.first_name[0].toUpperCase() : ' '}
                </Avatar>
                    <Box sx={{ textAlign: isMobile ? 'center' : 'left', width: isMobile ? '100%' : 'auto', minWidth: 0, flex: 1 }}>
                <Typography
                    component="h1"
                    variant={isMobile ? "h5" : "h4"}
                    gutterBottom
                            sx={{ wordBreak: 'break-word' }}
                >
                    {isProfileDataLoading && !displayDemographicInfo.first_name ? <CircularProgress size={20} sx={{ mr: 1 }} /> : ''}
                    {displayDemographicInfo.first_name && displayDemographicInfo.first_name !== t('data_not_available_short') && displayDemographicInfo.first_name !== t('error_generic_short')
                        ? `${displayDemographicInfo.title || ''} ${displayDemographicInfo.first_name} ${displayDemographicInfo.last_name}`
                        : (isProfileDataLoading ? t('loading_profile_name') : t('profile_name_unavailable'))}
                </Typography>
                        {isViewingOwnProfile && (
                            <Box sx={{ mt: 1, display: 'flex', gap: 1, justifyContent: isMobile ? 'center' : 'flex-start', flexWrap: 'wrap' }}>
                                <input 
                                    type="file" 
                                    hidden 
                                    accept="image/jpeg,image/png,image/gif" 
                                    ref={fileInputRef} 
                                    onChange={handleFileSelect} 
                                />
                                <Button 
                                    variant="outlined" 
                                    size="small" 
                                    startIcon={<PhotoCamera />} 
                                    onClick={() => fileInputRef.current && fileInputRef.current.click()}
                                >
                                    {t('change_avatar')}
                                </Button>
                                {avatarUrl && (
                                    <Button 
                                        variant="outlined" 
                                        size="small" 
                                        color="error" 
                                        startIcon={<DeleteIcon />} 
                                        onClick={() => setDeleteConfirmOpen(true)}
                                    >
                                        {t('delete_avatar')}
                                    </Button>
                                )}
                            </Box>
                        )}
                    </Box>
                </Box>
                {isViewingOwnProfile && avatarFile && avatarPreview && (
                     <Box sx={{ my: 2, p:2, border: `1px dashed ${theme.palette.divider}`, borderRadius: 1, textAlign: 'center' }}>
                        <Typography variant="subtitle2" gutterBottom>{t('avatar_preview')}</Typography>
                        <Avatar src={avatarPreview} sx={{ width: 120, height: 120, m: 'auto', mb: 1 }} />
                        <Button 
                            variant="contained" 
                            onClick={handleUploadAvatar} 
                            disabled={isAvatarLoading}
                            startIcon={isAvatarLoading ? <CircularProgress size={20} color="inherit" /> : <CloudUploadIcon />}
                        >
                            {isAvatarLoading ? t('uploading_avatar') : t('upload_now')}
                        </Button>
                        <Button variant="text" onClick={() => { setAvatarFile(null); setAvatarPreview(null);}} sx={{ml:1}}>{t('cancel')}</Button>
                        {avatarError && <Alert severity="error" sx={{mt:1}}>{avatarError}</Alert>}
                    </Box>
                )}
                
                <Dialog open={deleteConfirmOpen} onClose={() => setDeleteConfirmOpen(false)}>
                    <DialogTitle>{t('confirm_delete_avatar_title')}</DialogTitle>
                    <DialogContent>
                        <DialogContentText>{t('confirm_delete_avatar_text')}</DialogContentText>
                    </DialogContent>
                    <DialogActions>
                        <Button onClick={() => setDeleteConfirmOpen(false)}>{t('cancel')}</Button>
                        <Button onClick={handleDeleteAvatar} color="error" disabled={isAvatarLoading}>
                            {isAvatarLoading ? <CircularProgress size={20}/> : t('delete')}
                        </Button>
                    </DialogActions>
                </Dialog>

                <Grid container spacing={3} sx={{ width: '100%' }}>
                    <Grid item xs={12}>
                        <Card variant="outlined">
                            <CardContent>
                                <Typography variant="h6" gutterBottom>
                                    <PersonIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                                    {t('personal_information')}
                                </Typography>
                                <Divider />
                                <Grid container spacing={2} sx={{ mt: 1 }}>
                                    <Grid item xs={12} sm={6}>
                                        <InfoItem
                                            icon={<PersonIcon />}
                                            primary={t('full_name')}
                                            secondary={isProfileDataLoading ? t('loading_info') : (displayDemographicInfo.first_name && displayDemographicInfo.first_name !== t('data_not_available_short') && displayDemographicInfo.first_name !== t('error_generic_short') ? `${displayDemographicInfo.title || ''} ${displayDemographicInfo.first_name} ${displayDemographicInfo.last_name}` : t('data_not_available'))}
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6}>
                                        <InfoItem
                                            icon={<HomeIcon />}
                                            primary={t('address')}
                                            secondary={isProfileDataLoading ? t('loading_info') : (displayDemographicInfo.address && displayDemographicInfo.address !== t('data_not_available_short') && displayDemographicInfo.address !== t('error_generic_short') ? `${displayDemographicInfo.address}, ${displayDemographicInfo.city}, ${displayDemographicInfo.province} ${displayDemographicInfo.postal}` : t('data_not_available'))}
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6}>
                                        <InfoItem
                                            icon={<PhoneIcon />}
                                            primary={t('phone')}
                                            secondary={isProfileDataLoading ? t('loading_info') : (displayDemographicInfo.phone && displayDemographicInfo.phone !== t('data_not_available_short') && displayDemographicInfo.phone !== t('error_generic_short') ? displayDemographicInfo.phone : t('data_not_available'))}
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6}>
                                        <InfoItem
                                            icon={<EmailIcon />}
                                            primary={t('email')}
                                            secondary={isProfileDataLoading ? t('loading_info') : (displayEmail && displayEmail !== t('data_not_available_short') && displayEmail !== t('error_generic_short') ? displayEmail : t('data_not_available'))}
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6}>
                                        <InfoItem
                                            icon={<MedicalInformationIcon />}
                                            primary={t('care_card')}
                                            secondary={isProfileDataLoading ? t('loading_info') : (displayDemographicInfo.hin && displayDemographicInfo.hin !== t('data_not_available_short') && displayDemographicInfo.hin !== t('error_generic_short') ? displayDemographicInfo.hin : t('data_not_available'))}
                                        />
                                    </Grid>
                                    <Grid item xs={12} sm={6}>
                                        <InfoItem
                                            icon={<WcIcon />}
                                            primary={t('sex')}
                                            secondary={isProfileDataLoading ? t('loading_info') : (displayDemographicInfo.sex && displayDemographicInfo.sex !== t('data_not_available_short') && displayDemographicInfo.sex !== t('error_generic_short') ? displayDemographicInfo.sex : t('data_not_available'))}
                                        />
                                    </Grid>
                                    
                                    {/* 会员信息部分 */}
                                    {membershipData && membershipData.current && (
                                        <>
                                            <Grid item xs={12}>
                                                <Divider sx={{ my: 2 }}>
                                                    <Chip
                                                        icon={<CardMembershipIcon />}
                                                        label={t('membership_info')}
                                                        color="primary"
                                                        variant="outlined"
                                                    />
                                                </Divider>
                                            </Grid>
                                            <Grid item xs={12} sm={6}>
                                                <InfoItem
                                                    icon={<CreditCardIcon />}
                                                    primary={t('membership_type')}
                                                    secondary={getMembershipTypeLabel(membershipData.current.billing_code)}
                                                />
                                            </Grid>
                                            <Grid item xs={12} sm={6}>
                                                <InfoItem
                                                    icon={<CalendarTodayIcon />}
                                                    primary={t('membership_start_date')}
                                                    secondary={formatMembershipDate(membershipData.current.service_date)}
                                                />
                                            </Grid>
                                            <Grid item xs={12} sm={6}>
                                                <InfoItem
                                                    icon={<CalendarTodayIcon />}
                                                    primary={t('membership_end_date')}
                                                    secondary={formatMembershipDate(membershipData.current.endDate)}
                                                />
                                            </Grid>
                                            <Grid item xs={12} sm={6}>
                                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                                    <Box sx={{
                                                        mr: 2,
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        borderRadius: '50%',
                                                        bgcolor: theme.palette.primary.main + '14',
                                                        p: 1,
                                                        minWidth: 40,
                                                        height: 40
                                                    }}>
                                                        <CardMembershipIcon color="primary" />
                                                    </Box>
                                                    <Box sx={{ width: '100%' }}>
                                                        <Typography variant="body2" color="text.secondary">{t('membership_status')}</Typography>
                                                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                            <Chip
                                                                label={membershipData.current.isExpired ? t('expired') : t('active')}
                                                                color={membershipData.current.isExpired ? 'error' : 'success'}
                                                                size="small"
                                                                sx={{ fontWeight: 'medium' }}
                                                            />
                                                        </Box>
                                                    </Box>
                                                </Box>
                                            </Grid>
                                        </>
                                    )}
                                    
                                    {isMembershipLoading && (
                                        <Grid item xs={12}>
                                            <Box sx={{ display: 'flex', justifyContent: 'center', py: 2 }}>
                                                <CircularProgress size={24} />
                                                <Typography variant="body2" color="text.secondary" sx={{ ml: 2 }}>
                                                    {t('loading_membership')}
                                                </Typography>
                                            </Box>
                                        </Grid>
                                    )}
                                </Grid>
                            </CardContent>
                        </Card>
                    </Grid>

                    <Grid item xs={12}>
                        <Card variant="outlined">
                            <CardContent>
                                <Typography variant="h6" gutterBottom>
                                    <HistoryEduIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                                    {t('health_review')}
                                </Typography>
                                <Divider sx={{ mb: 2 }} />

                                {isHalfYearLoading || isSummaryLoading ? (
                                    <Box sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', my: 3 }}>
                                        <CircularProgress />
                                        <Typography variant="body2" color="text.secondary" sx={{ mt: 2, textAlign: 'center' }}>
                                            {isHalfYearLoading 
                                                ? t('generating_health_summary') || '正在生成健康摘要，请稍候...'
                                                : t('loading_summary') || '正在加载摘要...'
                                            }
                                        </Typography>
                                        <Typography variant="caption" color="text.secondary" sx={{ mt: 1, textAlign: 'center', maxWidth: '300px' }}>
                                            {t('ai_generation_notice') || 'AI 正在分析您的医疗记录，这可能需要1-2分钟时间'}
                                        </Typography>
                                    </Box>
                                ) : halfYearError && summaryError ? (
                                    <Alert severity="warning" sx={{ whiteSpace: 'pre-wrap' }}>
                                        {halfYearError || summaryError}
                                    </Alert>
                                ) : (
                                    <>
                                        <AIDisclaimer sx={{ mb: 3 }} />
                                        {periods.length > 0 ? (
                                            <>
                                                <Box sx={{ display: 'flex', justifyContent: 'flex-start', alignItems: 'center', mb: 1, width: '100%' }} className="half-year-summary-header">
                                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                        <Typography variant="subtitle1" sx={{ fontWeight: 'medium', display: 'flex', alignItems: 'center' }}>
                                                            <CalendarTodayIcon sx={{ mr: 1, fontSize: '1rem' }} />
                                                            {t('half_year_summaries')}
                                                        </Typography>
                                                        {lastUpdated && (
                                                            <Tooltip title={t('last_updated_tooltip')} placement="top">
                                                                <Typography
                                                                    variant="caption"
                                                                    color="text.secondary"
                                                                    sx={{ ml: 2, display: 'flex', alignItems: 'center' }}
                                                                >
                                                                    <UpdateIcon sx={{ mr: 0.5, fontSize: '0.9rem' }} />
                                                                    {t('last_updated')}: {formatLastUpdated(lastUpdated)}
                                                                </Typography>
                                                            </Tooltip>
                                                        )}
                                                    </Box>
                                                </Box>
                                                <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
                                                    <Tabs
                                                        value={activeTab}
                                                        onChange={handleTabChange}
                                                        variant="scrollable"
                                                        scrollButtons="auto"
                                                        aria-label="health history periods"
                                                        sx={{
                                                            '& .MuiTab-root': {
                                                                minWidth: '120px',
                                                                fontWeight: 'medium',
                                                                borderRadius: '4px 4px 0 0',
                                                                '&.Mui-selected': {
                                                                    backgroundColor: theme.palette.primary.light + '20',
                                                                }
                                                            }
                                                        }}
                                                    >
                                                        {periods.map((period, index) => (
                                                            <Tab
                                                                key={period}
                                                                label={formatPeriod(period)}
                                                                icon={<CalendarTodayIcon />}
                                                                iconPosition="start"
                                                            />
                                                        ))}
                                                    </Tabs>
                                                </Box>

                                                {periods.map((period, index) => (
                                                    <Box
                                                        key={period}
                                                        role="tabpanel"
                                                        hidden={activeTab !== index}
                                                        id={`period-tabpanel-${index}`}
                                                        aria-labelledby={`period-tab-${index}`}
                                                        sx={{
                                                            p: 2,
                                                            backgroundColor: theme.palette.background.paper,
                                                            borderRadius: '0 4px 4px 4px',
                                                            boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
                                                            minHeight: '150px'
                                                        }}
                                                    >
                                                        {activeTab === index && (
                                                            <Typography
                                                                variant="body1"
                                                                sx={{
                                                                    whiteSpace: 'pre-wrap',
                                                                    lineHeight: 1.6,
                                                                }}
                                                                dangerouslySetInnerHTML={{ __html: renderMarkdown(halfYearSummaries[period] || t('no_summary_available')) }}
                                                            />
                                                        )}
                                                    </Box>
                                                ))}
                                            </>
                                        ) : historicalSummary ? (
                                            <>
                                                <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 'medium', display: 'flex', alignItems: 'center' }}>
                                                    <HistoryEduIcon sx={{ mr: 1, fontSize: '1rem' }} />
                                                    {t('health_summary')}
                                                </Typography>
                                                <SummaryContent dangerouslySetInnerHTML={{ __html: renderMarkdown(historicalSummary) }} />
                                            </>
                                        ) : (
                                            <Typography variant="body2" color="text.secondary" sx={{ p: 2 }}>
                                                {t('no_summary_available')}
                                            </Typography>
                                        )}
                                    </>
                                )}
                            </CardContent>
                        </Card>
                    </Grid>
                </Grid>
            </StyledPaper>
            <Snackbar open={snackbarOpen} autoHideDuration={6000} onClose={handleCloseSnackbar} anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}>
                <Alert onClose={handleCloseSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>
                    {snackbarMessage}
                </Alert>
            </Snackbar>
        </Container>
    );
};

export default Profile;