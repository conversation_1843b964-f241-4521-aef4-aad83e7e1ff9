import axios from 'axios';
// API_URL import is no longer strictly needed here if only relative paths are used with axios
// import { API_URL } from '../utils/env'; 

export async function getDieticianComments(demographic_no) {
    // Assuming axios.defaults.baseURL is set to something like 'https://app-backend.mmcwellness.ca'
    // We need to prepend /api for this specific service call.
    const response = await axios.get(`/api/formDietician/${demographic_no}`); 
    return response.data;
} 

export async function getDieticianCommentSummary(commentId, language = 'zh') {
    const response = await axios.get(`/api/formDietician/summary/${commentId}?lang=${language}`);
    return response.data;
} 