const db = require('../config/database');

/**
 * 检查用户是否有权限查看特定患者的数据
 * @param {number} loggedInDemoNo - 登录用户的 demographic_no
 * @param {number} targetDemoNo - 目标患者的 demographic_no
 * @param {string} role - 用户角色
 * @returns {Promise<boolean>} - 是否有权限
 */
const checkPermission = async (loggedInDemoNo, targetDemoNo, role) => {
    try {
        // 如果用户是管理员，直接返回 true
        if (role === 'admin') {
            console.log(`Admin user with demo ${loggedInDemoNo} granted access to demo ${targetDemoNo}`);
            return true;
        }

        // 如果查看自己的数据，直接返回 true
        if (parseInt(loggedInDemoNo) === parseInt(targetDemoNo)) {
            return true;
        }

        // 检查是否是家庭成员关系
        const [relationships] = await db.query(`
            SELECT * FROM relationships 
            WHERE (demographic_no = ? AND relative_no = ? OR demographic_no = ? AND relative_no = ?)
            AND deleted = 0
        `, [loggedInDemoNo, targetDemoNo, targetDemoNo, loggedInDemoNo]);

        if (relationships && relationships.length > 0) {
            return true;
        }

        // 检查是否有临时查看权限（通过验证码获取的）
        const [tempAccess] = await db.query(`
            SELECT * FROM view_verification_codes
            WHERE requester_demographic_no = ? 
            AND target_demographic_no = ?
            AND verified = 1
            AND verified_at > DATE_SUB(NOW(), INTERVAL 30 MINUTE)
        `, [loggedInDemoNo, targetDemoNo]);

        return tempAccess && tempAccess.length > 0;
    } catch (error) {
        console.error('Error checking permission:', error);
        return false;
    }
};

module.exports = {
    checkPermission
};
