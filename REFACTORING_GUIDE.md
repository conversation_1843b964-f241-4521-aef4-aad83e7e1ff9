# AI服务重构指南

## 🎯 概述

本指南说明如何将现有的AI服务重构为使用统一的AI服务层。我们已经完成了基础架构的搭建，现在需要逐步重构其他服务。

## 📁 已完成的基础设施

### ✅ 核心组件
- `backend/src/config/aiConfig.js` - 统一配置管理
- `backend/src/utils/aiService.js` - 核心AI服务类
- `backend/src/prompts/medicalPrompts.js` - 提示词模板管理
- `backend/src/routes/ai-monitor.js` - 监控端点

### ✅ 示例重构
- `backend/src/services/appointmentNoteService.js` - 已重构完成（从475行减少到约150行）

## 🔧 重构步骤

### 步骤1：识别需要重构的文件

待重构的文件列表：
- `backend/src/services/prescriptionExplanationService.js`
- `backend/src/services/immunizationExplanationService.js`
- `backend/src/services/dieticianService.js`
- `backend/src/scripts/auto-fetch-medical-videos.js`
- `chatbot-service/server.js`

### 步骤2：重构单个服务文件

以 `prescriptionExplanationService.js` 为例：

#### 重构前（152行代码）：
```javascript
const axios = require('axios');
// ... 大量重复的API配置和调用代码
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
const GEMINI_MODEL = process.env.GEMINI_MODEL || 'gemini-2.0-flash';
// ... 复杂的提示词构建
// ... 手动的API调用和错误处理
```

#### 重构后（约30-40行代码）：
```javascript
const aiService = require('../utils/aiService');
const MedicalPromptTemplates = require('../prompts/medicalPrompts');

class PrescriptionExplanationService {
    static async explainWithAI(prescription, language = 'zh') {
        try {
            // 构建提示数据
            const promptData = {
                id: prescription.id,
                datePrescribed: prescription.datePrescribed,
                providerName: prescription.providerName,
                details: prescription.details,
                comments: prescription.comments
            };

            // 使用模板构建提示词
            const prompt = MedicalPromptTemplates.buildPrompt('prescription', language, promptData);

            // 调用统一AI服务
            const result = await aiService.generatePrescriptionExplanation(prompt, prescription.id, language);

            if (result.success) {
                // 保存到数据库
                await PrescriptionExplanationDAO.createOrUpdate(
                    prescription.id,
                    result.content,
                    language
                );
                return { success: true, explanation: result.content };
            } else {
                return { success: false, message: result.error };
            }
        } catch (error) {
            return { success: false, message: error.message };
        }
    }
}
```

### 步骤3：重构检查清单

对于每个文件，确保：

#### 🔍 删除的重复代码
- [ ] 删除 `const GEMINI_API_KEY = process.env.GEMINI_API_KEY`
- [ ] 删除 `const GEMINI_MODEL = process.env.GEMINI_MODEL || 'gemini-2.0-flash'`
- [ ] 删除手动的axios API调用代码
- [ ] 删除硬编码的配置（temperature, topK等）
- [ ] 删除重复的错误处理逻辑

#### ➕ 添加的导入
- [ ] 添加 `const aiService = require('../utils/aiService')`
- [ ] 添加 `const MedicalPromptTemplates = require('../prompts/medicalPrompts')`

#### 🔄 重构的方法
- [ ] 将原始的AI调用方法改为使用统一服务
- [ ] 将硬编码的提示词移到模板系统
- [ ] 简化错误处理（依赖统一服务的处理）
- [ ] 添加适当的缓存键生成

### 步骤4：特殊文件处理指南

#### `dieticianService.js`
- 保留原有的重试逻辑设计，但使用统一服务的重试机制
- 确保"不要有开场白"的特殊要求在提示词模板中得到体现

#### `auto-fetch-medical-videos.js`
- 视频评估是简单的"是/否"判断，使用特殊的提示词模板
- 配置 `maxOutputTokens: 10` 的特殊设置

#### `chatbot-service/server.js`
- 这是一个独立的服务，需要复制AI配置和核心服务类
- 或者考虑将AI服务作为共享的npm包

## 📊 预期的改进效果

### 代码减少量
- `prescriptionExplanationService.js`: 152行 → ~40行 (74%减少)
- `immunizationExplanationService.js`: 172行 → ~45行 (74%减少)  
- `dieticianService.js`: 169行 → ~35行 (79%减少)
- `appointmentNoteService.js`: 475行 → ~150行 (68%减少) ✅

### 总计效果
- **总代码减少**: 约968行 → 约270行 (72%减少)
- **维护文件减少**: 从8个分散的AI实现 → 1个统一服务
- **配置管理**: 从8个地方的重复配置 → 1个集中配置

## 🧪 测试验证

### 功能测试
每个重构后的服务都应该：
1. 保持相同的输入输出接口
2. 生成相同质量的AI内容
3. 正确处理错误情况

### 性能测试
- 缓存命中率应该 > 30%
- API调用减少（由于缓存）
- 响应时间保持或改善

### 监控验证
使用新的监控端点：
```bash
# 检查AI服务健康状况
GET /api/ai-monitor/health

# 查看性能指标
GET /api/ai-monitor/metrics

# 清理缓存
POST /api/ai-monitor/cache/cleanup
```

## 🚨 注意事项

### 1. 渐进式重构
- 一次重构一个文件
- 每次重构后进行测试
- 确保原有功能不受影响

### 2. 提示词迁移
- 仔细比较重构前后的提示词
- 确保所有语言版本都正确迁移
- 保持相同的输出格式和质量

### 3. 缓存策略
- 为每种服务设置合适的缓存TTL
- 医疗笔记：1小时
- 处方解释：2小时
- 视频评估：24小时

### 4. 错误处理
- 统一服务已包含重试逻辑
- 保留业务特定的错误处理
- 确保用户友好的错误消息

## 📈 下一步计划

### 短期目标（1周内）
1. 重构 `prescriptionExplanationService.js`
2. 重构 `immunizationExplanationService.js`
3. 重构 `dieticianService.js`

### 中期目标（2周内）
1. 重构视频评估脚本
2. 实现Redis缓存（替代内存缓存）
3. 添加更详细的监控指标

### 长期目标（1个月内）
1. 重构chatbot服务
2. 实现A/B测试框架
3. 添加提示词版本控制
4. 性能优化和调优

## 🎉 完成标志

当所有服务重构完成后，你应该能够：
- 在单一位置管理所有AI配置
- 在单一位置更新提示词
- 通过监控端点查看整体AI使用情况
- 享受更快的开发速度和更低的维护成本 