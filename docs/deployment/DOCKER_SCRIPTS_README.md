# Docker 维护脚本使用指南

本项目提供了多个Docker维护脚本，用于简化容器管理和重建流程。

## 脚本概览

### 1. `quick_rebuild.sh` - 快速重建脚本 ⚡
最简单的重建脚本，执行基本的维护操作。

```bash
./quick_rebuild.sh
```

**执行的操作：**
- `docker compose down` - 停止并移除容器
- `docker image prune -a -f` - 清理未使用的镜像
- `docker compose up -d --build` - 重新构建并启动服务

### 2. `rebuild_docker.sh` - 高级重建脚本 🔧
提供更多选项和控制的重建脚本。

```bash
# 基本用法
./rebuild_docker.sh                    # 快速重建所有服务
./rebuild_docker.sh -q                 # 快速重建（显式指定）
./rebuild_docker.sh -c                 # 深度清理后重建
./rebuild_docker.sh -s backend         # 只重建后端服务
./rebuild_docker.sh -q -l              # 快速重建并显示日志
./rebuild_docker.sh --no-cache         # 重建时不使用缓存
```

**选项说明：**
- `-h, --help` - 显示帮助信息
- `-q, --quick` - 快速重建模式
- `-c, --clean` - 深度清理模式
- `-s, --service` - 指定特定服务 (backend|frontend|chatbot-service)
- `-l, --logs` - 重建后显示日志
- `--no-cache` - 构建时不使用缓存

### 3. `docker_maintenance.sh` - 完整维护脚本 🛠️
功能最全面的维护脚本，提供详细的控制选项。

```bash
# 基本用法
./docker_maintenance.sh                           # 使用默认设置
./docker_maintenance.sh -f docker-compose.prod.yml # 使用指定的compose文件
./docker_maintenance.sh --no-prune               # 跳过镜像清理
./docker_maintenance.sh --no-build               # 跳过重新构建
./docker_maintenance.sh -q                       # 静默模式
```

**选项说明：**
- `-h, --help` - 显示帮助信息
- `-f, --file` - 指定docker-compose文件
- `-q, --quiet` - 静默模式，减少输出
- `--no-prune` - 跳过镜像清理步骤
- `--no-build` - 跳过重新构建，直接启动现有镜像

## 使用场景

### 🚀 日常开发重建
当你修改了代码并需要快速重建时：
```bash
./quick_rebuild.sh
```

### 🔧 指定服务重建
当只需要重建特定服务时：
```bash
./rebuild_docker.sh -s backend
```

### 🧹 深度清理
当遇到构建问题或需要彻底清理时：
```bash
./rebuild_docker.sh -c
```

### 📋 调试模式
重建后立即查看日志：
```bash
./rebuild_docker.sh -q -l
```

### 🔒 生产环境
使用特定的compose文件：
```bash
./docker_maintenance.sh -f docker-compose.prod.yml
```

## 脚本特性

### ✅ 安全特性
- 所有脚本都包含错误处理 (`set -e`)
- 提供确认提示（除非使用静默模式）
- 彩色输出便于识别不同类型的信息

### 🎨 用户体验
- 彩色输出和emoji图标
- 清晰的步骤说明
- 进度指示

### 🛡️ 错误处理
- 自动检测Docker是否运行
- 验证compose文件是否存在
- 验证服务名称是否有效

## 常见问题

### Q: 脚本执行权限问题
```bash
chmod +x *.sh
```

### Q: Docker权限问题
确保当前用户在docker组中：
```bash
sudo usermod -aG docker $USER
# 然后重新登录
```

### Q: 如何查看所有可用服务
```bash
docker compose config --services
```

### Q: 如何只清理镜像不重建
```bash
docker image prune -a -f
```

## 注意事项

⚠️ **重要提醒：**
- 深度清理模式会删除所有未使用的Docker资源
- 在生产环境使用前请先在测试环境验证
- 建议在执行前备份重要数据

## 脚本维护

这些脚本位于项目根目录，可以根据项目需求进行自定义修改。主要的配置项包括：
- 服务名称列表
- 默认compose文件路径
- 日志显示行数
- 清理策略 