import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
    Box,
    Drawer,
    Typography,
    List,
    ListItem,
    ListItemButton,
    ListItemIcon,
    ListItemText,
    Divider,
    useTheme,
    alpha,
    <PERSON>lapse,
    Button,
    Toolbar} from '@mui/material';
import VideocamIcon from '@mui/icons-material/Videocam';
import PersonIcon from '@mui/icons-material/Person';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import DescriptionIcon from '@mui/icons-material/Description';
import CardMembershipIcon from '@mui/icons-material/CardMembership';
// import DashboardIcon from '@mui/icons-material/Dashboard';
import SettingsIcon from '@mui/icons-material/Settings';
import AssignmentIndIcon from '@mui/icons-material/AssignmentInd';
import VaccinesIcon from '@mui/icons-material/Vaccines';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import HealthAndSafetyIcon from '@mui/icons-material/HealthAndSafety';
import FamilyRestroomIcon from '@mui/icons-material/FamilyRestroom';
import ExpandLess from '@mui/icons-material/ExpandLess';
import ExpandMore from '@mui/icons-material/ExpandMore';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import CardGiftcardIcon from '@mui/icons-material/CardGiftcard';
// import TipsAndUpdatesIcon from '@mui/icons-material/TipsAndUpdates';
// import VideocamIcon from '@mui/icons-material/Videocam';
import VideoLibraryIcon from '@mui/icons-material/VideoLibrary';
import RestaurantMenuIcon from '@mui/icons-material/RestaurantMenu';
import StoreIcon from '@mui/icons-material/Store';
import { useView } from '../context/ViewContext';
import { useLanguage } from '../context/LanguageContext';
import { useThemeMode } from '../context/ThemeContext';
import HelpIcon from '@mui/icons-material/Help';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';
import OndemandVideoIcon from '@mui/icons-material/OndemandVideo';
import SupervisedUserCircleIcon from '@mui/icons-material/SupervisedUserCircle';
// import DashboardCustomizeIcon from '@mui/icons-material/DashboardCustomize';

// At the top of the file, add a style tag with direct CSS
const sidebarStyle = document.createElement('style');
sidebarStyle.textContent = `
  @media (min-width: 600px) {
    .MuiDrawer-root.MuiDrawer-permanent {
      display: block !important;
      visibility: visible !important;
    }
  }
`;
document.head.appendChild(sidebarStyle);

// Sidebar component now acts as the Navigation Drawer
// Accepts props from App.js to control responsiveness
function Sidebar({ drawerWidth, mobileOpen, handleDrawerToggle }) {
    const [expandedIndex, setExpandedIndex] = useState(null);
    const [openAdminMenu, setOpenAdminMenu] = useState(false);

    const { loggedInUser, viewingDemographicNo, isViewingOwnProfile, setViewAs, toggleChat } = useView();
    const { language, toggleLanguage, t } = useLanguage();
    const { themeMode, toggleTheme, isDarkMode } = useThemeMode();

    const navigate = useNavigate();
    const location = useLocation();
    const theme = useTheme();

    // Navigation items grouped by category with translation keys
    const navGroups = [
        // 只有admin用户才会显示的管理菜单
        // ...(isAdmin() ? [{
        //     title: '系统管理',
        //     items: [
        //         { label: '管理控制台', icon: <AdminPanelSettingsIcon color="secondary" />, path: '/admin' },
        //         { label: '客户搜索', icon: <SupervisedUserCircleIcon color="secondary" />, path: '/admin-client-search' },
        //         { label: 'YouTube管理', icon: <VideoLibraryIcon color="secondary" />, path: '/admin/youtube' },
        //         { label: '用户管理', icon: <PeopleIcon color="secondary" />, path: '/admin/users' },
        //         { label: '系统设置', icon: <ManageAccountsIcon color="secondary" />, path: '/admin/settings' }
        //     ]
        // }] : []),
        {
            title: t('basic_info'),
            items: [
                { label: t('profile'), icon: <PersonIcon color="primary" />, path: '/profile' },
                { label: t('family_members'), icon: <FamilyRestroomIcon color="primary" />, path: '/family' },
            ]
        },
        {
            title: t('appointment_mgmt'),
            items: [
                { label: t('booking'), icon: <AddCircleOutlineIcon color="primary" />, path: '/booking' },
                { label: t('appointments'), icon: <CalendarMonthIcon color="primary" />, path: '/appointments' },
                // { label: t('video_conference'), icon: <VideocamIcon color="primary" />, path: '/video-conference' }, // Temporarily hidden
            ]
        },
        {
            title: t('health_records'),
            items: [
                { label: t('lab_reports'), icon: <DescriptionIcon color="primary" />, path: '/lab-reports' },
                { label: t('prescriptions'), icon: <AssignmentIndIcon color="primary" />, path: '/prescriptions' },
                { label: t('immunizations'), icon: <VaccinesIcon color="primary" />, path: '/immunizations' },
                { label: t('dietician'), icon: <RestaurantMenuIcon color="primary" />, path: '/dietician' },
                // { label: '健康报告分析', icon: <SmartToyIcon color="primary" />, path: '/health-report-analyzer' },
            ]
        },
        {
            title: t('membership_services'),
            items: [
                { label: t('membership'), icon: <CardMembershipIcon color="primary" />, path: '/membership' },
                { label: t('mmc_health_store') || 'MMC健康商城', icon: <StoreIcon color="primary" />, path: '/store' },
                { label: 'MMC健康指南', icon: <HealthAndSafetyIcon color="primary" />, path: '/health-guide' },
                { label: '健康小贴士', icon: <HelpIcon color="primary" />, path: '/health-tips' },
                { label: 'YouTube视频', icon: <VideoLibraryIcon color="primary" />, path: '/youtube-videos' },
                { label: t('health_assistant'), icon: <SmartToyIcon color="primary" />, action: toggleChat },
            ]
        },
        {
            title: t('system_settings'),
            items: [
                { label: t('settings'), icon: <SettingsIcon color="primary" />, path: '/settings' },
            ]
        }
    ];

    const adminMenuItems = [
        { text: '客户档案搜索', icon: <SupervisedUserCircleIcon />, path: '/admin/client-search' },
        { text: 'YouTube管理', icon: <OndemandVideoIcon />, path: '/admin/youtube' },
        { text: '商城管理', icon: <StoreIcon />, path: '/admin/store' },
        { text: '健康指南管理', icon: <HealthAndSafetyIcon />, path: '/admin/health-guides' },
        { text: 'AI管理中心', icon: <SmartToyIcon />, path: '/admin/ai-dashboard' },
        // Add other admin links here if needed
    ];

    const handleItemClick = (item) => {
        if (item.action) {
            item.action();
        } else if (item.path) {
            // Check if we're currently viewing a specific client (non-self)
            const isViewingOtherClient = viewingDemographicNo && 
                loggedInUser && 
                viewingDemographicNo !== loggedInUser.demographic_no &&
                loggedInUser.role === 'admin';
            
            if (isViewingOtherClient) {
                // For certain paths that support demographic parameter, append it
                const pathsWithDemographicSupport = [
                    '/profile', '/appointments', '/membership', '/lab-reports', 
                    '/prescriptions', '/immunizations', '/booking', '/dietician', 
                    '/video-conference', '/family', '/renew-membership'
                ];
                
                if (pathsWithDemographicSupport.includes(item.path)) {
                    navigate(`${item.path}/${viewingDemographicNo}`);
                } else {
                    navigate(item.path);
                }
            } else {
                navigate(item.path);
            }
        }
        if (mobileOpen) {
            handleDrawerToggle();
        }
    };

    // Update toggle handler for exclusive accordion
    const handleToggleGroup = (index) => {
        setExpandedIndex(prevIndex => (prevIndex === index ? null : index));
    };

    const handleSwitchBack = () => {
        if (loggedInUser?.demographic_no) {
            setViewAs(loggedInUser.demographic_no);
        }
    };

    // Check if path is active - improved to handle demographic parameters
    const isActive = (path) => {
        if (!path) return false;
        
        // Check exact match first
        if (location.pathname === path) return true;
        
        // Check if current path starts with the base path and has demographic parameter
        const pathWithSlash = path.endsWith('/') ? path : path + '/';
        const currentPath = location.pathname;
        
        // For paths like /profile/123, /appointments/456, etc.
        if (currentPath.startsWith(pathWithSlash) && /\/\d+$/.test(currentPath)) {
            return true;
        }
        
        return false;
    };

    // Create the sidebar content
    const drawerContent = (
        <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
            <Toolbar sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 1 }}>
                <img src={`${process.env.PUBLIC_URL}/assets/images/mmc-logo.png`} alt="MMC Logo" style={{ height: 40, cursor: 'pointer' }} onClick={() => navigate('/profile')} />
            </Toolbar>
            <Divider />

            {!isViewingOwnProfile && (
                <Box sx={{ p: 2, bgcolor: alpha(theme.palette.primary.main, 0.1) }}>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                        {t('viewing_family_member')}
                    </Typography>
                    <Button
                        variant="outlined"
                        size="small"
                        onClick={handleSwitchBack}
                        startIcon={<PersonIcon />}
                        fullWidth
                    >
                        {t('return_to_profile')}
                    </Button>
                </Box>
            )}

            <Box sx={{ overflowY: 'auto', flexGrow: 1 }}>
                {navGroups.map((group, groupIndex) => (
                    <React.Fragment key={group.title + groupIndex}>
                        <ListItemButton
                            onClick={() => handleToggleGroup(groupIndex)}
                            sx={{
                                px: 2, py: 1,
                            }}
                        >
                            <ListItemText
                                primary={group.title}
                                primaryTypographyProps={{ variant: 'overline', sx: { fontWeight: 'bold', color: 'text.secondary' } }}
                            />
                            {expandedIndex === groupIndex ? <ExpandLess /> : <ExpandMore />}
                        </ListItemButton>
                        <Collapse in={expandedIndex === groupIndex} timeout="auto" unmountOnExit>
                            <List component="div" disablePadding>
                                {group.items.map((item) => (
                                    <ListItem key={item.label} disablePadding sx={{ display: 'block' }}>
                                        <ListItemButton
                                            sx={{
                                                minHeight: 48,
                                                justifyContent: 'initial',
                                                px: 2.5,
                                                pl: 4,
                                                bgcolor: isActive(item.path) ? alpha(theme.palette.primary.main, 0.1) : 'transparent',
                                                '&:hover': {
                                                    bgcolor: isActive(item.path) ? alpha(theme.palette.primary.main, 0.15) : alpha(theme.palette.action.hover, 0.04),
                                                },
                                                borderLeft: isActive(item.path) ? `4px solid ${theme.palette.primary.main}` : 'none',
                                            }}
                                            onClick={() => handleItemClick(item)}
                                            selected={isActive(item.path)}
                                        >
                                            <ListItemIcon
                                                sx={{
                                                    minWidth: 0,
                                                    mr: 3,
                                                    justifyContent: 'center',
                                                    color: isActive(item.path) ? theme.palette.primary.main : theme.palette.action.active
                                                }}
                                            >
                                                {item.icon}
                                            </ListItemIcon>
                                            <ListItemText primary={item.label} sx={{ opacity: 1 }} primaryTypographyProps={{ variant: 'body2' }} />
                                        </ListItemButton>
                                    </ListItem>
                                ))}
                            </List>
                        </Collapse>
                    </React.Fragment>
                ))}

                {/* Admin Menu Section */}
                {loggedInUser && loggedInUser.role === 'admin' && (
                    <>
                        <ListItem disablePadding sx={{ display: 'block' }} onClick={() => setOpenAdminMenu(!openAdminMenu)}>
                            <ListItemButton
                                sx={{
                                    minHeight: 48,
                                    justifyContent: 'initial',
                                    px: 2.5,
                                    borderRadius: '8px',
                                    margin: '4px 8px',
                                }}
                            >
                                <ListItemIcon
                                    sx={{
                                        minWidth: 0,
                                        mr: 3,
                                        justifyContent: 'center',
                                    }}
                                >
                                    <AdminPanelSettingsIcon />
                                </ListItemIcon>
                                <ListItemText primary={t('admin_panel')} sx={{ opacity: 1 }} />
                                {openAdminMenu ? <ExpandLess /> : <ExpandMore />}
                            </ListItemButton>
                        </ListItem>
                        <Collapse in={openAdminMenu} timeout="auto" unmountOnExit>
                            <List component="div" disablePadding>
                                {adminMenuItems.map((item) => (
                                    <ListItemButton
                                        key={item.text}
                                        selected={isActive(item.path)}
                                        onClick={() => handleItemClick(item)}
                                        sx={{
                                            pl: 4, // Indent admin items
                                            minHeight: 48,
                                            borderRadius: '8px',
                                            margin: '4px 8px',
                                            '&.Mui-selected': {
                                                backgroundColor: theme.palette.action.selected,
                                                color: theme.palette.primary.main,
                                            },
                                        }}
                                    >
                                        <ListItemIcon
                                            sx={{
                                                minWidth: 0,
                                                mr: 3,
                                                justifyContent: 'center',
                                                color: isActive(item.path) ? theme.palette.primary.main : 'inherit',
                                            }}
                                        >
                                            {item.icon}
                                        </ListItemIcon>
                                        <ListItemText primary={item.text} sx={{ opacity: 1 }} />
                                    </ListItemButton>
                                ))}
                            </List>
                        </Collapse>
                    </>
                )}
            </Box>
        </Box>
    );

    return (
        <Box
            component="nav"
            sx={{
                width: { sm: drawerWidth },
                flexShrink: { sm: 0 },
                display: { xs: 'block', sm: 'block' } // Ensure sidebar is visible
            }}
            aria-label="mailbox folders"
        >
            <Drawer
                variant="temporary"
                open={mobileOpen}
                onClose={handleDrawerToggle}
                ModalProps={{
                    keepMounted: true,
                }}
                sx={{
                    display: { xs: 'block', sm: 'none' },
                    '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
                }}
            >
                {drawerContent}
            </Drawer>
            <Drawer
                variant="permanent"
                sx={{
                    display: { xs: 'none', sm: 'block' },
                    '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
                    visibility: 'visible' // Ensure drawer is visible
                }}
                open
            >
                {drawerContent}
            </Drawer>
        </Box>
    );
}

export default Sidebar;