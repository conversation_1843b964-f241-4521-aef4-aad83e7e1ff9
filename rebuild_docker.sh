#!/bin/bash

# MMC Wellness Docker 重建脚本
# 提供多种重建和维护选项

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 显示帮助信息
show_help() {
    echo "MMC Wellness Docker 重建脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help      显示此帮助信息"
    echo "  -q, --quick     快速重建 (down + prune + up --build)"
    echo "  -c, --clean     深度清理 + 重建"
    echo "  -s, --service   指定服务 (backend|frontend|chatbot-service)"
    echo "  -l, --logs      重建后显示日志"
    echo "  --no-cache      构建时不使用缓存"
    echo ""
    echo "示例:"
    echo "  $0 -q                    # 快速重建所有服务"
    echo "  $0 -s backend            # 只重建后端服务"
    echo "  $0 -c                    # 深度清理后重建"
    echo "  $0 -q -l                 # 快速重建并显示日志"
}

# 快速重建函数
quick_rebuild() {
    local service=$1
    local no_cache=$2
    
    echo -e "${BLUE}🚀 开始快速重建...${NC}"
    
    if [[ -n "$service" ]]; then
        echo -e "${YELLOW}📦 停止服务: $service${NC}"
        docker compose stop "$service"
        
        echo -e "${YELLOW}🔨 重建服务: $service${NC}"
        if [[ "$no_cache" == "true" ]]; then
            docker compose up -d --build --no-cache "$service"
        else
            docker compose up -d --build "$service"
        fi
    else
        echo -e "${YELLOW}📦 停止所有容器...${NC}"
        docker compose down
        
        echo -e "${YELLOW}🧹 清理未使用的镜像...${NC}"
        docker image prune -a -f
        
        echo -e "${YELLOW}🔨 重新构建所有服务...${NC}"
        if [[ "$no_cache" == "true" ]]; then
            docker compose up -d --build --no-cache
        else
            docker compose up -d --build
        fi
    fi
    
    echo -e "${GREEN}✅ 快速重建完成！${NC}"
}

# 深度清理函数
deep_clean_rebuild() {
    local no_cache=$1
    
    echo -e "${RED}🧹 开始深度清理...${NC}"
    
    # 停止所有容器
    echo -e "${YELLOW}📦 停止所有容器...${NC}"
    docker compose down
    
    # 清理容器
    echo -e "${YELLOW}🗑️  清理停止的容器...${NC}"
    docker container prune -f
    
    # 清理镜像
    echo -e "${YELLOW}🖼️  清理所有未使用的镜像...${NC}"
    docker image prune -a -f
    
    # 清理网络
    echo -e "${YELLOW}🌐 清理未使用的网络...${NC}"
    docker network prune -f
    
    # 清理构建缓存
    echo -e "${YELLOW}💾 清理构建缓存...${NC}"
    docker builder prune -f
    
    # 重新构建
    echo -e "${YELLOW}🔨 重新构建所有服务...${NC}"
    if [[ "$no_cache" == "true" ]]; then
        docker compose up -d --build --no-cache
    else
        docker compose up -d --build
    fi
    
    echo -e "${GREEN}✅ 深度清理重建完成！${NC}"
}

# 显示日志函数
show_logs() {
    local service=$1
    
    echo -e "${BLUE}📋 显示日志 (按 Ctrl+C 退出)...${NC}"
    if [[ -n "$service" ]]; then
        docker compose logs -f "$service"
    else
        docker compose logs -f
    fi
}

# 检查服务状态
check_status() {
    echo -e "${YELLOW}📊 检查服务状态...${NC}"
    docker compose ps
    echo ""
    echo -e "${YELLOW}💾 Docker 系统使用情况:${NC}"
    docker system df
}

# 默认参数
QUICK_MODE=false
CLEAN_MODE=false
SHOW_LOGS=false
NO_CACHE=false
SERVICE=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -q|--quick)
            QUICK_MODE=true
            shift
            ;;
        -c|--clean)
            CLEAN_MODE=true
            shift
            ;;
        -s|--service)
            SERVICE="$2"
            shift 2
            ;;
        -l|--logs)
            SHOW_LOGS=true
            shift
            ;;
        --no-cache)
            NO_CACHE=true
            shift
            ;;
        *)
            echo -e "${RED}未知参数: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# 验证服务名称
if [[ -n "$SERVICE" ]]; then
    case $SERVICE in
        backend|frontend|chatbot-service)
            ;;
        *)
            echo -e "${RED}错误: 无效的服务名称 '$SERVICE'${NC}"
            echo -e "${YELLOW}可用服务: backend, frontend, chatbot-service${NC}"
            exit 1
            ;;
    esac
fi

# 如果没有指定模式，默认使用快速模式
if [[ "$QUICK_MODE" == "false" && "$CLEAN_MODE" == "false" ]]; then
    QUICK_MODE=true
fi

# 显示操作信息
echo -e "${BLUE}=== MMC Wellness Docker 重建工具 ===${NC}"
echo -e "${YELLOW}工作目录: $(pwd)${NC}"

if [[ "$CLEAN_MODE" == "true" ]]; then
    echo -e "${YELLOW}模式: 深度清理重建${NC}"
else
    echo -e "${YELLOW}模式: 快速重建${NC}"
fi

if [[ -n "$SERVICE" ]]; then
    echo -e "${YELLOW}目标服务: $SERVICE${NC}"
else
    echo -e "${YELLOW}目标服务: 所有服务${NC}"
fi

echo ""

# 执行操作
if [[ "$CLEAN_MODE" == "true" ]]; then
    deep_clean_rebuild "$NO_CACHE"
else
    quick_rebuild "$SERVICE" "$NO_CACHE"
fi

# 检查状态
check_status

# 显示日志
if [[ "$SHOW_LOGS" == "true" ]]; then
    echo ""
    show_logs "$SERVICE"
fi

echo -e "${GREEN}🎉 所有操作完成！${NC}" 