# Guide: Adding a New Feature (e.g., Dietician)

This guide outlines the general steps and files involved when adding a new feature to the MMC Wellness application, using the "Dietician" feature as an example.

## I. Backend Development

### 1. Database Access Object (DAO)
   - **Purpose**: Handles direct database interactions (queries, updates).
   - **Action**: Create or modify a DAO file.
   - **Example File**: `backend/src/dao/formDieticianDAO.js`
   - **Details**:
     - Define functions to fetch, create, update, or delete data related to the new feature.
     - Ensure proper SQL queries and error handling.

### 2. Controller
   - **Purpose**: Contains the business logic for the feature. It uses the DAO to interact with the database and processes data before sending it to the frontend.
   - **Action**: Create a new controller file.
   - **Example File**: `backend/src/controllers/formDieticianController.js`
   - **Details**:
     - Define functions to handle requests from the frontend.
     - Call appropriate DAO functions.
     - Format responses and handle errors.

### 3. Routes
   - **Purpose**: Defines the API endpoints for the new feature.
   - **Action**: Create a new routes file.
   - **Example File**: `backend/src/routes/formDieticianRoutes.js`
   - **Details**:
     - Define HTTP methods (GET, POST, PUT, DELETE) and corresponding controller functions.
     - **Crucially, add authentication middleware (e.g., `auth`) to protect the endpoints.**
     - Ensure routes are prefixed appropriately (e.g., `/api/your-feature-route`) when registered in the main router.

### 4. Register Routes
   - **Purpose**: Make the new feature's routes accessible through the main API.
   - **Action**: Modify the main router file.
   - **File**: `backend/src/routes/index.js`
   - **Details**:
     - Import the new routes file.
     - Use `router.use('/feature-base-path', newFeatureRoutes);` (e.g., `/formDietician`). This path will be appended to the global API prefix (e.g., `/api`) set in `backend/src/index.js`. So the full path becomes `/api/feature-base-path`.

### 5. (Optional) Database Migrations
   - **Purpose**: If the new feature requires new database tables or modifications to existing ones.
   - **Action**: Create SQL migration scripts.
   - **Example Directory**: `backend/src/database/migrations/`
   - **Details**:
     - Write SQL scripts for creating/altering tables.
     - Ensure you have a way to run these migrations (e.g., a script like `backend/src/database/run-migration.js`).

## II. Frontend Development

### 1. Service
   - **Purpose**: Handles API calls from the frontend to the backend for the new feature.
   - **Action**: Create a new service file.
   - **Example File**: `frontend/src/services/dieticianService.js`
   - **Details**:
     - Define functions that use `axios` to make requests.
     - **API URL Construction is CRITICAL**:
       - The global `axios.defaults.baseURL` is typically set in `frontend/src/utils/env.js` (e.g., to `https://app-backend.mmcwellness.ca` or `https://app-backend.mmcwellness.ca/api` via `API_URL`).
       - **If `axios.defaults.baseURL` already includes the common API prefix (e.g., `/api`)**:
         Your service calls should use paths relative to that, e.g., `axios.get('/formDietician/123')`.
       - **If `axios.defaults.baseURL` is just the hostname (e.g., `https://app-backend.mmcwellness.ca`) and does NOT include `/api` (and other services work this way)**:
         Your service call must explicitly include the `/api` prefix, e.g., `axios.get('/api/formDietician/123')`.
       - **Verification**: Add temporary `console.log(axios.defaults.baseURL)` in your component or service to confirm its value at runtime if unsure.
     - **Example for Dietician (assuming baseURL is `https://hostname` and does not include `/api`):**
       ```javascript
       // frontend/src/services/dieticianService.js
       import axios from 'axios';
       export async function getDieticianComments(demographic_no) {
           const response = await axios.get(`/api/formDietician/${demographic_no}`);
           return response.data;
       }
       ```

### 2. Component
   - **Purpose**: Creates the user interface for the new feature.
   - **Action**: Create a new React component file.
   - **Example File**: `frontend/src/components/DieticianPage.js`
   - **Details**:
     - Use Material UI components for layout and styling.
     - Fetch data using the service created in Frontend Step 1 (typically within a `useEffect` hook).
     - Display data, loading states, and error messages.
     - Use the `useLanguage` hook for translations.

### 3. Add to Sidebar (if applicable)
   - **Purpose**: Make the new feature accessible from the main navigation.
   - **Action**: Modify the Sidebar component.
   - **File**: `frontend/src/components/Sidebar.js`
   - **Details**:
     - Import a suitable Material UI icon for the new menu item.
     - Add a new entry to the `navGroups` array, specifying the label (using `t()` for translation), icon, and path.

### 4. Add Translations
   - **Purpose**: Provide text for the new feature in supported languages.
   - **Action**: Modify the language context file.
   - **File**: `frontend/src/context/LanguageContext.js`
   - **Details**:
     - Add new translation keys and their corresponding translated strings in both English (`en`) and Chinese (`zh`) sections (or any other supported languages).
     - For example, for "Dietician", add `\'dietician\': \'Dietician\'` to `en` and `\'dietician\': \'营养师\'` to `zh`.

### 5. Add Route
   - **Purpose**: Make the new component accessible via a URL.
   - **Action**: Modify the main application routing file.
   - **File**: `frontend/src/App.js`
   - **Details**:
     - Import the new component created in Frontend Step 2.
     - Add a new `<Route>` within the `<Routes>` definition, mapping a path to the new component (e.g., `<Route path="/dietician" element={<DieticianPage />} />`).
     - Ensure the route is placed within the appropriate layout (e.g., `AppLayout` if it requires the sidebar and top bar).

## III. General Considerations

*   **Error Handling**: Implement robust error handling on both backend and frontend.
*   **Loading States**: Show loading indicators on the frontend while data is being fetched.
*   **Permissions**: Ensure backend endpoints correctly check user permissions if the data is sensitive or user-specific (the `auth` middleware is a first step, but more granular checks might be needed in controllers or services).
*   **Code Style and Conventions**: Follow existing code style and project conventions.
*   **Testing**: Consider adding tests for new backend and frontend logic.

### Debugging Tips for API Connectivity
*   **Browser Developer Tools**:
    *   **Network Tab**: Check the exact URL being requested, the HTTP method, status code, and response payload. This is crucial for seeing what the frontend is *actually* requesting.
    *   **Console Tab**: Look for JavaScript errors, `console.log` outputs (like the `axios.defaults.baseURL` check), and error messages from API calls.
*   **Backend Logs**: Check the logs of your backend application (and Nginx proxy if applicable) to see if the request is reaching the backend and if there are any errors there.
*   **`curl`**: Use `curl` from your terminal to test backend API endpoints directly. This helps isolate whether the issue is with the backend itself or the frontend's request.
    *   Remember to include `Content-Type` headers for POST/PUT requests.
    *   Include authentication tokens (`Authorization: Bearer YOUR_TOKEN`) if the endpoint is protected.
    *   Example: `curl -X GET https://app-backend.mmcwellness.ca/api/formDietician/2 -H "Authorization: Bearer <token>"`
*   **Environment Variables**: Double-check environment variables like `REACT_APP_API_URL` in your frontend build environment and `.env` files. These can override default configurations and affect `axios.defaults.baseURL`.
*   **Caching**: Clear browser cache and perform hard refreshes (Ctrl+Shift+R or Cmd+Shift+R) after frontend changes. Restart development servers.

This guide provides a general framework. The specific files and complexity will vary based on the requirements of the new feature. 