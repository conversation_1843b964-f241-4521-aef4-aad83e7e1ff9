const dieticianService = require('../services/dieticianService');
const pool = require('../config/database');

async function testDieticianSummary() {
    try {
        console.log('Testing Dietician AI Summary functionality...');
        
        // Get a sample comment
        const [comments] = await pool.query(
            'SELECT ID as dietician_comment_id, comments, demographic_no, consultTime as entry_date FROM formDietician WHERE ID = 4'
        );
        
        if (comments.length === 0) {
            console.log('No dietician comment found with ID 4');
            return;
        }
        
        const commentData = comments[0];
        console.log('Testing with comment data:', {
            id: commentData.dietician_comment_id,
            demographic_no: commentData.demographic_no,
            entry_date: commentData.entry_date,
            comment_length: commentData.comments?.length || 0
        });
        
        // Test AI summary generation
        console.log('\n--- Testing AI Summary Generation ---');
        const result = await dieticianService.summarizeCommentWithAI(commentData, 'zh');
        
        if (result.success) {
            console.log('✅ AI Summary generated successfully!');
            console.log('Summary length:', result.summary.length);
            console.log('Summary preview:', result.summary.substring(0, 200) + '...');
        } else {
            console.log('❌ AI Summary generation failed:', result.message);
            if (result.error) {
                console.log('Error details:', result.error);
            }
        }
        
    } catch (error) {
        console.error('Test failed with error:', error);
    } finally {
        await pool.end();
    }
}

testDieticianSummary(); 