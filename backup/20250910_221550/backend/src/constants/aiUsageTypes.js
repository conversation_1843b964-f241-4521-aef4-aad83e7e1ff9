/**
 * AI 使用统计分类常量定义
 */

// 主要服务类型分类
const SERVICE_CATEGORIES = {
    USER_INTERACTION: 'user_interaction',
    CONTENT_GENERATION: 'content_generation',
    MEDICAL_ASSISTANCE: 'medical_assistance',
    SYSTEM_MANAGEMENT: 'system_management'
};

// 服务类别名称映射
const SERVICE_CATEGORY_NAMES = {
    [SERVICE_CATEGORIES.USER_INTERACTION]: '用户交互',
    [SERVICE_CATEGORIES.CONTENT_GENERATION]: '内容生成',
    [SERVICE_CATEGORIES.MEDICAL_ASSISTANCE]: '医疗辅助',
    [SERVICE_CATEGORIES.SYSTEM_MANAGEMENT]: '系统管理'
};

// 详细服务类型定义
const DETAILED_SERVICE_TYPES = {
    // 聊天机器人相关 - 用户交互类
    'chatbot_general': {
        name: '通用健康咨询',
        category: SERVICE_CATEGORIES.USER_INTERACTION,
        description: '用户通过聊天机器人进行一般健康问题咨询'
    },
    'chatbot_mmc_info': {
        name: 'MMC服务介绍',
        category: SERVICE_CATEGORIES.USER_INTERACTION,
        description: '介绍MMC Wellness的服务和信息'
    },
    'chatbot_appointment': {
        name: '预约相关咨询',
        category: SERVICE_CATEGORIES.USER_INTERACTION,
        description: '关于预约、时间安排等的咨询'
    },
    'chatbot_health_tips': {
        name: '健康建议提供',
        category: SERVICE_CATEGORIES.USER_INTERACTION,
        description: '提供个性化健康建议和指导'
    },
    
    // 内容生成相关
    'tips_daily_generation': {
        name: '每日健康贴士生成',
        category: SERVICE_CATEGORIES.CONTENT_GENERATION,
        description: '自动生成每日健康小贴士内容'
    },
    'tips_seasonal': {
        name: '季节性健康提醒',
        category: SERVICE_CATEGORIES.CONTENT_GENERATION,
        description: '生成季节性健康注意事项和提醒'
    },
    'news_health_crawler': {
        name: '健康新闻抓取',
        category: SERVICE_CATEGORIES.CONTENT_GENERATION,
        description: '抓取和整理健康相关新闻内容'
    },
    'news_content_summary': {
        name: '新闻内容总结',
        category: SERVICE_CATEGORIES.CONTENT_GENERATION,
        description: '对健康新闻进行智能总结和提取'
    },
    'article_generation': {
        name: '医疗文章生成',
        category: SERVICE_CATEGORIES.CONTENT_GENERATION,
        description: '生成医疗健康相关的教育文章'
    },
    
    // 医疗辅助相关
    'medical_note_generation': {
        name: '医疗记录生成',
        category: SERVICE_CATEGORIES.MEDICAL_ASSISTANCE,
        description: '辅助生成和整理医疗记录'
    },
    'diagnosis_assistance': {
        name: '诊断辅助',
        category: SERVICE_CATEGORIES.MEDICAL_ASSISTANCE,
        description: '为医生提供诊断参考建议'
    },
    'symptom_analysis': {
        name: '症状分析',
        category: SERVICE_CATEGORIES.MEDICAL_ASSISTANCE,
        description: '分析和评估患者症状描述'
    },
    
    // 系统功能相关
    'content_translation': {
        name: '内容翻译',
        category: SERVICE_CATEGORIES.SYSTEM_MANAGEMENT,
        description: '多语言内容翻译服务'
    },
    'data_extraction': {
        name: '数据提取',
        category: SERVICE_CATEGORIES.SYSTEM_MANAGEMENT,
        description: '从文档或内容中提取结构化数据'
    },
    'quality_check': {
        name: '质量检查',
        category: SERVICE_CATEGORIES.SYSTEM_MANAGEMENT,
        description: '内容质量检查和优化建议'
    }
};

// 使用场景定义
const USE_CASES = {
    PATIENT_INQUIRY: 'patient_inquiry',
    STAFF_ASSISTANCE: 'staff_assistance',
    CONTENT_CREATION: 'content_creation',
    AUTOMATED_TASK: 'automated_task',
    QUALITY_ASSURANCE: 'quality_assurance',
    DATA_PROCESSING: 'data_processing'
};

const USE_CASE_NAMES = {
    [USE_CASES.PATIENT_INQUIRY]: '患者咨询',
    [USE_CASES.STAFF_ASSISTANCE]: '员工辅助',
    [USE_CASES.CONTENT_CREATION]: '内容创作',
    [USE_CASES.AUTOMATED_TASK]: '自动化任务',
    [USE_CASES.QUALITY_ASSURANCE]: '质量保证',
    [USE_CASES.DATA_PROCESSING]: '数据处理'
};

// 用户类型定义
const USER_TYPES = {
    PATIENT: 'patient',
    DOCTOR: 'doctor',
    NURSE: 'nurse',
    ADMIN: 'admin',
    SYSTEM: 'system',
    ANONYMOUS: 'anonymous'
};

const USER_TYPE_NAMES = {
    [USER_TYPES.PATIENT]: '患者',
    [USER_TYPES.DOCTOR]: '医生',
    [USER_TYPES.NURSE]: '护士',
    [USER_TYPES.ADMIN]: '管理员',
    [USER_TYPES.SYSTEM]: '系统自动',
    [USER_TYPES.ANONYMOUS]: '匿名用户'
};

/**
 * 根据服务类型获取服务类别
 */
function getServiceCategory(serviceType) {
    const typeInfo = DETAILED_SERVICE_TYPES[serviceType];
    return typeInfo ? typeInfo.category : SERVICE_CATEGORIES.SYSTEM_MANAGEMENT;
}

/**
 * 根据服务类型获取服务名称
 */
function getServiceTypeName(serviceType) {
    const typeInfo = DETAILED_SERVICE_TYPES[serviceType];
    return typeInfo ? typeInfo.name : serviceType;
}

/**
 * 根据内容分析推断服务类型
 */
function inferServiceType(content, context = {}) {
    if (!content) return 'chatbot_general';
    
    const contentLower = content.toLowerCase();
    
    // MMC相关内容
    if (contentLower.includes('mmc') || contentLower.includes('预约') || contentLower.includes('appointment')) {
        return 'chatbot_mmc_info';
    }
    
    // 健康建议相关
    if (contentLower.includes('建议') || contentLower.includes('tips') || contentLower.includes('健康')) {
        return 'chatbot_health_tips';
    }
    
    // 每日贴士生成
    if (context.isAutomated && (contentLower.includes('daily') || contentLower.includes('每日'))) {
        return 'tips_daily_generation';
    }
    
    // 新闻相关
    if (contentLower.includes('news') || contentLower.includes('新闻')) {
        return 'news_content_summary';
    }
    
    // 默认为通用咨询
    return 'chatbot_general';
}

module.exports = {
    SERVICE_CATEGORIES,
    SERVICE_CATEGORY_NAMES,
    DETAILED_SERVICE_TYPES,
    USE_CASES,
    USE_CASE_NAMES,
    USER_TYPES,
    USER_TYPE_NAMES,
    getServiceCategory,
    getServiceTypeName,
    inferServiceType
}; 