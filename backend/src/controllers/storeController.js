const pool = require('../config/database');
// 延迟加载PaymentService以避免启动时的Stripe错误
let PaymentService = null;
const getPaymentService = () => {
    if (!PaymentService) {
        PaymentService = require('../services/paymentService');
    }
    return PaymentService;
};
const Product = require('../models/Product');
const Order = require('../models/Order');

/**
 * 获取所有商品
 */
const getProducts = async (req, res) => {
    try {
        const filters = {
            category_id: req.query.category_id,
            search: req.query.search,
            limit: req.query.limit
        };

        const result = await Product.getAllProducts(filters);
        
        if (result.success) {
            res.json({
                success: true,
                products: result.products
            });
        } else {
            res.status(500).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        console.error('Error in getProducts:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

/**
 * 获取商品详情
 */
const getProductById = async (req, res) => {
    try {
        const { productId } = req.params;
        const result = await Product.getProductById(productId);
        
        if (result.success) {
            res.json({
                success: true,
                product: result.product
            });
        } else {
            res.status(404).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        console.error('Error in getProductById:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

/**
 * 获取商品分类
 */
const getCategories = async (req, res) => {
    try {
        const result = await Product.getCategories();
        
        if (result.success) {
            res.json({
                success: true,
                categories: result.categories
            });
        } else {
            res.status(500).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        console.error('Error in getCategories:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

/**
 * 创建商品 (管理员功能)
 */
const createProduct = async (req, res) => {
    try {
        // 检查管理员权限
        if (!req.user || req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Access denied. Admin privileges required.'
            });
        }

        const productData = req.body;
        const result = await Product.createProduct(productData);
        
        if (result.success) {
            res.status(201).json({
                success: true,
                product: result.product,
                message: 'Product created successfully'
            });
        } else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        console.error('Error in createProduct:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

/**
 * 更新商品 (管理员功能)
 */
const updateProduct = async (req, res) => {
    try {
        // 检查管理员权限
        if (!req.user || req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Access denied. Admin privileges required.'
            });
        }

        const { productId } = req.params;
        const productData = req.body;
        
        const result = await Product.updateProduct(productId, productData);
        
        if (result.success) {
            res.json({
                success: true,
                product: result.product,
                message: 'Product updated successfully'
            });
        } else {
            res.status(404).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        console.error('Error in updateProduct:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

/**
 * 删除商品 (管理员功能)
 */
const deleteProduct = async (req, res) => {
    try {
        // 检查管理员权限
        if (!req.user || req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Access denied. Admin privileges required.'
            });
        }

        const { productId } = req.params;
        const result = await Product.deleteProduct(productId);
        
        if (result.success) {
            res.json({
                success: true,
                message: 'Product deleted successfully'
            });
        } else {
            res.status(404).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        console.error('Error in deleteProduct:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

/**
 * 创建订单
 */
const createOrder = async (req, res) => {
    try {
        const userId = req.user.id;
        const userDemographicNo = req.user.demographic_no;
        
        const {
            items, // [{ product_id, quantity }]
            billing_address,
            notes
        } = req.body;

        // 验证商品并计算总价
        let total_amount = 0;
        const orderItems = [];

        for (const item of items) {
            const productResult = await Product.getProductById(item.product_id);
            if (!productResult.success) {
                return res.status(400).json({
                    success: false,
                    message: `Product ${item.product_id} not found`
                });
            }

            const product = productResult.product;
            const itemTotal = product.price * item.quantity;
            total_amount += itemTotal;

            orderItems.push({
                product_id: item.product_id,
                quantity: item.quantity,
                price: product.price
            });
        }

        // 创建订单
        const orderData = {
            user_id: userId,
            demographic_no: userDemographicNo,
            items: orderItems,
            total_amount,
            payment_method: 'stripe',
            billing_address,
            notes
        };

        const orderResult = await Order.createOrder(orderData);
        
        if (orderResult.success) {
            res.json({
                success: true,
                orderId: orderResult.orderId,
                orderNumber: orderResult.orderNumber,
                totalAmount: total_amount
            });
        } else {
            res.status(500).json({
                success: false,
                message: orderResult.message
            });
        }
    } catch (error) {
        console.error('Error in createOrder:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

/**
 * 创建支付意图
 */
const createPaymentIntent = async (req, res) => {
    try {
        // 检查Stripe是否可用
        if (!getPaymentService().isStripeAvailable()) {
            return res.status(503).json({
                success: false,
                message: 'Payment service is currently unavailable. Stripe is not configured.',
                error: 'PAYMENT_SERVICE_UNAVAILABLE'
            });
        }

        const { orderId } = req.body;
        const userId = req.user.id;

        // 获取订单详情
        const orderResult = await Order.getOrderById(orderId, userId);
        if (!orderResult.success) {
            return res.status(404).json({
                success: false,
                message: 'Order not found'
            });
        }

        const order = orderResult.order;

        // 创建支付意图
        const paymentResult = await getPaymentService().createPaymentIntent({
            amount: order.total_amount,
            currency: 'cad',
            orderId: order.id,
            customerEmail: req.user.email
        });

        if (paymentResult.success) {
            // 更新订单的支付意图ID
            await pool.query(
                'UPDATE orders SET stripe_payment_intent_id = ? WHERE id = ?',
                [paymentResult.paymentIntentId, orderId]
            );

            res.json({
                success: true,
                clientSecret: paymentResult.clientSecret,
                paymentIntentId: paymentResult.paymentIntentId
            });
        } else {
            res.status(500).json({
                success: false,
                message: paymentResult.message
            });
        }
    } catch (error) {
        console.error('Error in createPaymentIntent:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

/**
 * 获取用户订单
 */
const getUserOrders = async (req, res) => {
    try {
        const userId = req.user.id;
        const filters = {
            status: req.query.status,
            limit: req.query.limit
        };

        const result = await Order.getUserOrders(userId, filters);
        
        if (result.success) {
            res.json({
                success: true,
                orders: result.orders
            });
        } else {
            res.status(500).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        console.error('Error in getUserOrders:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

/**
 * 获取订单详情
 */
const getOrderById = async (req, res) => {
    try {
        const { orderId } = req.params;
        const userId = req.user.id;

        const result = await Order.getOrderById(orderId, userId);
        
        if (result.success) {
            res.json({
                success: true,
                order: result.order
            });
        } else {
            res.status(404).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        console.error('Error in getOrderById:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

/**
 * 处理Stripe Webhook
 */
const handleStripeWebhook = async (req, res) => {
    try {
        const signature = req.headers['stripe-signature'];
        const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

        const verificationResult = getPaymentService().verifyWebhookSignature(
            req.body,
            signature,
            endpointSecret
        );

        if (!verificationResult.success) {
            return res.status(400).json({
                success: false,
                message: verificationResult.message
            });
        }

        const event = verificationResult.event;
        const webhookResult = await getPaymentService().handleWebhook(event);

        if (webhookResult.success) {
            res.json({ received: true });
        } else {
            res.status(500).json({
                success: false,
                message: webhookResult.message
            });
        }
    } catch (error) {
        console.error('Error in handleStripeWebhook:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

/**
 * 管理员功能 - 获取所有订单
 */
const getAllOrders = async (req, res) => {
    try {
        // 检查管理员权限
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Access denied'
            });
        }

        const filters = {
            status: req.query.status,
            payment_status: req.query.payment_status,
            date_from: req.query.date_from,
            date_to: req.query.date_to,
            limit: req.query.limit
        };

        const result = await Order.getAllOrders(filters);
        
        if (result.success) {
            res.json({
                success: true,
                orders: result.orders
            });
        } else {
            res.status(500).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        console.error('Error in getAllOrders:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

/**
 * 管理员功能 - 更新订单状态
 */
const updateOrderStatus = async (req, res) => {
    try {
        // 检查管理员权限
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Access denied'
            });
        }

        const { orderId } = req.params;
        const { status, payment_status } = req.body;

        const result = await Order.updateOrderStatus(orderId, status, payment_status);
        
        if (result.success) {
            res.json({
                success: true,
                message: 'Order status updated successfully'
            });
        } else {
            res.status(404).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        console.error('Error in updateOrderStatus:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

module.exports = {
    getProducts,
    getProductById,
    getCategories,
    createOrder,
    createPaymentIntent,
    getUserOrders,
    getOrderById,
    handleStripeWebhook,
    getAllOrders,
    updateOrderStatus,
    createProduct,
    updateProduct,
    deleteProduct
}; 