# MMC Web Application 文档中心

欢迎来到MMC医疗管理系统的文档中心。这里包含了系统的完整文档，按功能分类组织。

## 📚 文档分类

### 🚀 [部署文档](./deployment/)
系统部署、安装和配置相关文档
- [Docker脚本使用指南](./deployment/DOCKER_SCRIPTS_README.md) - Docker维护脚本详细说明
- [在线商店设置指南](./deployment/STORE_SETUP.md) - 商店功能部署配置

### 🔧 [开发文档](./development/)
开发相关指南、规范和架构文档
- [开发规范指南](./development/DEV_GUIDELINES.md) - 代码规范和开发流程
- [新功能添加指南](./development/add_new_feature_guide.md) - 添加新功能的步骤
- [AI解释功能实现](./development/ai_explanation_feature_implementation.md) - AI功能技术实现
- [前端架构总览](./development/frontend_summary.md) - 前端技术栈和组件说明
- [后端架构总览](./development/backend_summary.md) - 后端架构和API设计

### 🛠️ [运维文档](./operations/)
系统运维、监控和维护相关文档
- [PDF同步脚本指南](./operations/SYNC_PDFS_README.md) - 文档同步系统使用说明
- [同步脚本变更日志](./operations/SYNC_SCRIPT_CHANGELOG.md) - 同步功能更新记录

### 📡 [API文档](./api/)
系统API接口和集成相关文档
- [API接口文档](./api/API.md) - RESTful API接口规范
- [推荐系统文档](./api/REFERRAL_SYSTEM.md) - 推荐奖励系统API说明

### 👥 [用户指南](./user-guides/)
系统使用说明和用户手册
- *待补充用户操作手册*

## 🔍 快速查找

### 按角色查找文档

#### 开发人员
- [开发规范](./development/DEV_GUIDELINES.md)
- [新功能开发](./development/add_new_feature_guide.md)
- [API文档](./api/API.md)
- [架构文档](./development/)

#### 运维人员
- [Docker部署](./deployment/DOCKER_SCRIPTS_README.md)
- [文档同步](./operations/SYNC_PDFS_README.md)
- [系统维护](./operations/)

#### 系统管理员
- [商店设置](./deployment/STORE_SETUP.md)
- [推荐系统](./api/REFERRAL_SYSTEM.md)
- [部署指南](./deployment/)

### 按功能查找文档

#### 🤖 AI功能
- [AI解释功能实现](./development/ai_explanation_feature_implementation.md)
- [API接口](./api/API.md#ai-endpoints)

#### 🛒 电商功能
- [商店设置指南](./deployment/STORE_SETUP.md)
- [推荐系统](./api/REFERRAL_SYSTEM.md)

#### 📁 文档管理
- [PDF同步系统](./operations/SYNC_PDFS_README.md)
- [文档同步变更](./operations/SYNC_SCRIPT_CHANGELOG.md)

#### 🐳 容器化部署
- [Docker脚本指南](./deployment/DOCKER_SCRIPTS_README.md)
- [部署文档](./deployment/)

## 📝 文档贡献

### 文档更新流程
1. 在相应分类目录下创建或更新文档
2. 遵循Markdown规范
3. 更新本导航页面
4. 提交变更

### 文档规范
- 使用清晰的标题层级
- 包含目录和导航链接
- 提供实用的代码示例
- 保持文档的时效性

## 🔗 外部链接

- [项目主页](../README.md) - 项目概述和快速开始
- [GitHub仓库](#) - 源代码仓库
- [技术支持](#) - 获取技术支持

---

## 📊 文档统计

| 分类 | 文档数量 | 最后更新 |
|------|----------|----------|
| 部署文档 | 2 | 2025-05-28 |
| 开发文档 | 5 | 2025-05-28 |
| 运维文档 | 2 | 2025-05-28 |
| API文档 | 2 | 2025-05-28 |
| 用户指南 | 0 | - |

**总计**: 11个文档文件

---

*最后更新: 2025-05-28*
