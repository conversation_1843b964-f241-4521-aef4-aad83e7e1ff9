const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库连接配置
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 54322,
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME || 'postgres'
};

async function testAIUsageStats() {
    let connection;
    
    try {
        console.log('🔍 连接数据库...');
        connection = await mysql.createConnection(dbConfig);
        
        console.log('\n📊 AI使用统计分析报告');
        console.log('=' .repeat(60));
        
        // 1. 总体统计
        console.log('\n1️⃣ 总体使用统计:');
        const [totalStats] = await connection.query(`
            SELECT 
                COUNT(*) as total_calls,
                SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_calls,
                SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed_calls,
                ROUND(AVG(response_time_ms), 2) as avg_response_time,
                COUNT(DISTINCT provider) as unique_providers,
                COUNT(DISTINCT service_type) as unique_services
            FROM ai_usage_stats
        `);
        
        if (totalStats.length > 0) {
            const stats = totalStats[0];
            console.log(`   📞 总调用次数: ${stats.total_calls}`);
            console.log(`   ✅ 成功调用: ${stats.successful_calls}`);
            console.log(`   ❌ 失败调用: ${stats.failed_calls}`);
            console.log(`   ⏱️  平均响应时间: ${stats.avg_response_time}ms`);
            console.log(`   🏢 提供商数量: ${stats.unique_providers}`);
            console.log(`   🔧 服务类型数量: ${stats.unique_services}`);
            
            if (stats.total_calls > 0) {
                const successRate = ((stats.successful_calls / stats.total_calls) * 100).toFixed(2);
                console.log(`   📈 成功率: ${successRate}%`);
            }
        } else {
            console.log('   ⚠️  暂无使用统计数据');
        }
        
        // 2. 按提供商统计
        console.log('\n2️⃣ 按提供商统计:');
        const [providerStats] = await connection.query(`
            SELECT 
                provider,
                COUNT(*) as total_calls,
                SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_calls,
                SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed_calls,
                ROUND(AVG(response_time_ms), 2) as avg_response_time,
                COUNT(DISTINCT model_name) as unique_models
            FROM ai_usage_stats 
            GROUP BY provider 
            ORDER BY total_calls DESC
        `);
        
        providerStats.forEach(provider => {
            const successRate = provider.total_calls > 0 ? 
                ((provider.successful_calls / provider.total_calls) * 100).toFixed(2) : '0.00';
            console.log(`   🏢 ${provider.provider}:`);
            console.log(`      📞 调用次数: ${provider.total_calls}`);
            console.log(`      ✅ 成功: ${provider.successful_calls} (${successRate}%)`);
            console.log(`      ❌ 失败: ${provider.failed_calls}`);
            console.log(`      ⏱️  平均响应: ${provider.avg_response_time}ms`);
            console.log(`      🤖 模型数量: ${provider.unique_models}`);
            console.log('');
        });
        
        // 3. 按服务类型统计
        console.log('\n3️⃣ 按服务类型统计:');
        const [serviceStats] = await connection.query(`
            SELECT 
                service_type,
                COUNT(*) as total_calls,
                SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_calls,
                ROUND(AVG(response_time_ms), 2) as avg_response_time,
                COUNT(DISTINCT provider) as unique_providers
            FROM ai_usage_stats 
            GROUP BY service_type 
            ORDER BY total_calls DESC
        `);
        
        serviceStats.forEach(service => {
            const successRate = service.total_calls > 0 ? 
                ((service.successful_calls / service.total_calls) * 100).toFixed(2) : '0.00';
            console.log(`   🔧 ${service.service_type}:`);
            console.log(`      📞 调用次数: ${service.total_calls}`);
            console.log(`      ✅ 成功率: ${successRate}%`);
            console.log(`      ⏱️  平均响应: ${service.avg_response_time}ms`);
            console.log(`      🏢 提供商数量: ${service.unique_providers}`);
            console.log('');
        });
        
        // 4. 按模型统计
        console.log('\n4️⃣ 按模型统计:');
        const [modelStats] = await connection.query(`
            SELECT 
                model_name,
                provider,
                COUNT(*) as total_calls,
                SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_calls,
                ROUND(AVG(response_time_ms), 2) as avg_response_time
            FROM ai_usage_stats 
            WHERE model_name IS NOT NULL
            GROUP BY model_name, provider 
            ORDER BY total_calls DESC
        `);
        
        modelStats.forEach(model => {
            const successRate = model.total_calls > 0 ? 
                ((model.successful_calls / model.total_calls) * 100).toFixed(2) : '0.00';
            console.log(`   🤖 ${model.model_name} (${model.provider}):`);
            console.log(`      📞 调用次数: ${model.total_calls}`);
            console.log(`      ✅ 成功率: ${successRate}%`);
            console.log(`      ⏱️  平均响应: ${model.avg_response_time}ms`);
            console.log('');
        });
        
        // 5. 最近的调用记录
        console.log('\n5️⃣ 最近10次调用记录:');
        const [recentCalls] = await connection.query(`
            SELECT 
                provider,
                service_type,
                model_name,
                success,
                response_time_ms,
                created_at
            FROM ai_usage_stats 
            ORDER BY created_at DESC 
            LIMIT 10
        `);
        
        recentCalls.forEach((call, index) => {
            const status = call.success ? '✅' : '❌';
            const time = new Date(call.created_at).toLocaleString('zh-CN');
            console.log(`   ${index + 1}. ${status} ${call.provider}/${call.service_type} (${call.model_name}) - ${call.response_time_ms}ms - ${time}`);
        });
        
        // 6. 错误统计
        console.log('\n6️⃣ 错误统计:');
        const [errorStats] = await connection.query(`
            SELECT 
                provider,
                service_type,
                error_type,
                COUNT(*) as error_count,
                MAX(created_at) as last_error
            FROM ai_error_logs 
            GROUP BY provider, service_type, error_type 
            ORDER BY error_count DESC
            LIMIT 10
        `);
        
        if (errorStats.length > 0) {
            errorStats.forEach(error => {
                const lastError = new Date(error.last_error).toLocaleString('zh-CN');
                console.log(`   ❌ ${error.provider}/${error.service_type} - ${error.error_type}: ${error.error_count}次 (最近: ${lastError})`);
            });
        } else {
            console.log('   🎉 暂无错误记录！');
        }
        
        // 7. 今日统计
        console.log('\n7️⃣ 今日统计:');
        const [todayStats] = await connection.query(`
            SELECT 
                provider,
                service_type,
                COUNT(*) as calls_today,
                SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_today
            FROM ai_usage_stats 
            WHERE DATE(created_at) = CURDATE()
            GROUP BY provider, service_type 
            ORDER BY calls_today DESC
        `);
        
        if (todayStats.length > 0) {
            todayStats.forEach(today => {
                const successRate = today.calls_today > 0 ? 
                    ((today.successful_today / today.calls_today) * 100).toFixed(2) : '0.00';
                console.log(`   📅 ${today.provider}/${today.service_type}: ${today.calls_today}次调用 (${successRate}%成功)`);
            });
        } else {
            console.log('   📅 今日暂无调用记录');
        }
        
        console.log('\n' + '='.repeat(60));
        console.log('✅ AI使用统计分析完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error('详细错误:', error);
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

// 运行测试
if (require.main === module) {
    testAIUsageStats().catch(console.error);
}

module.exports = { testAIUsageStats }; 