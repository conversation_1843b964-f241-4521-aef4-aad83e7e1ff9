#!/usr/bin/env node

const axios = require('axios');

async function testChatbotAPI() {
    console.log('=== 测试聊天机器人API ===');
    
    try {
        const response = await axios.post('http://localhost:3002/api/chat', {
            messages: [
                { role: 'user', content: '请简单介绍一下MMC Wellness的服务' }
            ],
            conversationId: 'final-test-' + Date.now()
        }, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 30000
        });
        
        console.log('✅ 聊天机器人API测试成功');
        console.log('响应状态:', response.status);
        console.log('响应头:', response.headers['content-type']);
        
    } catch (error) {
        console.error('❌ 聊天机器人API测试失败:', error.message);
    }
}

async function testBackendHealth() {
    console.log('\n=== 测试后端健康状态 ===');
    
    try {
        const response = await axios.get('http://localhost:3000/health');
        console.log('✅ 后端健康检查成功');
        console.log('响应:', response.data);
        
    } catch (error) {
        console.error('❌ 后端健康检查失败:', error.message);
    }
}

async function testFrontendHealth() {
    console.log('\n=== 测试前端健康状态 ===');
    
    try {
        const response = await axios.get('http://localhost:3001', { timeout: 10000 });
        console.log('✅ 前端服务正常运行');
        console.log('响应状态:', response.status);
        
    } catch (error) {
        console.error('❌ 前端服务测试失败:', error.message);
    }
}

async function testChatbotHealth() {
    console.log('\n=== 测试聊天机器人健康状态 ===');
    
    try {
        const response = await axios.get('http://localhost:3002/health');
        console.log('✅ 聊天机器人服务正常运行');
        console.log('响应:', response.data);
        
    } catch (error) {
        console.error('❌ 聊天机器人服务测试失败:', error.message);
    }
}

async function runAllTests() {
    console.log('🚀 开始MMC Web App最终系统测试\n');
    
    await testBackendHealth();
    await testChatbotHealth();
    await testFrontendHealth();
    await testChatbotAPI();
    
    console.log('\n🎉 所有测试完成！');
    console.log('\n📊 系统状态总结:');
    console.log('- 后端服务: http://localhost:3000');
    console.log('- 前端服务: http://localhost:3001');
    console.log('- 聊天机器人服务: http://localhost:3002');
    console.log('- AI智能切换功能: ✅ 已启用');
    console.log('- AI使用统计记录: ✅ 已启用');
    console.log('- 数据库连接: ✅ 正常');
    
    console.log('\n🔧 主要功能:');
    console.log('- Gemini AI (gemini-2.0-flash): 主要AI提供商');
    console.log('- OpenRouter AI (deepseek/deepseek-r1-0528:free): 备用AI提供商');
    console.log('- 自动故障转移: 配额用完时自动切换');
    console.log('- AI使用统计: 完整记录所有AI调用');
    console.log('- 现代化仪表板: 实时监控和图表展示');
}

// 运行测试
runAllTests().catch(console.error); 