# Frontend Documentation

This document provides an overview of the frontend structure, components, and deployment of the MMC Wellness application.

## Environment Configuration (`frontend/`)

*   **`.env`, `.env.production`, `.env.development`:** Define environment variables. Currently, all define `PORT=3001` and `REACT_APP_API_URL=https://app-backend.mmcwellness.ca`. The `docker-compose.yml` also sets `REACT_APP_API_URL` for the deployed container.
*   **Local Development Tunneling (`cloudflared`)**: The user utilizes `cloudflared` for local development, tunneling local ports to public hostnames:
    *   `localhost:3000` (Local Backend) -> `app-backend.mmcwellness.ca`
    *   `localhost:3001` (Local Frontend) -> `app.mmcwellness.ca`
    This allows the local frontend to access the backend via the public URL.

## Deployment (Docker)

The frontend is deployed as a Docker container, managed by `docker-compose.yml`.

*   **Dockerfile (`frontend/Dockerfile`):** Uses a multi-stage build.
    *   **Build Stage (`node:20-alpine` as `build`):
        *   Sets working directory to `/app`.
        *   **Initializes a new React app using `npx create-react-app .`**. This seems unusual as it implies the source code in `frontend/src` might be copied *over* a newly created CRA structure.
        *   Installs specific dependencies (`@mui/material`, `@mui/icons-material`, `@emotion/react`, `@emotion/styled`, `axios`, `react-router-dom@6`, `date-fns`, `dayjs`). It doesn't use `package.json` for this install step.
        *   Attempts to disable service workers in `src/index.js` using `sed`.
        *   Copies `src/`, `public/` from the host into the build stage.
        *   Copies `.env.production` to `.env` for the build.
        *   Sets build-related environment variables (`GENERATE_SOURCEMAP=false`, `INLINE_RUNTIME_CHUNK=false`, `REACT_APP_VERSION`).
        *   Runs `npm run build` to create the production build in `/app/build`.
    *   **Production Stage (`nginx:alpine`):
        *   Copies the built React app (`/app/build`) from the build stage to `/usr/share/nginx/html`.
        *   Creates a `/usr/share/nginx/html/version.js` file containing a timestamped version string.
        *   Injects a `<script>` tag into `index.html` to load `version.js` (likely for cache busting or version display).
        *   Copies the custom `frontend/nginx.conf` to `/etc/nginx/conf.d/default.conf`.
        *   Exposes port 3001.
        *   Runs Nginx in the foreground.
*   **Nginx Configuration (`frontend/nginx.conf`):**
    *   Listens on port 3001.
    *   Serves static files from `/usr/share/nginx/html`.
    *   Implements `try_files` to serve `index.html` for client-side routing.
    *   Sets specific cache control headers for different file types (no cache for HTML, revalidation for JS/CSS, long cache for images/fonts).
    *   Proxies requests starting with `/api` to a backend URL. The backend URL is dynamically set to `https://app-backend.mmcwellness.ca` if the host is `app.mmcwellness.ca`, otherwise it defaults to `http://backend:3000` (the service name from `docker-compose.yml`).
*   **Docker Compose (`./docker-compose.yml`):
    *   Defines the `frontend` service.
    *   Builds the image using `frontend/Dockerfile`.
    *   Maps host port 3001 to container port 3001.
    *   Sets the `TZ` environment variable to `America/Vancouver`.
    *   Sets `REACT_APP_API_URL` environment variable (seems redundant given `.env` copy in Dockerfile, but docker-compose overrides take precedence).
    *   Depends on the `backend` service being healthy.
    *   Connects to `webapp-network`.
    *   Sets `restart: always`.

## Source Code (`frontend/src/`)

Contains the React application source code.

*   **`App.js`:** The main application component.
    *   Sets up routing using `react-router-dom`.
    *   Wraps the application with Context Providers (`ThemeProvider`, `LanguageProvider`, `ViewProvider`).
    *   Includes an `AutoLogout` component.
    *   Defines an `AppLayout` component that conditionally renders a `Sidebar`.
    *   Defines routes for public pages (Login, Register, etc.) and private pages (Profile, Appointments, **FamilyMembers**, Membership, etc.), redirecting appropriately based on authentication.
*   **`theme.js`:** Defines a custom theme for Material UI (`@mui/material`).
    *   Uses `createTheme` to customize the default theme.
    *   Sets primary (`#1976d2`) and secondary (`#dc004e`) colors.
    *   Defines background colors (`default: #f5f5f7`, `paper: #ffffff`).
    *   Specifies the font stack (`Roboto`, `Helvetica Neue`, `Arial`, system fonts).
    *   Sets default border radius (`shape.borderRadius: 12`).
    *   Provides style overrides for various MUI components (`MuiCard`, `MuiPaper`, `MuiButton`, `MuiAppBar`, `MuiDrawer`, `MuiChip`, `MuiContainer`, `MuiDivider`, `MuiTab`, `MuiTabs`, `MuiListItemButton`, `MuiAvatar`) to create a consistent, modern look (e.g., increased border radius, subtle box shadows).
*   **Context Providers (`frontend/src/context/`):**
    *   **`ViewContext.js`:** Manages the user's viewing state (self vs. family member).
        *   Stores `loggedInUser` (fetched from `/api/auth/profile`, now only contains basic user info and `demographicInfo`).
        *   Stores the currently `viewingDemographicNo`.
        *   **No longer stores `familyMembers`.**
        *   Provides `setViewAs` function (simplified to only allow switching back to self directly).
        *   Provides `isLoading` and `error` states for the initial profile fetch.
        *   Manages `isChatOpen` state and `toggleChat` function for the chatbot.
    *   **`LanguageContext.js`:** Handles internationalization (i18n).
        *   Stores language preference (`en` or `zh`) in state and `localStorage`.
        *   **Contains a large embedded `translations` object with English and Chinese strings.** New keys were added for the `FamilyMembers` component.
        *   Provides `toggleLanguage` and `t(key, options)` functions (the `t` function now supports placeholder replacement, e.g., `t('greeting', { name: 'User' })` would replace `{name}`).
    *   **`ThemeContext.js`:** Manages the application's theme (light/dark mode).
        *   Defines `lightTheme` and `darkTheme` objects using `createTheme` from MUI.
        *   Stores the current `themeMode` (`light` or `dark`) in state and `localStorage` (defaults to `light`).
        *   Provides a `toggleTheme` function.
        *   Wraps children with the MUI `MuiThemeProvider`, passing the currently active theme object.
*   **Components (`frontend/src/components/`):**
    *   **`Sidebar.js`:** Renders the main navigation sidebar **for desktop view**.
        *   Uses MUI `Drawer` (permanent variant).
        *   Defines navigation items grouped by category (`basic_info`, `appointment_mgmt`, etc.) using translation keys from `LanguageContext`.
        *   Groups are collapsible (`Collapse`), with state managed by `expanded` (defaults to open).
        *   Displays user name/email and provides a user menu (`Menu`) with links to Profile, Settings, and Logout.
        *   Shows a notification and a "Switch Back" button when viewing a family member's profile (`!isViewingOwnProfile` from `ViewContext`).
        *   Handles navigation using `useNavigate` and highlights the active link based on `useLocation`.
        *   **Now includes a link to the `/family` page** under "Basic Information".
    *   **`Login.js`:** Handles the user sign-in process with integrated email verification and account linking for existing patients.
        *   Uses MUI components (`Container`, `Paper`, `TextField`, `Button`, `Dialog`, `Alert`, `Snackbar`) for the UI.
        *   Includes fields for email and password.
        *   Implements a multi-step process:
            1.  User enters email.
            2.  Calls `/api/auth/check-email` to see if the email exists in `user_auth` (normal login) or only in `demographic` (existing patient, needs linking).
            3.  Calls `/api/auth/send-verification-code` to send a code to the entered email.
            4.  Shows a verification code input field.
            5.  User enters code, calls `/api/auth/verify-email-code` to verify.
            6.  **If email exists in `user_auth`:** User enters password, calls `/api/auth/login` on submit. Stores token/user in `localStorage` and navigates to `/profile`.
            7.  **If email exists only in `demographic`:**
                *   If multiple patients found, shows a Dialog to select the correct patient.
                *   Shows a Dialog (`linkDialogOpen`) prompting the user to set a password to link their account.
                *   Calls `/api/auth/link-oscar-patient` with email, new password, and selected demographic number.
        *   Provides links to Register, Forgot Password, and separate flows for Linking Account / Registering New Patient (navigating to `/email-verification` with different modes).
        *   Handles loading states and displays errors/info messages using `Alert` and `Snackbar`.
    *   **`Profile.js`:** Primarily responsible for displaying the *logged-in user's* profile information, obtained directly from `ViewContext`. 
        *   Uses `ViewContext` to get `loggedInUser`, `viewingDemographicNo`, and `isViewingOwnProfile`.
        *   Displays demographic information (name, address, phone, email, care card, sex) for the `loggedInUser` using MUI `Card`, `Grid`, and a custom `InfoItem` component.
        *   Shows an Alert and a "Switch Back" button when viewing a family member's profile (`!isViewingOwnProfile`).
        *   **Delegates the fetching and display of a *family member's* profile information to the `FamilyMemberDisplay` component when `!isViewingOwnProfile` is true.**
    *   **`FamilyMembers.js`:** Dedicated page (/family) to display and manage family members.
        *   Fetches family member data from the `/api/relationships/family` endpoint.
        *   Displays a list of family members using MUI `List` and `ListItem`.
        *   Shows member name, relationship type, and access status (`hasRecentAccess` chip).
        *   Implements the "Request Access" flow, including:
            *   A confirmation dialog using granular translation keys (e.g., `request_access_intro`, `request_access_point1_title`) from `LanguageContext`, formatted within the component.
            *   API calls to `/api/relationships/switch-view/initiate` and `/api/relationships/switch-view/verify`.
            *   **Refreshes the family member list (`fetchFamilyMembers()`) after successful verification** to update the UI status before switching the view context.
    *   **`FamilyMemberDisplay.js` (NEW):** Component rendered by `Profile.js` when viewing a family member.
        *   Receives the `demographicNo` prop for the family member to display.
        *   Fetches profile data for that specific `demographicNo` by calling `GET /api/auth/profile/:demographicNo`.
        *   **Expects the backend response to contain the profile data in `response.data.demographicInfo`.**
        *   Displays the fetched demographic information using MUI components, similar in structure to `Profile.js`.
        *   Handles its own loading and error states for the fetch operation.
    *   **`ForgotPassword.js`:** Handles the password reset process.
        *   Provides a link to navigate to the Login page for existing users.
    *   **`LinkAccount.js`:** Handles linking an existing patient record (found via email) to a new web portal account by setting a password. It is typically reached after email verification with `mode=link`. Key features:
        *   **Pre-filled Email**:
            - Expects a verified email address to be passed via URL query parameter (`?email=...`).
            - Redirects to `/email-verification?mode=link` if the email parameter is missing.
            - Disables the email input field.
        *   **Patient Record Check**:
            - On load, calls `/api/auth/check-email` for the provided email.
            - If multiple patient records (`demographics` array in response) are associated with the email, it displays a selection step (`multipleRecordsStep`).
        *   **Patient Selection (if multiple)**:
            - Displays a list of found patient records (name, address) using MUI `RadioGroup`.
            - Requires the user to select one record.
            - On "Continue", proceeds to link the selected patient record.
        *   **Password Setup**:
            - Prompts the user to enter and confirm a password.
            * Validates that passwords match and meet length requirements (minimum 8 characters).
        *   **Account Linking Submission**:
            - Calls `/api/auth/link-oscar-patient` with the email, new password, and the demographic number.
              * If only one patient record was found initially, its `demographic_no` is used.
              * If multiple records were found, the `demographic_no` selected by the user is used.
            - If successful:
              * Stores the received token and user data in `localStorage`.
              * Shows a success message.
              * Redirects to `/profile` after 3 seconds.
            - Handles loading states and errors.
        *   **Layout and Navigation**:
            - Uses MUI `Container`, `Paper`, `TextField`, `Button`, `Alert`, `RadioGroup` for the UI.
            - Provides a link back to the Login page.
    *   **`Appointments.js`:** Displays upcoming and past appointments.
        *   Organized in tabs for upcoming and past appointments
        *   Responsive design with mobile-specific layouts
        *   Status chips with appropriate icons for different appointment states (confirmed, pending, completed, cancelled)
        *   Empty state handling with clear messaging
        *   Loading indicators during data fetching
        *   Interface for canceling or rescheduling confirmed appointments
        *   Quick navigation to booking new appointments
        *   Option to switch back when viewing family member appointments
        *   Network error detection with user-friendly messages
        *   Retry mechanisms for failed API calls
        *   Reload option for connectivity issues
        *   Full translation support through the translation utility
    *   **`LabReports.js`:** Displays medical documents and test values.
        *   Tab-Based Interface: Two main tabs: "Documents" for viewing medical reports and "Test Values" for viewing lab measurements
        *   Each tab has specialized display formats for their respective data types
        *   Lists documents with title, type, and date information
        *   Provides document viewing through an embedded iframe in a dialog
        *   Offers download functionality for PDF documents
        *   Color-coded document type chips (lab, radiology, consult, etc.)
        *   Shows value, unit, date, and any comments for each measurement
        *   Card-based layout for clear separation between different test types
        *   Shows appropriate notification when viewing family member's documents
        *   "Switch Back" button when viewing family member's data
        *   Data Fetching: Makes API calls to different endpoints based on whether viewing own profile or family member
        *   `/api/lab-results/reports` (own) or `/api/lab-results/reports/demographic/{id}` (family)
        *   Similar pattern for measurements endpoints
        *   Handles loading states and error conditions
        *   Adapts layout for different screen sizes
        *   Mobile-friendly document list and viewer
    *   **`Membership.js`:** Displays user and potentially family membership status (needs review if family info is fetched here separately now).
        *   Shows the current membership status (active/expired) for the logged-in user and linked family members using MUI `Card` components.
        *   Displays key details like start date, end date, membership type, and payment amount using a custom `InfoItem` component.
        *   Includes an optional, expandable history section for each member (currently only implemented for a simulated user "HUBERY SONG") showing past membership periods and statuses.
        *   Provides a "Renew Membership" / "Renew Early" button for the logged-in user.
        *   Opens a dialog (`renewDialogOpen`) when the renew button is clicked.
        *   Fetches available membership types and prices from `/api/membership/types`.
        *   Displays available types in a dropdown (`Select`) for the user to choose.
        *   On confirmation, calls `/api/auth/renew-membership` with the selected type.
        *   Shows success or error messages and reloads the page on successful renewal.
    *   **`Settings.js`:** Allows users to manage security, notifications, and preferences.
        *   Tabbed Navigation: Organizes settings into three tabs: "Security", "Notifications", and "Preferences".
        *   Uses MUI `Tabs` and a custom `TabPanel` component for organization.
        *   Provides a form to change the user's password.
        *   Includes fields for current password, new password, and confirmation.
        *   Implements password visibility toggles (`Visibility`, `VisibilityOff` icons).
        *   Performs basic validation (passwords match, minimum length).
        *   **Note**: Currently simulates API call; actual API integration is marked as TODO.
        *   Allows users to toggle various notification types using MUI `Switch` components.
        *   Includes options for general email notifications, appointment reminders, document upload notifications, and marketing emails.
        *   **Note**: Currently simulates API call; actual API integration is marked as TODO.
        *   Language Selection: Allows changing the interface language (`en`/`zh`) using a `TextField` with `select` prop, interacting with `LanguageContext`.
        *   Theme Mode: Provides a `Switch` to toggle between light and dark themes, interacting with `ThemeContext`.
        *   Time Format: Includes a `Switch` to toggle between 12-hour and 24-hour time formats (actual implementation for time display not shown in this component).
        *   Displays success or error messages using MUI `Alert` components after submitting changes.
    *   **`Prescriptions.js`:** Displays prescription records.
        *   Fetches prescription data from `/api/prescriptions/:demographicNo` using the `viewingDemographicNo` from `ViewContext`.
        *   Requires authentication token.
        *   Shows prescriptions in a list of MUI `Card` components.
        *   For each prescription, displays:
            * Date prescribed (`formatDate` utility).
            * Details (drug name, dosage, etc.).
            * Comments/Instructions.
            * Prescribing provider name.
        *   Uses a custom `InfoItem` component with icons for clarity.
        *   Shows a "No prescriptions found" message if the list is empty.
    *   **`Immunizations.js`:** Displays immunization records.
        *   Fetches immunization data from `/api/immunizations/:demographicNo` using the `viewingDemographicNo` from `ViewContext`.
        *   Requires authentication token.
        *   Shows immunization records in a list of MUI `Card` components.
        *   For each record, displays:
            * Vaccine/Prevention Type.
            * Date administered (`formatDate` utility).
            * Administering provider name (if available).
            * Next due date (if available).
        *   Uses a custom `InfoItem` component with icons for clarity.
        *   Shows a "No immunization records found" message if the list is empty.
    *   **`BookingPage.js`:** Multi-step appointment booking process.
        *   Uses Material UI `Stepper` component to guide users through the booking process
        *   Three sequential steps: Select Provider, Select Date & Time, Confirm Booking
        *   Validates each step before allowing progression
        *   Fetches available providers from `/api/providers/doctors`
        *   Filters specifically for bookable providers (with IDs '818', '830', '8000')
        *   Dropdown interface for provider selection
        *   Calendar date picker with minimum date restriction (defaults to 2 days from today)
        *   Fetches available time slots for selected provider and date
        *   Displays time slots in a scrollable list with visual selection indicator
        *   Final confirmation step with summary of provider, date, and time
        *   Text area for entering booking reason/notes
        *   Submit button to confirm the booking
        *   Posts booking data to `/api/appointments/book` endpoint
        *   Handles success with confirmation message and link to appointments page
        *   Provides error handling with user-friendly messages
        *   Adapts layout for mobile devices
        *   Adjusts stepper and input layouts based on screen size
        *   Mobile-optimized time slot selection
        *   Uses translation utility for all UI text
        *   Handles different languages through the `LanguageContext`
    *   **`Chatbot.js`:** Chat interface component displayed as a FAB and Dialog.
        *   Uses a `StyledPaper` container within a MUI `Dialog`.
        *   `MessagesContainer`: Scrollable area using `List` and a custom `ChatMessageItem`.
        *   `ChatMessageItem`: Styles messages with user messages/avatars aligned right, bot messages/avatars aligned left, and uses background boxes for a bubble effect.
        *   `InputContainer`: Holds the text input field and send button.
        *   Uses `Avatar` with initials for the user and a `SmartToyIcon` for the bot.
        *   Bot name ("MMC Health Assistant" / "MMC 健康助手") and initial greeting are now fetched using `t('chat_bot_name')` and `t('chat_greeting')` from `LanguageContext`.
        *   Dialog title ("Health Assistant" / "健康助手") uses `t('chat_window_title')`.
        *   Maintains conversation history in the `messages` state array.
        *   Sends messages to `/api/chat` and handles streaming responses (SSE).
    *   **`Register.js`:** Provides a form for new patients to register an account.
        *   Collects comprehensive patient demographic information using MUI `TextField` and `Select` components within a `Grid` layout.
        *   Fields include: Title, First Name, Last Name, Address, City, Province, Postal Code, Phone Number, Email, Date of Birth, Health Insurance Number (Optional), Sex.
        *   Also includes fields for Password and Confirm Password.
        *   Reads the `email` query parameter from the URL (if present, likely after email verification) and pre-fills/disables the email field.
        *   On submit, validates that passwords match.
        *   Sends registration data (including demographics and password) to `/api/auth/register`.
        *   If successful:
            * Stores the received authentication token in `localStorage`.
            * Redirects the user to `/dashboard` (Note: The existence/purpose of the Dashboard component is unclear).
        *   Displays errors received from the API.
        *   Uses MUI `Container` and `Paper` for styling.
        *   Provides a link to navigate to the Login page for existing users.
    *   **`ForgotPassword.js`:** Handles the password reset process.
        *   Prompts the user to enter their email address.
        *   Calls `/api/auth/send-verification-code` to send a code to the email.
        *   Displays a field for the user to enter the received verification code.
        *   Calls `/api/auth/verify-email-code` to verify the code.
        *   Handles loading states and displays success/error messages using `Snackbar` and `Alert`.
        *   Once the email is verified (`isVerified` state is true), it shows the password reset form (`showResetForm` state).
        *   Prompts for a new password and confirmation.
        *   Validates that the passwords match.
        *   Submits the email and new password to `/api/auth/reset-password-direct`.
        *   On successful password reset, displays a success message and a `Dialog` indicating the user will be redirected to the login page after a countdown.
        *   Automatically redirects to `/login` after 5 seconds.
        *   Uses MUI `Container` and `Paper` for styling.
        *   Provides a link back to the Login page.
    *   **`EmailVerification.js`:** Handles the process of verifying a user's email address, potentially as part of registration, account linking, or password reset flows.
        *   Reads `email`, `code`, and `mode` query parameters from the URL (`useSearchParams`).
        *   The `mode` parameter determines the action after successful verification:
            * `register`: Redirects to `/register?email=...`
            * `link`: Redirects to `/link-account?email=...`
            * *Default/None*: Assumes password reset flow and shows the password form.
        *   Displays fields for Email and Verification Code.
        *   Provides a "Send Code" button that calls `/api/auth/send-verification-code`.
        *   Provides a "Resend Code" button after the initial code is sent.
        *   Calls `/api/auth/verify-email-code` with the entered email and code.
        *   If verification is successful, shows a success message and proceeds based on the `mode` (redirect or show password form).
        *   Handles loading states and errors.
        *   If no `mode` is specified and verification is successful, it displays a form (`showPasswordForm`) to set a new password.
        *   Requires password and confirmation, validates matching and length.
        *   Calls `/api/auth/reset-password-direct` to set the new password.
        *   Redirects to `/login` after successful password setting.
        *   Uses MUI `Container`, `Paper`, `TextField`, `Button`, `Alert`, etc. for the UI.
    *   **`RenewMembership.js`:** Provides a dedicated page for renewing a membership.
        *   Displays a dropdown (`Select`) listing available membership types and prices.
        *   Uses a hardcoded `membershipTypes` array (unlike `Membership.js` which tries to fetch them).
        *   Requires the user to select a type.
        *   Calls `/api/auth/renew-membership` with the selected `membershipType`.
        *   Requires authentication token.
        *   Redirects to `/profile` on success.
        *   Displays errors if the renewal fails.
        *   Uses MUI `Container`, `Paper`, `FormControl`, `Select`, `Button` for the UI.
        *   Provides a link back to the Login page.
    *   **`CardContainer.js`:** Simple presentational component used to wrap lists of cards (like appointments or prescriptions) and provide consistent styling and spacing.
        *   Uses MUI `Box` and `Stack` to arrange child elements vertically.
        *   Applies configurable vertical spacing between child elements (defaults to 2, increases on mobile).
        *   Applies specific styles to nested `MuiCard` components on mobile (`isMobile` from `useMediaQuery`):
            * Sets card width to 95%.
            * Centers cards horizontally (`mx: 'auto'`).
            * Reduces border radius and box shadow.
            * Removes the card border.
            * Reduces padding within `MuiCardContent`.
        *   Accepts `children`, `spacing`, `sx` (for additional styles), and `padded` (controls mobile padding, defaults true).
    *   **`Dashboard.js`:** This file does not export a React component. It defines a `drawerItems` array containing objects with `title`, `path`, and `icon` for navigation (Dashboard, Profile, Appointments, Lab Results, Settings). This array does not appear to be used by the `Sidebar.js` or `Header.js` components, which define their own navigation structures. It might be unused or leftover code.
    *   **`AppointmentLookup.js`:** Provides a way to look up a patient's appointment history without requiring user authentication.
        *   Allows searching by either:
            * Last Name and Phone Number
            * Patient ID (Demographic Number)
        *   Uses MUI `RadioGroup` to switch between search methods.
        *   Calls different API endpoints based on the selected search method:
            * `/api/appointments/lookup` (POST with `lastName`, `phone`)
            * `/api/appointments/guest/:demographicNo` (GET)
        *   Handles loading states and displays errors.
        *   If the name/phone search returns multiple patients, it displays a list (`multiplePatients`) allowing the user to select the correct one.
        *   Clicking a patient in the list triggers a new fetch using the selected patient's demographic number via the `/api/appointments/guest/:demographicNo` endpoint.
        *   Once a single patient's data is retrieved (`patientData`), it displays:
            * Basic patient info (name, phone, DOB, patient ID).
            * A tabbed view (`Tabs`, `TabPanel`) for "Upcoming Appointments" and "Past Appointments".
            * Lists appointments within each tab using styled `Card` components.
            * Shows appointment details: Reason, Status (with icon), Date, Time, Doctor, Notes.
            * Uses helper functions to format dates (`formatDate`) and times (`formatTimeString`).
            * Shows messages if no appointments are found.
        *   Uses MUI `Container`, `Paper`, `TextField`, `Button`, `Tabs`, `Card`, `Grid`, `List` for the UI.
        *   Hides itself entirely on authentication paths (Login, Register, etc.).
    *   **`Profile.js`:** Uses `ViewContext` to get and set the viewed profile (`viewedProfile`, `setViewedProfile`).
        *   Fetches profile data (`useFetchProfile`) based on `viewedProfile.demographicNo`. Handles loading (`isLoading`) and error (`error`) states during the fetch.
        *   Uses Material UI components (`Card`, `CardContent`, `Typography`, `Grid`, `List`, `ListItem`, `ListItemIcon`, `ListItemText`) to display demographic information (Name, DoB, PHN, Address, Contact Info).
        *   Shows an Alert and a "Switch Back" button when viewing family member data.
        *   Uses `LanguageContext` for translations (`t` function).
    *   **`Appointments.js`:** Uses `ViewContext` to access the currently viewed profile's demographic number for fetching relevant appointment data.
        *   Organizes appointments into "Upcoming" and "Past" tabs using Material UI's `TabContext`, `TabList`, and `TabPanel`. Displays appointment details in `AppointmentCard` components within each tab.
        *   Fetches appointment data from the API using the `useFetchAppointments` hook, triggered by changes in the viewed profile's demographic number.
        *   Shows a loading indicator (`CircularProgress`) while fetching data.
        *   Displays status chips with appropriate colors for different appointment statuses (e.g., Booked, Arrived, Cancelled).
        *   Presents an "Empty State" message when no appointments are found for a specific tab.
        *   Includes buttons for booking new appointments (`ButtonBookAppointment`).
        *   Allows users to cancel or reschedule appointments through integrated actions within the `AppointmentCard` (details likely handled within `AppointmentCard` or related components).
        *   Displays user-friendly error messages with a retry mechanism if fetching appointments fails.
        *   Uses the `useTranslation` hook for displaying text, ensuring the component supports multiple languages.
        *   Leverages Material UI's grid system (`Grid`) for layout, likely ensuring responsiveness across different screen sizes.
    *   **`AppointmentLookup.js`:** Provides a way to look up a patient's appointment history without requiring user authentication.
        *   Allows searching by either:
            * Last Name and Phone Number
            * Patient ID (Demographic Number)
        *   Uses MUI `RadioGroup` to switch between search methods.
        *   Calls different API endpoints based on the selected search method:
            * `/api/appointments/lookup` (POST with `lastName`, `phone`)
            * `/api/appointments/guest/:demographicNo` (GET)
        *   Handles loading states and displays errors.
        *   If the name/phone search returns multiple patients, it displays a list (`multiplePatients`) allowing the user to select the correct one.
        *   Clicking a patient in the list triggers a new fetch using the selected patient's demographic number via the `/api/appointments/guest/:demographicNo` endpoint.
        *   Once a single patient's data is retrieved (`patientData`), it displays:
            * Basic patient info (name, phone, DOB, patient ID).
            * A tabbed view (`Tabs`, `TabPanel`) for "Upcoming Appointments" and "Past Appointments".
            * Lists appointments within each tab using styled `Card` components.
            * Shows appointment details: Reason, Status (with icon), Date, Time, Doctor, Notes.
            * Uses helper functions to format dates (`formatDate`) and times (`formatTimeString`).
            * Shows messages if no appointments are found.
        *   Uses MUI `Container`, `Paper`, `TextField`, `Button`, `Tabs`, `Card`, `Grid`, `List` for the UI.
        *   Hides itself entirely on authentication paths (Login, Register, etc.).
        *   Uses `ViewContext` to get and set the viewed profile (`viewedProfile`, `setViewedProfile`).
        *   Fetches profile data (`useFetchProfile`) based on `viewedProfile.demographicNo`. Handles loading (`isLoading`) and error (`error`) states during the fetch.
        *   Uses Material UI components (`Card`, `CardContent`, `Typography`, `Grid`, `List`, `ListItem`, `ListItemIcon`, `ListItemText`) to display demographic information (Name, DoB, PHN, Address, Contact Info).
        *   Shows an Alert and a "Switch Back" button when viewing family member data.
        *   Uses `LanguageContext` for translations (`t` function).
        *   Leverages Material UI's grid system (`Grid`) for layout, likely ensuring responsiveness across different screen sizes. 