import React, { useState, useEffect, useMemo } from 'react';
import { API_URL } from '../utils/env';
import {
    Box,
    Typography,
    Card,
    CardContent,
    Grid,
    Button,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    IconButton,
    Divider,
    CircularProgress,
    Alert,
    useTheme
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import HealthAndSafetyIcon from '@mui/icons-material/HealthAndSafety';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import FavoriteIcon from '@mui/icons-material/Favorite';
import PsychologyIcon from '@mui/icons-material/Psychology';
import FitnessCenterIcon from '@mui/icons-material/FitnessCenter';
import { useLanguage } from '../context/LanguageContext';
import axios from 'axios';

const HealthGuide = () => {
    const { t } = useLanguage();
    const theme = useTheme();
    const [guides, setGuides] = useState([]);
    const [selectedGuide, setSelectedGuide] = useState(null);
    const [guideContent, setGuideContent] = useState('');
    const [dialogOpen, setDialogOpen] = useState(false);
    const [loading, setLoading] = useState(true);
    const [guideLoading, setGuideLoading] = useState(false);
    const [error, setError] = useState('');

    // 模拟健康指南数据 - 直接使用常量避免每次重渲染
    const mockGuides = useMemo(() => [
        {
            id: 1,
            title: '心血管健康指南',
            description: '了解如何保护心脏健康，预防心血管疾病，包括饮食建议、运动指导和生活方式调整',
            category: 'cardiovascular',
            content: '# 心血管健康指南\n\n心血管疾病是全球主要的健康威胁之一。通过正确的生活方式和预防措施，我们可以显著降低患病风险。\n\n## 饮食建议\n\n- 减少饱和脂肪和反式脂肪的摄入\n- 增加蔬菜、水果和全谷物的摄入\n- 控制钠的摄入量\n- 适量摄入健康脂肪（如橄榄油、坚果）\n\n## 运动指导\n\n- 每周至少150分钟中等强度有氧运动\n- 每周2-3次力量训练\n- 避免久坐，定期活动\n\n## 生活方式调整\n\n- 戒烟限酒\n- 保持健康体重\n- 管理压力\n- 保证充足睡眠\n\n定期体检和监测血压、血脂等指标也非常重要。如有疑问，请咨询专业医生。'
        },
        {
            id: 2,
            title: '营养与健康饮食',
            description: '专业营养师指导，帮助您建立健康的饮食习惯，获得均衡营养',
            category: 'nutrition',
            content: '# 营养与健康饮食\n\n良好的营养是健康的基础。均衡的饮食可以提供身体所需的所有营养素，增强免疫力，预防疾病。\n\n## 营养基础知识\n\n- **蛋白质**: 构建和修复组织，每日需求量约为体重×0.8-1.2克\n- **碳水化合物**: 主要能量来源，应占总热量的45-65%\n- **脂肪**: 必需脂肪酸和脂溶性维生素的来源，应占总热量的20-35%\n- **维生素和矿物质**: 维持正常生理功能\n\n## 健康饮食原则\n\n1. 多样化饮食，确保营养全面\n2. 控制份量，避免过量摄入\n3. 多吃新鲜蔬果\n4. 选择全谷物食品\n5. 限制加工食品和添加糖\n6. 保持充足水分摄入\n\n## 特殊人群营养建议\n\n- **孕妇**: 增加叶酸、铁、钙的摄入\n- **老年人**: 注意蛋白质和维生素D的补充\n- **运动员**: 适当增加碳水化合物和蛋白质\n\n如需个性化营养建议，建议咨询注册营养师。'
        },
        {
            id: 3,
            title: '心理健康与压力管理',
            description: '学习有效的压力管理技巧，维护心理健康，提升生活质量',
            category: 'mental-health',
            content: '# 心理健康与压力管理\n\n心理健康与身体健康同样重要。学会管理压力和情绪，可以显著提升生活质量和整体健康水平。\n\n## 识别压力信号\n\n### 身体症状\n- 头痛、肌肉紧张\n- 疲劳、失眠\n- 消化问题\n- 免疫力下降\n\n### 情绪症状\n- 焦虑、烦躁\n- 情绪低落\n- 注意力难以集中\n- 感到不知所措\n\n## 压力管理技巧\n\n### 放松技术\n- **深呼吸**: 4-7-8呼吸法\n- **冥想**: 每日10-20分钟正念练习\n- **渐进性肌肉放松**: 逐步放松各部位肌肉\n\n### 生活方式调整\n- 规律作息，保证充足睡眠\n- 适量运动，释放压力\n- 培养兴趣爱好\n- 维持社交联系\n\n### 认知调整\n- 识别负面思维模式\n- 练习积极思考\n- 设定现实目标\n- 学会说"不"\n\n## 何时寻求专业帮助\n\n如果压力持续影响日常生活，或出现抑郁、焦虑等症状，应及时寻求心理健康专业人士的帮助。'
        },
        {
            id: 4,
            title: '运动健身指导',
            description: '科学的运动计划和健身指导，帮助您建立健康的运动习惯',
            category: 'fitness',
            content: '# 运动健身指导\n\n规律的运动是维持健康的重要组成部分。科学的运动计划可以改善心肺功能、增强肌肉力量、提升心理健康。\n\n## 运动的益处\n\n### 身体益处\n- 改善心血管健康\n- 增强肌肉力量和骨密度\n- 提高免疫力\n- 控制体重\n- 改善睡眠质量\n\n### 心理益处\n- 减轻压力和焦虑\n- 改善情绪\n- 增强自信心\n- 提高认知功能\n\n## 运动类型\n\n### 有氧运动\n- **推荐**: 快走、跑步、游泳、骑自行车\n- **频率**: 每周150分钟中等强度或75分钟高强度\n- **强度**: 运动时能说话但不能唱歌\n\n### 力量训练\n- **推荐**: 举重、阻力带训练、体重训练\n- **频率**: 每周2-3次\n- **目标**: 主要肌肉群都要训练\n\n### 柔韧性训练\n- **推荐**: 瑜伽、太极、拉伸\n- **频率**: 每周2-3次\n- **时间**: 每次10-30分钟\n\n## 运动安全\n\n- 运动前充分热身\n- 循序渐进增加强度\n- 注意身体信号，避免过度训练\n- 运动后适当放松\n- 保持充足水分\n\n## 特殊人群注意事项\n\n- **初学者**: 从低强度开始，逐步增加\n- **老年人**: 注重平衡和柔韧性训练\n- **慢性病患者**: 运动前咨询医生\n\n开始运动计划前，建议咨询健身专业人士或医生，制定适合自己的运动方案。'
        }
    ], []);

    // 优化：将这些辅助函数移到组件外部以减少重渲染开销，或使用useMemo缓存结果
    const guideIcons = useMemo(() => ({
        1: <FavoriteIcon sx={{ color: '#E91E63' }} />, // 粉红色 - 心血管
        2: <LocalHospitalIcon sx={{ color: '#4CAF50' }} />, // 绿色 - 营养
        3: <PsychologyIcon sx={{ color: '#9C27B0' }} />, // 紫色 - 心理健康
        4: <FitnessCenterIcon sx={{ color: '#FF9800' }} />  // 橙色 - 运动健身
    }), []);

    const guideColors = useMemo(() => ({
        1: 'rgba(233, 30, 99, 0.08)', // 粉红色
        2: 'rgba(76, 175, 80, 0.08)',  // 绿色
        3: 'rgba(156, 39, 176, 0.08)', // 紫色
        4: 'rgba(255, 152, 0, 0.08)'   // 橙色
    }), []);

    const guideTextColors = useMemo(() => ({
        1: '#E91E63', // 粉红色
        2: '#4CAF50', // 绿色
        3: '#9C27B0', // 紫色
        4: '#FF9800'  // 橙色
    }), []);

    // 获取健康指南图标 - 使用缓存数据
    const getGuideIcon = (id) => guideIcons[id] || guideIcons[1];

    // 获取指南卡片颜色 - 使用缓存数据
    const getGuideColor = (id) => guideColors[id] || guideColors[1];

    // 获取指南文字颜色 - 使用缓存数据
    const getGuideTextColor = (id) => guideTextColors[id] || guideTextColors[1];

    // 优化：减少不必要的延迟时间，立即加载静态数据
    useEffect(() => {
        const fetchGuides = async () => {
            setLoading(true);
            setError('');

            try {
                // 健康指南API现在是公共的，不需要认证
                const response = await axios.get(`${API_URL}/api/health-guides`);
                setGuides(response.data || []);
                setLoading(false);
            } catch (err) {
                console.error('Error fetching health guides:', err);
                // 如果API失败，使用模拟数据
                setTimeout(() => {
                    setGuides(mockGuides);
                    setLoading(false);
                }, 100);
            }
        };

        fetchGuides();
    }, [mockGuides]);

    // 获取特定指南的详细内容
    const fetchGuideContent = async (id) => {
        setGuideLoading(true);

        try {
            // 从API获取健康指南详细内容
            const response = await axios.get(`${API_URL}/api/health-guides/${id}`);
            setGuideContent(response.data?.content || '**内容不可用**');
            setGuideLoading(false);
        } catch (err) {
            console.error(`Error fetching guide content for ${id}:`, err);
            // 如果API失败，尝试从模拟数据中获取
            const guide = mockGuides.find(g => g.id === id);
            setGuideContent(guide ? guide.content : '**内容不可用**');
            setGuideLoading(false);
        }
    };

    const handleOpenGuide = (guide) => {
        setSelectedGuide(guide);
        // 优化：直接使用已知内容，避免加载延迟
        setGuideContent(guide.content || '');
        setDialogOpen(true);
        // 如果没有缓存内容则获取
        if (!guide.content) {
            fetchGuideContent(guide.id);
        } else {
            setGuideLoading(false);
        }
    };

    const handleCloseDialog = () => {
        setDialogOpen(false);
        setSelectedGuide(null);
        setGuideContent('');
    };

    // 优化Markdown渲染函数 - 使用useMemo减少重复计算
    const renderMarkdown = useMemo(() => {
        return (text) => {
            if (!text) return '';

            // 替换标题
            let formatted = text.replace(/^# (.*$)/gm, '<h1>$1</h1>');
            formatted = formatted.replace(/^## (.*$)/gm, '<h2>$1</h2>');
            formatted = formatted.replace(/^### (.*$)/gm, '<h3>$1</h3>');

            // 替换列表
            formatted = formatted.replace(/^\- (.*$)/gm, '<li>$1</li>');
            formatted = formatted.replace(/<\/li>\n<li>/g, '</li><li>');
            formatted = formatted.replace(/(<li>.*<\/li>)/gms, '<ul>$1</ul>');

            // 替换粗体和斜体
            formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
            formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>');

            // 替换段落
            formatted = formatted.replace(/(?:^|\n)(?!\<h|\<ul)(.+)/g, '<p>$1</p>');

            return formatted;
        };
    }, []);

    // 使用useMemo优化渲染内容以减少重复计算
    const renderedGuideContent = useMemo(() => {
        if (guideLoading) {
            return (
                <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                    <CircularProgress color="primary" />
                    <Typography sx={{ ml: 2 }}>加载中...</Typography>
                </Box>
            );
        }

        return (
            <Box sx={{
                '& h1': {
                    mb: 2,
                    fontSize: '1.8rem',
                    color: theme.palette.mode === 'dark' ? '#90CAF9' : '#1565C0',
                    pb: 1,
                    borderBottom: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(144, 202, 249, 0.2)' : 'rgba(21, 101, 192, 0.2)'}`
                },
                '& h2': {
                    mt: 3,
                    mb: 2,
                    fontSize: '1.4rem',
                    color: theme.palette.mode === 'dark' ? '#64B5F6' : '#1976D2',
                    pb: 0.5,
                    borderBottom: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(100, 181, 246, 0.1)' : 'rgba(25, 118, 210, 0.1)'}`
                },
                '& h3': {
                    mt: 2,
                    mb: 1,
                    fontSize: '1.2rem',
                    color: theme.palette.mode === 'dark' ? '#42A5F5' : '#1976D2',
                },
                '& p': { mb: 2, lineHeight: 1.6 },
                '& ul': { mb: 2, pl: 2 },
                '& li': { mb: 0.5 },
                '& strong': { 
                    color: theme.palette.mode === 'dark' ? '#fff' : '#333',
                    fontWeight: 600 
                }
            }}
                dangerouslySetInnerHTML={{ __html: renderMarkdown(guideContent) }}
            />
        );
    }, [guideLoading, guideContent, theme.palette.mode, renderMarkdown]);

    // 使用useMemo缓存渲染指南列表，减少重新计算
    const renderedGuidesList = useMemo(() => {
        if (loading) {
            return (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
                    <CircularProgress color="primary" />
                </Box>
            );
        }

        if (error) {
            return (
                <Alert
                    severity="error"
                    sx={{
                        my: 3,
                        backgroundColor: theme.palette.mode === 'dark' ? 'rgba(211, 47, 47, 0.1)' : 'rgba(253, 237, 237, 0.9)',
                        border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(211, 47, 47, 0.2)' : 'rgba(211, 47, 47, 0.1)'}`,
                        '& .MuiAlert-icon': {
                            color: theme.palette.mode === 'dark' ? '#ef5350' : '#d32f2f'
                        }
                    }}
                >
                    {error}
                </Alert>
            );
        }

        if (guides.length === 0) {
            return (
                <Box sx={{
                    my: 4,
                    textAlign: 'center',
                    p: 3,
                    backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(255, 255, 255, 0.7)',
                    borderRadius: '12px',
                    border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)'}`,
                }}>
                    <Box sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        mb: 2,
                        color: theme.palette.mode === 'dark' ? '#64B5F6' : '#1976D2'
                    }}>
                        <HealthAndSafetyIcon sx={{ fontSize: 40 }} />
                    </Box>
                    <Typography variant="h6" color="text.secondary">暂无健康指南信息</Typography>
                </Box>
            );
        }

        return (
            <Box sx={{ mt: 3 }}>
                <Typography
                    variant="body1"
                    color="text.secondary"
                    paragraph
                    sx={{
                        mb: 3,
                        textAlign: 'center',
                        fontStyle: 'italic',
                        px: 2,
                        py: 1.5,
                        backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(25, 118, 210, 0.05)',
                        borderRadius: '8px',
                        border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(25, 118, 210, 0.08)'}`,
                    }}
                >
                    MMC健康指南为您提供专业的健康知识和实用建议，帮助您建立健康的生活方式。
                </Typography>
                <Box
                    sx={{
                        display: 'grid',
                        gridTemplateColumns: {
                            xs: '1fr',
                            sm: 'repeat(2, 1fr)',
                            md: 'repeat(2, 1fr)',
                            lg: 'repeat(3, 1fr)'
                        },
                        gap: 3
                    }}
                >
                    {guides.map((guide) => (
                        <Card
                            key={guide.id}
                            elevation={0}
                            sx={{
                                height: '100%',
                                display: 'flex',
                                flexDirection: 'column',
                                transition: 'transform 0.3s, box-shadow 0.3s',
                                backgroundColor: theme.palette.mode === 'dark'
                                    ? 'rgba(66, 66, 66, 0.8)'
                                    : 'rgba(255, 255, 255, 0.8)',
                                borderRadius: '12px',
                                border: `1px solid ${theme.palette.mode === 'dark'
                                    ? 'rgba(255, 255, 255, 0.08)'
                                    : 'rgba(0, 0, 0, 0.05)'}`,
                                boxShadow: theme.palette.mode === 'dark'
                                    ? '0 4px 20px rgba(0, 0, 0, 0.2)'
                                    : '0 4px 20px rgba(0, 0, 0, 0.06)',
                                overflow: 'hidden',
                                position: 'relative',
                                '&::before': {
                                    content: '""',
                                    position: 'absolute',
                                    top: 0,
                                    left: 0,
                                    width: '100%',
                                    height: '100%',
                                    background: `linear-gradient(160deg, ${getGuideColor(guide.id)} 0%, transparent 100%)`,
                                    opacity: theme.palette.mode === 'dark' ? 0.1 : 0.3,
                                    zIndex: 0
                                },
                                '&:hover': {
                                    transform: 'translateY(-4px)',
                                    boxShadow: theme.palette.mode === 'dark'
                                        ? '0 8px 30px rgba(0, 0, 0, 0.3)'
                                        : '0 8px 30px rgba(0, 0, 0, 0.1)',
                                    cursor: 'pointer'
                                }
                            }}
                            onClick={() => handleOpenGuide(guide)}
                        >
                            <CardContent sx={{ flexGrow: 1, position: 'relative', zIndex: 1, p: 3 }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                    <Box sx={{
                                        width: 40,
                                        height: 40,
                                        borderRadius: '50%',
                                        backgroundColor: getGuideColor(guide.id),
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        mr: 2
                                    }}>
                                        {getGuideIcon(guide.id)}
                                    </Box>
                                    <Typography
                                        variant="h6"
                                        component="h3"
                                        sx={{
                                            fontWeight: 600,
                                            color: theme.palette.mode === 'dark'
                                                ? '#fff'
                                                : getGuideTextColor(guide.id),
                                        }}
                                    >
                                        {guide.title}
                                    </Typography>
                                </Box>
                                <Typography
                                    variant="body2"
                                    color="text.secondary"
                                    sx={{
                                        mb: 3,
                                        lineHeight: 1.6,
                                        display: '-webkit-box',
                                        WebkitLineClamp: 3,
                                        WebkitBoxOrient: 'vertical',
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis',
                                        height: 60,
                                        pl: 1,
                                        borderLeft: `2px solid ${getGuideColor(guide.id)}`,
                                    }}
                                >
                                    {guide.description}
                                </Typography>
                                <Button
                                    variant="contained"
                                    size="small"
                                    sx={{
                                        mt: 'auto',
                                        backgroundColor: theme.palette.mode === 'dark'
                                            ? 'rgba(25, 118, 210, 0.8)'
                                            : getGuideTextColor(guide.id),
                                        color: '#fff',
                                        borderRadius: '8px',
                                        textTransform: 'none',
                                        boxShadow: 'none',
                                        '&:hover': {
                                            backgroundColor: theme.palette.mode === 'dark'
                                                ? 'rgba(25, 118, 210, 0.9)'
                                                : getGuideTextColor(guide.id),
                                            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)'
                                        }
                                    }}
                                    endIcon={<InfoOutlinedIcon />}
                                >
                                    查看详情
                                </Button>
                            </CardContent>
                        </Card>
                    ))}
                </Box>
            </Box>
        );
    }, [guides, loading, error, theme.palette.mode, getGuideColor, getGuideIcon, getGuideTextColor]);

    return (
        <>
            <Box sx={{
                my: 3,
                p: 3,
                position: 'relative',
                backgroundColor: theme.palette.mode === 'dark'
                    ? 'rgba(18, 18, 18, 0.7)'
                    : 'rgba(248, 250, 252, 0.8)',
                borderRadius: '24px',
                border: `1px solid ${theme.palette.mode === 'dark'
                    ? 'rgba(255, 255, 255, 0.05)'
                    : 'rgba(0, 0, 0, 0.02)'}`,
                boxShadow: theme.palette.mode === 'dark'
                    ? '0 8px 32px rgba(0, 0, 0, 0.2)'
                    : '0 8px 32px rgba(0, 0, 0, 0.05)',
            }}>
                <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    mb: 2,
                    pb: 2,
                    borderBottom: `1px solid ${theme.palette.mode === 'dark'
                        ? 'rgba(255, 255, 255, 0.05)'
                        : 'rgba(0, 0, 0, 0.05)'}`
                }}>
                    <HealthAndSafetyIcon
                        sx={{
                            fontSize: 32,
                            mr: 2,
                            color: theme.palette.mode === 'dark' ? '#90CAF9' : '#1976D2'
                        }}
                    />
                    <Typography
                        variant="h4"
                        component="h1"
                        sx={{
                            fontWeight: 600,
                            color: theme.palette.mode === 'dark' ? '#fff' : '#333'
                        }}
                    >
                        MMC健康指南
                    </Typography>
                </Box>
                <Typography variant="body1" color="text.secondary" paragraph>
                    专业的健康知识和实用建议，帮助您建立健康的生活方式，提升整体健康水平。
                </Typography>

                {renderedGuidesList}
            </Box>

            {/* 健康指南详情对话框 */}
            <Dialog
                open={dialogOpen}
                onClose={handleCloseDialog}
                fullWidth
                maxWidth="md"
                scroll="paper"
                sx={{
                    '& .MuiDialog-paper': {
                        backgroundColor: theme.palette.mode === 'dark'
                            ? 'rgba(66, 66, 66, 0.9)'
                            : 'rgba(255, 255, 255, 0.95)',
                        borderRadius: '20px',
                        boxShadow: theme.palette.mode === 'dark'
                            ? '0 15px 60px rgba(0, 0, 0, 0.5)'
                            : '0 15px 60px rgba(0, 0, 0, 0.15)',
                        border: `1px solid ${theme.palette.mode === 'dark'
                            ? 'rgba(255, 255, 255, 0.05)'
                            : 'rgba(0, 0, 0, 0.02)'}`,
                        overflow: 'hidden'
                    }
                }}
            >
                <DialogTitle
                    sx={{
                        py: 2,
                        backgroundColor: theme.palette.mode === 'dark'
                            ? 'rgba(18, 18, 18, 0.5)'
                            : 'rgba(248, 250, 252, 0.8)',
                        borderBottom: `1px solid ${theme.palette.mode === 'dark'
                            ? 'rgba(255, 255, 255, 0.05)'
                            : 'rgba(0, 0, 0, 0.05)'}`,
                    }}
                >
                    {selectedGuide && (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Box sx={{
                                width: 40,
                                height: 40,
                                borderRadius: '50%',
                                backgroundColor: getGuideColor(selectedGuide.id),
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                mr: 2
                            }}>
                                {getGuideIcon(selectedGuide.id)}
                            </Box>
                            <Typography
                                variant="h6"
                                sx={{
                                    fontWeight: 600,
                                    color: theme.palette.mode === 'dark' ? '#fff' : getGuideTextColor(selectedGuide.id),
                                }}
                            >
                                {selectedGuide.title}
                            </Typography>
                        </Box>
                    )}
                    <IconButton
                        aria-label="close"
                        onClick={handleCloseDialog}
                        sx={{
                            position: 'absolute',
                            right: 8,
                            top: 8,
                            color: theme.palette.text.secondary,
                        }}
                    >
                        <CloseIcon />
                    </IconButton>
                </DialogTitle>
                <DialogContent
                    dividers
                    sx={{
                        backgroundColor: theme.palette.mode === 'dark'
                            ? 'rgba(35, 35, 35, 0.5)'
                            : 'rgba(255, 255, 255, 0.8)',
                        p: 3
                    }}
                >
                    {renderedGuideContent}
                </DialogContent>
                <DialogActions
                    sx={{
                        backgroundColor: theme.palette.mode === 'dark'
                            ? 'rgba(18, 18, 18, 0.5)'
                            : 'rgba(248, 250, 252, 0.8)',
                        p: 2
                    }}
                >
                    <Button
                        variant="contained"
                        onClick={handleCloseDialog}
                        sx={{
                            backgroundColor: theme.palette.mode === 'dark'
                                ? 'rgba(25, 118, 210, 0.8)'
                                : '#1976D2',
                            color: '#fff',
                            borderRadius: '8px',
                            textTransform: 'none',
                            '&:hover': {
                                backgroundColor: theme.palette.mode === 'dark'
                                    ? 'rgba(25, 118, 210, 0.9)'
                                    : '#1565C0',
                                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)'
                            }
                        }}
                    >
                        关闭
                    </Button>
                </DialogActions>
            </Dialog>
        </>
    );
};

export default HealthGuide; 