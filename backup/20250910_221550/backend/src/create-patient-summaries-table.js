const mysql = require('mysql2/promise');
require('dotenv').config();

async function createPatientSummariesTable() {
  try {
    console.log('===== Creating patient_summaries table =====');

    // 创建数据库连接
    const pool = mysql.createPool({
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      waitForConnections: true,
      connectionLimit: 2,
      queueLimit: 0
    });

    // 检查表是否存在
    console.log('Checking if patient_summaries table exists...');
    const [tables] = await pool.query(`
      SHOW TABLES LIKE 'patient_summaries'
    `);

    if (tables.length === 0) {
      console.log('Table does not exist, creating it...');

      // 创建表
      const createTableSQL = `
        CREATE TABLE IF NOT EXISTS patient_summaries (
          id int(10) unsigned NOT NULL AUTO_INCREMENT,
          demographic_no int(10) NOT NULL,
          language varchar(10) NOT NULL DEFAULT 'en',
          summary_type varchar(50) NOT NULL,
          generated_summary text NOT NULL,
          AIgenerated_date datetime DEFAULT NULL,
          last_appointment_date_covered date DEFAULT NULL,
          last_updated timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (id),
          UNIQUE KEY unique_patient_summary (demographic_no, language, summary_type),
          KEY demographic_no_index (demographic_no),
          KEY summary_type_index (summary_type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='存储患者健康摘要，包括历史摘要和半年度摘要'
      `;

      await pool.query(createTableSQL);
      console.log('Table patient_summaries created successfully!');

      // 验证表是否创建成功
      const [tablesAfterCreate] = await pool.query(`
        SHOW TABLES LIKE 'patient_summaries'
      `);

      if (tablesAfterCreate.length > 0) {
        console.log('Verification: Table creation confirmed!');
      } else {
        console.log('Verification: Table creation FAILED!');
      }
    } else {
      console.log('Table patient_summaries already exists');

      // 检查AIgenerated_date字段是否存在
      console.log('Checking if AIgenerated_date column exists...');
      const [columns] = await pool.query(`
        SHOW COLUMNS FROM patient_summaries LIKE 'AIgenerated_date'
      `);

      if (columns.length === 0) {
        console.log('AIgenerated_date column does not exist, adding it...');
        await pool.query(`
          ALTER TABLE patient_summaries
          ADD COLUMN AIgenerated_date DATETIME DEFAULT NULL AFTER generated_summary
        `);
        console.log('AIgenerated_date column added successfully!');
      } else {
        console.log('AIgenerated_date column already exists');
      }
    }

    // 检查表结构
    const [columns] = await pool.query('DESCRIBE patient_summaries');
    console.log('\nTable structure:');
    columns.forEach(column => {
      console.log(`- ${column.Field} (${column.Type}) ${column.Key ? 'Key: ' + column.Key : ''}`);
    });

    console.log('\n===== Patient_summaries table setup completed =====');

    // 关闭连接池
    await pool.end();

    process.exit(0);
  } catch (error) {
    console.error('Error creating table:', error);
    process.exit(1);
  }
}

createPatientSummariesTable();
