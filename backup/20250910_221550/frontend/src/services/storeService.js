import axios from 'axios';

// 使用相对路径，因为axios baseURL已经在env.js中配置
const API_BASE_URL = '/api';

// 商品相关API
export const getProducts = async (filters = {}) => {
    try {
        const params = new URLSearchParams();
        if (filters.category_id) params.append('category_id', filters.category_id);
        if (filters.search) params.append('search', filters.search);
        if (filters.limit) params.append('limit', filters.limit);

        const response = await axios.get(`${API_BASE_URL}/store/products?${params}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching products:', error);
        throw error;
    }
};

export const getProductById = async (productId) => {
    try {
        const response = await axios.get(`${API_BASE_URL}/store/products/${productId}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching product:', error);
        throw error;
    }
};

export const createProduct = async (productData) => {
    try {
        const response = await axios.post(`${API_BASE_URL}/store/products`, productData);
        return response.data;
    } catch (error) {
        console.error('Error creating product:', error);
        throw error;
    }
};

export const updateProduct = async (productId, productData) => {
    try {
        const response = await axios.put(`${API_BASE_URL}/store/products/${productId}`, productData);
        return response.data;
    } catch (error) {
        console.error('Error updating product:', error);
        throw error;
    }
};

export const deleteProduct = async (productId) => {
    try {
        const response = await axios.delete(`${API_BASE_URL}/store/products/${productId}`);
        return response.data;
    } catch (error) {
        console.error('Error deleting product:', error);
        throw error;
    }
};

export const getCategories = async () => {
    try {
        const response = await axios.get(`${API_BASE_URL}/store/categories`);
        return response.data;
    } catch (error) {
        console.error('Error fetching categories:', error);
        throw error;
    }
};

// 订单相关API
export const createOrder = async (orderData) => {
    try {
        const response = await axios.post(`${API_BASE_URL}/store/orders`, orderData);
        return response.data;
    } catch (error) {
        console.error('Error creating order:', error);
        throw error;
    }
};

export const getUserOrders = async (filters = {}) => {
    try {
        const params = new URLSearchParams();
        if (filters.status) params.append('status', filters.status);
        if (filters.limit) params.append('limit', filters.limit);

        const response = await axios.get(`${API_BASE_URL}/store/orders?${params}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching orders:', error);
        throw error;
    }
};

export const getOrderById = async (orderId) => {
    try {
        const response = await axios.get(`${API_BASE_URL}/store/orders/${orderId}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching order:', error);
        throw error;
    }
};

// 支付相关API
export const createPaymentIntent = async (orderId) => {
    try {
        const response = await axios.post(`${API_BASE_URL}/store/payment/create-intent`, {
            orderId
        });
        return response.data;
    } catch (error) {
        console.error('Error creating payment intent:', error);
        throw error;
    }
};

// 购物车相关API（如果需要服务器端购物车）
export const addToCart = async (productId, quantity = 1) => {
    try {
        const response = await axios.post(`${API_BASE_URL}/store/cart`, {
            product_id: productId,
            quantity
        });
        return response.data;
    } catch (error) {
        console.error('Error adding to cart:', error);
        throw error;
    }
};

export const getCart = async () => {
    try {
        const response = await axios.get(`${API_BASE_URL}/store/cart`);
        return response.data;
    } catch (error) {
        console.error('Error fetching cart:', error);
        throw error;
    }
};

export const updateCartItem = async (productId, quantity) => {
    try {
        const response = await axios.put(`${API_BASE_URL}/store/cart/${productId}`, {
            quantity
        });
        return response.data;
    } catch (error) {
        console.error('Error updating cart item:', error);
        throw error;
    }
};

export const removeFromCart = async (productId) => {
    try {
        const response = await axios.delete(`${API_BASE_URL}/store/cart/${productId}`);
        return response.data;
    } catch (error) {
        console.error('Error removing from cart:', error);
        throw error;
    }
};

// 图片上传相关API
export const uploadProductImage = async (imageFile) => {
    try {
        const formData = new FormData();
        formData.append('image', imageFile);

        const response = await axios.post(`${API_BASE_URL}/files/upload/product-image`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
        return response.data;
    } catch (error) {
        console.error('Error uploading product image:', error);
        throw error;
    }
};

export const deleteProductImage = async (filename) => {
    try {
        const response = await axios.delete(`${API_BASE_URL}/files/product-image/${filename}`);
        return response.data;
    } catch (error) {
        console.error('Error deleting product image:', error);
        throw error;
    }
}; 