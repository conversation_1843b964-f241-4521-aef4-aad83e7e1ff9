import React, { useMemo } from 'react';
import { Box, Typography, useTheme } from '@mui/material';

const MarkdownPreview = ({ content, maxHeight = 400 }) => {
    const theme = useTheme();
    
    const renderMarkdown = useMemo(() => {
        if (!content) return '';

        // 替换标题
        let formatted = content.replace(/^# (.*$)/gm, '<h1>$1</h1>');
        formatted = formatted.replace(/^## (.*$)/gm, '<h2>$1</h2>');
        formatted = formatted.replace(/^### (.*$)/gm, '<h3>$1</h3>');

        // 替换列表
        formatted = formatted.replace(/^\- (.*$)/gm, '<li>$1</li>');
        formatted = formatted.replace(/<\/li>\n<li>/g, '</li><li>');
        formatted = formatted.replace(/(<li>.*<\/li>)/gms, '<ul>$1</ul>');

        // 替换粗体和斜体
        formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>');

        // 替换段落
        formatted = formatted.replace(/(?:^|\n)(?!\<h|\<ul)(.+)/g, '<p>$1</p>');

        return formatted;
    }, [content]);

    return (
        <Box 
            sx={{
                maxHeight: maxHeight,
                overflow: 'auto',
                p: 2,
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 1,
                backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)',
                '& h1': {
                    mb: 2,
                    fontSize: '1.5rem',
                    color: theme.palette.primary.main,
                    fontWeight: 600,
                    borderBottom: `2px solid ${theme.palette.primary.main}`,
                    pb: 1
                },
                '& h2': {
                    mt: 2,
                    mb: 1.5,
                    fontSize: '1.25rem',
                    color: theme.palette.primary.main,
                    fontWeight: 600
                },
                '& h3': {
                    mt: 1.5,
                    mb: 1,
                    fontSize: '1.1rem',
                    color: theme.palette.primary.main,
                    fontWeight: 600
                },
                '& p': { 
                    mb: 1.5, 
                    lineHeight: 1.6,
                    color: theme.palette.text.primary
                },
                '& ul': { 
                    mb: 1.5, 
                    pl: 2,
                    '& li': {
                        mb: 0.5,
                        color: theme.palette.text.primary
                    }
                },
                '& strong': { 
                    fontWeight: 600,
                    color: theme.palette.text.primary
                },
                '& em': {
                    fontStyle: 'italic',
                    color: theme.palette.text.secondary
                }
            }}
        >
            {content ? (
                <div dangerouslySetInnerHTML={{ __html: renderMarkdown }} />
            ) : (
                <Typography color="text.secondary" sx={{ fontStyle: 'italic' }}>
                    暂无内容...
                </Typography>
            )}
        </Box>
    );
};

export default MarkdownPreview; 