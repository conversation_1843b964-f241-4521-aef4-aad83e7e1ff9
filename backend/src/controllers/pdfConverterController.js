const pdfToMarkdownService = require('../services/pdfToMarkdownService');
const path = require('path');
const fs = require('fs').promises;
const logger = require('../utils/logger');

const pdfConverterController = {
    /**
     * 转换单个PDF文件到Markdown
     */
    async convertPDF(req, res) {
        try {
            const { filePath, options = {} } = req.body;
            
            if (!filePath) {
                return res.status(400).json({
                    success: false,
                    message: 'File path is required'
                });
            }

            // 检查文件是否存在
            try {
                await fs.access(filePath);
            } catch (error) {
                return res.status(404).json({
                    success: false,
                    message: 'PDF file not found'
                });
            }

            const markdown = await pdfToMarkdownService.convertPDFToMarkdown(filePath, options);

            res.json({
                success: true,
                markdown,
                message: 'PDF converted successfully'
            });

        } catch (error) {
            logger.error('Error in convertPDF controller:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to convert PDF',
                error: error.message
            });
        }
    },

    /**
     * 转换PDF并保存为文件
     */
    async convertAndSave(req, res) {
        try {
            const { filePath, outputPath, options = {} } = req.body;
            
            if (!filePath || !outputPath) {
                return res.status(400).json({
                    success: false,
                    message: 'File path and output path are required'
                });
            }

            const savedPath = await pdfToMarkdownService.convertAndSave(filePath, outputPath, options);

            res.json({
                success: true,
                outputPath: savedPath,
                message: 'PDF converted and saved successfully'
            });

        } catch (error) {
            logger.error('Error in convertAndSave controller:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to convert and save PDF',
                error: error.message
            });
        }
    },

    /**
     * 批量转换PDF文件
     */
    async batchConvert(req, res) {
        try {
            const { pdfFiles, outputDir, options = {} } = req.body;
            
            if (!pdfFiles || !Array.isArray(pdfFiles) || pdfFiles.length === 0) {
                return res.status(400).json({
                    success: false,
                    message: 'PDF files array is required'
                });
            }

            if (!outputDir) {
                return res.status(400).json({
                    success: false,
                    message: 'Output directory is required'
                });
            }

            // 确保输出目录存在
            await fs.mkdir(outputDir, { recursive: true });

            const results = await pdfToMarkdownService.batchConvert(pdfFiles, outputDir, options);

            const successCount = results.filter(r => r.success).length;
            const failureCount = results.length - successCount;

            res.json({
                success: true,
                results,
                summary: {
                    total: results.length,
                    successful: successCount,
                    failed: failureCount
                },
                message: `Batch conversion completed: ${successCount} successful, ${failureCount} failed`
            });

        } catch (error) {
            logger.error('Error in batchConvert controller:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to batch convert PDFs',
                error: error.message
            });
        }
    },

    /**
     * 转换医疗报告PDF（专门针对您的应用场景）
     */
    async convertMedicalReport(req, res) {
        try {
            const { demographicNo, reportType, reportId } = req.params;
            const { pages, extractImages = true } = req.body;

            // 构建PDF文件路径（根据您的文档结构调整）
            const pdfPath = path.join('/OscarDocument', demographicNo, 'labs', `${reportId}.pdf`);
            
            // 检查文件是否存在
            try {
                await fs.access(pdfPath);
            } catch (error) {
                return res.status(404).json({
                    success: false,
                    message: 'Medical report PDF not found'
                });
            }

            // 设置针对医疗报告优化的选项
            const options = {
                pages,
                writeImages: extractImages,
                imagePath: path.join('/tmp', 'medical_images', demographicNo),
                pageChunks: true, // 医疗报告通常需要分页处理
                extractWords: true // 提取详细的文字信息
            };

            const result = await pdfToMarkdownService.convertPDFToMarkdown(pdfPath, options);

            // 解析结果（如果是JSON格式的分块数据）
            let parsedResult;
            try {
                parsedResult = JSON.parse(result);
            } catch {
                parsedResult = result; // 如果不是JSON，直接使用原始字符串
            }

            res.json({
                success: true,
                data: parsedResult,
                metadata: {
                    demographicNo,
                    reportType,
                    reportId,
                    extractedImages: extractImages,
                    processingOptions: options
                },
                message: 'Medical report converted successfully'
            });

        } catch (error) {
            logger.error('Error in convertMedicalReport controller:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to convert medical report',
                error: error.message
            });
        }
    }
};

module.exports = pdfConverterController; 