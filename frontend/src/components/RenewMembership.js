import React, { useState, useEffect } from 'react';
import { API_URL } from '../utils/env';
import { useNavigate } from 'react-router-dom';
import {
    Container,
    Paper,
    Typography,
    Button,
    Grid,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Box,
    CircularProgress,
    Alert,
} from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledPaper = styled(Paper)(({ theme }) => ({
    marginTop: theme.spacing(8),
    padding: theme.spacing(4),
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
}));

const StyledButton = styled(Button)(({ theme }) => ({
    margin: theme.spacing(3, 0, 2),
}));

const membershipTypes = [
    { value: 'AHM_EM', label: 'Individual Membership', price: 2350 },
    { value: 'AHM_EM_KD', label: 'Child Membership', price: 850 },
    { value: 'AHM_SIG', label: 'Family Membership', price: 5500 },
    { value: 'AHM_SIG_KD', label: 'Family Child Membership', price: 850 },
];

const RenewMembership = () => {
    const [selectedType, setSelectedType] = useState('');
    const [error, setError] = useState('');
    const navigate = useNavigate();

    const handleChange = (event) => {
        setSelectedType(event.target.value);
    };

    const handleSubmit = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`${API_URL}/api/auth/renew-membership`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`,
                },
                body: JSON.stringify({ membershipType: selectedType }),
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'Failed to renew membership');
            }

            navigate('/profile');
        } catch (err) {
            setError(err.message);
        }
    };

    return (
        <Container component="main" maxWidth="xs">
            <StyledPaper elevation={3}>
                <Typography component="h1" variant="h5">
                    Renew Membership
                </Typography>
                {error && (
                    <Typography color="error" align="center">
                        {error}
                    </Typography>
                )}
                <Grid container spacing={2} sx={{ mt: 2 }}>
                    <Grid item xs={12}>
                        <FormControl fullWidth>
                            <InputLabel>Membership Type</InputLabel>
                            <Select
                                value={selectedType}
                                onChange={handleChange}
                                label="Membership Type"
                            >
                                {membershipTypes.map((type) => (
                                    <MenuItem key={type.value} value={type.value}>
                                        {type.label} - ${type.price}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid item xs={12}>
                        <StyledButton
                            fullWidth
                            variant="contained"
                            color="primary"
                            onClick={handleSubmit}
                            disabled={!selectedType}
                        >
                            Renew Membership
                        </StyledButton>
                    </Grid>
                </Grid>
            </StyledPaper>
        </Container>
    );
};

export default RenewMembership; 