const pool = require('../config/database');

// 验证是否为管理员中间件
exports.isAdmin = async (req, res, next) => {
    try {
        // 从req.user获取用户信息（authenticate中间件已设置）
        if (!req.user || req.user.role !== 'admin') {
            return res.status(403).json({ success: false, message: '无权限执行此操作' });
        }
        next();
    } catch (error) {
        console.error('Admin权限验证错误:', error);
        res.status(500).json({ success: false, message: '服务器错误' });
    }
};

// 获取所有视频
exports.getAllVideos = async (req, res) => {
    try {
        const [rows] = await pool.query(
            'SELECT id, title, description, thumbnailUrl, youtubeLink, category, views, likes, is_active, created_at, updated_at FROM youtube_videos WHERE is_active = 1 ORDER BY created_at DESC'
        );
        res.json(rows);
    } catch (error) {
        console.error('获取YouTube视频失败:', error);
        res.status(500).json({ success: false, message: '服务器错误' });
    }
};

// 按类别获取视频
exports.getVideosByCategory = async (req, res) => {
    try {
        const { category } = req.params;
        const [rows] = await pool.query(
            'SELECT id, title, description, thumbnailUrl, youtubeLink, category, views, likes, is_active, created_at, updated_at FROM youtube_videos WHERE category = ? AND is_active = 1 ORDER BY created_at DESC',
            [category]
        );
        res.json(rows);
    } catch (error) {
        console.error('获取分类YouTube视频失败:', error);
        res.status(500).json({ success: false, message: '服务器错误' });
    }
};

// 添加视频
exports.addVideo = async (req, res) => {
    try {
        const { title, description, thumbnailUrl, youtubeLink, category } = req.body;

        // 验证必填字段
        if (!title || !youtubeLink || !category) {
            return res.status(400).json({ success: false, message: '标题、链接和分类为必填项' });
        }

        const [result] = await pool.query(
            'INSERT INTO youtube_videos (title, description, thumbnailUrl, youtubeLink, category, created_at) VALUES (?, ?, ?, ?, ?, NOW())',
            [title, description || '', thumbnailUrl || '', youtubeLink, category]
        );

        res.status(201).json({
            success: true,
            message: '视频添加成功',
            id: result.insertId
        });
    } catch (error) {
        console.error('添加YouTube视频失败:', error);
        res.status(500).json({ success: false, message: '服务器错误' });
    }
};

// 更新视频
exports.updateVideo = async (req, res) => {
    try {
        const { id } = req.params;
        const { title, description, thumbnailUrl, youtubeLink, category, views, likes, is_active } = req.body;

        await pool.query(
            'UPDATE youtube_videos SET title = ?, description = ?, thumbnailUrl = ?, youtubeLink = ?, category = ?, views = ?, likes = ?, is_active = ? WHERE id = ?',
            [title, description, thumbnailUrl, youtubeLink, category, views, likes, is_active, id]
        );

        res.json({ success: true, message: '视频更新成功' });
    } catch (error) {
        console.error('更新YouTube视频失败:', error);
        res.status(500).json({ success: false, message: '服务器错误' });
    }
};

// 删除视频（软删除）
exports.deleteVideo = async (req, res) => {
    try {
        const { id } = req.params;

        await pool.query(
            'UPDATE youtube_videos SET is_active = 0, updated_at = NOW() WHERE id = ?',
            [id]
        );

        res.json({ success: true, message: '视频删除成功' });
    } catch (error) {
        console.error('删除YouTube视频失败:', error);
        res.status(500).json({ success: false, message: '服务器错误' });
    }
};

// 获取所有视频（管理员）
exports.getAllVideosAdmin = async (req, res) => {
    try {
        const [rows] = await pool.query(
            'SELECT id, title, description, thumbnailUrl, youtubeLink, category, views, likes, is_active, created_at, updated_at FROM youtube_videos ORDER BY created_at DESC'
        );
        res.json(rows);
    } catch (error) {
        console.error('获取所有YouTube视频失败:', error);
        res.status(500).json({ success: false, message: '服务器错误' });
    }
}; 