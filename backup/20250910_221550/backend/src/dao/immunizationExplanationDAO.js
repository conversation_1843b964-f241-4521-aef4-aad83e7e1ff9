const pool = require('../config/database');
const { createLogger } = require('../utils/logger');

const logger = createLogger('ImmunizationExplanationDAO');

class ImmunizationExplanationDAO {
    /**
     * 根据疫苗ID获取疫苗接种解释
     * @param {number} immunizationId - 疫苗接种记录ID
     * @param {string} language - 语言 ('zh' 或 'en')
     * @returns {Promise<Object>} 疫苗接种解释对象
     */
    static async getByImmunizationId(immunizationId, language = 'zh') {
        const query = `
            SELECT id, immunization_id, explanation, language, created_at, updated_at
            FROM immunization_explanations
            WHERE immunization_id = ? AND language = ?
            LIMIT 1;
        `;

        try {
            const startTime = Date.now();
            const [results] = await pool.query(query, [immunizationId, language]);
            const queryTime = Date.now() - startTime;

            if (queryTime > 1000) {
                logger.warn(`Slow query detected: ${queryTime}ms`, {
                    operation: 'getByImmunizationId',
                    immunizationId,
                    language
                });
            }

            return results.length > 0 ? results[0] : null;
        } catch (error) {
            logger.error('Error in getByImmunizationId:', {
                error,
                immunizationId,
                language
            });
            throw error;
        }
    }

    /**
     * 创建或更新疫苗接种解释
     * @param {number} immunizationId - 疫苗接种记录ID
     * @param {string} explanation - AI生成的解释文本
     * @param {string} language - 语言 ('zh' 或 'en')
     * @returns {Promise<Object>} 处理结果
     */
    static async createOrUpdate(immunizationId, explanation, language = 'zh') {
        const query = `
            INSERT INTO immunization_explanations (immunization_id, explanation, language)
            VALUES (?, ?, ?)
            ON DUPLICATE KEY UPDATE
                explanation = VALUES(explanation),
                updated_at = CURRENT_TIMESTAMP;
        `;

        try {
            const startTime = Date.now();
            const [result] = await pool.query(query, [immunizationId, explanation, language]);
            const queryTime = Date.now() - startTime;

            if (queryTime > 1000) {
                logger.warn(`Slow query detected: ${queryTime}ms`, {
                    operation: 'createOrUpdate',
                    immunizationId,
                    language
                });
            }

            return {
                success: true,
                id: result.insertId || null,
                affectedRows: result.affectedRows
            };
        } catch (error) {
            logger.error('Error in createOrUpdate:', {
                error,
                immunizationId,
                language
            });
            throw error;
        }
    }

    /**
     * 删除疫苗接种解释
     * @param {number} immunizationId - 疫苗接种记录ID
     * @param {string} language - 语言 ('zh' 或 'en')
     * @returns {Promise<Object>} 处理结果
     */
    static async delete(immunizationId, language = 'zh') {
        const query = `
            DELETE FROM immunization_explanations
            WHERE immunization_id = ? AND language = ?;
        `;

        try {
            const [result] = await pool.query(query, [immunizationId, language]);
            return {
                success: true,
                affectedRows: result.affectedRows
            };
        } catch (error) {
            logger.error('Error in delete:', {
                error,
                immunizationId,
                language
            });
            throw error;
        }
    }
}

module.exports = ImmunizationExplanationDAO; 