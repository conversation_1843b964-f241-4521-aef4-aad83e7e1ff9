const db = require('../config/database');
const { ValidationError, AuthorizationError } = require('../utils/errors');

// 获取所有健康指南
const getHealthGuides = async (req, res) => {
    try {
        const { category, featured, status = 'published' } = req.query;
        
        let query = `
            SELECT id, title, description, category, featured, status, created_at, updated_at
            FROM health_guides 
            WHERE status = ?
        `;
        const params = [status];

        if (category) {
            query += ' AND category = ?';
            params.push(category);
        }

        if (featured !== undefined) {
            query += ' AND featured = ?';
            params.push(featured === 'true' ? 1 : 0);
        }

        query += ' ORDER BY featured DESC, created_at DESC';

        const [guides] = await db.execute(query, params);
        
        res.json(guides);
    } catch (error) {
        console.error('Error fetching health guides:', error);
        res.status(500).json({ 
            message: 'Failed to fetch health guides',
            error: error.message 
        });
    }
};

// 获取单个健康指南详情
const getHealthGuideById = async (req, res) => {
    try {
        const { id } = req.params;
        
        const [guides] = await db.execute(
            'SELECT * FROM health_guides WHERE id = ? AND status = ?',
            [id, 'published']
        );

        if (guides.length === 0) {
            return res.status(404).json({ message: 'Health guide not found' });
        }

        res.json(guides[0]);
    } catch (error) {
        console.error('Error fetching health guide:', error);
        res.status(500).json({ 
            message: 'Failed to fetch health guide',
            error: error.message 
        });
    }
};

// 创建健康指南 (仅管理员)
const createHealthGuide = async (req, res) => {
    try {
        const { title, description, content, category, featured = false } = req.body;
        const authorId = req.user?.id;

        // 验证必填字段
        if (!title || !description || !content) {
            throw new ValidationError('Title, description, and content are required');
        }

        // 检查管理员权限
        if (req.user?.role !== 'admin') {
            throw new AuthorizationError('Only administrators can create health guides');
        }

        const [result] = await db.execute(
            `INSERT INTO health_guides (title, description, content, category, author_id, featured, status) 
             VALUES (?, ?, ?, ?, ?, ?, 'published')`,
            [title, description, content, category, authorId, featured ? 1 : 0]
        );

        const [newGuide] = await db.execute(
            'SELECT * FROM health_guides WHERE id = ?',
            [result.insertId]
        );

        res.status(201).json({
            message: 'Health guide created successfully',
            guide: newGuide[0]
        });
    } catch (error) {
        console.error('Error creating health guide:', error);
        
        if (error instanceof ValidationError || error instanceof AuthorizationError) {
            return res.status(error instanceof ValidationError ? 400 : 403).json({ 
                message: error.message 
            });
        }
        
        res.status(500).json({ 
            message: 'Failed to create health guide',
            error: error.message 
        });
    }
};

// 更新健康指南 (仅管理员)
const updateHealthGuide = async (req, res) => {
    try {
        const { id } = req.params;
        const { title, description, content, category, featured, status } = req.body;

        // 检查管理员权限
        if (req.user?.role !== 'admin') {
            throw new AuthorizationError('Only administrators can update health guides');
        }

        // 检查指南是否存在
        const [existingGuides] = await db.execute(
            'SELECT id FROM health_guides WHERE id = ?',
            [id]
        );

        if (existingGuides.length === 0) {
            return res.status(404).json({ message: 'Health guide not found' });
        }

        // 构建更新查询
        const updateFields = [];
        const updateValues = [];

        if (title !== undefined) {
            updateFields.push('title = ?');
            updateValues.push(title);
        }
        if (description !== undefined) {
            updateFields.push('description = ?');
            updateValues.push(description);
        }
        if (content !== undefined) {
            updateFields.push('content = ?');
            updateValues.push(content);
        }
        if (category !== undefined) {
            updateFields.push('category = ?');
            updateValues.push(category);
        }
        if (featured !== undefined) {
            updateFields.push('featured = ?');
            updateValues.push(featured ? 1 : 0);
        }
        if (status !== undefined) {
            updateFields.push('status = ?');
            updateValues.push(status);
        }

        if (updateFields.length === 0) {
            return res.status(400).json({ message: 'No fields to update' });
        }

        updateValues.push(id);

        await db.execute(
            `UPDATE health_guides SET ${updateFields.join(', ')} WHERE id = ?`,
            updateValues
        );

        const [updatedGuide] = await db.execute(
            'SELECT * FROM health_guides WHERE id = ?',
            [id]
        );

        res.json({
            message: 'Health guide updated successfully',
            guide: updatedGuide[0]
        });
    } catch (error) {
        console.error('Error updating health guide:', error);
        
        if (error instanceof AuthorizationError) {
            return res.status(403).json({ message: error.message });
        }
        
        res.status(500).json({ 
            message: 'Failed to update health guide',
            error: error.message 
        });
    }
};

// 删除健康指南 (仅管理员)
const deleteHealthGuide = async (req, res) => {
    try {
        const { id } = req.params;

        // 检查管理员权限
        if (req.user?.role !== 'admin') {
            throw new AuthorizationError('Only administrators can delete health guides');
        }

        // 检查指南是否存在
        const [existingGuides] = await db.execute(
            'SELECT id FROM health_guides WHERE id = ?',
            [id]
        );

        if (existingGuides.length === 0) {
            return res.status(404).json({ message: 'Health guide not found' });
        }

        // 软删除：将状态设置为 archived
        await db.execute(
            'UPDATE health_guides SET status = ? WHERE id = ?',
            ['archived', id]
        );

        res.json({ message: 'Health guide deleted successfully' });
    } catch (error) {
        console.error('Error deleting health guide:', error);
        
        if (error instanceof AuthorizationError) {
            return res.status(403).json({ message: error.message });
        }
        
        res.status(500).json({ 
            message: 'Failed to delete health guide',
            error: error.message 
        });
    }
};

// 获取健康指南分类
const getHealthGuideCategories = async (req, res) => {
    try {
        const [categories] = await db.execute(
            `SELECT DISTINCT category, COUNT(*) as count 
             FROM health_guides 
             WHERE status = 'published' AND category IS NOT NULL 
             GROUP BY category 
             ORDER BY count DESC, category ASC`
        );

        res.json(categories);
    } catch (error) {
        console.error('Error fetching health guide categories:', error);
        res.status(500).json({ 
            message: 'Failed to fetch categories',
            error: error.message 
        });
    }
};

// 管理员获取所有健康指南（包括草稿和已归档）
const getHealthGuidesAdmin = async (req, res) => {
    try {
        // 检查管理员权限
        if (req.user?.role !== 'admin') {
            throw new AuthorizationError('Only administrators can access this endpoint');
        }

        const { category, status, page = 1, limit = 20 } = req.query;
        const offset = (page - 1) * limit;
        
        let query = `
            SELECT hg.*, ua.email as author_email
            FROM health_guides hg
            LEFT JOIN user_auth ua ON hg.author_id = ua.id
            WHERE 1=1
        `;
        const params = [];

        if (category) {
            query += ' AND hg.category = ?';
            params.push(category);
        }

        if (status) {
            query += ' AND hg.status = ?';
            params.push(status);
        }

        query += ' ORDER BY hg.created_at DESC LIMIT ? OFFSET ?';
        params.push(parseInt(limit), parseInt(offset));

        const [guides] = await db.execute(query, params);

        // 获取总数
        let countQuery = 'SELECT COUNT(*) as total FROM health_guides WHERE 1=1';
        const countParams = [];

        if (category) {
            countQuery += ' AND category = ?';
            countParams.push(category);
        }

        if (status) {
            countQuery += ' AND status = ?';
            countParams.push(status);
        }

        const [countResult] = await db.execute(countQuery, countParams);
        const total = countResult[0].total;

        res.json({
            guides,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        console.error('Error fetching health guides for admin:', error);
        
        if (error instanceof AuthorizationError) {
            return res.status(403).json({ message: error.message });
        }
        
        res.status(500).json({ 
            message: 'Failed to fetch health guides',
            error: error.message 
        });
    }
};

module.exports = {
    getHealthGuides,
    getHealthGuideById,
    createHealthGuide,
    updateHealthGuide,
    deleteHealthGuide,
    getHealthGuideCategories,
    getHealthGuidesAdmin
}; 