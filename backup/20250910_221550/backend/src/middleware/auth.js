console.log('>>> Loading backend/src/middleware/auth.js...'); // <<< DEBUG LOG
const jwt = require('jsonwebtoken');
const User = require('../models/user');

/**
 * 检测用户类型：member 或 user
 * @param {number} demographicNo - 用户的demographic_no
 * @returns {string} - 'member' 或 'user'
 */
async function detectUserType(demographicNo) {
    try {
        // 获取用户的membership信息
        const membershipInfo = await User.getMembershipInfo(demographicNo);
        
        // 如果有当前有效的membership，则为member
        if (membershipInfo && membershipInfo.current && !membershipInfo.current.isExpired) {
            return 'member';
        }
        
        // 否则为普通user
        return 'user';
    } catch (error) {
        console.error('Error detecting user type:', error);
        // 出错时默认为user
        return 'user';
    }
}

/**
 * 更新用户的role字段
 * @param {number} userId - 用户ID
 * @param {string} userType - 用户类型 ('member' 或 'user')
 */
async function updateUserRole(userId, userType) {
    try {
        const pool = require('../config/database');
        await pool.query(
            'UPDATE user_auth SET role = ? WHERE id = ?',
            [userType, userId]
        );
    } catch (error) {
        console.error('Error updating user role:', error);
    }
}

const auth = async (req, res, next) => {
    try {
        let token;

        // 尝试从Authorization头获取token
        const authHeader = req.header('Authorization');
        if (authHeader) {
            token = authHeader.replace('Bearer ', '');
        }
        // 如果Authorization头不存在，尝试从URL参数获取token
        else if (req.query.token) {
            token = req.query.token;
        }
        // 两者都不存在则抛出错误
        else {
            throw new Error('No auth token provided');
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);

        const user = await User.findById(decoded.id);
        if (!user) {
            throw new Error('User not found');
        }

        // 如果用户有demographic_no，检测并更新用户类型（但不影响admin）
        if (user.demographic_no && user.role !== 'admin') {
            const currentUserType = await detectUserType(user.demographic_no);
            
            // 如果检测到的用户类型与数据库中的不同，更新数据库
            if (user.role !== currentUserType) {
                await updateUserRole(user.id, currentUserType);
                // 更新user对象中的role，以便后续使用
                const oldRole = user.role;
                user.role = currentUserType;
                console.log(`Updated user ${user.id} role from ${oldRole} to ${currentUserType}`);
            }
        }

        req.user = user;
        req.token = token;
        next();
    } catch (error) {
        res.status(401).json({ message: '请先登录' });
    }
};

module.exports = auth; 