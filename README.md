# MMC Web Application

MMC医疗中心 (MMC) 的综合医疗管理Web应用程序。

## 项目概述

这是一个基于Docker的全栈医疗管理系统，包含前端、后端和聊天机器人服务。系统提供患者医疗记录管理、预约服务、会员管理、在线咨询等功能。

## 架构组成

- **Frontend**: React.js + Material-UI (端口: 3001)
- **Backend**: Node.js + Express + MySQL (端口: 3000)
- **Chatbot Service**: AI聊天机器人服务 (端口: 3002)
- **Database**: MySQL 数据库
- **File Storage**: Supabase 云存储

## 快速开始

### 使用Docker部署

```bash
# 快速重建所有服务
./quick_rebuild.sh

# 高级重建选项
./rebuild_docker.sh -c  # 完整清理重建
./rebuild_docker.sh -s backend  # 只重建后端服务
./rebuild_docker.sh -l  # 重建后显示日志

# 完整的维护脚本
./docker_maintenance.sh --clean --logs
```

### 文档同步

系统包含增强版的PDF文档同步脚本，用于从远程Oscar服务器同步医疗文档：

```bash
# 正常同步
./sync-pdfs.sh

# 查看统计信息
./sync-pdfs.sh --stats-only

# 模拟运行（测试）
./sync-pdfs.sh --dry-run

# 查看帮助
./sync-pdfs.sh --help
```

详细使用说明请参考 [PDF同步脚本指南](./docs/operations/SYNC_PDFS_README.md)

## 可用脚本

| 脚本名称 | 功能描述 | 使用示例 |
|---------|---------|---------|
| `quick_rebuild.sh` | 快速Docker重建 | `./quick_rebuild.sh` |
| `rebuild_docker.sh` | 高级Docker重建选项 | `./rebuild_docker.sh -c` |
| `docker_maintenance.sh` | 完整Docker维护 | `./docker_maintenance.sh --clean` |
| `sync-pdfs.sh` | 文档同步脚本 | `./sync-pdfs.sh --stats-only` |
| `deploy_store.sh` | 商店部署脚本 | `./deploy_store.sh` |
| `set-gemini-api-key.sh` | 设置Gemini API密钥 | `./set-gemini-api-key.sh` |

## 📚 文档中心

完整的项目文档已按功能分类整理，请访问 [文档中心](./docs/) 查看：

### 🚀 部署相关
- [Docker脚本使用指南](./docs/deployment/DOCKER_SCRIPTS_README.md) - Docker维护脚本详细说明
- [在线商店设置指南](./docs/deployment/STORE_SETUP.md) - 商店功能部署配置

### 🔧 开发相关  
- [开发规范指南](./docs/development/DEV_GUIDELINES.md) - 代码规范和开发流程
- [新功能添加指南](./docs/development/add_new_feature_guide.md) - 添加新功能的步骤
- [前端架构总览](./docs/development/frontend_summary.md) - 前端技术栈说明
- [后端架构总览](./docs/development/backend_summary.md) - 后端架构设计

### 🛠️ 运维相关
- [PDF同步脚本指南](./docs/operations/SYNC_PDFS_README.md) - 文档同步系统使用说明
- [同步脚本变更日志](./docs/operations/SYNC_SCRIPT_CHANGELOG.md) - 同步功能更新记录

### 📡 API相关
- [API接口文档](./docs/api/API.md) - RESTful API接口规范
- [推荐系统文档](./docs/api/REFERRAL_SYSTEM.md) - 推荐奖励系统API说明

## 服务访问地址

- **前端应用**: http://localhost:3001
- **后端API**: http://localhost:3000
- **聊天机器人**: http://localhost:3002

## 主要功能

### 患者管理
- 📋 医疗记录管理
- 💊 处方记录和AI解释
- 🩺 免疫接种记录
- 📊 实验室报告
- 📋 预约管理

### AI功能
- 🤖 智能聊天机器人
- 📝 处方AI解释
- 🩺 免疫接种AI说明
- 📊 营养师评论AI总结
- 📈 患者半年总结AI生成

### 会员服务
- 👥 会员管理系统
- 🛒 在线商店
- 🎯 推荐奖励系统
- 📚 健康资源库
- 🎥 YouTube视频管理

### 系统管理
- 👨‍💼 管理员后台
- 📁 文件管理和共享
- 🔐 用户认证和授权
- 👨‍👩‍👧‍👦 家庭成员管理
- ⚙️ 系统设置

## 环境要求

- Docker & Docker Compose
- Node.js 20+ (用于开发)
- MySQL 8.0+
- SSH访问远程Oscar服务器 (用于文档同步)

## 配置文件

- `.env` - 环境变量配置
- `docker-compose.yml` - Docker服务配置
- `frontend/nginx.conf` - Nginx代理配置
- `credentials.json` - Google服务凭据

## 日志管理

系统日志存储在 `logs/` 目录下：

```bash
# 查看同步日志
tail -f logs/sync-pdfs.log

# 查看cron同步日志
tail -f logs/cron-sync.log

# 查看错误日志
grep "ERROR" logs/*.log
```

## 开发指南

### 添加新功能
1. 参考 [新功能添加指南](./docs/development/add_new_feature_guide.md)
2. 遵循 [开发规范](./docs/development/DEV_GUIDELINES.md)
3. 更新相关文档
4. 添加必要的测试

### 部署流程
1. 确保所有环境变量已配置
2. 运行 `./rebuild_docker.sh -c` 进行完整重建
3. 验证所有服务正常运行
4. 执行 `./sync-pdfs.sh --stats-only` 检查文档同步状态

## 故障排除

### Docker相关问题
```bash
# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f [service_name]

# 完整重建
./rebuild_docker.sh --clean
```

### 文档同步问题
```bash
# 检查SSH连接
ssh mmc@216.232.48.211 "echo 'Connection test'"

# 查看同步日志
./sync-pdfs.sh --stats-only

# 详细日志
./sync-pdfs.sh -v
```

### 常见错误
- **端口冲突**: 检查端口3000-3002是否被占用
- **权限问题**: 确保脚本有执行权限 `chmod +x *.sh`
- **网络连接**: 验证到远程服务器的连接

## 安全注意事项

- 🔐 定期更新SSH密钥
- 🛡️ 保护敏感环境变量
- 📊 监控系统日志
- 🔄 定期备份数据库
- 🚫 限制管理员权限访问

## 支持与联系

如有问题或需要技术支持，请：

1. 查阅 [文档中心](./docs/) 相关文档
2. 查看项目日志文件
3. 联系系统管理员

## 更多信息

- 📖 [完整文档中心](./docs/) - 系统完整文档导航
- 🔧 [开发者文档](./docs/development/) - 开发相关资源
- 🚀 [部署文档](./docs/deployment/) - 部署和配置指南
- 🛠️ [运维文档](./docs/operations/) - 系统运维指南

---

## 许可证

本项目为MMC医疗中心专用系统。未经授权不得复制、分发或修改。
# mmcwebapp
