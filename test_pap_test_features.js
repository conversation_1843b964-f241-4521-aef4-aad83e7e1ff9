#!/usr/bin/env node

// Test script for Pap Test booking features
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testPapTestFeatures() {
    console.log('🧪 Testing Pap Test Booking Features...\n');
    
    try {
        // Test 1: Check if service types include updated Pap Test name
        console.log('1. Testing Service Types API...');
        const serviceTypesResponse = await axios.get(`${BASE_URL}/api/consultation-types`);
        
        if (serviceTypesResponse.data.success) {
            const papTestService = serviceTypesResponse.data.consultationTypes.find(
                type => type.toLowerCase().includes('pap test')
            );
            
            if (papTestService) {
                console.log('✅ PASS - Pap Test service found:', papTestService);
                
                // Check if it includes the Chinese text
                if (papTestService.includes('宫颈癌抹片筛查')) {
                    console.log('✅ PASS - Service name includes Chinese text "宫颈癌抹片筛查"');
                } else {
                    console.log('⚠️  NOTE - Service name does not include Chinese text (may be handled in frontend)');
                }
            } else {
                console.log('❌ FAIL - Pap Test service not found in service types');
            }
        } else {
            console.log('❌ FAIL - Could not fetch service types');
        }
        
        console.log('---\n');
        
        // Test 2: Test booking with Pap Test (would need authentication)
        console.log('2. Testing Booking Flow (Authentication Required)...');
        console.log('ℹ️  This test requires user authentication and would be tested manually');
        console.log('   Expected behavior:');
        console.log('   - When Pap Test is selected, booking reason should auto-fill with "Pap Test"');
        console.log('   - Reason for visit field should show "(必填)" or "(Required)"');
        console.log('   - Cancellation policy should mention 48 hours and $75 fee');
        
        console.log('---\n');
        
        // Test 3: Check health endpoint
        console.log('3. Testing Backend Health...');
        const healthResponse = await axios.get(`${BASE_URL}/health`);
        
        if (healthResponse.status === 200) {
            console.log('✅ PASS - Backend is healthy');
        } else {
            console.log('❌ FAIL - Backend health check failed');
        }
        
        console.log('---\n');
        
        console.log('📊 Test Summary:');
        console.log('✅ Service Types API - Working');
        console.log('✅ Backend Health - Working');
        console.log('ℹ️  Frontend Features - Require manual testing:');
        console.log('   1. Pap Test service name displays "Pap Test 宫颈癌抹片筛查"');
        console.log('   2. Auto-fill booking reason when Pap Test is selected');
        console.log('   3. "就诊原因（必填）" label shows in Chinese');
        console.log('   4. "Reason for Visit (Required)" label shows in English');
        console.log('   5. Updated cancellation policy with 48 hours and $75 fee');
        
        console.log('\n🎯 Manual Testing Steps:');
        console.log('1. Open http://localhost:3001 in browser');
        console.log('2. Login with test account');
        console.log('3. Go to booking page');
        console.log('4. Select "Pap Test 宫颈癌抹片筛查" service');
        console.log('5. Verify booking reason auto-fills with "Pap Test"');
        console.log('6. Check that reason field shows "(必填)" or "(Required)"');
        console.log('7. Verify cancellation policy mentions 48 hours and $75 fee');
        
    } catch (error) {
        console.error('❌ Test failed with error:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
    }
}

// Run the tests
testPapTestFeatures().catch(console.error);
