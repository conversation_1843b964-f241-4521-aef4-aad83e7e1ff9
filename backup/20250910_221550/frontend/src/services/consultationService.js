import axios from 'axios';

// Function to fetch consultation requests for a given demographic number
export const getConsultations = async (demographicNo) => {
    if (!demographicNo) {
        throw new Error('Demographic number is required to fetch consultations.');
    }
    try {
        // Assuming the API base URL is handled by axios defaults or interceptors
        const response = await axios.get(`/api/consultations/${demographicNo}`);

        if (response.data && response.data.success) {
            return response.data.consultations; // Return the array of consultations
        } else {
            // Handle cases where the API responds with success: false or unexpected structure
            throw new Error(response.data?.message || 'Failed to fetch consultations');
        }
    } catch (error) {
        console.error('Error fetching consultations:', error.response?.data?.message || error.message);
        // Rethrow or handle error as appropriate for the calling component
        throw error;
    }
}; 