const { createClient } = require('@supabase/supabase-js');
const { v4: uuidv4 } = require('uuid');
const path = require('path');
const demographicDAO = require('../dao/demographicDAO');
const multer = require('multer');

// Initialize Supabase Client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
    console.error('DemographicController: Supabase URL or Service Key is missing.');
}
const supabase = supabaseUrl && supabaseServiceKey ? createClient(supabaseUrl, supabaseServiceKey) : null;

const AVATAR_BUCKET_NAME = 'avatars';

// Multer configuration for memory storage
const storage = multer.memoryStorage();
const upload = multer({
    storage: storage,
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
    fileFilter: (req, file, cb) => {
        const allowedTypes = /jpeg|jpg|png|gif/;
        const mimetype = allowedTypes.test(file.mimetype);
        const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
        if (mimetype && extname) {
            return cb(null, true);
        }
        cb(new Error('File type not allowed. Only JPEG, PNG, GIF are permitted.'));
    }
}).single('avatar'); // Field name for the avatar file

const handleError = (res, error, message = 'Internal server error', statusCode = 500) => {
    console.error(`DemographicController Error: ${message}`, error);
    res.status(statusCode).json({
        success: false,
        message: message,
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
};

const demographicController = {
    uploadAvatar: async (req, res) => {
        console.log(`[AvatarUpload] Attempting to upload avatar for demographic_no: ${req.params.demographic_no}`);
        if (!supabase) {
            console.error('[AvatarUpload] Supabase client not initialized.');
            return handleError(res, new Error('Supabase client not initialized.'), 'Supabase not configured', 503);
        }

        upload(req, res, async (err) => {
            console.log('[AvatarUpload] Multer processing complete.');
            if (err) {
                console.error('[AvatarUpload] Multer error:', err);
                if (err.message.includes('File type not allowed')) {
                    return handleError(res, err, err.message, 400);
                }
                 if (err instanceof multer.MulterError && err.code === 'LIMIT_FILE_SIZE') {
                    return handleError(res, err, 'File too large. Maximum size is 5MB.', 400);
                }
                return handleError(res, err, 'File upload error');
            }

            try {
                const { demographic_no } = req.params;
                console.log(`[AvatarUpload] Processing for demographic_no: ${demographic_no}`);
                
                if (!req.file || !req.file.buffer) {
                    console.warn('[AvatarUpload] No file or file buffer in req.file.');
                    return handleError(res, new Error('No file buffer uploaded.'), 'No file uploaded or file buffer missing.', 400);
                }
                console.log('[AvatarUpload] req.file details:', { originalname: req.file.originalname, mimetype: req.file.mimetype, size: req.file.size });

                if (!demographic_no) {
                    console.warn('[AvatarUpload] Demographic number missing in params.');
                    return handleError(res, new Error('Demographic number missing.'), 'Demographic number is required in path parameters.', 400);
                }

                // 1. Get current avatar key to delete old one if exists
                console.log(`[AvatarUpload] Fetching old avatar key for demographic_no: ${demographic_no}`);
                const oldAvatarKey = await demographicDAO.getAvatarKeyByDemographicNo(demographic_no);
                console.log(`[AvatarUpload] Old avatar key: ${oldAvatarKey}`);

                // 2. Construct new avatar key for Supabase
                const fileExtension = path.extname(req.file.originalname);
                const newAvatarKey = `public/${demographic_no}-${uuidv4()}${fileExtension}`;
                console.log(`[AvatarUpload] New avatar key constructed: ${newAvatarKey}`);

                // 3. Upload new avatar to Supabase
                console.log(`[AvatarUpload] Uploading to Supabase bucket: ${AVATAR_BUCKET_NAME}, key: ${newAvatarKey}`);
                const { data: uploadData, error: uploadError } = await supabase.storage
                    .from(AVATAR_BUCKET_NAME)
                    .upload(newAvatarKey, req.file.buffer, {
                        contentType: req.file.mimetype,
                        upsert: false
                    });

                if (uploadError) {
                    console.error('[AvatarUpload] Supabase upload error:', uploadError);
                    return handleError(res, uploadError, 'Failed to upload avatar to Supabase.');
                }
                console.log('[AvatarUpload] Supabase upload successful. Upload data:', uploadData);

                // 4. Update database with new avatar key
                console.log(`[AvatarUpload] Updating database with new avatar key: ${newAvatarKey} for demographic_no: ${demographic_no}`);
                const updateSuccess = await demographicDAO.updateAvatarKey(demographic_no, newAvatarKey);
                if (!updateSuccess) {
                    console.error('[AvatarUpload] Database update failed for new avatar key.');
                    // If DB update fails, try to delete the recently uploaded Supabase object to avoid orphans
                    console.log(`[AvatarUpload] Attempting to delete orphaned Supabase object: ${newAvatarKey}`);
                    await supabase.storage.from(AVATAR_BUCKET_NAME).remove([newAvatarKey]);
                    return handleError(res, new Error('Failed to update avatar in database.'), 'Database update for avatar failed.');
                }
                console.log('[AvatarUpload] Database update successful.');

                // 5. If new avatar uploaded and DB updated successfully, delete old avatar from Supabase
                if (oldAvatarKey && oldAvatarKey !== newAvatarKey) {
                    console.log(`[AvatarUpload] Attempting to delete old Supabase avatar: ${oldAvatarKey}`);
                    const { error: deleteOldError } = await supabase.storage
                        .from(AVATAR_BUCKET_NAME)
                        .remove([oldAvatarKey]);
                    if (deleteOldError) {
                        console.error(`[AvatarUpload] Failed to delete old avatar ${oldAvatarKey} from Supabase:`, deleteOldError);
                    }
                }

                res.status(201).json({
                    success: true,
                    message: 'Avatar uploaded successfully.',
                    avatarKey: newAvatarKey,
                    // For immediate use, a pre-signed URL could be returned, but client can also request it via GET endpoint
                });

            } catch (error) {
                handleError(res, error, 'Error processing avatar upload.');
            }
        });
    },

    getAvatar: async (req, res) => {
        if (!supabase) return handleError(res, new Error('Supabase client not initialized.'), 'Supabase not configured', 503);
        try {
            const { demographic_no } = req.params;
            if (!demographic_no) {
                return handleError(res, new Error('Demographic number missing.'), 'Demographic number is required.', 400);
            }

            const avatarKey = await demographicDAO.getAvatarKeyByDemographicNo(demographic_no);
            if (!avatarKey) {
                return handleError(res, new Error('Avatar not found for user.'), 'Avatar not found.', 404);
            }

            const expiresIn = 3600; // 1 hour
            const { data, error: signedUrlError } = await supabase.storage
                .from(AVATAR_BUCKET_NAME)
                .createSignedUrl(avatarKey, expiresIn);

            if (signedUrlError) {
                return handleError(res, signedUrlError, 'Failed to create signed URL for avatar.');
            }

            res.json({ success: true, signedUrl: data.signedUrl });

        } catch (error) {
            handleError(res, error, 'Error retrieving avatar.');
        }
    },

    deleteAvatar: async (req, res) => {
        if (!supabase) return handleError(res, new Error('Supabase client not initialized.'), 'Supabase not configured', 503);
        try {
            const { demographic_no } = req.params;
            if (!demographic_no) {
                return handleError(res, new Error('Demographic number missing.'), 'Demographic number is required.', 400);
            }

            const avatarKey = await demographicDAO.getAvatarKeyByDemographicNo(demographic_no);
            if (!avatarKey) {
                return handleError(res, new Error('No avatar to delete.'), 'No avatar found for this user.', 404); // Or 204 No Content if preferred for idempotency
            }

            // 1. Delete from Supabase
            const { error: deleteError } = await supabase.storage
                .from(AVATAR_BUCKET_NAME)
                .remove([avatarKey]);

            if (deleteError) {
                // Log error but proceed to attempt DB update, as the key might be orphaned or already deleted
                console.error(`DemographicController: Failed to delete avatar ${avatarKey} from Supabase:`, deleteError);
                // Depending on strictness, you might choose to return an error here
            }

            // 2. Remove from database (set to null)
            const updateSuccess = await demographicDAO.updateAvatarKey(demographic_no, null);
            if (!updateSuccess) {
                 // This is more critical as the DB still points to a non-existent/deleted key
                return handleError(res, new Error('Failed to update avatar record in database after deletion attempt.'), 'Database update failed after avatar deletion.');
            }

            res.status(200).json({ success: true, message: 'Avatar deleted successfully.' });

        } catch (error) {
            handleError(res, error, 'Error deleting avatar.');
        }
    },

    getDemographicDetails: async (req, res) => {
        try {
            const { demographic_no } = req.params;
            if (!demographic_no) {
                return handleError(res, new Error('Demographic number missing.'), 'Demographic number is required.', 400);
            }

            // Get demographic details from database
            const demographicInfo = await demographicDAO.getDemographicDetails(demographic_no);
            
            if (!demographicInfo) {
                return handleError(res, new Error('Demographic not found.'), 'No demographic record found for this number.', 404);
            }

            res.json({
                success: true,
                demographicInfo: demographicInfo,
                email: demographicInfo.email || null,
                message: 'Demographic details retrieved successfully.'
            });

        } catch (error) {
            handleError(res, error, 'Error retrieving demographic details.');
        }
    }
};

module.exports = demographicController; 