const express = require('express');
const router = express.Router();
const pool = require('../config/database');
const auth = require('../middleware/auth');
const { createLogger } = require('../utils/logger');
// const DocumentExplanationDAO = require('../dao/documentExplanationDAO'); // Temporarily commented out

const logger = createLogger('DocumentRoutes');

/**
 * @route   GET /api/documents/explanation/:documentNo
 * @desc    获取文档的AI解释
 * @access  Private
 */
/* // Temporarily commented out route
router.get('/explanation/:documentNo', auth, async (req, res) => {
    try {
        const { documentNo } = req.params;
        const language = req.query.lang || 'zh';
        const user = req.user;

        logger.info(`获取文档${documentNo}的AI解释, 请求用户ID: ${user.id}, 语言: ${language}`);

        // 获取文档所属的患者ID
        const demographicNo = await DocumentExplanationDAO.getDocumentDemographicNo(documentNo);

        if (!demographicNo) {
            logger.warn(`文档${documentNo}不存在或不属于任何患者`);
            return res.status(404).json({
                success: false,
                message: '文档不存在'
            });
        }

        // 验证用户是否有权限访问所请求的患者数据
        if (user.demographic_no != demographicNo) {
            // 如果不是请求自己的数据，检查是否有家庭关系
            const [familyRelation] = await pool.query(
                `SELECT 1 FROM relationships
                WHERE (relation_demographic_no = ? AND demographic_no = ?)
                OR (demographic_no = ? AND relation_demographic_no = ?)
                AND deleted != \'1\'`,
                [demographicNo, user.demographic_no, demographicNo, user.demographic_no]
            );

            if (familyRelation.length === 0) {
                logger.warn(`用户${user.id}尝试访问无关联的患者${demographicNo}的文档解释`);
                return res.status(403).json({
                    success: false,
                    message: '无权访问此患者的文档'
                });
            }
            logger.info(`用户${user.id}通过家庭关系验证，可访问患者${demographicNo}的文档解释`);
        }

        // 获取解释
        let explanation = await DocumentExplanationDAO.getByDocumentNo(documentNo, language);

        if (explanation) {
            logger.info(`成功获取文档${documentNo}的AI解释`);
            return res.json({
                success: true,
                explanation: explanation.explanation
            });
        } else {
            logger.info(`文档${documentNo}没有AI解释`);
            return res.status(404).json({
                success: false,
                message: '未找到AI解释'
            });
        }
    } catch (error) {
        logger.error(`获取文档AI解释出错: ${error.message}`, { error });
        res.status(500).json({
            success: false,
            message: '获取文档AI解释出错',
            error: error.message
        });
    }
});
*/

/**
 * @route   GET /api/documents/:demographicNo
 * @desc    获取特定患者的所有文档
 * @access  Private
 */
router.get('/:demographicNo', auth, async (req, res) => {
    try {
        const { demographicNo } = req.params;
        const user = req.user;

        logger.info(`获取患者${demographicNo}的文档, 请求用户ID: ${user.id}`);

        // 验证用户是否有权限访问所请求的患者数据
        if (user.demographic_no != demographicNo) {
            // 如果不是请求自己的数据，检查是否有家庭关系
            const [familyRelation] = await pool.query(
                `SELECT 1 FROM relationships
                WHERE (relation_demographic_no = ? AND demographic_no = ?)
                OR (demographic_no = ? AND relation_demographic_no = ?)
                AND deleted != '1'`,
                [demographicNo, user.demographic_no, demographicNo, user.demographic_no]
            );

            if (familyRelation.length === 0) {
                logger.warn(`用户${user.id}尝试访问无关联的患者${demographicNo}的文档`);
                return res.status(403).json({
                    success: false,
                    message: '无权访问此患者的文档'
                });
            }
            logger.info(`用户${user.id}通过家庭关系验证，可访问患者${demographicNo}的文档`);
        }

        // 获取患者的所有文档
        const [documents] = await pool.query(`
            SELECT
                d.document_no as id,
                d.docdesc as title,
                d.doctype as type,
                d.contenttype,
                d.updatedatetime as updatedAt,
                d.observationdate as observationDate,
                d.docfilename as filename
            FROM document d
            JOIN ctl_document c ON d.document_no = c.document_no
            WHERE c.module = 'demographic'
            AND c.module_id = ?
            ORDER BY d.updatedatetime DESC
        `, [demographicNo]);

        if (documents.length === 0) {
            logger.info(`患者${demographicNo}没有文档记录`);
            return res.json({
                success: true,
                message: '没有找到文档',
                reports: []
            });
        }

        // 过滤掉无效的文档（可能会在前端进行，这里保留基础验证）
        const validDocuments = documents.filter(doc => doc.filename);

        logger.info(`成功获取患者${demographicNo}的文档，共${validDocuments.length}条`);
        res.json({
            success: true,
            count: validDocuments.length,
            documents: validDocuments
        });
    } catch (error) {
        logger.error(`获取文档出错: ${error.message}`, { error });
        res.status(500).json({
            success: false,
            message: '获取文档出错',
            error: error.message
        });
    }
});

module.exports = router; 