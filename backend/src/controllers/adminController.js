const multiProviderAIService = require('../utils/multiProviderAIService');
const aiUsageTracker = require('../utils/aiUsageTracker');
const aiServiceManager = require('../utils/aiServiceManager');
const User = require('../models/user');



/**
 * 管理员控制器
 * 提供管理员专用的API端点
 */
class AdminController {


    /**
     * 获取AI服务指标
     */
    async getAIMetrics(req, res) {
        try {
            console.log('[AdminController] Getting AI metrics...');
            
            // 获取主backend的AI服务指标
            const backendMetrics = multiProviderAIService.getMetrics();
            
            // 尝试获取聊天机器人服务的AI指标
            let chatbotMetrics = null;
            try {
                const chatbotServiceUrl = process.env.CHATBOT_SERVICE_URL || 'http://mmcwebapp-chatbot-service:3002';
                const axios = require('axios');
                const chatbotResponse = await axios.get(`${chatbotServiceUrl}/api/ai-metrics`, {
                    timeout: 5000 // 5秒超时
                });
                
                if (chatbotResponse.status === 200 && chatbotResponse.data.success) {
                    chatbotMetrics = chatbotResponse.data.data;
                    console.log('[AdminController] Successfully retrieved chatbot AI metrics');
                }
            } catch (chatbotError) {
                console.warn('[AdminController] Failed to get chatbot metrics:', chatbotError.message);
                // 继续执行，不让聊天机器人的错误影响整体功能
            }
            
            // 合并指标
            const combinedMetrics = {
                ...backendMetrics,
                services: {
                    backend: backendMetrics,
                    chatbot: chatbotMetrics
                }
            };
            
            // 如果有聊天机器人指标，合并统计数据
            if (chatbotMetrics) {
                combinedMetrics.totalCalls += chatbotMetrics.totalCalls || 0;
                combinedMetrics.successfulCalls += chatbotMetrics.successfulCalls || 0;
                combinedMetrics.failedCalls += chatbotMetrics.failedCalls || 0;
                combinedMetrics.cacheHits += chatbotMetrics.cacheHits || 0;
                combinedMetrics.cacheMisses += chatbotMetrics.cacheMisses || 0;
                
                // 合并提供商使用统计
                Object.keys(chatbotMetrics.providerUsage || {}).forEach(provider => {
                    if (combinedMetrics.providerUsage[provider]) {
                        combinedMetrics.providerUsage[provider].calls += chatbotMetrics.providerUsage[provider].calls || 0;
                        combinedMetrics.providerUsage[provider].successes += chatbotMetrics.providerUsage[provider].successes || 0;
                        combinedMetrics.providerUsage[provider].failures += chatbotMetrics.providerUsage[provider].failures || 0;
                    } else {
                        combinedMetrics.providerUsage[provider] = { ...chatbotMetrics.providerUsage[provider] };
                    }
                });
            }
            
            console.log('[AdminController] AI metrics aggregated successfully');
            
            res.json({
                success: true,
                data: combinedMetrics,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('[AdminController] Failed to get AI metrics:', error);
            res.status(500).json({
                success: false,
                message: '获取AI指标失败',
                error: process.env.NODE_ENV === 'development' ? error.message : '内部服务器错误'
            });
        }
    }

    /**
     * 重置AI服务指标
     */
    async resetAIMetrics(req, res) {
        try {
            console.log('[AdminController] Resetting AI metrics...');
            
            // 重置主backend的AI服务指标
            multiProviderAIService.resetMetrics();
            
            // 尝试重置聊天机器人服务的AI指标
            try {
                const chatbotServiceUrl = process.env.CHATBOT_SERVICE_URL || 'http://mmcwebapp-chatbot-service:3002';
                const axios = require('axios');
                await axios.post(`${chatbotServiceUrl}/api/ai-metrics/reset`, {}, {
                    timeout: 5000
                });
                console.log('[AdminController] Successfully reset chatbot AI metrics');
            } catch (chatbotError) {
                console.warn('[AdminController] Failed to reset chatbot metrics:', chatbotError.message);
            }
            
            console.log('[AdminController] AI metrics reset successfully');
            
            res.json({
                success: true,
                message: 'AI指标已重置',
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('[AdminController] Failed to reset AI metrics:', error);
            res.status(500).json({
                success: false,
                message: '重置AI指标失败',
                error: process.env.NODE_ENV === 'development' ? error.message : '内部服务器错误'
            });
        }
    }

    /**
     * 获取AI服务健康状态
     */
    async getAIHealthStatus(req, res) {
        try {
            console.log('[AdminController] Getting AI health status...');
            
            const metrics = multiProviderAIService.getMetrics();
            
            // 计算整体健康状态
            const totalCalls = metrics.totalCalls || 0;
            const successfulCalls = metrics.successfulCalls || 0;
            const failedCalls = metrics.failedCalls || 0;
            
            const successRate = totalCalls > 0 ? (successfulCalls / totalCalls) * 100 : 100;
            const recentFailureRate = totalCalls > 0 ? (failedCalls / totalCalls) * 100 : 0;
            
            let healthStatus = 'healthy';
            let healthMessage = '所有AI服务运行正常';
            
            if (successRate < 80) {
                healthStatus = 'critical';
                healthMessage = 'AI服务成功率过低，需要立即关注';
            } else if (successRate < 90) {
                healthStatus = 'warning';
                healthMessage = 'AI服务成功率较低，建议检查';
            } else if (recentFailureRate > 10) {
                healthStatus = 'warning';
                healthMessage = 'AI服务失败率较高，建议监控';
            }
            
            // 检查提供商状态
            const providerIssues = [];
            if (metrics.providers) {
                metrics.providers.forEach(provider => {
                    if (!provider.available) {
                        providerIssues.push(`${provider.name} 不可用`);
                    } else if (provider.temporarilyUnavailable) {
                        providerIssues.push(`${provider.name} 临时不可用`);
                    }
                });
            }
            
            if (providerIssues.length > 0) {
                healthStatus = healthStatus === 'healthy' ? 'warning' : healthStatus;
                healthMessage += ` (${providerIssues.join(', ')})`;
            }
            
            console.log('[AdminController] AI health status retrieved successfully');
            
            res.json({
                success: true,
                data: {
                    status: healthStatus,
                    message: healthMessage,
                    metrics: {
                        totalCalls,
                        successRate: Math.round(successRate * 100) / 100,
                        failureRate: Math.round(recentFailureRate * 100) / 100,
                        availableProviders: metrics.providers ? metrics.providers.filter(p => p.available && !p.temporarilyUnavailable).length : 0,
                        totalProviders: metrics.providers ? metrics.providers.length : 0
                    },
                    issues: providerIssues
                },
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('[AdminController] Failed to get AI health status:', error);
            res.status(500).json({
                success: false,
                message: '获取AI健康状态失败',
                error: process.env.NODE_ENV === 'development' ? error.message : '内部服务器错误'
            });
        }
    }

    /**
     * 获取系统信息
     */
    async getSystemInfo(req, res) {
        try {
            console.log('[AdminController] Getting system info...');
            
            const systemInfo = {
                nodeVersion: process.version,
                platform: process.platform,
                architecture: process.arch,
                uptime: process.uptime(),
                memoryUsage: process.memoryUsage(),
                environment: process.env.NODE_ENV || 'development',
                timestamp: new Date().toISOString()
            };
            
            res.json({
                success: true,
                data: systemInfo
            });
        } catch (error) {
            console.error('[AdminController] Failed to get system info:', error);
            res.status(500).json({
                success: false,
                message: '获取系统信息失败',
                error: process.env.NODE_ENV === 'development' ? error.message : '内部服务器错误'
            });
        }
    }

    /**
     * 获取AI使用统计 - 按时间范围和聚合维度
     */
    async getAIUsageStats(req, res) {
        try {
            const { 
                startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 默认7天前
                endDate = new Date().toISOString().split('T')[0], // 默认今天
                groupBy = 'day' // day, week, month, hour
            } = req.query;

            console.log(`[AdminController] Getting AI usage stats from ${startDate} to ${endDate}, grouped by ${groupBy}`);

            // 直接获取数据，暂时移除缓存
            const statsData = await (async () => {
                // 获取使用统计
                const usageStats = await aiUsageTracker.getUsageStats(startDate, endDate, groupBy);
                
                // 获取汇总统计
                const summaryStats = await aiUsageTracker.getSummaryStats(startDate, endDate);
                
                // 获取错误统计
                const errorStats = await aiUsageTracker.getErrorStats(startDate, endDate);

                return { usageStats, summaryStats, errorStats };
            })();

            const { usageStats, summaryStats, errorStats } = statsData;

            // 处理数据以便前端展示
            const processedData = {
                summary: summaryStats,
                timeline: usageStats,
                errors: errorStats,
                dateRange: { startDate, endDate },
                groupBy
            };

            res.json({
                success: true,
                data: processedData,
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            console.error('[AdminController] Failed to get AI usage stats:', error);
            res.status(500).json({
                success: false,
                message: '获取AI使用统计失败',
                error: process.env.NODE_ENV === 'development' ? error.message : '内部服务器错误'
            });
        }
    }

    /**
     * 获取AI使用统计看板数据
     */
    async getAIDashboard(req, res) {
        try {
            console.log('[AdminController] Getting AI dashboard data...');

            const today = new Date().toISOString().split('T')[0];
            const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0];
            const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
            const monthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

            // 直接获取数据，暂时移除缓存
            const dashboardData = await (async () => {
                // 简化查询，只获取必要数据，减少数据库负载
                try {
                    const [
                        todayStats,
                        weekStats
                    ] = await Promise.all([
                        aiUsageTracker.getSummaryStats(today, today),
                        aiUsageTracker.getSummaryStats(weekAgo, today)
                    ]);

                    // 返回简化的数据结构
                    return { 
                        todayStats, 
                        yesterdayStats: [], // 暂时为空，减少查询
                        weekStats, 
                        monthStats: [], // 暂时为空，减少查询
                        recentErrors: [] // 暂时为空，减少查询
                    };
                } catch (dbError) {
                    console.error('[AdminController] Database query failed, returning mock data:', dbError);
                    // 如果数据库查询失败，返回模拟数据
                    return {
                        todayStats: [],
                        yesterdayStats: [],
                        weekStats: [],
                        monthStats: [],
                        recentErrors: []
                    };
                }
            })();

            const { todayStats, yesterdayStats, weekStats, monthStats, recentErrors } = dashboardData;

            // 计算同比数据
            const dashboard = {
                today: {
                    summary: todayStats.reduce((acc, stat) => {
                        acc.totalCalls += stat.total_calls || 0;
                        acc.successfulCalls += stat.successful_calls || 0;
                        acc.failedCalls += stat.failed_calls || 0;
                        acc.totalTokens += stat.total_tokens || 0;
                        acc.estimatedCost += parseFloat(stat.estimated_cost_usd || 0);
                        return acc;
                    }, { totalCalls: 0, successfulCalls: 0, failedCalls: 0, totalTokens: 0, estimatedCost: 0 }),
                    byProvider: todayStats
                },
                yesterday: {
                    summary: yesterdayStats.reduce((acc, stat) => {
                        acc.totalCalls += stat.total_calls || 0;
                        acc.successfulCalls += stat.successful_calls || 0;
                        acc.failedCalls += stat.failed_calls || 0;
                        return acc;
                    }, { totalCalls: 0, successfulCalls: 0, failedCalls: 0 })
                },
                week: {
                    summary: weekStats.reduce((acc, stat) => {
                        acc.totalCalls += stat.total_calls || 0;
                        acc.totalTokens += stat.total_tokens || 0;
                        acc.estimatedCost += parseFloat(stat.estimated_cost_usd || 0);
                        return acc;
                    }, { totalCalls: 0, totalTokens: 0, estimatedCost: 0 }),
                    byProvider: weekStats
                },
                month: {
                    summary: monthStats.reduce((acc, stat) => {
                        acc.totalCalls += stat.total_calls || 0;
                        acc.totalTokens += stat.total_tokens || 0;
                        acc.estimatedCost += parseFloat(stat.estimated_cost_usd || 0);
                        return acc;
                    }, { totalCalls: 0, totalTokens: 0, estimatedCost: 0 }),
                    byProvider: monthStats
                },
                errors: {
                    recent: recentErrors.slice(0, 10), // 最近10个错误
                    summary: recentErrors.reduce((acc, error) => {
                        acc[error.provider] = (acc[error.provider] || 0) + error.error_count;
                        return acc;
                    }, {})
                }
            };

            res.json({
                success: true,
                data: dashboard,
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            console.error('[AdminController] Failed to get AI dashboard:', error);
            res.status(500).json({
                success: false,
                message: '获取AI看板数据失败',
                error: process.env.NODE_ENV === 'development' ? error.message : '内部服务器错误'
            });
        }
    }

    /**
     * 清理AI统计数据
     */
    async cleanupAIStats(req, res) {
        try {
            const { retentionDays = 90 } = req.body;
            
            console.log(`[AdminController] Cleaning up AI stats older than ${retentionDays} days`);
            
            const result = await aiUsageTracker.cleanupOldData(retentionDays);
            
            res.json({
                success: true,
                message: `已清理 ${result.usageRecordsDeleted} 条使用记录和 ${result.errorRecordsDeleted} 条错误记录`,
                data: result,
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            console.error('[AdminController] Failed to cleanup AI stats:', error);
            res.status(500).json({
                success: false,
                message: '清理AI统计数据失败',
                error: process.env.NODE_ENV === 'development' ? error.message : '内部服务器错误'
            });
        }
    }

    /**
     * 获取AI提供商状态
     */
    async getAIProvidersStatus(req, res) {
        try {
            console.log('[AdminController] Getting AI providers status...');
            
            const providersStatus = aiServiceManager.getProvidersStatus();
            
            res.json({
                success: true,
                data: {
                    providers: providersStatus,
                    availableCount: providersStatus.filter(p => p.available).length,
                    totalCount: providersStatus.length,
                    lastUpdated: new Date().toISOString()
                },
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            console.error('[AdminController] Failed to get AI providers status:', error);
            res.status(500).json({
                success: false,
                message: '获取AI提供商状态失败',
                error: process.env.NODE_ENV === 'development' ? error.message : '内部服务器错误'
            });
        }
    }

    /**
     * 设置AI提供商可用性
     */
    async setAIProviderAvailability(req, res) {
        try {
            const { provider, available } = req.body;
            
            if (!provider || typeof available !== 'boolean') {
                return res.status(400).json({
                    success: false,
                    message: '提供商名称和可用性状态是必需的'
                });
            }
            
            console.log(`[AdminController] Setting AI provider ${provider} availability to ${available}`);
            
            aiServiceManager.setProviderAvailability(provider, available);
            
            res.json({
                success: true,
                message: `AI提供商 ${provider} 已${available ? '启用' : '禁用'}`,
                data: {
                    provider,
                    available,
                    updatedAt: new Date().toISOString()
                },
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            console.error('[AdminController] Failed to set AI provider availability:', error);
            res.status(500).json({
                success: false,
                message: '设置AI提供商可用性失败',
                error: process.env.NODE_ENV === 'development' ? error.message : '内部服务器错误'
            });
        }
    }

    /**
     * 搜索客户档案
     */
    async getClientDetails(req, res) {
        try {
            const { demographicNo } = req.params;
            
            if (!demographicNo) {
                return res.status(400).json({
                    success: false,
                    message: '档案号码是必需的'
                });
            }

            console.log(`[AdminController] Getting client details for demographic_no: ${demographicNo}`);

            // 获取客户详细信息
            const clientDetails = await User.getDemographicInfo(demographicNo);
            
            if (!clientDetails) {
                return res.status(404).json({
                    success: false,
                    message: '未找到客户档案'
                });
            }

            console.log(`[AdminController] Client details retrieved successfully for: ${demographicNo}`);

            res.json({
                success: true,
                demographicInfo: clientDetails,
                email: clientDetails.email || null,
                message: '客户详情获取成功'
            });

        } catch (error) {
            console.error('[AdminController] Error getting client details:', error);
            res.status(500).json({
                success: false,
                message: '获取客户详情失败',
                error: process.env.NODE_ENV === 'development' ? error.message : '内部服务器错误'
            });
        }
    }

    async searchClients(req, res) {
        try {
            // Check admin role
            if (!req.user || req.user.role !== 'admin') {
                return res.status(403).json({ 
                    success: false, 
                    message: '管理员权限要求' 
                });
            }

            const { q, page = 1, limit = 20 } = req.query;
            
            console.log(`[AdminController] Searching clients with query: "${q}", page: ${page}, limit: ${limit}`);

            if (!q || q.trim().length < 2) {
                return res.json({
                    success: true,
                    data: [],
                    pagination: {
                        page: parseInt(page),
                        limit: parseInt(limit),
                        total: 0,
                        totalPages: 0
                    }
                });
            }

            const searchTerm = q.trim();
            const searchParams = {
                page: parseInt(page, 10) || 1,
                limit: parseInt(limit, 10) || 20
            };
            
            // Determine search type based on the input pattern
            
            // Check for phone number patterns (contains digits and possibly formatting)
            const phonePattern = /[\d\-\(\)\s]+/;
            const digitsOnly = searchTerm.replace(/\D/g, '');
            
            // Check for HIN pattern (9 digits)
            const hinPattern = /^\d{9}$/;
            
            // Check for demographic number (pure digits, but not 9 digits)
            const demographicNoPattern = /^\d+$/ && !hinPattern.test(searchTerm);
            
            if (hinPattern.test(digitsOnly)) {
                // Search by HIN (9 digits)
                console.log(`[AdminController] Searching by HIN: ${digitsOnly}`);
                searchParams.hin = digitsOnly;
            }
            else if (demographicNoPattern && digitsOnly.length > 0 && digitsOnly.length < 9) {
                // Search by demographic number
                console.log(`[AdminController] Searching by demographic number: ${digitsOnly}`);
                searchParams.demographicNo = parseInt(digitsOnly);
            }
            else if (phonePattern.test(searchTerm) && digitsOnly.length >= 7) {
                // Search by phone number (at least 7 digits)
                console.log(`[AdminController] Searching by phone: ${searchTerm}`);
                searchParams.phone = searchTerm;
            } 
            else if (searchTerm.includes('@')) {
                // Search by email
                console.log(`[AdminController] Searching by email: ${searchTerm}`);
                searchParams.email = searchTerm;
            } 
            else {
                // Search by name (supports various formats)
                console.log(`[AdminController] Searching by name: ${searchTerm}`);
                searchParams.name = searchTerm;
            }

            // If searching by demographic number, use direct query
            if (searchParams.demographicNo) {
                try {
                    const clientDetails = await User.getDemographicInfo(searchParams.demographicNo);
                    if (clientDetails) {
                        return res.json({
                            success: true,
                            data: [clientDetails],
                            pagination: {
                                page: 1,
                                limit: parseInt(limit, 10) || 20,
                                total: 1,
                                totalPages: 1
                            }
                        });
                    }
                } catch (error) {
                    console.log(`[AdminController] Direct demographic search failed: ${error.message}`);
                }
            }

            // Use the enhanced searchDemographics method
            const result = await User.searchDemographics(searchParams);

            const formattedResult = {
                success: true,
                data: result.results || [],
                pagination: {
                    page: parseInt(page, 10) || 1,
                    limit: parseInt(limit, 10) || 20,
                    total: result.total || 0,
                    totalPages: Math.ceil((result.total || 0) / (parseInt(limit, 10) || 20))
                }
            };

            console.log(`[AdminController] Search completed, found ${formattedResult.data.length} results`);
            res.json(formattedResult);

        } catch (error) {
            console.error('[AdminController] Search clients error:', error);
            res.status(500).json({ 
                success: false, 
                message: '搜索客户时发生错误',
                error: process.env.NODE_ENV === 'development' ? error.message : '内部服务器错误'
            });
        }
    }
}

module.exports = new AdminController(); 