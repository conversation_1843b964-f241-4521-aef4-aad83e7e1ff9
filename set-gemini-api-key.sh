#!/bin/bash

# Set script to exit on error
set -e

# Print with colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}===== Setting GEMINI_API_KEY Environment Variable =====${NC}"

# The API key from health-crawler-gemini.env
GEMINI_API_KEY="AIzaSyAtUeyqdRIldUH_jq6qZoDpD_BT010X0F4"

# Export the variable to the current shell
export GEMINI_API_KEY=$GEMINI_API_KEY
echo -e "${GREEN}GEMINI_API_KEY has been set in the current shell.${NC}"

# Check if containers are running
echo -e "${YELLOW}Checking for running containers...${NC}"
CHATBOT_CONTAINER=$(docker ps --format '{{.Names}}' | grep chatbot-service || echo "")
BACKEND_CONTAINER=$(docker ps --format '{{.Names}}' | grep mmcwebapp-backend || echo "")

# Set GEMINI_API_KEY for chatbot-service container
if [ -n "$CHATBOT_CONTAINER" ]; then
  echo -e "${GREEN}Setting GEMINI_API_KEY in $CHATBOT_CONTAINER container...${NC}"
  docker exec $CHATBOT_CONTAINER /bin/sh -c "export GEMINI_API_KEY=$GEMINI_API_KEY"
  echo -e "${GREEN}GEMINI_API_KEY has been set in the chatbot-service container.${NC}"
else
  echo -e "${RED}No running chatbot-service container found.${NC}"
fi

# Set GEMINI_API_KEY for backend container
if [ -n "$BACKEND_CONTAINER" ]; then
  echo -e "${GREEN}Setting GEMINI_API_KEY in $BACKEND_CONTAINER container...${NC}"
  docker exec $BACKEND_CONTAINER /bin/sh -c "export GEMINI_API_KEY=$GEMINI_API_KEY"
  echo -e "${GREEN}GEMINI_API_KEY has been set in the backend container.${NC}"
else
  echo -e "${RED}No running backend container found.${NC}"
fi

echo -e "${YELLOW}Note: Setting environment variables with 'export' inside 'docker exec' only affects that specific command.${NC}"
echo -e "${YELLOW}For a permanent solution, we need to create/update the .env file and rebuild the containers.${NC}"

# Create a .env file in the current directory if it doesn't exist
if [ ! -f .env ]; then
  echo -e "${YELLOW}Creating a .env file in the current directory...${NC}"
  echo "GEMINI_API_KEY=$GEMINI_API_KEY" > .env
  echo -e "${GREEN}.env file created with GEMINI_API_KEY.${NC}"
else
  echo -e "${YELLOW}Updating existing .env file...${NC}"
  # Check if GEMINI_API_KEY already exists in .env
  if grep -q "GEMINI_API_KEY=" .env; then
    # Replace existing GEMINI_API_KEY
    sed -i "s/GEMINI_API_KEY=.*/GEMINI_API_KEY=$GEMINI_API_KEY/" .env
  else
    # Add GEMINI_API_KEY to .env
    echo "GEMINI_API_KEY=$GEMINI_API_KEY" >> .env
  fi
  echo -e "${GREEN}.env file updated with GEMINI_API_KEY.${NC}"
fi

# Recommend rebuilding the containers
echo -e "\n${GREEN}=================================${NC}"
echo -e "${YELLOW}Next steps to apply the changes:${NC}"
echo -e "${YELLOW}1. Rebuild and restart your Docker containers with:${NC}"
echo -e "${YELLOW}   docker-compose down${NC}"
echo -e "${YELLOW}   docker-compose up -d${NC}"
echo -e "${GREEN}=================================${NC}" 