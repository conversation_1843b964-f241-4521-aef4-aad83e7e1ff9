console.log('>>> Loading backend/src/index-modified.js...'); // <<< DEBUG LOG
const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const cron = require('cron');
require('dotenv').config();

const authRoutes = require('./routes/auth');
const medicalRecordRoutes = require('./routes/medicalRecordRoutes');
const labResultRoutes = require('./routes/labResultRoutes');
const appointmentRoutes = require('./routes/appointmentRoutes');
const providerRoutes = require('./routes/providerRoutes');
const membershipRoutes = require('./routes/membershipRoutes');
const prescriptionRoutes = require('./routes/prescriptionRoutes');
const immunizationRoutes = require('./routes/immunizationRoutes');
const relationshipRoutes = require('./routes/relationshipRoutes');
const consultationRoutes = require('./routes/consultationRoutes');
const articleRoutes = require('./routes/articleRoutes');
const fileRoutes = require('./routes/fileRoutes');
const tipsRoutes = require('./routes/tipsRoutes');
const healthReportRoutes = require('./routes/healthReportRoutes');
const pdfConverterRoutes = require('./routes/pdfConverterRoutes');
const enhancedPDFRoutes = require('./routes/enhancedPDFRoutes');
const adminRoutes = require('./routes/adminRoutes');
const referralRoutes = require('./routes/referralRoutes');

// Import the main API router
const apiRoutes = require('./routes/index');

const app = express();

// Enhanced logging middleware
app.use((req, res, next) => {
    const start = Date.now();
    console.log(`[${new Date().toISOString()}] Request received: ${req.method} ${req.url}`);
    console.log(`Headers:`, JSON.stringify(req.headers, null, 2));

    // Add response logging
    const originalSend = res.send;
    res.send = function (body) {
        const duration = Date.now() - start;
        console.log(`[${new Date().toISOString()}] Response sent: ${req.method} ${req.url} - Status: ${res.statusCode} - Duration: ${duration}ms`);
        if (res.statusCode >= 400) {
            console.log(`Response body: ${body.substring ? body.substring(0, 200) : JSON.stringify(body)}`);
        }
        return originalSend.call(this, body);
    };

    next();
});

// CORS configuration
app.use(cors({
    origin: ['https://app.mmcwellness.ca', 'http://localhost:3001', 'https://app-backend.mmcwellness.ca'],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Access-Control-Allow-Origin'],
    credentials: true
}));

app.use(express.json());

// 静态文件服务 - 用于访问上传的商品图片
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// Add a public endpoint for categories before the auth routes
app.get('/api/public/categories', (req, res) => {
    // Import the categories controller function
    const tipsController = require('./controllers/tipsController');

    // Call the listCategories method directly without auth
    tipsController.listCategories(req, res);
});

// Temporary direct health guides routes to bypass routing issues
const { getHealthGuides, getHealthGuideById, getHealthGuideCategories } = require('./controllers/healthGuideController');
app.get('/api/health-guides', getHealthGuides);
app.get('/api/health-guides/categories', getHealthGuideCategories);
app.get('/api/health-guides/:id', getHealthGuideById);

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/medical-records', medicalRecordRoutes);
app.use('/api/lab-results', labResultRoutes);
app.use('/api/appointments', appointmentRoutes);
app.use('/api/providers', providerRoutes);
app.use('/api/membership', membershipRoutes);
app.use('/api/prescriptions', prescriptionRoutes);
app.use('/api/immunizations', immunizationRoutes);
app.use('/api/relationships', relationshipRoutes);
app.use('/api/consultations', consultationRoutes);
app.use('/api/articles', articleRoutes);
app.use('/api/files', fileRoutes);
app.use('/api/tips', tipsRoutes);
app.use('/api/health-reports', healthReportRoutes);
app.use('/api/pdf-converter', pdfConverterRoutes);
app.use('/api/smart-pdf', enhancedPDFRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/referrals', referralRoutes);

// Mount the main API router
app.use('/api', apiRoutes);

// Mount the asset serving route separately
// Note: This uses the same router instance, but we mount a specific route from it here.
// Alternatively, we could create a separate router just for assets.
app.use('/api', articleRoutes); // Mount the router at /api to catch /api/content/assets/*

// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({ status: 'healthy' });
});

// Temporary test route to view verification codes (remove in production)
app.get('/test/verification-codes', (req, res) => {
    res.json(require('./controllers/authController').emailVerificationCodes || {});
});

// Temporary test route to view verified emails (remove in production)
app.get('/test/verified-emails', (req, res) => {
    const verifiedEmails = require('./controllers/authController').verifiedEmails;
    res.json({
        emails: verifiedEmails ? Array.from(verifiedEmails) : []
    });
});

// 添加测试路由来检查local files (无需验证)
app.get('/test-files', (req, res) => {
    const fs = require('fs');
    const docsPath = process.env.LOCAL_TIPS_PATH || '/app/documents';

    try {
        if (fs.existsSync(docsPath)) {
            const files = fs.readdirSync(docsPath)
                .filter(filename => filename.endsWith('.md') && !filename.startsWith('.'));

            res.json({
                path: docsPath,
                exists: true,
                files
            });
        } else {
            res.json({
                path: docsPath,
                exists: false,
                error: 'Path does not exist'
            });
        }
    } catch (error) {
        res.status(500).json({
            path: docsPath,
            error: error.message
        });
    }
});

// Error handling
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ message: 'Internal Server Error' });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`);
});

// 设置每日健康小贴士生成任务
try {
    const { CronJob } = cron;
    const generateDailyTipPath = path.join(__dirname, 'scripts/generateDailyTip.js');

    // 检查脚本是否存在
    if (fs.existsSync(generateDailyTipPath)) {
        console.log('设置每日健康小贴士生成定时任务...');

        // 创建一个每天早上6点运行的cron任务 - 修改为温哥华时区
        const dailyTipJob = new CronJob('0 6 * * *', function () {
            console.log(`[${new Date().toISOString()}] 开始生成每日健康小贴士...`);
            const { execSync } = require('child_process');
            try {
                const output = execSync(`node ${generateDailyTipPath}`, { encoding: 'utf8' });
                console.log(`健康小贴士生成结果: ${output}`);
            } catch (error) {
                console.error(`生成健康小贴士出错: ${error.message}`);
            }
        }, null, true, 'America/Vancouver');  // 修改时区从Shanghai到Vancouver

        // 启动任务
        dailyTipJob.start();
        console.log('每日健康小贴士定时任务已启动，时区: America/Vancouver');

        // 检查是否需要立即生成一个小贴士
        const documentsPath = process.env.LOCAL_TIPS_PATH || path.join(__dirname, '../documents');
        const files = fs.readdirSync(documentsPath);
        const todayTipExists = files.some(file =>
            file.startsWith('daily-tip-') &&
            file.includes(new Date().toISOString().split('T')[0].replace(/-/g, ''))
        );

        if (!todayTipExists) {
            console.log('今天还没有生成健康小贴士，立即生成一个...');
            const { execSync } = require('child_process');
            try {
                const output = execSync(`node ${generateDailyTipPath}`, { encoding: 'utf8' });
                console.log(`初始健康小贴士生成结果: ${output}`);
            } catch (error) {
                console.error(`初始生成健康小贴士出错: ${error.message}`);
            }
        }
    } else {
        console.log(`健康小贴士生成脚本不存在: ${generateDailyTipPath}`);
    }
} catch (error) {
    console.error(`设置健康小贴士定时任务出错: ${error.message}`);
} 