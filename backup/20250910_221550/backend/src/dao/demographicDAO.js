const pool = require('../config/database');

const demographicDAO = {
    /**
     * Retrieves the avatar_url (Supabase object key) for a given demographic_no.
     * @param {string} demographicNo - The demographic number of the user.
     * @returns {Promise<string|null>} The avatar_url or null if not found.
     */
    async getAvatarKeyByDemographicNo(demographicNo) {
        const [rows] = await pool.query('SELECT avatar_url FROM demographic WHERE demographic_no = ?', [demographicNo]);
        if (rows.length > 0) {
            return rows[0].avatar_url;
        }
        return null;
    },

    /**
     * Updates the avatar_url for a given demographic_no.
     * @param {string} demographicNo - The demographic number of the user.
     * @param {string|null} avatarKey - The new avatar_url (Supabase object key) or null to remove it.
     * @returns {Promise<boolean>} True if the update was successful, false otherwise.
     */
    async updateAvatarKey(demographicNo, avatarKey) {
        try {
            const [result] = await pool.query(
                'UPDATE demographic SET avatar_url = ? WHERE demographic_no = ?',
                [avatarKey, demographicNo]
            );
            return result.affectedRows > 0;
        } catch (error) {
            console.error('Error updating avatar key in DAO:', error);
            // It might be beneficial to throw the error to be handled by the controller
            // or return a more specific error indicator if needed.
            return false; 
        }
    },

    /**
     * Retrieves demographic details for a given demographic_no.
     * @param {string} demographicNo - The demographic number of the user.
     * @returns {Promise<object|null>} The demographic details or null if not found.
     */
    async getDemographicDetails(demographicNo) {
        try {
            const [rows] = await pool.query(
                'SELECT demographic_no, title, first_name, last_name, address, city, province, postal, phone, hin, sex, email FROM demographic WHERE demographic_no = ?',
                [demographicNo]
            );
            if (rows.length > 0) {
                return rows[0];
            }
            return null;
        } catch (error) {
            console.error('Error fetching demographic details in DAO:', error);
            throw error;
        }
    }
};

module.exports = demographicDAO; 