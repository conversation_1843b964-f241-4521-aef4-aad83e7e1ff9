const pool = require('../config/database');
const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');
const mainServerPool = pool.mainServerPool;
const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc');
const timezone = require('dayjs/plugin/timezone');
const customParseFormat = require('dayjs/plugin/customParseFormat');
const User = require('../models/user'); // Ensure User model is required
const axios = require('axios'); // Need axios for AI call
const appointmentNoteService = require('../services/appointmentNoteService'); // Import the service
const { createClient } = require('@supabase/supabase-js'); // Added for Supabase
const path = require('path'); // Moved to top
const sharp = require('sharp'); // Added for image processing

// Helper to handle errors consistently for appointment operations
const handleAppointmentError = (res, error, message = 'Server error') => {
    console.error(`Appointment operation error: ${error.message}`, error);
    res.status(500).json({
        success: false,
        message: message,
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
};

// Helper function to clean and format provider names
function formatProviderName(firstName, lastName, providerType = null) {
    // Clean the first name and last name by removing _number pattern
    const cleanFirstName = firstName ? firstName.replace(/_\d+/g, '').trim() : '';
    const cleanLastName = lastName ? lastName.replace(/_\d+/g, '').trim() : '';
    
    // Filter out Ken Sun completely
    if ((cleanFirstName.toLowerCase() === 'ken' && cleanLastName.toLowerCase() === 'sun') ||
        (cleanFirstName.toLowerCase().includes('ken') && cleanLastName.toLowerCase() === 'sun')) {
        return null; // Return null to indicate this provider should not be displayed
    }
    
    // Format the name
    let formattedName = `${cleanFirstName} ${cleanLastName}`.trim();
    
    // Add Dr. prefix if it's a doctor
    if (providerType === 'doctor' && formattedName) {
        formattedName = `Dr. ${formattedName}`;
    }
    
    return formattedName || 'Unknown Provider';
}

// 配置 dayjs
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(customParseFormat);

// 设置默认时区为温哥华
const TIMEZONE = 'America/Vancouver';

// Initialize Supabase Client (similar to fileController.js)
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
    console.error('Supabase URL or Service Key is missing in appointmentController. Please check environment variables.');
}
const supabase = supabaseUrl && supabaseServiceKey ? createClient(supabaseUrl, supabaseServiceKey) : null;

// Email sending function for appointment confirmations
async function sendAppointmentConfirmationEmail(appointmentData) {
    try {
        const nodemailer = require('nodemailer');

        // Email configuration
        const transporter = nodemailer.createTransport({
            host: process.env.EMAIL_HOST || 'smtp.gmail.com',
            port: parseInt(process.env.EMAIL_PORT || '587'),
            secure: process.env.EMAIL_SECURE === 'true',
            auth: {
                user: process.env.EMAIL_USER || '<EMAIL>',
                pass: process.env.EMAIL_PASS || 'cmba qinv nrll rqqp',
            },
        });

        // Format appointment date and time
        const appointmentDateTime = dayjs.tz(`${appointmentData.appointmentDate} ${appointmentData.startTime}`, TIMEZONE);
        const formattedDate = appointmentDateTime.format('MMMM D, YYYY');
        const formattedTime = appointmentDateTime.format('h:mm A');

        // Email content
        const mailOptions = {
            from: process.env.EMAIL_FROM || '"MMC Wellness" <<EMAIL>>',
            to: appointmentData.patientEmail,
            subject: 'Appointment Confirmation - MMC Wellness',
            html: `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <h2 style="color: #3f51b5;">Appointment Confirmation</h2>
                    <p>Dear ${appointmentData.patientName},</p>
                    <p>Your appointment has been successfully booked. Here are the details:</p>

                    <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Date:</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${formattedDate}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Time:</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${formattedTime}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Provider:</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${appointmentData.providerName}</td>
                        </tr>
                        ${appointmentData.serviceType ? `
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Service Type:</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${appointmentData.serviceType}</td>
                        </tr>
                        ` : ''}
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Location:</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${appointmentData.location === 'online/phone' ? 'Phone/Online Consultation' : '#130 8780 Blundell Rd, Richmond, BC. (Midtown Medical Clinic)'}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Reason:</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${appointmentData.reason}</td>
                        </tr>
                    </table>

                    <p><strong>Important Notes:</strong></p>
                    <ul>
                        <li>Please arrive 5-10 minutes early to the clinic</li>
                        <li>Please bring your Care Card</li>
                        <li>Please wear loose-fitting clothing for your convenience during the examination</li>
                        <li>This appointment is for Pap (cervical cancer) screening only. For any other medical concerns, please consult your family doctor</li>
                        <li>Please have your family doctor's information ready. The test report will be sent directly to your family doctor's office in approximately 8-12 weeks</li>
                        <li>Cancellations or reschedules must be made at least 48 hours in advance. Same-day cancellations or no-shows will be charged $75</li>
                    </ul>

                    <p>If you need to cancel or reschedule your appointment, please contact us as soon as possible.</p>

                    <p>Thank you for choosing MMC Wellness!</p>
                    <p>Best regards,<br>MMC Wellness Team</p>
                </div>
            `,
        };

        // Send email
        const info = await transporter.sendMail(mailOptions);
        console.log(`Appointment confirmation email sent to ${appointmentData.patientEmail}: ${info.messageId}`);
        return true;
    } catch (error) {
        console.error('Failed to send appointment confirmation email:', error);
        throw error;
    }
}

// Email sending function for appointment cancellations
async function sendAppointmentCancellationEmail(appointmentData) {
    try {
        const nodemailer = require('nodemailer');

        // Email configuration
        const transporter = nodemailer.createTransport({
            host: process.env.EMAIL_HOST || 'smtp.gmail.com',
            port: parseInt(process.env.EMAIL_PORT || '587'),
            secure: process.env.EMAIL_SECURE === 'true',
            auth: {
                user: process.env.EMAIL_USER || '<EMAIL>',
                pass: process.env.EMAIL_PASS || 'cmba qinv nrll rqqp',
            },
        });

        // Format appointment date and time
        const appointmentDateTime = dayjs.tz(`${appointmentData.appointmentDate} ${appointmentData.startTime}`, TIMEZONE);
        const formattedDate = appointmentDateTime.format('MMMM D, YYYY');
        const formattedTime = appointmentDateTime.format('h:mm A');

        // Email content
        const mailOptions = {
            from: process.env.EMAIL_FROM || '"MMC Wellness" <<EMAIL>>',
            to: appointmentData.patientEmail,
            subject: 'Appointment Cancellation Confirmation - MMC Wellness',
            html: `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <h2 style="color: #d32f2f;">Appointment Cancellation Confirmation</h2>
                    <p>Dear ${appointmentData.patientName},</p>
                    <p>Your appointment has been successfully cancelled. Here are the details of the cancelled appointment:</p>

                    <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Date:</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${formattedDate}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Time:</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${formattedTime}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Provider:</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${appointmentData.providerName}</td>
                        </tr>
                        ${appointmentData.serviceType ? `
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Service Type:</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${appointmentData.serviceType}</td>
                        </tr>
                        ` : ''}
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Location:</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${appointmentData.location === 'online/phone' ? 'Phone/Online Consultation' : '#130 8780 Blundell Rd, Richmond, BC. (Midtown Medical Clinic)'}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Reason:</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${appointmentData.reason}</td>
                        </tr>
                    </table>

                    <p><strong>Cancellation Policy:</strong></p>
                    <p>Please note: To cancel appointments, at least 48 hours notice is required, otherwise a $75 fee will be charged. Thank you for your understanding and cooperation.</p>

                    <p>If you need to book a new appointment, please log into your patient portal or contact us directly.</p>

                    <p>Thank you for choosing MMC Wellness!</p>
                    <p>Best regards,<br>MMC Wellness Team</p>
                </div>
            `,
        };

        // Send email
        const info = await transporter.sendMail(mailOptions);
        console.log(`Appointment cancellation email sent to ${appointmentData.patientEmail}: ${info.messageId}`);
        return true;
    } catch (error) {
        console.error('Failed to send appointment cancellation email:', error);
        throw error;
    }
}

const BOOKING_IMAGES_BUCKET_NAME = process.env.SUPABASE_BUCKET_NAME || 'patient-files'; // Use the same bucket or a specific one
const BOOKING_IMAGES_FOLDER = 'booking_images'; // Define a folder for these images

// Function to combine multiple images into a single image
async function combineImages(imageBuffers) {
    if (!imageBuffers || imageBuffers.length === 0) {
        throw new Error('No images provided for combination');
    }
    
    if (imageBuffers.length === 1) {
        return imageBuffers[0]; // Return single image as-is
    }
    
    try {
        // Resize all images to a consistent width (e.g., 800px) while maintaining aspect ratio
        const targetWidth = 800;
        const processedImages = [];
        
        for (const buffer of imageBuffers) {
            const image = sharp(buffer);
            const metadata = await image.metadata();
            
            // Calculate height to maintain aspect ratio
            const targetHeight = Math.round((metadata.height * targetWidth) / metadata.width);
            
            const resizedBuffer = await image
                .resize(targetWidth, targetHeight, {
                    fit: 'inside',
                    withoutEnlargement: true
                })
                .jpeg({ quality: 85 })
                .toBuffer();
                
            processedImages.push({
                buffer: resizedBuffer,
                width: targetWidth,
                height: targetHeight
            });
        }
        
        // Calculate total height for the combined image
        const totalHeight = processedImages.reduce((sum, img) => sum + img.height, 0);
        const spacing = 10; // 10px spacing between images
        const finalHeight = totalHeight + (spacing * (processedImages.length - 1));
        
        // Create a white background canvas
        let combinedImage = sharp({
            create: {
                width: targetWidth,
                height: finalHeight,
                channels: 3,
                background: { r: 255, g: 255, b: 255 }
            }
        });
        
        // Prepare composite operations
        const composite = [];
        let currentTop = 0;
        
        for (const img of processedImages) {
            composite.push({
                input: img.buffer,
                top: currentTop,
                left: 0
            });
            currentTop += img.height + spacing;
        }
        
        // Combine all images
        const finalBuffer = await combinedImage
            .composite(composite)
            .jpeg({ quality: 90 })
            .toBuffer();
            
        console.log(`[IMAGE_COMBINE] Successfully combined ${imageBuffers.length} images into one. Final size: ${targetWidth}x${finalHeight}`);
        return finalBuffer;
        
    } catch (error) {
        console.error('[IMAGE_COMBINE] Error combining images:', error);
        throw new Error(`Failed to combine images: ${error.message}`);
    }
}

/**
 * Get user's appointments
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} JSON response with upcoming and past appointments
 */
exports.getUserAppointments = async (req, res) => {
    try {
        const userId = req.user.id;

        // First get the demographic_no from the user_auth table
        const userQuery = `
            SELECT demographic_no
            FROM user_auth
            WHERE id = ?
        `;

        const [userResult] = await pool.query(userQuery, [userId]);

        if (!userResult || userResult.length === 0 || !userResult[0].demographic_no) {
            return res.status(404).json({
                message: 'User not linked to an Oscar account. Please link your account first.'
            });
        }

        const demographicNo = userResult[0].demographic_no;

        // Get appointment data from the appointment table
        const appointmentQuery = `
            SELECT
                a.appointment_no,
                a.appointment_date,
                a.start_time,
                a.end_time,
                a.reason,
                a.status,
                a.location,
                a.notes,
                p.first_name as provider_first_name,
                p.last_name as provider_last_name,
                p.provider_type
            FROM appointment a
            LEFT JOIN provider p ON a.provider_no = p.provider_no
            WHERE a.demographic_no = ?
            ORDER BY a.appointment_date DESC, a.start_time DESC
        `;

        const [appointments] = await pool.query(appointmentQuery, [demographicNo]);

        // Format and organize appointments
        // Use dayjs with timezone to get current date in Vancouver timezone
        const currentDate = dayjs().tz(TIMEZONE).startOf('day');

        console.log(`Current date in ${TIMEZONE}: ${currentDate.format('YYYY-MM-DD HH:mm:ss')}`);

        const formattedAppointments = appointments.map(appointment => {
            // Format appointment date using dayjs with timezone
            const appointmentDateStr = appointment.appointment_date.toISOString().split('T')[0];
            const appointmentDate = dayjs.tz(appointmentDateStr, TIMEZONE);

            console.log(`Raw appt date: ${appointmentDateStr}, Formatted: ${appointmentDate.format('YYYY-MM-DD')}`);

            // Format time from time format "HH:MM:SS" to "HH:MM AM/PM"
            let timeStr = appointment.start_time.toString();

            // Parse hours and minutes from time string (format: "HH:MM:SS")
            let [hours, minutes] = timeStr.split(':').map(Number);

            const ampm = hours >= 12 ? 'PM' : 'AM';
            hours = hours % 12 || 12; // Convert to 12-hour format
            const formattedTime = `${hours}:${minutes.toString().padStart(2, '0')} ${ampm}`;

            const doctorName = formatProviderName(appointment.provider_first_name, appointment.provider_last_name, appointment.provider_type);
            
            // Skip appointments for filtered providers (like Ken Sun)
            if (doctorName === null) {
                return null;
            }

            return {
                id: appointment.appointment_no,
                date: appointmentDateStr, // 使用原始日期字符串
                time: formattedTime,
                doctor: doctorName,
                location: appointment.location || 'MMC Wellness Clinic',
                status: getAppointmentStatus(appointment.status, appointmentDate, currentDate),
                reason: appointment.reason || 'General Consultation',
                notes: appointment.notes || ''
            };
        }).filter(appointment => appointment !== null); // Remove null appointments

        // Separate into upcoming and past appointments using dayjs for consistent timezone handling
        const upcoming = formattedAppointments.filter(appt => {
            const apptDate = dayjs.tz(appt.date, TIMEZONE);
            // Include all non-cancelled future appointments
            return apptDate.isAfter(currentDate) || apptDate.isSame(currentDate, 'day') && appt.status !== 'cancelled';
        });

        const past = formattedAppointments.filter(appt => {
            const apptDate = dayjs.tz(appt.date, TIMEZONE);
            // Include all past appointments or cancelled appointments
            return apptDate.isBefore(currentDate) && !apptDate.isSame(currentDate, 'day') || appt.status === 'cancelled';
        });

        console.log(`Found ${upcoming.length} upcoming and ${past.length} past appointments`);

        return res.json({
            upcoming,
            past
        });

    } catch (error) {
        console.error('Error retrieving appointments:', error);
        return res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Create a new appointment request
 * @param {Object} req - Request object with appointment details
 * @param {Object} res - Response object
 * @returns {Object} JSON response with created appointment details
 */
exports.createAppointmentRequest = async (req, res) => {
    try {
        const userId = req.user.id;
        const { providerId, date, time, reason } = req.body;

        // Validate required fields
        if (!providerId || !date || !time || !reason) {
            return res.status(400).json({ message: 'Please provide all required appointment details' });
        }

        // Get demographic_no from user_auth (from local database)
        const userQuery = `SELECT demographic_no FROM user_auth WHERE id = ?`;
        const [userResult] = await pool.query(userQuery, [userId]);

        if (!userResult || userResult.length === 0 || !userResult[0].demographic_no) {
            return res.status(404).json({
                message: 'User not linked to an Oscar account. Please link your account first.'
            });
        }

        const demographicNo = userResult[0].demographic_no;

        // Parse time from "HH:MM AM/PM" to "HHMM" format
        const formattedTime = convertTimeToOscarFormat(time);

        // Calculate end time (use calculateEndTimeSync for backward compatibility)
        const endTime = calculateEndTimeSync(formattedTime);

        // 使用主服务器连接池插入预约请求
        const conn = await pool.getConnection();
        try {
            // Insert appointment request into appointment table on main server
            const appointmentQuery = `
                INSERT INTO appointment (
                    provider_no,
                    demographic_no,
                    appointment_date,
                    start_time,
                    end_time,
                    reason,
                    status,
                    notes,
                    creator
                ) VALUES (?, ?, ?, ?, ?, ?, 't', 'Patient Portal Request', 'PatientPortal')
            `;

            const [result] = await conn.query(appointmentQuery, [
                providerId,
                demographicNo,
                date,
                formattedTime,
                endTime,
                reason
            ]);

            if (result.affectedRows === 1) {
                return res.status(201).json({
                    message: 'Appointment request created successfully',
                    appointmentId: result.insertId
                });
            } else {
                return res.status(500).json({ message: 'Failed to create appointment request' });
            }
        } finally {
            conn.release(); // 确保释放连接
        }
    } catch (error) {
        console.error('Error creating appointment request:', error);
        return res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Update appointment status
 * @param {Object} req - Request object with appointment status
 * @param {Object} res - Response object
 * @returns {Object} JSON response with update status
 */
exports.updateAppointmentStatus = async (req, res) => {
    try {
        const userId = req.user.id;
        const { appointmentId } = req.params;
        const { status, notes } = req.body;

        // Verify user owns this appointment
        const userQuery = `
            SELECT a.appointment_no
            FROM appointment a
            JOIN user_auth u ON a.demographic_no = u.demographic_no
            WHERE u.id = ? AND a.appointment_no = ?
        `;
        const [appointmentResult] = await pool.query(userQuery, [userId, appointmentId]);

        if (!appointmentResult || appointmentResult.length === 0) {
            return res.status(404).json({ message: 'Appointment not found or not authorized' });
        }

        // Update appointment status
        const oscarStatus = mapStatusToOscarCode(status);

        const conn = await pool.getConnection();
        try {
            const updateQuery = `
                UPDATE appointment
                SET status = ?, notes = ?
                WHERE appointment_no = ?
            `;

            const [result] = await conn.query(updateQuery, [
                oscarStatus,
                notes || `Status updated to ${status} by patient via portal`,
                appointmentId
            ]);

            if (result.affectedRows === 1) {
                return res.json({ message: 'Appointment status updated successfully' });
            } else {
                return res.status(500).json({ message: 'Failed to update appointment status' });
            }
        } finally {
            conn.release(); // 确保释放连接
        }
    } catch (error) {
        console.error('Error updating appointment status:', error);
        return res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Delete appointment request
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} JSON response with deletion status
 */
exports.deleteAppointmentRequest = async (req, res) => {
    try {
        const userId = req.user.id;
        const appointmentId = req.params.appointmentId;

        // Check if this appointment belongs to the user
        const userQuery = `
            SELECT a.appointment_no, a.appointment_date
            FROM appointment a
            JOIN user_auth u ON a.demographic_no = u.demographic_no
            WHERE u.id = ? AND a.appointment_no = ?
        `;

        const [appointmentResult] = await pool.query(userQuery, [userId, appointmentId]);

        if (!appointmentResult || appointmentResult.length === 0) {
            return res.status(403).json({ message: 'Not authorized to delete this appointment' });
        }

        // Check if appointment date is in the future
        const appointmentDate = new Date(appointmentResult[0].appointment_date);
        const currentDate = new Date();
        currentDate.setHours(0, 0, 0, 0);

        if (appointmentDate < currentDate) {
            return res.status(400).json({ message: 'Cannot delete past appointments' });
        }

        // Instead of deleting, mark as cancelled
        const updateQuery = `
            UPDATE appointment
            SET status = 'C', notes = CONCAT(IFNULL(notes, ''), '\nCancelled by patient via portal')
            WHERE appointment_no = ?
        `;

        const [result] = await pool.query(updateQuery, [appointmentId]);

        if (result.affectedRows === 1) {
            return res.json({
                message: 'Appointment cancelled successfully'
            });
        } else {
            return res.status(500).json({ message: 'Failed to cancel appointment' });
        }

    } catch (error) {
        console.error('Error cancelling appointment:', error);
        return res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Get available appointment slots for a doctor
 * @param {Object} req - Request object with doctor ID and date range
 * @param {Object} res - Response object
 * @returns {Object} JSON response with available appointment slots
 */
exports.getDoctorAvailability = async (req, res) => {
    try {
        const { doctorId, startDate, endDate } = req.params;

        if (!doctorId || !startDate) {
            return res.status(400).json({ message: 'Doctor ID and start date are required' });
        }

        // Validate doctor exists
        const doctorQuery = `
            SELECT provider_no, first_name, last_name
            FROM provider
            WHERE provider_no = ? AND status = '1' AND provider_type = 'doctor'
        `;

        const [doctorResult] = await pool.query(doctorQuery, [doctorId]);

        if (!doctorResult || doctorResult.length === 0) {
            return res.status(404).json({ message: 'Doctor not found or inactive' });
        }

        // Set end date to 7 days after start date if not provided
        const searchEndDate = endDate || new Date(new Date(startDate).getTime() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

        // Get existing appointments for the doctor in the date range
        const appointmentsQuery = `
            SELECT
                appointment_date,
                start_time,
                end_time
            FROM appointment
            WHERE provider_no = ?
            AND appointment_date BETWEEN ? AND ?
            AND status != 'C'
            ORDER BY appointment_date, start_time
        `;

        const [bookedAppointments] = await pool.query(appointmentsQuery, [doctorId, startDate, searchEndDate]);

        // Generate available time slots (9:00 AM to 5:00 PM in 30-minute intervals)
        // This is simplified and should be replaced with actual doctor schedule data
        const availableSlots = [];
        const startDateObj = new Date(startDate);
        const endDateObj = new Date(searchEndDate);

        for (let currentDate = new Date(startDateObj); currentDate <= endDateObj; currentDate.setDate(currentDate.getDate() + 1)) {
            // Skip weekends (0 = Sunday, 6 = Saturday)
            const dayOfWeek = currentDate.getDay();
            if (dayOfWeek === 0 || dayOfWeek === 6) continue;

            const dateStr = currentDate.toISOString().split('T')[0];

            // Office hours: 9:00 AM to 5:00 PM
            const daySlots = [];

            // Create 30-minute slots
            for (let hour = 9; hour < 17; hour++) {
                for (let minute of [0, 30]) {
                    const slotStart = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:00`;

                    // Check if this slot conflicts with any booked appointments
                    const isAvailable = !bookedAppointments.some(appt => {
                        if (appt.appointment_date.toISOString().split('T')[0] !== dateStr) return false;

                        const apptStart = appt.start_time;
                        const apptEnd = appt.end_time;

                        const slotStartTime = new Date(`${dateStr}T${slotStart}`);
                        const slotEndTime = new Date(slotStartTime.getTime() + 30 * 60 * 1000);

                        // Check for overlap
                        return (
                            (slotStartTime >= apptStart && slotStartTime < apptEnd) ||
                            (slotEndTime > apptStart && slotEndTime <= apptEnd) ||
                            (slotStartTime <= apptStart && slotEndTime >= apptEnd)
                        );
                    });

                    if (isAvailable) {
                        // Format as "9:00 AM"
                        const displayHour = hour > 12 ? hour - 12 : hour;
                        const ampm = hour >= 12 ? 'PM' : 'AM';
                        const displayTime = `${displayHour}:${minute.toString().padStart(2, '0')} ${ampm}`;

                        daySlots.push({
                            time: displayTime,
                            rawTime: `${hour.toString().padStart(2, '0')}${minute.toString().padStart(2, '0')}`  // For OSCAR format (e.g., "0900")
                        });
                    }
                }
            }

            // Only add days that have available slots
            if (daySlots.length > 0) {
                availableSlots.push({
                    date: dateStr,
                    formattedDate: new Date(dateStr).toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' }),
                    slots: daySlots
                });
            }
        }

        return res.json({
            success: true,
            doctorName: `Dr. ${doctorResult[0].first_name} ${doctorResult[0].last_name}`,
            doctorId: doctorResult[0].provider_no,
            startDate,
            endDate: searchEndDate,
            availableSlots
        });

    } catch (error) {
        console.error('Error retrieving doctor availability:', error);
        return res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Helper function to determine appointment status
 * @param {string} dbStatus - Status from database
 * @param {Date} appointmentDate - Date of appointment
 * @param {Date} currentDate - Current date
 * @returns {string} User-friendly status
 */
function getAppointmentStatus(dbStatus, appointmentDate, currentDate) {
    if (!dbStatus) return 'pending';

    // Convert to uppercase for case-insensitive comparison
    const status = dbStatus.toString().toUpperCase();

    // Handle past appointments
    if (appointmentDate < currentDate) {
        if (['B', '1', 'P', 'T', 'BS'].includes(status)) {
            return 'completed';
        }
        if (['C', 'N'].includes(status)) {
            return 'cancelled';
        }
        return 'completed'; // Default for past appointments
    }
    // Handle future appointments
    else {
        if (['A', 'H', 'P', 'B', '1', 'T', 'BS'].includes(status)) {
            return 'confirmed';
        }
        if (['C', 'N'].includes(status)) {
            return 'cancelled';
        }
        return 'pending'; // Default for future appointments
    }
}

/**
 * Helper function to convert time to Oscar format with enhanced format support
 * @param {string} timeStr - Time string in various formats (e.g., "HH:MM AM/PM" or "HH:MM")
 * @returns {string} Time in Oscar format (HH:MM:SS)
 */
function convertTimeToOscarFormat(timeStr) {
    console.log('转换时间格式，输入:', timeStr);

    if (!timeStr) {
        console.error('提供了空的时间字符串给convertTimeToOscarFormat');
        return '00:00:00'; // 默认值
    }

    try {
        // Case 1: Format "HH:MM AM/PM"
        if (timeStr.includes(' ') && (timeStr.includes('AM') || timeStr.includes('PM') ||
            timeStr.includes('am') || timeStr.includes('pm'))) {
            const [timePart, ampm] = timeStr.split(' ');
            let [hours, minutes] = timePart.split(':').map(Number);

            // Convert to 24-hour format
            if (ampm.toUpperCase() === 'PM' && hours < 12) {
                hours += 12;
            } else if (ampm.toUpperCase() === 'AM' && hours === 12) {
                hours = 0;
            }

            const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00`;
            console.log('转换结果 (AM/PM格式):', formattedTime);
            return formattedTime;
        }

        // Case 2: Format "HH:MM" (24-hour)
        else if (timeStr.includes(':')) {
            let [hours, minutes] = timeStr.split(':').map(Number);
            const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00`;
            console.log('转换结果 (HH:MM格式):', formattedTime);
            return formattedTime;
        }

        // Case 3: Format "HHMM" (numeric format)
        else if (/^\d{3,4}$/.test(timeStr)) {
            // 确保是4位数字格式
            const paddedTime = timeStr.padStart(4, '0');
            const hours = parseInt(paddedTime.substring(0, 2));
            const minutes = parseInt(paddedTime.substring(2, 4));
            const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00`;
            console.log('转换结果 (HHMM格式):', formattedTime);
            return formattedTime;
        }

        // 处理未识别的格式
        console.error(`未知的时间格式: ${timeStr}`);
        return '00:00:00'; // 默认为午夜
    } catch (error) {
        console.error(`转换时间格式"${timeStr}"时出错:`, error);
        return '00:00:00'; // 默认为午夜
    }
}

/**
 * Helper function to calculate end time based on service type and template timecode
 * @param {number|string} startTime - Start time in Oscar format (HHMM)
 * @param {string} providerNo - Provider number
 * @param {string} serviceType - Service type (template name)
 * @returns {Promise<number>} End time in Oscar format (HHMM)
 */
async function calculateEndTime(startTime, providerNo, serviceType) {
    try {
        // Convert startTime to string if it's a number
        const startTimeStr = startTime.toString().padStart(4, '0');
        let hours = parseInt(startTimeStr.substring(0, 2));
        let minutes = parseInt(startTimeStr.substring(2));

        // Default slot duration (minutes)
        let slotDuration = 15;

        // If providerNo and serviceType are provided, lookup the actual slot duration
        if (providerNo && serviceType) {
            // Query the scheduletemplate to get the timecode
            const templateQuery = `
                SELECT timecode
                FROM scheduletemplate
                WHERE provider_no = ?
                AND name = ?
            `;

            const [templates] = await pool.query(templateQuery, [providerNo, serviceType]);

            if (templates.length > 0 && templates[0].timecode) {
                const timecode = templates[0].timecode;
                const timecodeLength = timecode.length;

                // Determine slot duration based on timecode length
                if (timecodeLength === 48) {
                    slotDuration = 30; // 30-minute slots
                } else if (timecodeLength === 96) {
                    slotDuration = 15; // 15-minute slots
                } else {
                    console.warn(`Unexpected timecode length (${timecodeLength}). Defaulting to 15 min slots.`);
                }

                console.log(`Determined slot duration: ${slotDuration} minutes based on timecode length ${timecodeLength}`);
            } else {
                console.warn(`No schedule template found for provider ${providerNo}, service ${serviceType}. Using default duration.`);
            }
        } else {
            console.warn('Provider number or service type not provided. Using default duration.');
        }

        // Add the slot duration to get the end time
        minutes += slotDuration;
        if (minutes >= 60) {
            hours += 1;
            minutes -= 60;
        }

        // Handle day wrap (unlikely for appointments but included for completeness)
        if (hours >= 24) {
            hours -= 24;
        }

        return parseInt(`${hours.toString().padStart(2, '0')}${minutes.toString().padStart(2, '0')}`);
    } catch (error) {
        console.error('Error calculating end time:', error);
        // Fallback to default 15-minute duration if there's an error
        const startTimeStr = startTime.toString().padStart(4, '0');
        let hours = parseInt(startTimeStr.substring(0, 2));
        let minutes = parseInt(startTimeStr.substring(2));
        minutes += 15;
        if (minutes >= 60) {
            hours += 1;
            minutes -= 60;
        }
        if (hours >= 24) {
            hours -= 24;
        }
        return parseInt(`${hours.toString().padStart(2, '0')}${minutes.toString().padStart(2, '0')}`);
    }
}

/**
 * DEPRECATED: Synchronous version of calculateEndTime for backward compatibility only
 * New code should use the async calculateEndTime function with provider and service type parameters
 *
 * @param {number} startTime - Start time in Oscar format (HHMM)
 * @returns {number} End time in Oscar format (HHMM) using default 15-minute duration
 */
function calculateEndTimeSync(startTime) {
    const startTimeStr = startTime.toString().padStart(4, '0');
    let hours = parseInt(startTimeStr.substring(0, 2));
    let minutes = parseInt(startTimeStr.substring(2));

    // Default to 15 minutes when not using the async version
    minutes += 15;
    if (minutes >= 60) {
        hours += 1;
        minutes -= 60;
    }

    // Handle day wrap (unlikely for appointments but included for completeness)
    if (hours >= 24) {
        hours -= 24;
    }

    return parseInt(`${hours.toString().padStart(2, '0')}${minutes.toString().padStart(2, '0')}`);
}

/**
 * Map user-friendly status to Oscar status codes
 * @param {string} status - User-friendly status
 * @returns {string} Oscar status code
 */
function mapStatusToOscarCode(status) {
    switch (status.toLowerCase()) {
        case 'confirmed':
            return 'H'; // Here
        case 'pending':
            return 't'; // Appointment
        case 'cancelled':
            return 'C'; // Cancelled
        case 'completed':
            return 'B'; // Billed
        default:
            return 't'; // Default is Appointment
    }
}

/**
 * Get appointment history for guest/visitor
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} JSON response with appointment history
 */
exports.getGuestAppointmentHistory = async (req, res) => {
    try {
        const { demographicNo } = req.params;

        // Validate input
        if (!demographicNo) {
            return res.status(400).json({ message: 'Patient ID is required' });
        }

        // Get appointment data from the appointment table
        const appointmentQuery = `
            SELECT
                a.appointment_no,
                a.appointment_date,
                a.start_time,
                a.end_time,
                a.reason,
                a.status,
                a.location,
                a.notes,
                p.first_name as provider_first_name,
                p.last_name as provider_last_name,
                p.provider_type
            FROM appointment a
            LEFT JOIN provider p ON a.provider_no = p.provider_no
            WHERE a.demographic_no = ?
            ORDER BY a.appointment_date DESC, a.start_time DESC
        `;

        const [appointments] = await pool.query(appointmentQuery, [demographicNo]);

        if (!appointments || appointments.length === 0) {
            return res.json({
                success: true,
                message: 'No appointment history found for this patient',
                appointments: []
            });
        }

        // Format appointments
        // Use dayjs with timezone for current date
        const currentDate = dayjs().tz(TIMEZONE).startOf('day');

        const formattedAppointments = appointments.map(appointment => {
            // Format appointment date using dayjs with timezone
            const appointmentDate = dayjs.tz(appointment.appointment_date, TIMEZONE);

            // Format time from time format "HH:MM:SS" to "HH:MM AM/PM"
            let timeStr = appointment.start_time.toString();

            // Parse hours and minutes from time string (format: "HH:MM:SS")
            let [hours, minutes] = timeStr.split(':').map(Number);

            const ampm = hours >= 12 ? 'PM' : 'AM';
            hours = hours % 12 || 12; // Convert to 12-hour format
            const formattedTime = `${hours}:${minutes.toString().padStart(2, '0')} ${ampm}`;

            const doctorName = formatProviderName(appointment.provider_first_name, appointment.provider_last_name, appointment.provider_type);
            
            // Skip appointments for filtered providers (like Ken Sun)
            if (doctorName === null) {
                return null;
            }

            return {
                id: appointment.appointment_no,
                date: appointmentDate.format('YYYY-MM-DD'), // Send formatted date string
                time: formattedTime,
                doctor: doctorName,
                location: appointment.location || 'MMC Wellness Clinic',
                // Pass dayjs objects for comparison
                status: getAppointmentStatus(appointment.status, appointmentDate, currentDate),
                reason: appointment.reason || 'General Consultation',
                notes: appointment.notes || ''
            };
        }).filter(appointment => appointment !== null); // Remove null appointments

        // Separate into upcoming and past appointments using dayjs for consistent timezone handling
        const upcoming = formattedAppointments.filter(appt => {
            const apptDate = dayjs.tz(appt.date, TIMEZONE); // Re-parse formatted date string in correct timezone
            // Include all non-cancelled future appointments
            return (apptDate.isAfter(currentDate) || apptDate.isSame(currentDate, 'day')) && appt.status !== 'cancelled';
        });

        const past = formattedAppointments.filter(appt => {
            const apptDate = dayjs.tz(appt.date, TIMEZONE); // Re-parse formatted date string in correct timezone
            // Include all past appointments or cancelled appointments
            return apptDate.isBefore(currentDate) || appt.status === 'cancelled';
        });

        return res.json({
            success: true,
            demographicNo,
            upcoming,
            past
        });

    } catch (error) {
        console.error('Error retrieving guest appointment history:', error);
        return res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Get appointment history by name and phone number
 * @param {Object} req - Request object with name and phone
 * @param {Object} res - Response object
 * @returns {Object} JSON response with appointment history
 */
exports.getAppointmentByNameAndPhone = async (req, res) => {
    try {
        const { lastName, phone } = req.body;

        // Validate required fields
        if (!lastName || !phone) {
            return res.status(400).json({ message: 'Last name and phone number are required' });
        }

        // First, find the demographic record using name and phone
        const demographicQuery = `
            SELECT demographic_no, first_name, last_name, phone, year_of_birth, month_of_birth, date_of_birth
            FROM demographic
            WHERE last_name LIKE ? AND phone LIKE ?
            LIMIT 10
        `;

        const [demographics] = await pool.query(demographicQuery, [`%${lastName}%`, `%${phone}%`]);

        if (!demographics || demographics.length === 0) {
            return res.status(404).json({ message: 'No patient found with the provided information' });
        }

        // If multiple patients found, return them for selection
        if (demographics.length > 1) {
            return res.json({
                success: true,
                message: 'Multiple patients found. Please select one.',
                patients: demographics.map(patient => ({
                    demographicNo: patient.demographic_no,
                    firstName: patient.first_name,
                    lastName: patient.last_name,
                    phone: patient.phone,
                    dob: patient.year_of_birth && patient.month_of_birth && patient.date_of_birth
                        ? `${patient.year_of_birth}-${patient.month_of_birth.toString().padStart(2, '0')}-${patient.date_of_birth.toString().padStart(2, '0')}`
                        : 'Not available'
                }))
            });
        }

        // Get the first demographic_no if only one found
        const demographicNo = demographics[0].demographic_no;

        // Get appointment data from the appointment table
        const appointmentQuery = `
            SELECT
                a.appointment_no,
                a.appointment_date,
                a.start_time,
                a.end_time,
                a.reason,
                a.status,
                a.location,
                a.notes,
                p.first_name as provider_first_name,
                p.last_name as provider_last_name,
                p.provider_type
            FROM appointment a
            LEFT JOIN provider p ON a.provider_no = p.provider_no
            WHERE a.demographic_no = ?
            ORDER BY a.appointment_date DESC, a.start_time DESC
        `;

        const [appointments] = await pool.query(appointmentQuery, [demographicNo]);

        if (!appointments || appointments.length === 0) {
            return res.json({
                success: true,
                message: 'No appointment history found for this patient',
                demographicNo,
                patient: {
                    firstName: demographics[0].first_name,
                    lastName: demographics[0].last_name,
                    phone: demographics[0].phone,
                    dob: demographics[0].year_of_birth && demographics[0].month_of_birth && demographics[0].date_of_birth
                        ? `${demographics[0].year_of_birth}-${demographics[0].month_of_birth.toString().padStart(2, '0')}-${demographics[0].date_of_birth.toString().padStart(2, '0')}`
                        : 'Not available'
                },
                appointments: []
            });
        }

        // Format appointments
        // Use dayjs with timezone for current date
        const currentDate = dayjs().tz(TIMEZONE).startOf('day');

        const formattedAppointments = appointments.map(appointment => {
            // Format appointment date using dayjs with timezone
            const appointmentDate = dayjs.tz(appointment.appointment_date, TIMEZONE);

            // Format time from time format "HH:MM:SS" to "HH:MM AM/PM"
            let timeStr = appointment.start_time.toString();

            // Parse hours and minutes from time string (format: "HH:MM:SS")
            let [hours, minutes] = timeStr.split(':').map(Number);

            const ampm = hours >= 12 ? 'PM' : 'AM';
            hours = hours % 12 || 12; // Convert to 12-hour format
            const formattedTime = `${hours}:${minutes.toString().padStart(2, '0')} ${ampm}`;

            const doctorName = formatProviderName(appointment.provider_first_name, appointment.provider_last_name, appointment.provider_type);
            
            // Skip appointments for filtered providers (like Ken Sun)
            if (doctorName === null) {
                return null;
            }

            return {
                id: appointment.appointment_no,
                date: appointmentDate.format('YYYY-MM-DD'), // Send formatted date string
                time: formattedTime,
                doctor: doctorName,
                location: appointment.location || 'MMC Wellness Clinic',
                // Pass dayjs objects for comparison
                status: getAppointmentStatus(appointment.status, appointmentDate, currentDate),
                reason: appointment.reason || 'General Consultation',
                notes: appointment.notes || ''
            };
        }).filter(appointment => appointment !== null); // Remove null appointments

        // Separate into upcoming and past appointments using dayjs for consistent timezone handling
        const upcoming = formattedAppointments.filter(appt => {
            const apptDate = dayjs.tz(appt.date, TIMEZONE); // Re-parse formatted date string in correct timezone
            // Include all non-cancelled future appointments
            return (apptDate.isAfter(currentDate) || apptDate.isSame(currentDate, 'day')) && appt.status !== 'cancelled';
        });

        const past = formattedAppointments.filter(appt => {
            const apptDate = dayjs.tz(appt.date, TIMEZONE); // Re-parse formatted date string in correct timezone
            // Include all past appointments or cancelled appointments
            return apptDate.isBefore(currentDate) || appt.status === 'cancelled';
        });

        return res.json({
            success: true,
            demographicNo,
            patient: {
                firstName: demographics[0].first_name,
                lastName: demographics[0].last_name,
                phone: demographics[0].phone,
                dob: demographics[0].year_of_birth && demographics[0].month_of_birth && demographics[0].date_of_birth
                    ? `${demographics[0].year_of_birth}-${demographics[0].month_of_birth.toString().padStart(2, '0')}-${demographics[0].date_of_birth.toString().padStart(2, '0')}`
                    : 'Not available'
            },
            upcoming,
            past
        });

    } catch (error) {
        console.error('Error retrieving appointment history by name and phone:', error);
        return res.status(500).json({ message: 'Server error' });
    }
};

// Helper to format note for AI
function formatNoteForAI(item) {
    const appt = item.appointment;
    const note = item.note;
    const apptDate = dayjs(appt.appointment_date).format('YYYY-MM-DD');
    const provider = formatProviderName(appt.provider_first_name, appt.provider_last_name) || 'Unknown Provider';

    let formattedText = "";
    formattedText += `\n---\nAppointment Date: ${apptDate}\n`;
    formattedText += `Provider: ${provider}\n`;
    formattedText += `Reason: ${appt.reason || 'N/A'}\n`;
    if (appt.appointment_notes) {
        formattedText += `Appointment Notes: ${appt.appointment_notes}\n`;
    }
    if (note && note.note_content) {
        formattedText += `Progress Note (${dayjs(note.observation_date).format('YYYY-MM-DD')}):
${note.note_content}\n`;
    }
    // Removed the "Progress Note: Not available" line - if no note exists, simply don't include any progress note section
    return formattedText.trim();
}

// Helper to format final summary prompt
function formatFinalSummaryPrompt(halfYearSummaries) {
    let formattedText = "";
    for (const key of Object.keys(halfYearSummaries)) {
        formattedText += `\n## Summary for ${key}\n${halfYearSummaries[key]}\n`;
    }
    return formattedText.trim();
}

// Get appointments for a specific demographic number (self or family member) with pagination
exports.getViewedAppointments = async (req, res) => {
    try {
        const loggedInUser = req.user;
        const targetDemographicNo = parseInt(req.params.demographicNo, 10);

        // Pagination and Tab parameters
        const page = parseInt(req.query.page, 10) || 1;
        const limit = parseInt(req.query.limit, 10) || 10;
        const tab = req.query.tab || 'upcoming'; // Default to 'upcoming'
        const offset = (page - 1) * limit;

        if (!loggedInUser || !loggedInUser.demographic_no) {
            return res.status(403).json({ success: false, message: 'Authentication required or user not linked.' });
        }

        const loggedInDemoNo = loggedInUser.demographic_no;

        // Check permissions (similar to getVievedProfile)
        const isSelf = targetDemographicNo === loggedInDemoNo;
        let isAllowedRelative = false;
        if (!isSelf) {
            // Use the required User model
            const family = await User.getFamilyMembers(loggedInDemoNo);
            isAllowedRelative = family.some(member => member.relative_demographic_no === targetDemographicNo);
        }

        if (!isSelf && !isAllowedRelative) {
            return res.status(403).json({ success: false, message: 'Permission denied to view these appointments.' });
        }

        // --- Permission granted, fetch appointments for targetDemographicNo ---

        // Use dayjs with timezone to get current date in Vancouver timezone
        const currentDate = dayjs().tz(TIMEZONE).startOf('day');
        const currentDateSQL = currentDate.format('YYYY-MM-DD'); // Format for SQL comparison

        let whereClause = '';
        let orderByClause = '';

        // Define conditions based on the requested tab
        switch (tab) {
            case 'past':
                whereClause = `a.demographic_no = ? AND a.appointment_date < ? AND a.status != 'C'`;
                orderByClause = `ORDER BY a.appointment_date DESC, a.start_time DESC`;
                break;
            case 'cancelled':
                whereClause = `a.demographic_no = ? AND a.status = 'C'`;
                orderByClause = `ORDER BY a.appointment_date DESC, a.start_time DESC`;
                break;
            case 'upcoming':
            default: // Default to upcoming
                whereClause = `a.demographic_no = ? AND a.appointment_date >= ? AND a.status != 'C'`;
                orderByClause = `ORDER BY a.appointment_date ASC, a.start_time ASC`;
                break;
        }

        const params = tab === 'past' ? [targetDemographicNo, currentDateSQL] :
            tab === 'cancelled' ? [targetDemographicNo] :
                [targetDemographicNo, currentDateSQL]; // Upcoming

        // --- Count total items for pagination ---
        const countQuery = `
            SELECT COUNT(*) as totalCount
            FROM appointment a
            WHERE ${whereClause}
        `;

        console.log(`Executing Count Query for tab '${tab}': ${countQuery.replace(/\\?/g, '%s')}`, ...params);
        const [countResult] = await pool.query(countQuery, params);
        const totalItems = countResult[0].totalCount;
        const totalPages = Math.ceil(totalItems / limit);

        // --- Fetch paginated appointments ---
        const appointmentQuery = `
            SELECT
                a.appointment_no, a.appointment_date, a.start_time, a.end_time,
                a.reason, a.status, a.location, a.notes,
                p.first_name as provider_first_name, p.last_name as provider_last_name,
                p.provider_type
            FROM appointment a
            LEFT JOIN provider p ON a.provider_no = p.provider_no
            WHERE ${whereClause}
            ${orderByClause}
            LIMIT ? OFFSET ?
        `;

        const queryParams = [...params, limit, offset];
        console.log(`Executing Appointment Query for tab '${tab}', page ${page}: ${appointmentQuery.replace(/\\?/g, '%s')}`, ...queryParams);
        const [appointments] = await pool.query(appointmentQuery, queryParams);

        // Format appointments (using the existing helper function logic)
        const formattedAppointments = appointments.map(appointment => {
            const appointmentDateStr = dayjs(appointment.appointment_date).format('YYYY-MM-DD'); // Ensure consistent formatting
            const appointmentDate = dayjs.tz(appointmentDateStr, TIMEZONE);

            let timeStr = appointment.start_time.toString();
            let [hours, minutes] = timeStr.split(':').map(Number);
            const ampm = hours >= 12 ? 'PM' : 'AM';
            hours = hours % 12 || 12;
            const formattedTime = `${hours}:${minutes.toString().padStart(2, '0')} ${ampm}`;

            // Determine status based on DB status and date relative to current date
            // Note: getAppointmentStatus might need adjustment if it relies on JS filtering
            // For paginated API, the status should ideally reflect the tab's definition
            let displayStatus = getAppointmentStatus(appointment.status, appointmentDate, currentDate);
            // Override status for clarity if it doesn't match the tab logic due to getAppointmentStatus complexity
            if (tab === 'cancelled' && displayStatus !== 'cancelled') displayStatus = 'cancelled';
            if (tab === 'past' && displayStatus !== 'completed' && displayStatus !== 'cancelled') displayStatus = 'completed'; // Assuming past non-cancelled are completed
            // Upcoming status logic in getAppointmentStatus should be okay

            const doctorName = formatProviderName(appointment.provider_first_name, appointment.provider_last_name, appointment.provider_type);
            
            // Skip appointments for filtered providers (like Ken Sun)
            if (doctorName === null) {
                return null;
            }

            return {
                id: appointment.appointment_no,
                date: appointmentDateStr,
                time: formattedTime,
                doctor: doctorName,
                location: appointment.location || 'MMC Wellness Clinic',
                status: displayStatus,
                reason: appointment.reason || 'General Consultation',
                notes: appointment.notes || ''
            };
        }).filter(appointment => appointment !== null); // Remove null appointments


        console.log(`Returning ${formattedAppointments.length} appointments for tab '${tab}', page ${page} for demographics ${targetDemographicNo}`);

        return res.json({
            success: true,
            demographicNo: targetDemographicNo,
            appointments: formattedAppointments,
            pagination: {
                currentPage: page,
                itemsPerPage: limit,
                totalItems: totalItems,
                totalPages: totalPages,
                tab: tab
            }
        });

    } catch (error) {
        console.error('Error retrieving viewed appointments:', error);
        res.status(500).json({ success: false, message: 'Server error fetching viewed appointments' });
    }
};

/**
 * Cancel appointment
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} JSON response with cancel status
 */
exports.cancelAppointment = async (req, res) => {
    try {
        const userId = req.user.id;
        const { appointmentId } = req.params;

        console.log('取消预约请求:', { userId, appointmentId });

        // Verify user owns this appointment
        const userQuery = `
            SELECT a.appointment_no, a.appointment_date
            FROM appointment a
            JOIN user_auth u ON a.demographic_no = u.demographic_no
            WHERE u.id = ? AND a.appointment_no = ?
        `;
        const [appointmentResult] = await pool.query(userQuery, [userId, appointmentId]);

        if (!appointmentResult || appointmentResult.length === 0) {
            console.log('预约未找到或无权限:', { userId, appointmentId });
            return res.status(404).json({ message: 'Appointment not found or not authorized' });
        }

        // Check if appointment is at least 2 days in the future
        const appointmentDate = dayjs(appointmentResult[0].appointment_date);
        const today = dayjs().tz(TIMEZONE).startOf('day');
        const daysDifference = appointmentDate.diff(today, 'day');

        console.log('预约日期检查:', {
            appointmentDate: appointmentDate.format('YYYY-MM-DD'),
            today: today.format('YYYY-MM-DD'),
            daysDifference
        });

        if (daysDifference < 2) {
            console.log('预约取消时间限制检查未通过:', { daysDifference });
            return res.status(400).json({
                message: 'Appointments must be cancelled at least 2 days in advance. Please call the clinic.'
            });
        }

        const updateQuery = `
            UPDATE appointment
            SET status = 'C', notes = CONCAT(IFNULL(notes, ''), ' Cancelled by patient via portal')
            WHERE appointment_no = ?
        `;

        // 取消预约
        console.log('尝试取消预约...');
        const conn = await pool.getConnection();
        try {
            const [result] = await conn.query(updateQuery, [appointmentId]);
            console.log('取消结果:', result);

            if (result.affectedRows === 1) {
                return res.json({
                    success: true,
                    message: 'Appointment cancelled successfully'
                });
            } else {
                return res.status(500).json({
                    success: false,
                    message: 'Failed to cancel appointment'
                });
            }
        } catch (error) {
            console.error('取消预约错误:', error);
            throw error;
        } finally {
            conn.release(); // 确保释放连接
            console.log('数据库连接已释放');
        }
    } catch (error) {
        console.error('预约取消处理错误:', error);
        return res.status(500).json({
            success: false,
            message: `Server error: ${error.message}`
        });
    }
};

/**
 * Book an appointment
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} JSON response with booking status
 */
exports.bookAppointment = async (req, res) => {
    console.log('bookAppointment req.body:', JSON.stringify(req.body)); // Log the entire body

    const {
            providerNo,
        demographicNo, // This can be the demographic_no of the logged-in user or a family member
            appointmentDate,
        time: startTime, // from req.body.time
        endTime,         // from req.body.endTime (optional)
            reason,
        notes, // Optional from client, will be appended
        location, // Optional
        resources, // Optional
        serviceType, // Optional, e.g., "Annual Checkup", "Follow Up", "Vaccination"
        bookingUnderDemographicNo, // Explicit demographic_no for booking if different from logged-in user (e.g., for family member)
        patientDemographicNo // Added patientDemographicNo here
    } = req.body;

    console.log(`Received startTime: ${startTime}, Received endTime: ${endTime}`);

    const loggedInUserDemographicNo = req.user.demographic_no; // Assuming auth middleware sets req.user

    if (!supabase) {
        return handleAppointmentError(res, new Error('Supabase client not initialized in appointmentController.'), 'Supabase not configured');
    }
    const { userId } = req; // Assuming authenticate middleware provides this

    let s3ObjectKey = null; // Will store the Supabase path/key

    console.log(`[BOOK_APPOINTMENT_DEBUG] Raw endTime from request: ${req.body.endTime}`); // Log raw endTime

    const conn = await pool.getConnection(); // Get connection early for transaction management

    try {
        await conn.beginTransaction(); // Start transaction

        // Handle reason images upload if files are present
        if (req.files && req.files.length > 0) {
            console.log(`[BOOK_APPOINTMENT_DEBUG] Processing ${req.files.length} reason images`);
            
            // Extract image buffers from uploaded files
            const imageBuffers = req.files.map(file => file.buffer);
            const firstFileName = req.files[0].originalname;
            const fileExtension = path.extname(firstFileName);
            
            try {
                // Combine multiple images into one
                const combinedImageBuffer = await combineImages(imageBuffers);
                
                // Generate unique filename for the combined image
                const uniqueFilename = `combined_${uuidv4()}.jpg`; // Always save as JPEG
                const supabasePath = `${BOOKING_IMAGES_FOLDER}/${uniqueFilename}`;

                console.log(`[BOOK_APPOINTMENT_DEBUG] Uploading combined reason image to Supabase. Path: ${supabasePath}`);
                console.log(`[BOOK_APPOINTMENT_DEBUG] Supabase URL configured: ${supabaseUrl}`);
                console.log(`[BOOK_APPOINTMENT_DEBUG] Supabase Bucket configured: ${BOOKING_IMAGES_BUCKET_NAME}`);

                let uploadData, uploadError;
                try {
                    const response = await supabase.storage
                        .from(BOOKING_IMAGES_BUCKET_NAME)
                        .upload(supabasePath, combinedImageBuffer, {
                            contentType: 'image/jpeg', // Combined image is always JPEG
                            upsert: false 
                        });
                    uploadData = response.data;
                    uploadError = response.error;
                    console.log('[BOOK_APPOINTMENT_DEBUG] Supabase upload API call finished.');
                } catch (sdkError) {
                    console.error('[BOOK_APPOINTMENT_DEBUG] Supabase SDK EXCEPTION during upload:', sdkError);
                    throw new Error(`Supabase SDK exception: ${sdkError.message}`);
                }

                if (uploadError) {
                    console.error('[BOOK_APPOINTMENT_DEBUG] Supabase upload error response:', JSON.stringify(uploadError, null, 2));
                    throw new Error(`Failed to upload combined reason image to Supabase Storage: ${uploadError.message}`);
                }
                if (!uploadData || !uploadData.path) {
                    console.error('[BOOK_APPOINTMENT_DEBUG] Supabase upload returned no data or path:', uploadData);
                    throw new Error('Supabase upload did not return a valid path.');
                }
                s3ObjectKey = uploadData.path; 
                console.log(`[BOOK_APPOINTMENT_DEBUG] Combined reason image uploaded to Supabase. Path: ${s3ObjectKey}`);
                
            } catch (combineError) {
                console.error('[BOOK_APPOINTMENT_DEBUG] Error combining images:', combineError);
                throw new Error(`Failed to process reason images: ${combineError.message}`);
            }
        } else if (req.body.s3_object_key) { // If client sends an existing key (e.g. re-using image)
             if (!req.body.s3_object_key.startsWith('http')) { // Basic check it's a key not a URL
                s3ObjectKey = req.body.s3_object_key;
                console.log(`Using provided s3_object_key for reason image: ${s3ObjectKey}`);
            } else {
                 console.warn('Received a full URL in s3_object_key for booking, expected a Supabase key. Ignoring.');
            }
        }

        // Validate required fields (using the aliased variables)
        if (!providerNo || !appointmentDate || !startTime || !reason) {
            await conn.rollback(); // Rollback transaction
            return res.status(400).json({ success: false, message: 'Missing required fields for booking.' });
        }

        // Determine demographicNoToUse
        let demographicNoToUse = patientDemographicNo; // Use patientDemographicNo from req.body

        if (!demographicNoToUse) { // If frontend didn't send patientDemographicNo (or it was empty)
            if (req.user && req.user.id) { // Check if logged-in user info is available from auth middleware
        const userQuery = `SELECT demographic_no FROM user_auth WHERE id = ?`;
                const [userResult] = await conn.query(userQuery, [req.user.id]); // Use req.user.id
        if (!userResult || userResult.length === 0 || !userResult[0].demographic_no) {
                    await conn.rollback();
                    return res.status(404).json({ success: false, message: 'Logged-in user not linked to an Oscar account.' });
        }
            demographicNoToUse = userResult[0].demographic_no;
            } else {
                 await conn.rollback();
                 return res.status(400).json({ success: false, message: 'Demographic number for booking is required and could not be determined.' });
            }
        }

        // Ensure startTime (from req.body.time) is formatted to HH:MM:SS
        let formattedStartTime = convertTimeToOscarFormat(startTime); 
        if (!formattedStartTime) { // Simplified check: if conversion returns null, it's invalid
            console.error('Invalid start time format or failed to convert:', startTime);
            await conn.rollback();
            return handleAppointmentError(res, new Error('Invalid start time format.'), 'Invalid start time format.');
        }

        const nameQuery = `SELECT CONCAT(first_name, ' ', last_name) as fullName FROM demographic WHERE demographic_no = ?`;
        const [nameResult] = await conn.query(nameQuery, [demographicNoToUse]);
        const patientName = nameResult.length > 0 ? nameResult[0].fullName : 'Patient';
        
        let formattedEndTime = endTime; // endTime from req.body

        console.log(`[BOOK_APPOINTMENT_TIMES_DEBUG] Initial req.body.endTime: ${endTime}`);
        console.log(`[BOOK_APPOINTMENT_TIMES_DEBUG] Condition (formattedEndTime) is truthy?: ${formattedEndTime ? true : false}`);

        if (formattedEndTime) {
            // This block is for when endTime IS provided by the client
            console.log('[BOOK_APPOINTMENT_TIMES_DEBUG] Entering block for pre-supplied endTime.');
            formattedEndTime = convertTimeToOscarFormat(endTime); 
            if (!formattedEndTime){
                console.error('Invalid end time format or failed to convert provided end time:', endTime);
                await conn.rollback();
                return handleAppointmentError(res, new Error('Invalid end time format.'), 'Invalid end time format.');
            }
        } else {
            // This block is for when endTime IS NOT provided, so we calculate it
            console.log('[BOOK_APPOINTMENT_TIMES_DEBUG] Entering block to CALCULATE endTime.');
            if (appointmentDate && formattedStartTime) {
                const startDateTimeStr = `${appointmentDate} ${formattedStartTime}`;
                let startDateTime = dayjs.tz(startDateTimeStr, TIMEZONE); // Simplified direct parsing

                if (startDateTime.isValid()) {
                    console.log(`[BOOK_APPOINTMENT_TIMES_DEBUG] Inside CALCULATE block - Parsed startDateTime (Vancouver): ${startDateTime.toString()}`);

                    // Check if this is a Pap Test appointment and set duration accordingly
                    let intendedOscarDisplayDurationMinutes = 15; // Default duration
                    if (serviceType && serviceType.toLowerCase().includes('pap test')) {
                        intendedOscarDisplayDurationMinutes = 15; // Ensure Pap Test appointments are 15 minutes
                        console.log(`[BOOK_APPOINTMENT_TIMES_DEBUG] Pap Test detected - using 15 minute duration`);
                    }

                    const actualDurationToAdd = intendedOscarDisplayDurationMinutes - 1; // To make Oscar show 15, the interval should be 14 minutes.
                    const endDateTime = startDateTime.add(actualDurationToAdd, 'minute');
                    console.log(`[BOOK_APPOINTMENT_TIMES_DEBUG] Inside CALCULATE block - Calculated endDateTime (Vancouver) with adjustment for Oscar: ${endDateTime.toString()}`);
                    formattedEndTime = endDateTime.format('HH:mm:ss');
                } else {
                    console.error('Could not parse start date/time for end_time calculation:', startDateTimeStr);
                    await conn.rollback();
                    return handleAppointmentError(res, new Error('Invalid date/time for end_time calculation.'), 'Error calculating appointment end time.');
                }
            } else {
                console.error('Cannot calculate end_time: appointmentDate or formattedStartTime is missing.');
                await conn.rollback();
                return handleAppointmentError(res, new Error('Missing data for end_time calculation.'), 'Error calculating appointment end time.');
            }
        }

        console.log('[BOOK_APPOINTMENT_TIMES_DEBUG] ------- Time Values Before DB Insert -------');
        console.log(`[BOOK_APPOINTMENT_TIMES_DEBUG] req.body.appointmentDate (raw): ${req.body.appointmentDate}`);
        console.log(`[BOOK_APPOINTMENT_TIMES_DEBUG] req.body.time (raw startTime): ${req.body.time}`);
        console.log(`[BOOK_APPOINTMENT_TIMES_DEBUG] req.body.endTime (raw endTime): ${req.body.endTime}`);
        console.log(`[BOOK_APPOINTMENT_TIMES_DEBUG] Variable appointmentDate (used for DB): ${appointmentDate}`);
        console.log(`[BOOK_APPOINTMENT_TIMES_DEBUG] Variable formattedStartTime (used for DB): ${formattedStartTime}`);
        console.log(`[BOOK_APPOINTMENT_TIMES_DEBUG] Variable formattedEndTime (used for DB): ${formattedEndTime}`);
        console.log('[BOOK_APPOINTMENT_TIMES_DEBUG] ------------------------------------------');

        const insertAppointmentQuery = `
            INSERT INTO appointment (
                provider_no, appointment_date, start_time, end_time, name,
                demographic_no, reason, notes, status, location, resources, creator, createdatetime
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 't', ?, ?, 'PatientPortal', NOW())
        `;
        const appointmentParams = [
            providerNo, appointmentDate, formattedStartTime, formattedEndTime, patientName, // Use appointmentDate and formattedStartTime
            demographicNoToUse, reason, 
            `Booked via Patient Portal${serviceType ? ` (Service Type: ${serviceType})` : ''}`, // Keep serviceType in notes for informational purposes
            location || 'online/phone', resources || 'onlinebooking'
        ];

        const [insertResult] = await conn.query(insertAppointmentQuery, appointmentParams);
        const newAppointmentId = insertResult.insertId;

        if (insertResult.affectedRows === 1 && newAppointmentId) {
            if (s3ObjectKey) { // If an image was uploaded/key provided
                    const imageInsertQuery = `
                    INSERT INTO appointment_reason_images (appointment_no_fk, s3_object_key) 
                        VALUES (?, ?)
                `; // Assumes column is renamed to s3_object_key
                await conn.query(imageInsertQuery, [newAppointmentId, s3ObjectKey]);
                console.log('Reason image Supabase key saved for appointment ID:', newAppointmentId);
                }
            await conn.commit(); // Commit transaction

            // Get provider information for email
            let providerName = 'MMC Provider';
            try {
                const providerQuery = `
                    SELECT first_name, last_name
                    FROM provider
                    WHERE provider_no = ?
                `;
                const [providerResult] = await conn.query(providerQuery, [providerNo]);
                if (providerResult && providerResult.length > 0) {
                    const provider = providerResult[0];
                    providerName = `Dr. ${provider.first_name} ${provider.last_name}`;
                }
            } catch (providerError) {
                console.error('Failed to get provider info for email:', providerError);
            }

            // Send confirmation email to patient
            try {
                await sendAppointmentConfirmationEmail({
                    patientEmail: req.user.email,
                    patientName: patientName,
                    appointmentDate: appointmentDate,
                    startTime: formattedStartTime,
                    endTime: formattedEndTime,
                    providerName: providerName,
                    serviceType: serviceType,
                    location: location || 'online/phone',
                    reason: reason
                });
                console.log('Confirmation email sent to patient:', req.user.email);
            } catch (emailError) {
                console.error('Failed to send confirmation email:', emailError);
                // Don't fail the booking if email fails
            }

            res.status(201).json({
                    success: true,
                    message: 'Appointment booked successfully',
                    appointmentId: newAppointmentId
                });
            } else {
            await conn.rollback(); // Rollback transaction
            res.status(500).json({ success: false, message: 'Failed to book appointment' });
            }
    } catch (error) {
        await conn.rollback(); // Rollback transaction on any error
        handleAppointmentError(res, error, 'Failed to book appointment due to server error.');
        } finally {
        if (conn) conn.release();
        console.log('Database connection released after booking attempt.');
    }
};

// Get historical summary for a patient, with caching
exports.getPatientHistorySummary = async (req, res) => {
    console.log('<<<<< ENTERING getPatientHistorySummary CONTROLLER >>>>>');

    try {
        const loggedInUser = req.user;
        const targetDemographicNo = parseInt(req.params.demographicNo, 10);
        const language = req.query.lang || 'en';

        console.log(`[History Summary Cache] Request received for demographic: ${targetDemographicNo}, lang: ${language}`);

        // --- 1. Permission Check ---
        if (!loggedInUser || !loggedInUser.demographic_no) {
            return res.status(403).json({ success: false, message: 'Authentication required or user not linked.' });
        }

        // 管理员权限检查 - 管理员可以查看任何患者的数据
        if (loggedInUser.role === 'admin') {
            console.log(`管理员用户 ${loggedInUser.id} 被授权查看患者 ${targetDemographicNo} 的半年历史`);
        } else {
            const loggedInDemoNo = loggedInUser.demographic_no;
            const isSelf = parseInt(targetDemographicNo, 10) === loggedInDemoNo;

            let isAllowedRelative = false;
            if (!isSelf) {
                const family = await User.getFamilyMembers(loggedInDemoNo);
                isAllowedRelative = family.some(member => member.relative_demographic_no === parseInt(targetDemographicNo, 10));
            }

            if (!isSelf && !isAllowedRelative) {
                return res.status(403).json({ success: false, message: 'Permission denied to view this history.' });
            }
        }
        // --- Permission Granted ---

        // --- 2. Check for Latest Appointment Date ---
        const latestApptQuery = `
            SELECT MAX(appointment_date) as latest_past_appt_date
            FROM appointment
            WHERE demographic_no = ?
              AND appointment_date < CURDATE()
              AND status NOT IN ('C', 'N')
        `;
        const [latestApptResult] = await pool.query(latestApptQuery, [targetDemographicNo]);
        const latestAppointmentDate = latestApptResult[0]?.latest_past_appt_date;
        // Format date to YYYY-MM-DD for comparison, handle null case
        const latestAppointmentDateStr = latestAppointmentDate ? dayjs(latestAppointmentDate).format('YYYY-MM-DD') : null;

        console.log(`[History Summary Cache] Latest actual past appointment date for ${targetDemographicNo}: ${latestAppointmentDateStr || 'None'}`);

        // --- 3. Check Cache (检查半年度总结缓存) ---
        const cacheQuery = `
            SELECT summary_type, generated_summary, last_appointment_date_covered
            FROM patient_summaries
            WHERE demographic_no = ? AND language = ? AND summary_type LIKE 'half_year_%'
        `;
        const [cacheResult] = await pool.query(cacheQuery, [targetDemographicNo, language]);

        if (cacheResult.length > 0) {
            console.log(`[History Summary Cache] Found ${cacheResult.length} cached half-year summaries.`);
            
            // 检查是否有新的预约需要更新缓存
            let needsRefresh = false;
            if (latestAppointmentDateStr) {
                // 检查缓存的最新覆盖日期
                const latestCachedDate = cacheResult
                    .map(cache => cache.last_appointment_date_covered ? dayjs(cache.last_appointment_date_covered).format('YYYY-MM-DD') : null)
                    .filter(date => date !== null)
                    .sort()
                    .pop(); // 获取最新的日期

                if (!latestCachedDate || latestCachedDate < latestAppointmentDateStr) {
                    needsRefresh = true;
                    console.log(`[History Summary Cache] Cache needs refresh (Latest: ${latestAppointmentDateStr}, Cached: ${latestCachedDate})`);
                }
            }

            if (!needsRefresh) {
                console.log(`[History Summary Cache] Cache is fresh. Returning cached summaries.`);
                
                // 构建返回的半年度总结对象
                const cachedSummaries = {};
                const cachedPeriods = [];
                cacheResult.forEach(cache => {
                    const period = cache.summary_type.replace('half_year_', '');
                    cachedSummaries[period] = cache.generated_summary;
                    cachedPeriods.push(period);
                });
                cachedPeriods.sort().reverse(); // 按时间倒序排列

                return res.json({
                    success: true,
                    demographicNo: targetDemographicNo,
                    summaries: cachedSummaries,
                    periods: cachedPeriods,
                    source: 'cache'
                });
            }
        } else {
            console.log(`[History Summary Cache] No cache found for demographic ${targetDemographicNo}, language ${language}. Generating...`);
        }

        // --- 4. Generate Summary (If Cache Miss or Stale) ---
        // ... [Existing logic from Step 2 (Fetch Past Appts) to Step 6 (Generate Final Summary)] ...

        // --- 4a. Fetch Past Appointments ---
        const currentDateSQL = dayjs().tz(TIMEZONE).format('YYYY-MM-DD');
        const appointmentQuery = `
            SELECT
                a.appointment_no, a.appointment_date, a.start_time, a.end_time,
                a.reason, a.status, a.location, a.notes AS appointment_notes,
                p.first_name as provider_first_name, p.last_name as provider_last_name,
                p.provider_type
            FROM appointment a
            LEFT JOIN provider p ON a.provider_no = p.provider_no
            WHERE a.demographic_no = ?
              AND a.appointment_date < ?
              AND a.status NOT IN ('C', 'N')
            ORDER BY a.appointment_date ASC, a.start_time ASC
        `;
        const [pastAppointments] = await pool.query(appointmentQuery, [targetDemographicNo, currentDateSQL]);
        console.log(`[History Summary Gen] Found ${pastAppointments.length} past appointments.`);
        if (pastAppointments.length === 0) {
            const emptySummary = language === 'zh' ? "没有找到过往预约历史记录。" : "No past appointment history found.";
            console.log(`[History Summary Gen] No past appointments found.`);
            return res.json({ 
                success: true, 
                demographicNo: targetDemographicNo, 
                summaries: {}, 
                periods: [],
                message: emptySummary,
                source: 'no_data' 
            });
        }

        // --- 4b. Fetch Notes ---
        console.log(`[History Summary Gen] Fetching notes...`);
        const appointmentsWithNotes = [];
        for (const appt of pastAppointments) {
            const noteResult = await appointmentNoteService.getAppointmentNote(appt.appointment_no, targetDemographicNo);
            appointmentsWithNotes.push({
                appointment: appt,
                note: noteResult.success ? noteResult.note : null // Use the structure from getAppointmentNote service
            });
        }

        // --- 检查是否有足够的真实医疗记录 ---
        const validNotes = appointmentsWithNotes.filter(item => item.note && item.note.content && item.note.content.trim().length > 0);
        if (validNotes.length === 0) {
            const noDataMessage = language === 'zh'
                ? "没有找到足够的医疗记录。系统无法生成历史摘要。"
                : "No sufficient medical records found. The system cannot generate a history summary.";

            console.log(`[History Summary Gen] No valid medical notes found for demographic ${targetDemographicNo}.`);

            return res.json({
                success: true,
                demographicNo: targetDemographicNo,
                summaries: {},
                periods: [],
                message: noDataMessage,
                source: 'no_data',
                hasData: false
            });
        }

        // --- 4c. Group by Half-Year ---
        console.log(`[History Summary Gen] Grouping notes...`);
        const groupedNotes = {};
        for (const item of appointmentsWithNotes) {
            const apptDate = dayjs(item.appointment.appointment_date);
            const year = apptDate.year();
            const month = apptDate.month();
            const halfYearKey = month < 6 ? `${year}-H1` : `${year}-H2`;
            if (!groupedNotes[halfYearKey]) { groupedNotes[halfYearKey] = []; }
            groupedNotes[halfYearKey].push(item);
        }

        // --- 4d. Summarize Chunks ---
        console.log(`[History Summary Gen] Summarizing half-year chunks...`);
        const halfYearSummaries = {};
        const sortedKeys = Object.keys(groupedNotes).sort().reverse(); // Sort in reverse to get newest first
        for (const key of sortedKeys) {
            const chunkText = groupedNotes[key].map(formatNoteForAI).join('\n\n---\n\n');
            const summaryResult = await appointmentNoteService.summarizeHistoryWithAI(chunkText, language, 'half-year');
            halfYearSummaries[key] = summaryResult.success ? summaryResult.summary : `Error: ${summaryResult.message}`;
        }

        // --- 5. Update Cache (只缓存半年度总结，不生成整体总结) ---
        if (latestAppointmentDateStr) {
            try {
                // Cache each half-year summary
                for (const key of Object.keys(halfYearSummaries)) {
                    const halfYearCacheQuery = `
                        INSERT INTO patient_summaries (demographic_no, language, summary_type, generated_summary, last_appointment_date_covered)
                        VALUES (?, ?, ?, ?, ?)
                        ON DUPLICATE KEY UPDATE
                        generated_summary = VALUES(generated_summary),
                        last_updated = NOW()
                    `;
                    await pool.query(halfYearCacheQuery, [
                        targetDemographicNo,
                        language,
                        `half_year_${key}`,
                        halfYearSummaries[key],
                        latestAppointmentDateStr
                    ]);
                    console.log(`[History Summary Cache] Cached half-year summary for ${key}`);
                }
            } catch (cacheError) {
                console.error(`[History Summary Cache] Error updating cache:`, cacheError);
                // Don't fail the request, just log the cache error
            }
        }
        else {
            console.log(`[History Summary Cache] No appointments found to cache.`);
        }

        // --- 6. Send Response (只返回半年度总结，移除整体总结) ---
        res.json({
            success: true,
            demographicNo: targetDemographicNo,
            summaries: halfYearSummaries, // 半年度总结对象
            periods: sortedKeys, // 期间数组 (例如: ["2023-H2", "2023-H1", ...])
            source: 'generated' // 表示是新生成的
        });

    } catch (error) {
        console.error('[History Summary] Error in getPatientHistorySummary:', error);
        res.status(500).json({ success: false, message: 'Server error generating history summary', error: error.message });
    }
};

/**
 * Get half-year summaries for a patient based on real appointment data
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} JSON response with all half-year summaries
 */
exports.getPatientHalfYearSummaries = async (req, res) => {
    console.log('<<<<< ENTERING getPatientHalfYearSummaries CONTROLLER >>>>>');

    try {
        // 检查patient_summaries表是否存在，如果不存在则创建
        try {
            await pool.query(`
                CREATE TABLE IF NOT EXISTS patient_summaries (
                    id int(10) unsigned NOT NULL AUTO_INCREMENT,
                    demographic_no int(10) NOT NULL,
                    language varchar(10) NOT NULL DEFAULT 'en',
                    summary_type varchar(50) NOT NULL,
                    generated_summary text NOT NULL,
                    last_appointment_date_covered date DEFAULT NULL,
                    AIgenerated_date datetime DEFAULT NULL,
                    last_updated timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    PRIMARY KEY (id),
                    UNIQUE KEY unique_patient_summary (demographic_no, language, summary_type),
                    KEY demographic_no_index (demographic_no),
                    KEY summary_type_index (summary_type)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='存储患者健康摘要，包括历史摘要和半年度摘要'
            `);
            console.log('[Half-Year Summary] Checked/created patient_summaries table');

            // 添加索引以优化新预约查询
            try {
                await pool.query(`
                    ALTER TABLE appointment ADD INDEX IF NOT EXISTS idx_appt_create_update 
                    (demographic_no, createdatetime, updatedatetime)
                `);
                console.log('[Half-Year Summary] Added index for appointment table if not exists');
            } catch (indexError) {
                console.error('[Half-Year Summary] Error adding index (may already exist):', indexError);
                // 索引可能已存在，继续执行
            }
        } catch (tableError) {
            console.error('[Half-Year Summary] Error checking/creating table:', tableError);
            // 继续执行，不要因为表创建失败而中断
        }

        // 获取请求参数
        const demographicNo = req.params.demographicNo;
        const language = req.query.lang || 'zh'; // 默认使用中文
        let forceRefresh = req.query.refresh === 'true';

        // 1. 验证权限
        const loggedInUser = req.user;
        if (!loggedInUser || !loggedInUser.demographic_no) {
            return res.status(403).json({ success: false, message: 'Authentication required or user not linked.' });
        }

        // 管理员权限检查 - 管理员可以查看任何患者的数据
        if (loggedInUser.role === 'admin') {
            console.log(`管理员用户 ${loggedInUser.id} 被授权查看患者 ${demographicNo} 的半年历史`);
        } else {
            const loggedInDemoNo = loggedInUser.demographic_no;
            const isSelf = parseInt(demographicNo, 10) === loggedInDemoNo;

            let isAllowedRelative = false;
            if (!isSelf) {
                const family = await User.getFamilyMembers(loggedInDemoNo);
                isAllowedRelative = family.some(member => member.relative_demographic_no === parseInt(demographicNo, 10));
            }

            if (!isSelf && !isAllowedRelative) {
                return res.status(403).json({ success: false, message: 'Permission denied to view this history.' });
            }
        }

        // 2. 检查缓存
        console.log(`[Half-Year Summary] Checking cache for demographicNo: ${demographicNo}, language: ${language}`);
        console.log(`[Half-Year Summary] Cache Strategy: Only refresh if new appointments found since last update`);

        const cacheQuery = `
            SELECT summary_type, generated_summary, last_updated, last_appointment_date_covered
            FROM patient_summaries
            WHERE demographic_no = ?
            AND language = ?
            AND summary_type LIKE 'half_year_%'
            ORDER BY summary_type DESC
        `;

        const [cacheResults] = await pool.query(cacheQuery, [demographicNo, language]);

        // 如果有缓存且不强制刷新，检查是否有新的预约
        if (cacheResults.length > 0 && !forceRefresh) {
            console.log(`[Half-Year Summary] Found ${cacheResults.length} cached summaries`);

            // 获取最新的缓存更新时间
            let latestCacheDate = null;
            if (cacheResults.length > 0) {
                latestCacheDate = new Date(cacheResults[0].last_updated);
                console.log(`[Half-Year Summary] Latest cache update time: ${latestCacheDate.toISOString()}`);
            }

            // 检查最后一次摘要生成后是否有新预约（总是检查，不管缓存年龄）
            let hasNewAppointments = false;
            if (latestCacheDate) {
                const newApptQuery = `
                    SELECT COUNT(*) as count 
                    FROM appointment 
                    WHERE demographic_no = ? 
                    AND (createdatetime > ? OR updatedatetime > ?)
                    AND status NOT IN ('C', 'N')
                `;

                const [newApptResult] = await pool.query(newApptQuery,
                    [demographicNo, latestCacheDate, latestCacheDate]);

                hasNewAppointments = newApptResult[0].count > 0;
                console.log(`[Half-Year Summary] New appointments since last cache update: ${hasNewAppointments ? 'Yes (' + newApptResult[0].count + ')' : 'No'}`);

                // 只有在有新预约的情况下才强制刷新
                if (hasNewAppointments) {
                    console.log(`[Half-Year Summary] Found new appointments, forcing refresh to include new medical data`);
                    forceRefresh = true;
                } else {
                    console.log(`[Half-Year Summary] No new appointments found, using cached summaries`);
                }
            }

            // 如果不需要强制刷新（即没有新预约），返回缓存
            if (!forceRefresh) {
                const summaries = {};
                const periods = [];
                let lastUpdated = null;

                cacheResults.forEach(result => {
                    const periodKey = result.summary_type.replace('half_year_', '');
                    summaries[periodKey] = result.generated_summary;
                    periods.push(periodKey);

                    // 记录最新的更新时间
                    const resultDate = new Date(result.last_updated);
                    if (!lastUpdated || resultDate > lastUpdated) {
                        lastUpdated = resultDate;
                    }
                });

                // 排序期间
                periods.sort().reverse();

                return res.json({
                    success: true,
                    demographicNo: demographicNo,
                    summaries: summaries,
                    periods: periods,
                    lastUpdated: lastUpdated,
                    source: 'cache',
                    reason: hasNewAppointments === false ? 'no_new_appointments' : 'cache_available'
                });
            }
        }

        // 3. 如果强制刷新或没有缓存，获取预约数据
        console.log(`[Half-Year Summary] ${forceRefresh ? 'Forcing refresh' : 'No cache found'}, generating summaries...`);

        // 获取预约历史
        const appointmentQuery = `
            SELECT 
                a.appointment_no, a.appointment_date, a.start_time, a.end_time,
                a.reason, a.status, a.location, a.notes,
                p.first_name as provider_first_name, p.last_name as provider_last_name,
                p.provider_type
            FROM appointment a
            LEFT JOIN provider p ON a.provider_no = p.provider_no
            WHERE a.demographic_no = ?
              AND a.status NOT IN ('C', 'N')
            ORDER BY a.appointment_date ASC, a.start_time ASC
        `;

        const [appointments] = await pool.query(appointmentQuery, [demographicNo]);

        if (!appointments || appointments.length === 0) {
            console.log(`[Half-Year Summary] No appointments found for demographic ${demographicNo}`);
            return res.json({
                success: true,
                demographicNo: demographicNo,
                summaries: {},
                periods: [],
                message: 'No appointment history found for this patient',
                lastUpdated: new Date()
            });
        }

        console.log(`[Half-Year Summary] Found ${appointments.length} appointments for demographic ${demographicNo}`);

        // 4. 按半年期间对预约进行分组
        const appointmentsByPeriod = {};

        appointments.forEach(appt => {
            const apptDate = new Date(appt.appointment_date);
            const year = apptDate.getFullYear();
            const month = apptDate.getMonth() + 1; // 月份从0开始，所以+1

            // 确定是上半年(H1)还是下半年(H2)
            const halfYear = month <= 6 ? 'H1' : 'H2';
            const period = `${year}-${halfYear}`;

            // 初始化该期间的数组（如果不存在）
            if (!appointmentsByPeriod[period]) {
                appointmentsByPeriod[period] = [];
            }

            // 添加预约到对应期间
            appointmentsByPeriod[period].push(appt);
        });

        // 5. 为每个期间生成摘要（优化为并行处理）
        const summaries = {};
        const periods = Object.keys(appointmentsByPeriod).sort().reverse();
        const now = new Date();

        // 获取患者的最后预约日期
        let latestAppointmentDate = null;
        if (appointments.length > 0) {
            // 假设appointments已按日期排序（最新的在后）
            const latestAppt = appointments[appointments.length - 1];
            latestAppointmentDate = new Date(latestAppt.appointment_date);
        }

        // 为每个期间并行生成摘要，提高性能
        const summaryPromises = periods.map(async (period) => {
            console.log(`[Half-Year Summary] Generating summary for period ${period}...`);

            const apptsInPeriod = appointmentsByPeriod[period];

            // 为该期间准备AI摘要提示语
            let prompt;
            if (language === 'zh') {
                prompt = `请根据以下患者在${period.split('-')[0]}年${period.split('-')[1] === 'H1' ? '上半年(1-6月)' : '下半年(7-12月)'}的就诊历史，生成一个简洁的健康摘要。总结患者来就诊的主要原因，诊断和治疗。使用简单直接的语言，专业术语需要解释，段落至少3点、最多5点，每点不超过3句话。\n\n`;
            } else {
                prompt = `Based on the following patient's visit history for the ${period.split('-')[1] === 'H1' ? 'first half (Jan-Jun)' : 'second half (Jul-Dec)'} of ${period.split('-')[0]}, generate a concise health summary. Summarize the main reasons for visits, diagnoses, and treatments. Use simple, direct language, explaining any medical terms. The summary should have at least 3 and no more than 5 points, with no more than 3 sentences per point.\n\n`;
            }

            // 添加预约详情到提示语
            apptsInPeriod.forEach(appt => {
                const apptDate = new Date(appt.appointment_date);
                const formattedDate = `${apptDate.getFullYear()}-${(apptDate.getMonth() + 1).toString().padStart(2, '0')}-${apptDate.getDate().toString().padStart(2, '0')}`;
                const provider = formatProviderName(appt.provider_first_name, appt.provider_last_name) || 'Unknown Provider';

                prompt += `Date: ${formattedDate}\n`;
                prompt += `Provider: ${provider}\n`;
                prompt += `Reason: ${appt.reason || 'Not specified'}\n`;
                if (appt.notes) prompt += `Notes: ${appt.notes}\n`;
                prompt += `\n`;
            });

            // 根据语言添加结尾提示
            if (language === 'zh') {
                prompt += `请用中文回复。生成一个结构良好的摘要，重点关注患者健康状况，避免重复信息。`;
            } else {
                prompt += `Please reply in English. Generate a well-structured summary focusing on the patient's health condition, avoiding repetitive information.`;
            }

            // 生成该期间的摘要
            let summary;
            let isAIGenerated = false;

            try {
                // 首先尝试使用AI生成摘要
                console.log(`[Half-Year Summary] Attempting AI summary for period ${period}...`);

                // 尝试调用AI服务生成摘要（已使用统一AI服务重构）
                const summaryResult = await appointmentNoteService.summarizeHistoryWithAI(prompt, language, 'half-year');

                if (summaryResult.success && summaryResult.summary) {
                    summary = summaryResult.summary;
                    isAIGenerated = true;
                    console.log(`[Half-Year Summary] Successfully generated AI summary for period ${period}`);
                } else {
                    throw new Error(summaryResult.message || 'Unknown error generating AI summary');
                }
            } catch (aiError) {
                // AI生成失败时的备用逻辑
                console.error(`[Half-Year Summary] Error generating AI summary for period ${period}:`, aiError);

                // 生成一个基本的摘要，基于可用数据
                if (language === 'zh') {
                    summary = `${period.split('-')[0]}年${period.split('-')[1] === 'H1' ? '上半年' : '下半年'}：检测到有${apptsInPeriod.length}次就诊记录。

主要就诊原因：${[...new Set(apptsInPeriod.map(a => a.reason || '未指明'))].join(', ')}

就诊医生：${[...new Set(apptsInPeriod.map(a => formatProviderName(a.provider_first_name, a.provider_last_name) || '未知医生'))].join(', ')}

注意：由于技术原因无法生成详细AI摘要。请咨询您的医生了解详细情况。`;
                } else {
                    summary = `${period.split('-')[1] === 'H1' ? 'First half' : 'Second half'} of ${period.split('-')[0]}: ${apptsInPeriod.length} visits detected.

Main reasons for visits: ${[...new Set(apptsInPeriod.map(a => a.reason || 'Not specified'))].join(', ')}

Providers seen: ${[...new Set(apptsInPeriod.map(a => formatProviderName(a.provider_first_name, a.provider_last_name) || 'Unknown provider'))].join(', ')}

Note: Detailed AI summary unavailable due to technical issues. Please consult your doctor for specific details.`;
                }
                console.log(`[Half-Year Summary] Generated fallback summary for period ${period}`);
            }

            // 将摘要保存到数据库
            try {
                const summaryType = `half_year_${period}`;
                const now = new Date();

                // 设置AIgenerated_date
                const aiGeneratedDate = isAIGenerated ? now : null;

                // 保存摘要，包括AIgenerated_date字段和last_appointment_date_covered
                await pool.query(`
                    REPLACE INTO patient_summaries
                    (demographic_no, language, summary_type, generated_summary, AIgenerated_date, last_appointment_date_covered, last_updated)
                    VALUES (?, ?, ?, ?, ?, ?, NOW())
                `, [demographicNo, language, summaryType, summary, aiGeneratedDate, latestAppointmentDate]);

                console.log(`[Half-Year Summary] Saved summary for period ${period}, AI generated: ${isAIGenerated ? 'Yes' : 'No'}`);
            } catch (insertError) {
                console.error(`[Half-Year Summary] Error saving summary for period ${period}:`, insertError);
            }

            return { period, summary };
        });

        // 等待所有摘要生成完成
        try {
            const summaryResults = await Promise.all(summaryPromises);
            summaryResults.forEach(result => {
                summaries[result.period] = result.summary;
            });
        } catch (error) {
            console.error('[Half-Year Summary] Error in parallel summary generation:', error);
            // 如果并行处理失败，返回已有的部分结果
        }

        // 6. 返回结果
        console.log(`[Half-Year Summary] Successfully generated summaries for ${periods.length} periods`);

        return res.json({
            success: true,
            demographicNo: demographicNo,
            summaries: summaries,
            periods: periods,
            lastUpdated: now,
            source: 'generated'
        });

    } catch (error) {
        console.error('[Half-Year Summary] Error generating half-year summaries:', error);
        return res.status(500).json({
            success: false,
            message: 'Error generating half-year summaries',
            error: error.message
        });
    }
};

/**
 * Debug function to check database tables
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} JSON response with database information
 */
exports.checkDatabaseTables = async (req, res) => {
    try {
        console.log('Checking database tables...');

        // 检查patient_summaries表是否存在
        try {
            const [tables] = await pool.query(`
                SHOW TABLES LIKE 'patient_summaries'
            `);

            let result = {};

            if (tables.length > 0) {
                result.tableExists = true;

                // 检查表结构
                const [columns] = await pool.query(`
                    DESCRIBE patient_summaries
                `);

                result.columns = columns;

                // 检查表中的数据
                const [count] = await pool.query(`
                    SELECT COUNT(*) as total FROM patient_summaries
                `);

                result.totalRecords = count[0].total;

                // 检查特定demographic_no的数据
                const [records] = await pool.query(`
                    SELECT demographic_no, language, summary_type, LEFT(generated_summary, 100) as summary_preview, last_updated
                    FROM patient_summaries
                    WHERE demographic_no = 54897
                `);

                result.recordsFor54897 = records;
            } else {
                result.tableExists = false;
            }

            // 检查appointment表中的数据
            const [appointmentCount] = await pool.query(`
                SELECT COUNT(*) as total FROM appointment WHERE demographic_no = 54897
            `);

            result.appointmentCount = appointmentCount[0].total;

            if (appointmentCount[0].total > 0) {
                // 获取最早和最晚的预约日期
                const [dateRange] = await pool.query(`
                    SELECT
                        MIN(appointment_date) as earliest_date,
                        MAX(appointment_date) as latest_date
                    FROM appointment
                    WHERE demographic_no = 54897
                `);

                result.appointmentDateRange = dateRange[0];

                // 按年份统计预约数量
                const [yearStats] = await pool.query(`
                    SELECT
                        YEAR(appointment_date) as year,
                        COUNT(*) as count
                    FROM appointment
                    WHERE demographic_no = 54897
                    GROUP BY YEAR(appointment_date)
                    ORDER BY year DESC
                `);

                result.appointmentsByYear = yearStats;
            }

            return res.json({
                success: true,
                result
            });
        } catch (error) {
            console.error('检查数据库表时出错:', error);
            return res.status(500).json({
                success: false,
                error: error.message
            });
        }
    } catch (error) {
        console.error('检查数据库表时出错:', error);
        return res.status(500).json({
            success: false,
            error: error.message
        });
    }
};

