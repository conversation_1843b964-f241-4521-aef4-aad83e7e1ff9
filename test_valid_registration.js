#!/usr/bin/env node

// Test script for valid registration with unique email
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testValidRegistration() {
    console.log('🧪 Testing Valid Registration with 10-digit Health Card...\n');
    
    // Generate unique email with timestamp
    const timestamp = Date.now();
    const testData = {
        firstName: "Test",
        lastName: "User",
        email: `test${timestamp}@example.com`,
        phone: "**********",
        healthInsuranceNumber: "**********",
        password: "password123"
    };
    
    try {
        console.log('Testing registration with:');
        console.log(`Email: ${testData.email}`);
        console.log(`Health Card Number: ${testData.healthInsuranceNumber}`);
        console.log('');
        
        const response = await axios.post(`${BASE_URL}/api/auth/register`, testData);
        
        if (response.data.success) {
            console.log('✅ SUCCESS - Registration completed successfully!');
            console.log('Response:', response.data);
        } else {
            console.log('❌ FAILED - Registration failed:', response.data.message);
        }
        
    } catch (error) {
        console.log('❌ ERROR - Registration failed with error:');
        if (error.response && error.response.data) {
            console.log('Status:', error.response.status);
            console.log('Message:', error.response.data.message);
        } else {
            console.log('Error:', error.message);
        }
    }
}

// Run the test
testValidRegistration().catch(console.error);
