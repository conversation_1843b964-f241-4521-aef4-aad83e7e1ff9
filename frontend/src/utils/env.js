import axios from 'axios';

// 根据环境设置API URL
export const API_URL = process.env.REACT_APP_API_URL || 'https://app-backend.mmcwellness.ca';

// Configure axios defaults
axios.defaults.baseURL = API_URL;
axios.defaults.withCredentials = true;
axios.defaults.headers.common['Access-Control-Allow-Origin'] = window.location.origin;

// Add request interceptor to include credentials and proper headers
axios.interceptors.request.use(
    config => {
        // Ensure credentials are included with every request
        config.withCredentials = true;

        // Add auth token if available
        const token = localStorage.getItem('token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }

        return config;
    },
    error => Promise.reject(error)
);

// Add response interceptor to handle common errors
axios.interceptors.response.use(
    response => response,
    error => {
        // Log the error for debugging
        console.error('API Error:', error);

        // Handle specific error cases
        if (error.response && error.response.status === 401) {
            // Clear auth token on 401 Unauthorized
            localStorage.removeItem('token');
            // Could also redirect to login page if needed
        }

        return Promise.reject(error);
    }
); 