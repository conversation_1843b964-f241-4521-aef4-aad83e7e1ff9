// 测试主服务器连接
console.log('正在测试主服务器数据库连接...');
const mysql = require('mysql2/promise');
require('dotenv').config();

async function main() {
    // 主服务器配置信息
    console.log('主服务器配置:');
    // 对于 Docker 网络，我们需要使用主服务器IP和Docker内部数据库名称
    const mainServerHost = process.env.MAIN_HOST || '**************';
    const mainDbHost = 'open-osp_db_1'; // Docker网络中数据库的主机名
    const mainDbPort = process.env.MAIN_DB_PORT || '3306';
    const mainDbUser = process.env.MAIN_DB_USER || 'root';
    const mainDbPassword = process.env.MAIN_DB_PASSWORD;
    const mainDbName = process.env.MAIN_DB_NAME || 'oscar';

    console.log('服务器主机:', mainServerHost);
    console.log('数据库主机:', mainDbHost);
    console.log('数据库端口:', mainDbPort);
    console.log('数据库用户:', mainDbUser);
    console.log('数据库密码:', mainDbPassword ? '已设置' : '未设置');
    console.log('数据库名称:', mainDbName);

    // 方法1: 直接连接到主服务器上的数据库
    console.log('\n========= 方法1: 直接连接主服务器IP =========');
    try {
        const mainPool = mysql.createPool({
            host: mainServerHost,
            port: mainDbPort,
            user: mainDbUser,
            password: mainDbPassword,
            database: mainDbName,
            waitForConnections: true,
            connectionLimit: 2,
            queueLimit: 0,
            connectTimeout: 10000 // 10秒超时
        });

        console.log('尝试直接连接到主服务器数据库...');
        const [rows] = await mainPool.query('SELECT 1 as test');
        console.log('直接连接成功!');
        console.log('查询结果:', rows);
        await mainPool.end();
    } catch (error) {
        console.error('直接连接失败:', error.message);
        if (error.code) console.error('错误代码:', error.code);
        if (error.errno) console.error('错误状态:', error.errno);
        if (error.sqlState) console.error('SQL状态:', error.sqlState);
        if (error.sqlMessage) console.error('SQL消息:', error.sqlMessage);
    }

    // 方法2: 通过主服务器SSH隧道连接
    console.log('\n========= 方法2: 使用连接池配置 =========');
    try {
        console.log('测试数据库连接池配置...');
        const pool = require('./config/database');

        if (pool.mainServerPool) {
            console.log('主服务器连接池已配置，尝试连接...');
            const [rows] = await pool.mainServerPool.query('SELECT 1 as test');
            console.log('连接池连接成功!');
            console.log('查询结果:', rows);
        } else {
            console.error('错误: mainServerPool 未正确配置');
        }
    } catch (error) {
        console.error('连接池连接失败:', error.message);
        if (error.code) console.error('错误代码:', error.code);
        if (error.errno) console.error('错误状态:', error.errno);
    }

    // 测试本地数据库连接
    try {
        console.log('\n========= 测试本地数据库连接 =========');
        const pool = require('./config/database');
        const [rows] = await pool.query('SELECT 1 as test');
        console.log('本地服务器连接成功!');
        console.log('查询结果:', rows);
    } catch (error) {
        console.error('本地服务器连接失败:', error.message);
        if (error.code) console.error('错误代码:', error.code);
        if (error.errno) console.error('错误状态:', error.errno);
    }
}

main().catch(error => {
    console.error('测试过程中发生错误:', error);
}); 