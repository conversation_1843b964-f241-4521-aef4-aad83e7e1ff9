import React, { useState, useEffect, useCallback } from 'react';
import { API_URL } from '../utils/env';
import axios from 'axios';
import {
    Paper,
    Typography,
    CircularProgress,
    Alert,
    Grid,
    Card,
    CardContent,
    Divider,
    Box,
    Avatar,
    Button
} from '@mui/material';
import { styled } from '@mui/material/styles';
import PersonIcon from '@mui/icons-material/Person';
import HomeIcon from '@mui/icons-material/Home';
import PhoneIcon from '@mui/icons-material/Phone';
import EmailIcon from '@mui/icons-material/Email';
import MedicalInformationIcon from '@mui/icons-material/MedicalInformation';
import WcIcon from '@mui/icons-material/Wc';
import { useLanguage } from '../context/LanguageContext';

// Reusing StyledPaper and InfoItem potentially from Profile.js or define locally
const StyledPaper = styled(Paper)(({ theme }) => ({
    marginTop: theme.spacing(3), // Added margin top
    marginBottom: theme.spacing(3),
    padding: theme.spacing(3),
    [theme.breakpoints.up('md')]: {
        padding: theme.spacing(4),
    },
}));

const InfoItem = ({ icon, primary, secondary }) => {
    // Simplified version for brevity, can copy from Profile.js if needed
    return (
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            {icon && <Box sx={{ mr: 1.5 }}>{React.cloneElement(icon, { fontSize: 'small' })}</Box>}
            <Box>
                <Typography variant="body2" color="text.secondary">{primary}</Typography>
                <Typography variant="body1" sx={{ fontWeight: 500 }}>{secondary || 'N/A'}</Typography>
            </Box>
        </Box>
    );
};

const FamilyMemberDisplay = ({ demographicNo }) => {
    const { t } = useLanguage();
    const [memberData, setMemberData] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);

    const fetchMemberData = useCallback(async (demoNoToFetch) => {
        if (!demoNoToFetch) return;

        console.log(`FamilyMemberDisplay: Fetching profile for ${demoNoToFetch}`);
        setIsLoading(true);
        setError(null);
        setMemberData(null);
        const token = localStorage.getItem('token');

        if (!token) {
            setError('Authentication required.');
            setIsLoading(false);
            return;
        }

        try {
            const response = await axios.get(`${API_URL}/api/auth/profile/${demoNoToFetch}`, {
                headers: { 'Authorization': `Bearer ${token}` },
                timeout: 15000
            });

            if (response.data && response.data.success && response.data.demographicInfo) {
                setMemberData(response.data.demographicInfo);
            } else {
                let errorMessage = 'Failed to fetch family member profile data.';
                if (response.data && !response.data.success) {
                    errorMessage = response.data.message || errorMessage;
                } else if (response.data && !response.data.demographicInfo) {
                    errorMessage = 'Received success, but profile data is missing in response.';
                } else {
                    errorMessage = 'Invalid response structure received.';
                }
                console.error('API Response Error:', response.data);
                throw new Error(errorMessage);
            }
        } catch (err) {
            console.error(`FamilyMemberDisplay: Error fetching profile for ${demoNoToFetch}:`, err);
            let errorMsg = t('fetch_error');
            if (axios.isCancel(err)) {
                errorMsg = 'Request timed out.';
            } else if (err.response) {
                errorMsg = err.response.data?.message || `Server error (${err.response.status})`;
            } else if (err.request) {
                errorMsg = 'Could not connect to server.';
            } else {
                errorMsg = err.message || errorMsg;
            }
            setError(errorMsg);
        } finally {
            setIsLoading(false);
        }
    }, [t]);

    useEffect(() => {
        fetchMemberData(demographicNo);
    }, [demographicNo, fetchMemberData]);

    // --- Render Logic ---

    if (isLoading) {
        return (
            <StyledPaper elevation={2} sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                <CircularProgress />
                <Typography sx={{ ml: 2 }}>Loading Family Member Profile...</Typography>
            </StyledPaper>
        );
    }

    if (error) {
        return (
            <StyledPaper elevation={2}>
                <Typography color="error" variant="h6">Error Loading Family Member</Typography>
                <Alert severity="error" sx={{ mt: 2, width: '100%' }}>{error}</Alert>
                <Button variant="outlined" onClick={() => fetchMemberData(demographicNo)} sx={{ mt: 2 }}>
                    {t('refresh')}
                </Button>
            </StyledPaper>
        );
    }

    if (!memberData) {
        return (
            <StyledPaper elevation={2}>
                <Typography color="textSecondary">Could not load family member data.</Typography>
            </StyledPaper>
        );
    }

    // --- Display Fetched Member Data ---
    return (
        <StyledPaper elevation={2}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <PersonIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                {memberData.first_name ? `${memberData.title || ''} ${memberData.first_name} ${memberData.last_name}` : 'Family Member'}'s Profile
            </Typography>
            <Divider sx={{ mb: 2 }} />
            <Grid container spacing={2}>
                {/* Replicate InfoItem structure from Profile.js, using memberData */}
                <Grid item xs={12} sm={6}>
                    <InfoItem icon={<PersonIcon />} primary={t('full_name')} secondary={`${memberData.title || ''} ${memberData.first_name} ${memberData.last_name}`} />
                </Grid>
                <Grid item xs={12} sm={6}>
                    <InfoItem icon={<HomeIcon />} primary={t('address')} secondary={memberData.address ? `${memberData.address}, ${memberData.city}, ${memberData.province} ${memberData.postal}` : 'N/A'} />
                </Grid>
                <Grid item xs={12} sm={6}>
                    <InfoItem icon={<PhoneIcon />} primary={t('phone')} secondary={memberData.phone} />
                </Grid>
                <Grid item xs={12} sm={6}>
                    <InfoItem icon={<EmailIcon />} primary={t('email')} secondary={memberData.email} />
                </Grid>
                <Grid item xs={12} sm={6}>
                    <InfoItem icon={<MedicalInformationIcon />} primary={t('care_card')} secondary={memberData.hin} />
                </Grid>
                <Grid item xs={12} sm={6}>
                    <InfoItem icon={<WcIcon />} primary={t('sex')} secondary={memberData.sex} />
                </Grid>
                {/* Add other relevant fields if needed */}
            </Grid>
        </StyledPaper>
    );
};

export default FamilyMemberDisplay; 