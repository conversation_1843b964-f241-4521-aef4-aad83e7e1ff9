/**
 * AI 使用统计分类常量定义 - 前端版本
 */

// 服务类别名称映射
export const SERVICE_CATEGORY_NAMES = {
    'user_interaction': '用户交互',
    'content_generation': '内容生成',
    'medical_assistance': '医疗辅助',
    'system_management': '系统管理'
};

// 详细服务类型名称映射
export const SERVICE_TYPE_NAMES = {
    // 聊天机器人相关 - 用户交互类
    'chatbot_general': '通用健康咨询',
    'chatbot_mmc_info': 'MMC服务介绍',
    'chatbot_appointment': '预约相关咨询',
    'chatbot_health_tips': '健康建议提供',
    
    // 内容生成相关
    'tips_daily_generation': '每日健康贴士生成',
    'tips_seasonal': '季节性健康提醒',
    'news_health_crawler': '健康新闻抓取',
    'news_content_summary': '新闻内容总结',
    'article_generation': '医疗文章生成',
    
    // 医疗辅助相关
    'medical_note_generation': '医疗记录生成',
    'diagnosis_assistance': '诊断辅助',
    'symptom_analysis': '症状分析',
    
    // 系统功能相关
    'content_translation': '内容翻译',
    'data_extraction': '数据提取',
    'quality_check': '质量检查',
    
    // 兼容旧的服务类型
    'chatbot': '聊天机器人',
    'tips_generation': '健康贴士生成',
    'health_news_generation': '健康新闻生成'
};

// 使用场景名称映射
export const USE_CASE_NAMES = {
    'patient_inquiry': '患者咨询',
    'staff_assistance': '员工辅助',
    'content_creation': '内容创作',
    'automated_task': '自动化任务',
    'quality_assurance': '质量保证',
    'data_processing': '数据处理'
};

// 用户类型名称映射
export const USER_TYPE_NAMES = {
    'patient': '患者',
    'doctor': '医生',
    'nurse': '护士',
    'admin': '管理员',
    'system': '系统自动',
    'anonymous': '匿名用户'
};

// 服务类型颜色映射（用于图表显示）
export const SERVICE_TYPE_COLORS = {
    'user_interaction': '#4CAF50',
    'content_generation': '#2196F3',
    'medical_assistance': '#FF9800',
    'system_management': '#9C27B0',
    
    // 具体服务类型颜色
    'chatbot_general': '#4CAF50',
    'chatbot_mmc_info': '#66BB6A',
    'chatbot_appointment': '#81C784',
    'chatbot_health_tips': '#A5D6A7',
    
    'tips_daily_generation': '#2196F3',
    'news_health_crawler': '#42A5F5',
    'news_content_summary': '#64B5F6',
    'article_generation': '#90CAF9',
    
    'medical_note_generation': '#FF9800',
    'diagnosis_assistance': '#FFB74D',
    'symptom_analysis': '#FFCC02',
    
    'content_translation': '#9C27B0',
    'data_extraction': '#BA68C8',
    'quality_check': '#CE93D8',
    
    // 兼容旧类型
    'chatbot': '#4CAF50',
    'tips_generation': '#2196F3',
    'health_news_generation': '#42A5F5'
};

/**
 * 获取服务类型的显示名称
 */
export function getServiceTypeName(serviceType) {
    return SERVICE_TYPE_NAMES[serviceType] || serviceType;
}

/**
 * 获取服务类别的显示名称
 */
export function getServiceCategoryName(serviceCategory) {
    return SERVICE_CATEGORY_NAMES[serviceCategory] || serviceCategory;
}

/**
 * 获取使用场景的显示名称
 */
export function getUseCaseName(useCase) {
    return USE_CASE_NAMES[useCase] || useCase;
}

/**
 * 获取用户类型的显示名称
 */
export function getUserTypeName(userType) {
    return USER_TYPE_NAMES[userType] || userType;
}

/**
 * 获取服务类型的颜色
 */
export function getServiceTypeColor(serviceType) {
    return SERVICE_TYPE_COLORS[serviceType] || '#757575';
}

/**
 * 根据服务类型分组数据
 */
export function groupByServiceCategory(data) {
    const grouped = {};
    
    data.forEach(item => {
        const category = item.service_category || 'system_management';
        if (!grouped[category]) {
            grouped[category] = [];
        }
        grouped[category].push(item);
    });
    
    return grouped;
}

/**
 * 计算服务类别统计
 */
export function calculateCategoryStats(data) {
    const stats = {};
    
    data.forEach(item => {
        const category = item.service_category || 'system_management';
        if (!stats[category]) {
            stats[category] = {
                name: getServiceCategoryName(category),
                totalCalls: 0,
                successfulCalls: 0,
                failedCalls: 0,
                totalTokens: 0,
                avgResponseTime: 0,
                color: getServiceTypeColor(category)
            };
        }
        
        stats[category].totalCalls += item.total_calls || 0;
        stats[category].successfulCalls += item.successful_calls || 0;
        stats[category].failedCalls += item.failed_calls || 0;
        stats[category].totalTokens += item.total_tokens || 0;
    });
    
    // 计算平均响应时间
    Object.keys(stats).forEach(category => {
        const categoryData = data.filter(item => (item.service_category || 'system_management') === category);
        const totalResponseTime = categoryData.reduce((sum, item) => sum + (item.total_response_time_ms || 0), 0);
        const totalCalls = stats[category].totalCalls;
        stats[category].avgResponseTime = totalCalls > 0 ? Math.round(totalResponseTime / totalCalls) : 0;
    });
    
    return stats;
} 