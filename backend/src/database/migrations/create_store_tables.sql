-- 商品分类表
CREATE TABLE IF NOT EXISTS `product_categories` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name_en` varchar(100) NOT NULL,
    `name_zh` varchar(100) NOT NULL,
    `description_en` text,
    `description_zh` text,
    `icon` varchar(255),
    `sort_order` int(11) DEFAULT 0,
    `status` enum('active','inactive') DEFAULT 'active',
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 商品表
CREATE TABLE IF NOT EXISTS `products` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name_en` varchar(200) NOT NULL,
    `name_zh` varchar(200) NOT NULL,
    `description_en` text,
    `description_zh` text,
    `features_en` text,
    `features_zh` text,
    `price` decimal(10,2) NOT NULL,
    `category_id` int(11),
    `image_url` varchar(500),
    `duration_days` int(11) DEFAULT NULL COMMENT '服务有效期（天）',
    `is_subscription` tinyint(1) DEFAULT 0 COMMENT '是否为订阅服务',
    `sort_order` int(11) DEFAULT 0,
    `status` enum('active','inactive','deleted') DEFAULT 'active',
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_category` (`category_id`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 订单表
CREATE TABLE IF NOT EXISTS `orders` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `order_number` varchar(50) NOT NULL UNIQUE,
    `user_id` int(11) NOT NULL,
    `demographic_no` int(11) NOT NULL,
    `total_amount` decimal(10,2) NOT NULL,
    `payment_method` varchar(50),
    `payment_status` enum('pending','paid','failed','refunded') DEFAULT 'pending',
    `billing_address` text,
    `notes` text,
    `status` enum('pending','confirmed','processing','completed','cancelled') DEFAULT 'pending',
    `stripe_payment_intent_id` varchar(255),
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_user` (`user_id`),
    KEY `idx_demographic` (`demographic_no`),
    KEY `idx_status` (`status`),
    KEY `idx_payment_status` (`payment_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 订单项表
CREATE TABLE IF NOT EXISTS `order_items` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `order_id` int(11) NOT NULL,
    `product_id` int(11) NOT NULL,
    `quantity` int(11) NOT NULL DEFAULT 1,
    `unit_price` decimal(10,2) NOT NULL,
    `total_price` decimal(10,2) NOT NULL,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_order` (`order_id`),
    KEY `idx_product` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 购物车表
CREATE TABLE IF NOT EXISTS `cart_items` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `product_id` int(11) NOT NULL,
    `quantity` int(11) NOT NULL DEFAULT 1,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_user_product` (`user_id`, `product_id`),
    KEY `idx_user` (`user_id`),
    KEY `idx_product` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 用户服务记录表（购买的服务）
CREATE TABLE IF NOT EXISTS `user_services` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `demographic_no` int(11) NOT NULL,
    `product_id` int(11) NOT NULL,
    `order_id` int(11) NOT NULL,
    `start_date` date NOT NULL,
    `end_date` date,
    `status` enum('active','expired','cancelled') DEFAULT 'active',
    `notes` text,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_user` (`user_id`),
    KEY `idx_demographic` (`demographic_no`),
    KEY `idx_product` (`product_id`),
    KEY `idx_order` (`order_id`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 插入初始商品分类
INSERT IGNORE INTO `product_categories` (`name_en`, `name_zh`, `description_en`, `description_zh`, `icon`, `sort_order`) VALUES
('Membership Services', '会员服务', 'Various membership plans and packages', '各种会员计划和套餐', 'card_membership', 1),
('Functional Medicine', '功能医学', 'Personalized functional medicine consultations and testing', '个性化功能医学咨询和检测', 'biotech', 2),
('Weight Management', '减重管理', 'Weight loss programs and nutrition guidance', '减重计划和营养指导', 'fitness_center', 3),
('Health Checkups', '健康检查', 'Comprehensive health screening packages', '全面健康筛查套餐', 'health_and_safety', 4),
('Nutrition Supplements', '营养补充', 'High-quality supplements and nutrition products', '高品质补充剂和营养产品', 'medication', 5),
('Online Consultations', '在线咨询', 'Virtual consultations with healthcare professionals', '与医疗专业人员的虚拟咨询', 'video_call', 6);

-- 插入示例商品
INSERT IGNORE INTO `products` (`name_en`, `name_zh`, `description_en`, `description_zh`, `features_en`, `features_zh`, `price`, `category_id`, `duration_days`, `is_subscription`) VALUES
('Premium Membership', '高级会员', 'Comprehensive healthcare membership with unlimited consultations', '全面的医疗保健会员资格，无限次咨询', '• Unlimited consultations\n• Priority booking\n• Health monitoring\n• Nutrition guidance', '• 无限次咨询\n• 优先预约\n• 健康监测\n• 营养指导', 299.99, 1, 365, 1),
('Functional Medicine Assessment', '功能医学评估', 'Complete functional medicine evaluation and personalized treatment plan', '完整的功能医学评估和个性化治疗计划', '• Comprehensive testing\n• Personalized plan\n• Follow-up consultations\n• Supplement recommendations', '• 全面检测\n• 个性化计划\n• 后续咨询\n• 补充剂建议', 599.99, 2, 90, 0),
('Weight Loss Program', '减重计划', '12-week medically supervised weight management program', '12周医疗监督减重管理计划', '• Medical supervision\n• Nutrition planning\n• Exercise guidance\n• Progress monitoring', '• 医疗监督\n• 营养规划\n• 运动指导\n• 进度监测', 899.99, 3, 84, 0),
('Annual Health Checkup', '年度健康检查', 'Comprehensive annual health screening package', '全面的年度健康筛查套餐', '• Blood work\n• Physical examination\n• Imaging studies\n• Health report', '• 血液检查\n• 体格检查\n• 影像学检查\n• 健康报告', 399.99, 4, 30, 0),
('Omega-3 Premium', '优质欧米伽3', 'High-quality omega-3 fish oil supplement', '高品质欧米伽3鱼油补充剂', '• Pharmaceutical grade\n• Third-party tested\n• 90-day supply\n• Easy absorption', '• 药用级别\n• 第三方检测\n• 90天供应\n• 易吸收', 79.99, 5, 90, 0),
('Nutrition Consultation', '营养咨询', 'One-on-one consultation with certified nutritionist', '与认证营养师的一对一咨询', '• 60-minute session\n• Personalized meal plan\n• Follow-up support\n• Recipe suggestions', '• 60分钟会话\n• 个性化膳食计划\n• 后续支持\n• 食谱建议', 149.99, 6, 7, 0); 