import React, { createContext, useState, useEffect, useContext, useCallback } from 'react';

// Create the context
const ViewContext = createContext();

// Custom hook to use the context
export const useView = () => useContext(ViewContext);

// Context Provider component
export const ViewProvider = ({ children, initialUser }) => {
    // Initialize state directly from the prop provided by AuthInitializer
    const [loggedInUser, setLoggedInUser] = useState(initialUser || null);
    const [viewingDemographicNo, setViewingDemographicNo] = useState(initialUser?.demographic_no || null);
    const [isLoading, setIsLoading] = useState(false); // Initial loading is handled by AuthInitializer
    const [error, setError] = useState(null);
    const [isChatOpen, setIsChatOpen] = useState(false);

    // This function can now be primarily for REFETCHING data if needed,
    // as initial load is handled by AuthInitializer.
    const refetchInitialData = useCallback(async () => {
        console.log('ViewProvider: Refetching initial data...');
        setIsLoading(true);
        setError(null);
        const token = localStorage.getItem('token');

        if (!token) {
            console.log('ViewProvider: No token found during refetch, clearing state.');
            setLoggedInUser(null);
            setViewingDemographicNo(null);
            setIsLoading(false);
            // Optionally redirect here? Or let AuthInitializer handle it on next render?
            // For now, just clear state.
            return;
        }

        try {
            // Fetch the full profile data again
            const response = await fetch(`${process.env.REACT_APP_API_URL}/api/auth/profile`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                },
                signal: AbortSignal.timeout(10000) // Shorter timeout for refetch?
            });

            if (response.status === 401 || response.status === 403) {
                console.error(`ViewProvider: Auth error on refetch (${response.status}). Clearing token.`);
                localStorage.removeItem('token');
                setLoggedInUser(null);
                setViewingDemographicNo(null);
                setError('认证已过期，请重新登录'); // Session expired, please login again
                setIsLoading(false); // Stop loading on auth error
                return;
            }

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || `Refetch failed (Status: ${response.status})`);
            }

            if (data.success && data.user) {
                console.log('ViewProvider: Refetch successful');
                setLoggedInUser(data.user);
                // Reset view to self on refetch?
                if (data.user.demographic_no) {
                    setViewingDemographicNo(data.user.demographic_no);
                } else {
                    setViewingDemographicNo(null);
                }
            } else {
                throw new Error('Invalid data structure during refetch');
            }

        } catch (err) {
            console.error('ViewProvider Refetch Error:', err);
            setError(err.message || '刷新用户信息失败');
            // Don't necessarily clear user data on refetch error, keep showing stale data?
        } finally {
            setIsLoading(false); // Ensure loading is set to false after refetch attempt
        }
    }, []); // Dependency array is empty, relies on localStorage

    // Remove the automatic useEffect that called fetchInitialData on mount
    // useEffect(() => {
    //     fetchInitialData(); // NO LONGER CALLED AUTOMATICALLY
    //     // ... storage listener remains useful ...
    // }, [fetchInitialData]); 

    // Keep storage listener to handle logout from other tabs
    useEffect(() => {
        const handleStorageChange = (e) => {
            if (e.key === 'token' && !e.newValue) {
                console.log('ViewProvider: Token removed by storage event, resetting state.');
                setLoggedInUser(null);
                setViewingDemographicNo(null);
                // We might want to navigate to login here, or let AuthInitializer handle it on refresh
            }
        };
        window.addEventListener('storage', handleStorageChange);
        return () => {
            window.removeEventListener('storage', handleStorageChange);
        };
    }, []); // Empty dependency array, runs once

    // Function to switch the viewing context - Updated to handle admin users
    const setViewAs = (demographicNo) => {
        console.log(`ViewProvider: Attempting to set view as demographic_no: ${demographicNo}`);
        const targetDemoNo = parseInt(demographicNo, 10);
        const loggedInDemoNo = loggedInUser?.demographic_no;

        if (!loggedInUser || !loggedInDemoNo) {
            console.error('ViewProvider: Cannot switch view, logged in user data missing.');
            return;
        }

        // Allow switching back to self
        if (targetDemoNo === loggedInDemoNo) {
            console.log(`ViewProvider: Switching view back to self: ${targetDemoNo}`);
            setViewingDemographicNo(targetDemoNo);
            return;
        }

        // Allow admin users to view any user
        if (loggedInUser.role === 'admin') {
            console.log(`ViewProvider: Admin user switching view to: ${targetDemoNo}`);
            setViewingDemographicNo(targetDemoNo);
            return;
        }

        // For non-admin users, keep the family member verification logic
        console.log(`ViewProvider: Non-admin user switching view to potentially verified family member: ${targetDemoNo}`);
        // TODO: Ensure this is only called AFTER successful verification in FamilyMembers page
        setViewingDemographicNo(targetDemoNo);
        // A better approach might involve a dedicated function like `setVerifiedView(demoNo)`
    };

    // Function to toggle the chat dialog
    const toggleChat = useCallback(() => {
        console.log('ViewProvider: Toggling chat dialog');
        setIsChatOpen(prev => !prev);
    }, []);

    // Value provided by the context
    const value = {
        loggedInUser,
        viewingDemographicNo,
        setViewAs,
        isViewingOwnProfile: !loggedInUser || viewingDemographicNo === loggedInUser.demographic_no,
        isLoading, // Represents ongoing refetch status, not initial load
        error,
        refetchInitialData: refetchInitialData, // Expose the refetch function
        isChatOpen,
        toggleChat
    };

    return (
        <ViewContext.Provider value={value}>
            {children}
        </ViewContext.Provider>
    );
}; 