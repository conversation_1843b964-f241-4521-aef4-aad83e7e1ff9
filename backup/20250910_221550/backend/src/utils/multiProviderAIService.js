const axios = require('axios');
const crypto = require('crypto');
const aiConfig = require('../config/aiConfig');

class MultiProviderAIService {
    constructor() {
        this.providers = [];
        this.currentProviderIndex = 0;
        this.metrics = {
            totalCalls: 0,
            successfulCalls: 0,
            failedCalls: 0,
            cacheHits: 0,
            cacheMisses: 0,
            providerUsage: {}
        };
        this.cache = new Map();
        this.failureCount = new Map(); // 跟踪每个提供商的失败次数
        this.lastFailureTime = new Map(); // 跟踪每个提供商的最后失败时间
        this.initializeProviders();
    }

    initializeProviders() {
        // 初始化可用的AI提供商
        if (aiConfig.gemini.apiKey) {
            this.providers.push({
                name: 'gemini',
                type: 'gemini',
                config: aiConfig.gemini,
                available: true
            });
        }

        if (aiConfig.openRouter.apiKey) {
            this.providers.push({
                name: 'openrouter-deepseek',
                type: 'openrouter',
                config: aiConfig.openRouter,
                available: true
            });
        }

        if (this.providers.length === 0) {
            throw new Error('No AI providers configured. Please set up at least one provider.');
        }

        console.log(`[Multi-Provider AI] Initialized with ${this.providers.length} providers:`, 
                   this.providers.map(p => p.name));

        // 初始化指标
        this.providers.forEach(provider => {
            this.metrics.providerUsage[provider.name] = {
                calls: 0,
                successes: 0,
                failures: 0
            };
            this.failureCount.set(provider.name, 0);
        });
    }

    /**
     * 生成缓存键
     */
    generateCacheKey(prompt, config) {
        const cacheData = {
            prompt: typeof prompt === 'string' ? prompt : JSON.stringify(prompt),
            config: config
        };
        return crypto
            .createHash('sha256')
            .update(JSON.stringify(cacheData))
            .digest('hex');
    }

    /**
     * 获取缓存
     */
    getCache(key) {
        const cached = this.cache.get(key);
        if (cached && cached.expires > Date.now()) {
            this.metrics.cacheHits++;
            return cached.data;
        }
        if (cached) {
            this.cache.delete(key);
        }
        this.metrics.cacheMisses++;
        return null;
    }

    /**
     * 设置缓存
     */
    setCache(key, data, ttl) {
        if (aiConfig.cache.enabled) {
            this.cache.set(key, {
                data: data,
                expires: Date.now() + (ttl * 1000)
            });
        }
    }

    /**
     * 检查提供商是否暂时不可用
     */
    isProviderTemporarilyUnavailable(providerName) {
        const failures = this.failureCount.get(providerName) || 0;
        const lastFailure = this.lastFailureTime.get(providerName) || 0;
        const now = Date.now();
        
        // 如果连续失败3次以上，且最后失败时间在5分钟内，则认为暂时不可用
        if (failures >= 3 && (now - lastFailure) < 5 * 60 * 1000) {
            return true;
        }
        
        // 如果超过5分钟，重置失败计数
        if ((now - lastFailure) > 5 * 60 * 1000) {
            this.failureCount.set(providerName, 0);
        }
        
        return false;
    }

    /**
     * 记录提供商失败
     */
    recordProviderFailure(providerName) {
        const current = this.failureCount.get(providerName) || 0;
        this.failureCount.set(providerName, current + 1);
        this.lastFailureTime.set(providerName, Date.now());
    }

    /**
     * 记录提供商成功
     */
    recordProviderSuccess(providerName) {
        this.failureCount.set(providerName, 0);
        this.lastFailureTime.delete(providerName);
    }

    /**
     * 获取下一个可用的提供商
     */
    getNextAvailableProvider() {
        // 找到所有可用的提供商
        const availableProviders = this.providers.filter(provider => 
            provider.available && !this.isProviderTemporarilyUnavailable(provider.name)
        );

        if (availableProviders.length === 0) {
            // 如果所有提供商都不可用，重置失败计数并返回第一个
            console.warn('[Multi-Provider AI] All providers temporarily unavailable, resetting...');
            this.providers.forEach(provider => {
                this.failureCount.set(provider.name, 0);
                this.lastFailureTime.delete(provider.name);
            });
            return this.providers[0];
        }

        // 简单的轮询策略：返回下一个可用的提供商
        if (this.currentProviderIndex >= availableProviders.length) {
            this.currentProviderIndex = 0;
        }
        
        const provider = availableProviders[this.currentProviderIndex];
        this.currentProviderIndex = (this.currentProviderIndex + 1) % availableProviders.length;
        
        return provider;
    }

    /**
     * 检查错误是否是临时错误
     */
    isTemporaryError(error) {
        if (!error) return false;
        
        const errorMessage = error.message?.toLowerCase() || '';
        const statusCode = error.status || error.response?.status;
        
        // 429 (Too Many Requests), 5xx 服务器错误, 网络错误等
        const temporaryStatuses = [429, 500, 502, 503, 504];
        if (temporaryStatuses.includes(statusCode)) {
            return true;
        }
        
        // 检查错误消息中的临时错误关键词
        const temporaryKeywords = [
            'rate limit',
            'quota',
            'too many requests',
            'timeout',
            'connection',
            'network',
            'service unavailable'
        ];
        
        return temporaryKeywords.some(keyword => errorMessage.includes(keyword));
    }

    /**
     * 使用Gemini API调用
     */
    async callGeminiAPI(prompt, config, provider) {
        const requestBody = this.buildGeminiRequestBody(prompt, config);
        const url = `${provider.config.baseURL}/models/${provider.config.model}:generateContent?key=${provider.config.apiKey}`;
        
        const httpClient = axios.create({
            timeout: provider.config.timeout || 30000,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'MMC-WebApp/1.0'
            }
        });

        const response = await httpClient.post(url, requestBody);
        return this.parseGeminiResponse(response);
    }

    /**
     * 使用OpenRouter API调用
     */
    async callOpenRouterAPI(prompt, config, provider) {
        const messages = this.buildOpenRouterMessages(prompt);
        const url = `${provider.config.baseURL}/chat/completions`;
        
        const requestBody = {
            model: provider.config.model,
            messages: messages,
            max_tokens: config.maxOutputTokens || 800,
            temperature: config.temperature || 0.1
        };

        const httpClient = axios.create({
            timeout: 30000,
            headers: {
                'Authorization': `Bearer ${provider.config.apiKey}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': 'app.mmcwellness.ca',
                'X-Title': 'MMC Wellness AI Service'
            }
        });

        const response = await httpClient.post(url, requestBody);
        return this.parseOpenRouterResponse(response);
    }

    /**
     * 构建Gemini请求体
     */
    buildGeminiRequestBody(prompt, config = {}) {
        const generationConfig = {
            ...aiConfig.gemini.defaultConfig,
            ...config
        };

        let requestBody = {
            generationConfig: generationConfig
        };

        if (typeof prompt === 'string') {
            requestBody.contents = [{
                parts: [{ text: prompt }]
            }];
        } else if (prompt.system && prompt.user) {
            requestBody.systemInstruction = {
                parts: [{ text: prompt.system }]
            };
            requestBody.contents = [{
                parts: [{ text: prompt.user }]
            }];
        } else if (prompt.user) {
            requestBody.contents = [{
                parts: [{ text: prompt.user }]
            }];
        } else {
            throw new Error('Invalid prompt format for Gemini API');
        }

        return requestBody;
    }

    /**
     * 构建OpenRouter消息格式
     */
    buildOpenRouterMessages(prompt) {
        if (typeof prompt === 'string') {
            return [{ role: 'user', content: prompt }];
        } else if (prompt.system && prompt.user) {
            return [
                { role: 'system', content: prompt.system },
                { role: 'user', content: prompt.user }
            ];
        } else if (prompt.user) {
            return [{ role: 'user', content: prompt.user }];
        } else {
            throw new Error('Invalid prompt format for OpenRouter API');
        }
    }

    /**
     * 解析Gemini响应
     */
    parseGeminiResponse(response) {
        if (!response.data) {
            throw new Error('Empty response from Gemini API');
        }

        const { data } = response;
        
        if (data.candidates && 
            data.candidates[0] && 
            data.candidates[0].content && 
            data.candidates[0].content.parts && 
            data.candidates[0].content.parts[0] && 
            data.candidates[0].content.parts[0].text) {
            
            return data.candidates[0].content.parts[0].text.trim();
        }

        throw new Error('Unexpected Gemini API response format');
    }

    /**
     * 解析OpenRouter响应
     */
    parseOpenRouterResponse(response) {
        if (!response.data) {
            throw new Error('Empty response from OpenRouter API');
        }

        const { data } = response;
        
        if (data.choices && data.choices[0] && data.choices[0].message) {
            const message = data.choices[0].message;
            
            // 优先使用content字段
            if (message.content && message.content.trim()) {
                return message.content.trim();
            }
            
            // 如果content为空，尝试使用reasoning字段（DeepSeek R1模型特有）
            if (message.reasoning && message.reasoning.trim()) {
                console.log('[Multi-Provider AI] Using reasoning field from DeepSeek R1 model');
                return message.reasoning.trim();
            }
        }

        console.error('[Multi-Provider AI] Unexpected OpenRouter response format:', JSON.stringify(data, null, 2));
        throw new Error('Unexpected OpenRouter API response format');
    }

    /**
     * 延迟执行
     */
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 使用单个提供商执行AI调用
     */
    async executeWithProvider(prompt, config, provider, attempt = 1) {
        const maxRetries = 3;
        
        try {
            console.log(`[Multi-Provider AI] Using ${provider.name} (attempt ${attempt})`);
            
            // 更新指标
            this.metrics.providerUsage[provider.name].calls++;
            
            let result;
            if (provider.type === 'gemini') {
                result = await this.callGeminiAPI(prompt, config, provider);
            } else if (provider.type === 'openrouter') {
                result = await this.callOpenRouterAPI(prompt, config, provider);
            } else {
                throw new Error(`Unsupported provider type: ${provider.type}`);
            }

            // 成功时记录
            this.recordProviderSuccess(provider.name);
            this.metrics.providerUsage[provider.name].successes++;
            
            return result;
            
        } catch (error) {
            console.error(`[Multi-Provider AI] ${provider.name} failed (attempt ${attempt}):`, error.message);
            
            // 更新失败指标
            this.metrics.providerUsage[provider.name].failures++;
            this.recordProviderFailure(provider.name);
            
            // 如果是临时错误且还有重试机会，则重试
            if (this.isTemporaryError(error) && attempt < maxRetries) {
                const delay = Math.pow(2, attempt - 1) * 1000; // 指数退避
                console.log(`[Multi-Provider AI] Retrying ${provider.name} in ${delay}ms...`);
                await this.delay(delay);
                return this.executeWithProvider(prompt, config, provider, attempt + 1);
            }
            
            throw error;
        }
    }

    /**
     * 主要的内容生成方法
     */
    async generateContent(prompt, options = {}) {
        this.metrics.totalCalls++;
        
        // 检查缓存
        const cacheKey = this.generateCacheKey(prompt, options);
        const cached = this.getCache(cacheKey);
        if (cached) {
            return cached;
        }

        const maxProviderAttempts = this.providers.length;
        let lastError;
        
        // 尝试所有可用的提供商
        for (let providerAttempt = 0; providerAttempt < maxProviderAttempts; providerAttempt++) {
            try {
                const provider = this.getNextAvailableProvider();
                const result = await this.executeWithProvider(prompt, options, provider);
                
                // 成功时缓存结果
                const ttl = aiConfig.cache.ttl[options.serviceType] || 3600;
                this.setCache(cacheKey, result, ttl);
                
                this.metrics.successfulCalls++;
                console.log(`[Multi-Provider AI] Successfully generated content using ${provider.name}`);
                
                return result;
                
            } catch (error) {
                lastError = error;
                console.warn(`[Multi-Provider AI] Provider failed, trying next provider...`);
                
                // 如果这是最后一个提供商，并且错误不是临时的，则抛出错误
                if (providerAttempt === maxProviderAttempts - 1) {
                    break;
                }
            }
        }
        
        // 所有提供商都失败了
        this.metrics.failedCalls++;
        console.error('[Multi-Provider AI] All providers failed');
        throw new Error(`All AI providers failed. Last error: ${lastError?.message}`);
    }

    /**
     * 兼容性方法 - 与原有GeminiAIService接口保持一致
     */
    async generateMedicalNoteSummary(prompt, noteId, language = 'en') {
        return this.generateContent(prompt, {
            serviceType: 'medicalNotes',
            noteId: noteId,
            language: language,
            maxOutputTokens: 800,
            temperature: 0.1
        });
    }

    async generatePrescriptionExplanation(prompt, prescriptionId, language = 'zh') {
        return this.generateContent(prompt, {
            serviceType: 'prescription',
            prescriptionId: prescriptionId,
            language: language,
            maxOutputTokens: 600,
            temperature: 0.1
        });
    }

    async generateImmunizationExplanation(prompt, immunizationId, language = 'zh') {
        return this.generateContent(prompt, {
            serviceType: 'immunization',
            immunizationId: immunizationId,
            language: language,
            maxOutputTokens: 800,
            temperature: 0.2
        });
    }

    async generateDieticianSummary(prompt, commentId, language = 'zh') {
        return this.generateContent(prompt, {
            serviceType: 'dietician',
            commentId: commentId,
            language: language,
            maxOutputTokens: 800,
            temperature: 0.1
        });
    }

    /**
     * 获取服务指标
     */
    getMetrics() {
        return {
            ...this.metrics,
            providers: this.providers.map(provider => ({
                name: provider.name,
                type: provider.type,
                available: provider.available,
                temporarilyUnavailable: this.isProviderTemporarilyUnavailable(provider.name),
                failures: this.failureCount.get(provider.name) || 0,
                lastFailure: this.lastFailureTime.get(provider.name)
            }))
        };
    }

    /**
     * 清理缓存
     */
    cleanupCache() {
        const now = Date.now();
        for (const [key, value] of this.cache.entries()) {
            if (value.expires <= now) {
                this.cache.delete(key);
            }
        }
    }
}

// 创建单例实例
const multiProviderAIService = new MultiProviderAIService();

// 定期清理缓存
setInterval(() => {
    multiProviderAIService.cleanupCache();
}, 10 * 60 * 1000); // 每10分钟清理一次

module.exports = multiProviderAIService; 