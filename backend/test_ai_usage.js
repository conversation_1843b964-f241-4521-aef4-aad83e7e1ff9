const aiUsageTracker = require('./src/utils/aiUsageTracker');

async function testAIUsage() {
    try {
        console.log('Testing AI usage tracking with model_name...');
        
        // 记录一个Gemini使用
        await aiUsageTracker.recordUsage({
            provider: 'gemini',
            serviceType: 'test_service',
            endpoint: '/test',
            modelName: 'gemini-2.0-flash',
            success: true,
            inputTokens: 100,
            outputTokens: 150,
            responseTimeMs: 1200,
            cacheHit: false,
            estimatedCostUsd: 0.001
        });
        
        // 记录一个OpenRouter使用
        await aiUsageTracker.recordUsage({
            provider: 'openrouter',
            serviceType: 'test_service',
            endpoint: '/test',
            modelName: 'deepseek/deepseek-r1-0528:free',
            success: true,
            inputTokens: 200,
            outputTokens: 300,
            responseTimeMs: 2000,
            cacheHit: false,
            estimatedCostUsd: 0.002
        });
        
        console.log('✅ Test AI usage records created successfully!');
        
        // 获取今天的统计
        const today = new Date().toISOString().split('T')[0];
        const stats = await aiUsageTracker.getUsageStats(today, today);
        
        console.log('📊 Today\'s AI usage stats:');
        console.log(JSON.stringify(stats, null, 2));
        
    } catch (error) {
        console.error('❌ Error testing AI usage:', error);
    }
}

testAIUsage(); 