const path = require('path');
const fs = require('fs');
const pool = require('../config/database');
const { v4: uuidv4 } = require('uuid');
const { createClient } = require('@supabase/supabase-js');
const multer = require('multer');

// Initialize Supabase Client
// Ensure SUPABASE_URL and SUPABASE_SERVICE_KEY are set in your environment variables
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
    console.error('Supabase URL or Service Key is missing. Please check environment variables.');
    // Optionally throw an error or handle this state to prevent the app from running without config
}
const supabase = supabaseUrl && supabaseServiceKey ? createClient(supabaseUrl, supabaseServiceKey) : null;

const DEFAULT_BUCKET_NAME = 'patient-files'; // Default bucket, can be overridden by env or specific logic
const BUCKET_NAME = process.env.SUPABASE_BUCKET_NAME || DEFAULT_BUCKET_NAME;

// Unified error handling
const handleError = (res, error, message = 'Internal server error') => {
    console.error(`File operation error: ${error.message}`, error);
    res.status(500).json({
        success: false,
        message: message,
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
};

// Get user file list (this function primarily interacts with the database, so Supabase changes are minimal here unless stored_filename interpretation changes)
exports.getUserFiles = async (req, res) => {
    try {
        const { demographicNo } = req.query;
        if (!demographicNo) {
            return res.status(400).json({ success: false, message: 'Demographic number is required' });
        }
        const query = `
            SELECT f.id, f.original_filename, f.stored_filename, f.file_size, f.file_type, 
                   f.upload_date, f.demographic_no, d.first_name, d.last_name
            FROM patient_files f
            JOIN demographic d ON f.demographic_no = d.demographic_no
            WHERE f.demographic_no = ?
            ORDER BY f.upload_date DESC
        `;
        const [files] = await pool.query(query, [demographicNo]);
        return res.json({
            success: true,
            files: files.map(file => ({
                id: file.id,
                name: file.original_filename,
                size: file.file_size,
                type: file.file_type,
                uploadDate: file.upload_date,
                uploadedBy: `${file.first_name} ${file.last_name}`,
                // stored_filename is now the Supabase path, not directly a public URL
            }))
        });
    } catch (error) {
        handleError(res, error, 'Failed to retrieve files');
    }
};

// Get shared files (similar to getUserFiles, database-centric)
exports.getSharedFiles = async (req, res) => {
    try {
        const { demographicNo } = req.query;
        if (!demographicNo) {
            return res.status(400).json({ success: false, message: 'Demographic number is required' });
        }
        const query = `
            SELECT f.id, f.original_filename, f.stored_filename, f.file_size, f.file_type, 
                   f.upload_date, f.demographic_no, d.first_name, d.last_name,
                   sf.shared_date
            FROM shared_files sf
            JOIN patient_files f ON sf.file_id = f.id
            JOIN demographic d ON f.demographic_no = d.demographic_no
            WHERE sf.shared_to = ?
            ORDER BY sf.shared_date DESC
        `;
        const [files] = await pool.query(query, [demographicNo]);
        return res.json({
            success: true,
            files: files.map(file => ({
                id: file.id,
                name: file.original_filename,
                size: file.file_size,
                type: file.file_type,
                uploadDate: file.upload_date,
                sharedDate: file.shared_date,
                sharedBy: `${file.first_name} ${file.last_name}`,
                sharedByDemographicNo: file.demographic_no
            }))
        });
    } catch (error) {
        handleError(res, error, 'Failed to retrieve shared files');
    }
};

// Upload file to Supabase Storage
exports.uploadFile = async (req, res) => {
    if (!supabase) return handleError(res, new Error('Supabase client not initialized.'), 'Supabase not configured');
    try {
        if (!req.file || !req.file.buffer) {
            return res.status(400).json({ success: false, message: 'No file buffer uploaded. Ensure multer is using memoryStorage.' });
        }

        const { demographicNo, fileCategory } = req.body;
        if (!demographicNo) {
            return res.status(400).json({ success: false, message: 'Demographic number is required' });
        }

        const originalFilename = req.file.originalname;
        const fileExtension = path.extname(originalFilename);
        // Construct the file path for Supabase Storage. Folder structure can be decided here.
        // Example: user_{demographicNo}/{uuid}{fileExtension} or {fileCategory}/{uuid}{fileExtension}
        let supabasePath = `${uuidv4()}${fileExtension}`;
        if (fileCategory) {
            supabasePath = `${fileCategory.replace(/[^a-zA-Z0-9_\-\/]/g, '')}/${supabasePath}`; // Sanitize category for path
        } else {
            // Default path if no category, e.g., directly in bucket or a default folder
            supabasePath = `general_uploads/${supabasePath}`;
        }

        const { data: uploadData, error: uploadError } = await supabase.storage
            .from(BUCKET_NAME)
            .upload(supabasePath, req.file.buffer, {
                contentType: req.file.mimetype,
                upsert: false // Set to true if you want to overwrite, false to error on conflict
            });

        if (uploadError) {
            console.error('Supabase upload error:', uploadError);
            return handleError(res, uploadError, 'Failed to upload file to Supabase Storage');
        }
        
        // The `uploadData.path` should be the actual path stored in Supabase, use this one.
        const actualSupabasePath = uploadData.path;

        const query = `
            INSERT INTO patient_files 
            (demographic_no, original_filename, stored_filename, file_size, file_type, upload_date)
            VALUES (?, ?, ?, ?, ?, NOW())
        `;
        const [result] = await pool.query(query, [
            demographicNo,
            originalFilename,
            actualSupabasePath, // Store the Supabase path/key
            req.file.size,
            req.file.mimetype
        ]);

        return res.status(201).json({
            success: true,
            message: 'File uploaded successfully to Supabase',
            fileId: result.insertId,
            supabasePath: actualSupabasePath
        });
    } catch (error) {
        handleError(res, error, 'Failed to upload file');
    }
};

// Generate pre-signed URL for downloading file from Supabase Storage
exports.downloadFile = async (req, res) => {
    if (!supabase) return handleError(res, new Error('Supabase client not initialized.'), 'Supabase not configured');
    try {
        const { fileId } = req.params; // This is the ID from patient_files table
        const { demographicNo } = req.query; // For permission checking

        if (!fileId || !demographicNo) {
            return res.status(400).json({ success: false, message: 'File ID and Demographic number are required' });
        }

        const accessQuery = `
            SELECT f.stored_filename, f.original_filename, f.file_type 
            FROM patient_files f
            WHERE f.id = ? AND (
                f.demographic_no = ? OR EXISTS (
                    SELECT 1 FROM shared_files sf
                    WHERE sf.file_id = f.id AND sf.shared_to = ?
                )
            )
        `;
        const [files] = await pool.query(accessQuery, [fileId, demographicNo, demographicNo]);

        if (files.length === 0) {
            return res.status(403).json({ success: false, message: 'Access denied to this file or file not found' });
        }

        const file = files[0];
        const supabasePath = file.stored_filename; // This is the path in Supabase Storage
        const expiresIn = 3600; // URL valid for 1 hour

        const { data: signedUrlData, error: signedUrlError } = await supabase.storage
            .from(BUCKET_NAME)
            .createSignedUrl(supabasePath, expiresIn);

        if (signedUrlError) {
            return handleError(res, signedUrlError, 'Failed to create signed URL');
        }

        return res.json({
            success: true,
            url: signedUrlData.signedUrl,
            originalFilename: file.original_filename,
            fileType: file.file_type
        });
    } catch (error) {
        handleError(res, error, 'Failed to generate download link');
    }
};

// Delete file from Supabase Storage
exports.deleteFile = async (req, res) => {
    if (!supabase) return handleError(res, new Error('Supabase client not initialized.'), 'Supabase not configured');
    try {
        const { fileId } = req.params;
        const { demographicNo } = req.query; // For permission checking

        if (!fileId || !demographicNo) {
            return res.status(400).json({ success: false, message: 'File ID and Demographic number are required' });
        }

        const accessQuery = `SELECT stored_filename FROM patient_files WHERE id = ? AND demographic_no = ?`;
        const [files] = await pool.query(accessQuery, [fileId, demographicNo]);

        if (files.length === 0) {
            return res.status(403).json({ success: false, message: 'You do not have permission to delete this file or file not found' });
        }

        const supabasePath = files[0].stored_filename;

        const { error: deleteError } = await supabase.storage
            .from(BUCKET_NAME)
            .remove([supabasePath]);

        if (deleteError && deleteError.statusCode !== '404') { // Don't error if file was already not found in storage
             console.warn(`Supabase delete warning for path ${supabasePath}:`, deleteError.message); 
             // If file not found in Supabase, we might still want to delete the DB record.
        }

        await pool.query('DELETE FROM shared_files WHERE file_id = ?', [fileId]);
        await pool.query('DELETE FROM patient_files WHERE id = ?', [fileId]);

        return res.json({ success: true, message: 'File deleted successfully' });
    } catch (error) {
        // Fallback DB deletion attempt (less critical if Supabase already handled or file was not there)
        try {
            const { fileId } = req.params; // Re-access fileId for this isolated catch block
            if (fileId) {
                console.warn(`Attempting DB cleanup for fileId ${fileId} after Supabase error or during general error handling in deleteFile`);
                await pool.query('DELETE FROM shared_files WHERE file_id = ?', [fileId]);
                await pool.query('DELETE FROM patient_files WHERE id = ?', [fileId]);
            }
        } catch (dbCleanupError) {
            console.error(`Failed during DB cleanup for fileId ${fileId}:`, dbCleanupError);
            // Log this error but don't override the original error sent to the client.
        }
        handleError(res, error, 'Failed to delete file');
    }
};

// Share file (database-centric, no direct Supabase interaction here)
exports.shareFile = async (req, res) => {
    try {
        const { fileId } = req.params;
        const { demographicNo, sharedTo } = req.body;

        if (!fileId || !demographicNo || !sharedTo) {
            return res.status(400).json({
                success: false,
                message: 'File ID, your demographic number, and recipient demographic number are required'
            });
        }

        const accessQuery = `
            SELECT * FROM patient_files
            WHERE id = ? AND demographic_no = ?
        `;
        const [files] = await pool.query(accessQuery, [fileId, demographicNo]);
        if (files.length === 0) {
            return res.status(403).json({ success: false, message: 'You do not have permission to share this file' });
        }
        const recipientQuery = `SELECT demographic_no FROM demographic WHERE demographic_no = ?`;
        const [recipients] = await pool.query(recipientQuery, [sharedTo]);
        if (recipients.length === 0) {
            return res.status(404).json({ success: false, message: 'Recipient not found' });
        }
        const existingShareQuery = `SELECT * FROM shared_files WHERE file_id = ? AND shared_to = ?`;
        const [existingShares] = await pool.query(existingShareQuery, [fileId, sharedTo]);
        if (existingShares.length > 0) {
            return res.status(409).json({ success: false, message: 'File already shared with this recipient' });
        }
        const shareQuery = `INSERT INTO shared_files (file_id, shared_by, shared_to, shared_date) VALUES (?, ?, ?, NOW())`;
        await pool.query(shareQuery, [fileId, demographicNo, sharedTo]);
        return res.json({ success: true, message: 'File shared successfully' });
    } catch (error) {
        handleError(res, error, 'Failed to share file');
    }
};

// Get file shares (database-centric)
exports.getFileShares = async (req, res) => {
    try {
        const { fileId } = req.params;
        const { demographicNo } = req.query;
        if (!fileId || !demographicNo) {
            return res.status(400).json({ success: false, message: 'File ID and demographic number are required' });
        }
        const ownershipQuery = `SELECT * FROM patient_files WHERE id = ? AND demographic_no = ?`;
        const [files] = await pool.query(ownershipQuery, [fileId, demographicNo]);
        if (files.length === 0) {
            return res.status(403).json({ success: false, message: 'You do not have permission to view shares for this file' });
        }
        const sharesQuery = `
            SELECT sf.*, d.first_name, d.last_name
            FROM shared_files sf
            JOIN demographic d ON sf.shared_to = d.demographic_no
            WHERE sf.file_id = ?
            ORDER BY sf.shared_date DESC
        `;
        const [shares] = await pool.query(sharesQuery, [fileId]);
        return res.json({
            success: true,
            shares: shares.map(share => ({
                id: share.id,
                fileId: share.file_id,
                sharedTo: share.shared_to,
                sharedToName: `${share.first_name} ${share.last_name}`,
                sharedDate: share.shared_date
            }))
        });
    } catch (error) {
        handleError(res, error, 'Failed to retrieve file shares');
    }
};

// Upload user avatar to Supabase Storage and update demographic table
exports.uploadUserAvatar = async (req, res) => {
    if (!supabase) return handleError(res, new Error('Supabase client not initialized.'), 'Supabase not configured');
    
    const { userId } = req.user; // Assuming auth middleware provides userId

    if (!req.file || !req.file.buffer) {
        return res.status(400).json({ success: false, message: 'No avatar image uploaded. Ensure multer is using memoryStorage.' });
    }

    if (!userId) {
        return res.status(400).json({ success: false, message: 'User ID not found. Authentication required.' });
    }

    let conn;
    try {
        conn = await pool.getConnection();
        await conn.beginTransaction();

        // Get demographic_no from user_auth table using userId
        const [userAuthRows] = await conn.query('SELECT demographic_no FROM user_auth WHERE id = ?', [userId]);
        if (userAuthRows.length === 0 || !userAuthRows[0].demographic_no) {
            await conn.rollback();
            return res.status(404).json({ success: false, message: 'User profile (demographic record) not found or not linked.' });
        }
        const demographicNo = userAuthRows[0].demographic_no;

        // Define a specific folder for avatars
        const AVATARS_FOLDER = 'avatars';
        const originalFilename = req.file.originalname;
        const fileExtension = path.extname(originalFilename);
        const uniqueFilename = `${uuidv4()}${fileExtension}`;
        const supabasePath = `${AVATARS_FOLDER}/${demographicNo}/${uniqueFilename}`; // e.g., avatars/12345/uuid.jpg (scoped by demographicNo for clarity)

        // Optional: Before uploading a new avatar, delete the old one from Supabase if it exists
        const [demographicRows] = await conn.query('SELECT avatar_url FROM demographic WHERE demographic_no = ?', [demographicNo]);
        if (demographicRows.length > 0 && demographicRows[0].avatar_url) {
            const oldAvatarKey = demographicRows[0].avatar_url;
            if (oldAvatarKey && !oldAvatarKey.startsWith('http')) { // Ensure it's a key, not a full URL from a previous system
                console.log(`Attempting to delete old avatar from Supabase: ${oldAvatarKey}`);
                const { error: deleteOldError } = await supabase.storage.from(BUCKET_NAME).remove([oldAvatarKey]);
                if (deleteOldError && deleteOldError.statusCode !== '404') {
                    console.warn(`Failed to delete old avatar (${oldAvatarKey}) from Supabase, continuing with new upload:`, deleteOldError.message);
                }
            }
        }

        // Upload the new avatar to Supabase
        console.log(`Attempting to upload avatar to Supabase path: ${supabasePath}`);
        const { data: uploadData, error: uploadError } = await supabase.storage
            .from(BUCKET_NAME)
            .upload(supabasePath, req.file.buffer, {
                contentType: req.file.mimetype,
                upsert: false // Should be unique due to UUID
            });

        if (uploadError) {
            console.error('Supabase avatar upload error:', uploadError);
            await conn.rollback();
            return handleError(res, uploadError, 'Failed to upload avatar to Supabase Storage');
        }
        
        const actualSupabaseAvatarPath = uploadData.path; // This is the key to store

        // Update avatar_url in demographic table
        const updateQuery = `UPDATE demographic SET avatar_url = ? WHERE demographic_no = ?`;
        const [updateResult] = await conn.query(updateQuery, [actualSupabaseAvatarPath, demographicNo]);

        if (updateResult.affectedRows === 0) {
            await conn.rollback();
            // This case might mean the demographic_no is valid (from user_auth) but doesn't exist in demographic table, which is unlikely if linked.
            return res.status(404).json({ success: false, message: 'Demographic record not found for update.' });
        }

        await conn.commit();

        // Optionally, generate a public URL or signed URL if needed by the frontend immediately
        // For now, just returning the path (key)
        return res.status(200).json({
            success: true,
            message: 'Avatar uploaded and profile updated successfully',
            avatarPath: actualSupabaseAvatarPath
        });

    } catch (error) {
        if (conn) await conn.rollback();
        handleError(res, error, 'Failed to upload avatar');
    } finally {
        if (conn) conn.release();
    }
};

// Utility functions like ensureDirectoryExists and getUploadDir are no longer directly used by these core S3/Supabase operations.
// They can be removed if no other part of the application uses them for local file system tasks.
// function ensureDirectoryExists() { ... }
// function getUploadDir() { ... }

// 配置multer用于商品图片上传
const productImageStorage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadPath = path.join(__dirname, '../../uploads/products');
        // 确保目录存在
        if (!fs.existsSync(uploadPath)) {
            fs.mkdirSync(uploadPath, { recursive: true });
        }
        cb(null, uploadPath);
    },
    filename: function (req, file, cb) {
        // 生成唯一文件名：product-timestamp-random.ext
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const ext = path.extname(file.originalname);
        cb(null, 'product-' + uniqueSuffix + ext);
    }
});

// 文件过滤器
const imageFilter = (req, file, cb) => {
    // 检查文件类型
    if (file.mimetype.startsWith('image/')) {
        cb(null, true);
    } else {
        cb(new Error('只允许上传图片文件'), false);
    }
};

// 创建multer实例
const uploadProductImage = multer({
    storage: productImageStorage,
    fileFilter: imageFilter,
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB限制
    }
});

/**
 * 上传商品图片
 */
const uploadProductImageHandler = async (req, res) => {
    try {
        // 检查管理员权限
        if (!req.user || req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Access denied. Admin privileges required.'
            });
        }

        if (!req.file) {
            return res.status(400).json({
                success: false,
                message: '没有上传文件'
            });
        }

        // 构建文件URL
        const fileUrl = `/uploads/products/${req.file.filename}`;
        
        res.json({
            success: true,
            message: '图片上传成功',
            imageUrl: fileUrl,
            filename: req.file.filename,
            originalName: req.file.originalname,
            size: req.file.size
        });

    } catch (error) {
        handleError(res, error, 'Error uploading product image');
    }
};

/**
 * 删除商品图片
 */
const deleteProductImage = async (req, res) => {
    try {
        // 检查管理员权限
        if (!req.user || req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Access denied. Admin privileges required.'
            });
        }

        const { filename } = req.params;
        const filePath = path.join(__dirname, '../../uploads/products', filename);

        // 检查文件是否存在
        if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
            res.json({
                success: true,
                message: '图片删除成功'
            });
        } else {
            res.status(404).json({
                success: false,
                message: '文件不存在'
            });
        }

    } catch (error) {
        handleError(res, error, 'Error deleting product image');
    }
};

module.exports = {
    getUserFiles: exports.getUserFiles,
    getSharedFiles: exports.getSharedFiles,
    uploadFile: exports.uploadFile,
    downloadFile: exports.downloadFile,
    deleteFile: exports.deleteFile,
    shareFile: exports.shareFile,
    getFileShares: exports.getFileShares,
    uploadUserAvatar: exports.uploadUserAvatar,
    uploadProductImage,
    uploadProductImageHandler,
    deleteProductImage
}; 