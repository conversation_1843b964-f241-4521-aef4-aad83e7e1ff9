const jwt = require('jsonwebtoken');
const User = require('../models/user');

/**
 * Authentication middleware
 * Verifies JWT token in Authorization header
 */
const authenticate = async (req, res, next) => {
    try {
        console.log('Authentication middleware triggered');

        // Get token from Authorization header
        const authHeader = req.headers.authorization;
        console.log('Authorization header:', authHeader ? 'Present' : 'Missing');

        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            console.log('No valid bearer token found in Authorization header');
            return res.status(401).json({
                message: 'Authentication required. No token provided.',
                code: 'NO_TOKEN'
            });
        }

        const token = authHeader.split(' ')[1];
        console.log('Token extracted from header');

        try {
            // Verify token
            const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
            console.log('Token verified successfully, user ID:', decoded.id);

            // Get full user data from database
            const userData = await User.findById(decoded.id);
            if (!userData) {
                console.log('User not found in database:', decoded.id);
                return res.status(401).json({
                    message: 'Authentication failed. User not found.',
                    code: 'USER_NOT_FOUND'
                });
            }

            console.log('User found:', userData.email);

            // Add user info to request
            req.user = userData;
            next();
        } catch (jwtError) {
            console.error('JWT verification error:', jwtError.name, jwtError.message);

            if (jwtError.name === 'TokenExpiredError') {
                return res.status(401).json({
                    message: 'Authentication failed. Token expired.',
                    code: 'TOKEN_EXPIRED'
                });
            } else if (jwtError.name === 'JsonWebTokenError') {
                return res.status(401).json({
                    message: 'Authentication failed. Invalid token.',
                    code: 'INVALID_TOKEN'
                });
            } else {
                return res.status(401).json({
                    message: 'Authentication failed. Token verification error.',
                    code: 'TOKEN_ERROR'
                });
            }
        }
    } catch (error) {
        console.error('Authentication error:', error);
        return res.status(401).json({
            message: 'Authentication failed. Server error.',
            code: 'AUTH_ERROR'
        });
    }
};

module.exports = authenticate; 