version: '3.8'

services:
  health-crawler:
    build:
      context: .
      dockerfile: Dockerfile.crawler
    container_name: mmcwebapp-health-crawler
    volumes:
      - ../backend/documents:/usr/src/backend/documents
    restart: "no"
    # Explicitly load the .env file from the project root directory
    env_file:
      - ../.env
    environment:
      # Keep GEMINI_API_KEY here in case it's not in the root .env
      # It will be overridden if present in ../.env
      - GEMINI_API_KEY=${GEMINI_API_KEY:-AIzaSyD6hvJFZ2dXyG4eApen_ic7vqRcjBoz5GU}
      # DB variables will be loaded from ../.env
    networks:
      - webapp-network
      - open-osp_back-tier

networks:
  webapp-network:
    driver: bridge
  open-osp_back-tier:
    external: true
