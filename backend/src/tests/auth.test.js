const request = require('supertest');
const app = require('../app');
const User = require('../models/user');
const pool = require('../config/database');

describe('Authentication System Tests', () => {
    let testUser;
    let authToken;

    beforeAll(async () => {
        // 清理测试数据
        await pool.query('DELETE FROM user_auth WHERE email LIKE ?', ['<EMAIL>']);
        await pool.query('DELETE FROM demographic WHERE hin LIKE ?', ['TEST%']);
    });

    afterAll(async () => {
        // 清理测试数据
        await pool.query('DELETE FROM user_auth WHERE email LIKE ?', ['<EMAIL>']);
        await pool.query('DELETE FROM demographic WHERE hin LIKE ?', ['TEST%']);
        await pool.end();
    });

    describe('Registration', () => {
        it('should register a new user successfully', async () => {
            const userData = {
                email: '<EMAIL>',
                password: 'Test123!',
                firstName: 'Test',
                lastName: 'User',
                address: '123 Test St',
                city: 'Test City',
                province: 'ON',
                postalCode: 'A1A1A1',
                phoneNumber: '**********',
                dateOfBirth: '1990-01-01',
                healthInsuranceNumber: 'TEST123456',
                sex: 'M'
            };

            const response = await request(app)
                .post('/api/auth/register')
                .send(userData);

            expect(response.status).toBe(201);
            expect(response.body.success).toBe(true);
            expect(response.body.token).toBeDefined();
            expect(response.body.user).toBeDefined();
            expect(response.body.user.email).toBe(userData.email);

            testUser = response.body.user;
            authToken = response.body.token;
        });

        it('should not register with existing email', async () => {
            const userData = {
                email: '<EMAIL>',
                password: 'Test123!',
                firstName: 'Test',
                lastName: 'User',
                address: '123 Test St',
                city: 'Test City',
                province: 'ON',
                postalCode: 'A1A1A1',
                phoneNumber: '**********',
                dateOfBirth: '1990-01-01',
                healthInsuranceNumber: 'TEST123456',
                sex: 'M'
            };

            const response = await request(app)
                .post('/api/auth/register')
                .send(userData);

            expect(response.status).toBe(400);
            expect(response.body.message).toBe('Email is already registered');
        });
    });

    describe('Login', () => {
        it('should login successfully with correct credentials', async () => {
            const loginData = {
                email: '<EMAIL>',
                password: 'Test123!'
            };

            const response = await request(app)
                .post('/api/auth/login')
                .send(loginData);

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
            expect(response.body.token).toBeDefined();
            expect(response.body.user).toBeDefined();
            expect(response.body.user.email).toBe(loginData.email);
        });

        it('should not login with incorrect password', async () => {
            const loginData = {
                email: '<EMAIL>',
                password: 'WrongPassword123!'
            };

            const response = await request(app)
                .post('/api/auth/login')
                .send(loginData);

            expect(response.status).toBe(401);
            expect(response.body.message).toBe('Invalid email or password');
        });
    });

    describe('Account Linking', () => {
        it('should link account with existing demographic record', async () => {
            const linkData = {
                hin: 'TEST123456',
                dateOfBirth: '1990-01-01',
                email: '<EMAIL>',
                password: 'Test123!'
            };

            const response = await request(app)
                .post('/api/auth/link')
                .send(linkData);

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
            expect(response.body.token).toBeDefined();
            expect(response.body.user).toBeDefined();
            expect(response.body.user.demographicNo).toBeDefined();
        });
    });

    describe('Profile', () => {
        it('should get user profile with valid token', async () => {
            const response = await request(app)
                .get('/api/auth/profile')
                .set('Authorization', `Bearer ${authToken}`);

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
            expect(response.body.user).toBeDefined();
            expect(response.body.user.email).toBe('<EMAIL>');
        });

        it('should not get profile without token', async () => {
            const response = await request(app)
                .get('/api/auth/profile');

            expect(response.status).toBe(401);
        });
    });
}); 