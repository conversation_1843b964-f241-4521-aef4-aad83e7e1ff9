# 🛠️ 运维文档

系统运维、监控和维护相关文档

## 📋 文档列表

### 文档同步系统
- [**PDF同步脚本指南**](./SYNC_PDFS_README.md) - 文档同步系统详细使用说明
  - 脚本功能特性
  - 多种运行模式
  - 日志系统
  - 故障排除

- [**同步脚本变更日志**](./SYNC_SCRIPT_CHANGELOG.md) - 同步功能更新记录
  - v2.0 增强版功能
  - 技术改进说明
  - 迁移指南

## 🔧 运维操作

### 文档同步
```bash
# 查看统计信息
./sync-pdfs.sh --stats-only

# 正常同步
./sync-pdfs.sh

# 模拟运行（测试）
./sync-pdfs.sh --dry-run

# 查看帮助
./sync-pdfs.sh --help
```

### 日志监控
```bash
# 查看同步日志
tail -f logs/sync-pdfs.log

# 查看错误日志
grep "ERROR" logs/*.log

# 查看cron任务日志
tail -f logs/cron-sync.log
```

### 系统维护
```bash
# Docker服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f [service_name]

# 重启服务
docker-compose restart [service_name]
```

## 📊 监控指标

### 同步系统状态
- 远程文件数量: 31,479个文件
- 本地目录大小: 2.9GB
- 连接检查时间: < 1秒
- 统计获取时间: < 2秒

### 系统资源
- 磁盘空间使用
- 网络连接状态
- SSH连接健康度
- 日志文件大小

## 🚨 故障处理

### 常见问题
1. **SSH连接失败**
   - 检查网络连接
   - 验证SSH密钥
   - 确认远程服务器状态

2. **同步失败**
   - 查看详细日志
   - 检查磁盘空间
   - 验证权限设置

3. **性能问题**
   - 监控资源使用
   - 检查网络延迟
   - 优化同步策略

### 应急处理
- 立即停止problematic进程
- 查看错误日志
- 联系技术支持
- 记录故障详情

## 📅 定期维护

### 每日任务
- 检查同步状态
- 监控日志文件
- 验证服务运行

### 每周任务
- 清理旧日志文件
- 检查磁盘空间
- 验证备份完整性

### 每月任务
- 更新系统组件
- 检查安全配置
- 性能优化评估

## 🔗 相关链接

- [返回文档中心](../)
- [部署文档](../deployment/) - 系统部署
- [开发文档](../development/) - 开发指南
- [API文档](../api/) - 接口文档

## 📞 运维支持

- 紧急故障请立即联系系统管理员
- 一般问题请先查阅相关文档
- 新需求请提交工单

---

*最后更新: 2025-05-28* 