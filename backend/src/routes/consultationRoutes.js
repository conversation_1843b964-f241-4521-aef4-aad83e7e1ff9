console.log('>>> Loading backend/src/routes/consultationRoutes.js...'); // <<< DEBUG LOG
const express = require('express');
const consultationController = require('../controllers/consultationController');
const authenticateToken = require('../middleware/auth');

const router = express.Router();

// Route to get consultations for a specific patient
// Applies authentication middleware first
router.get(
    '/:demographicNo',
    authenticateToken,
    consultationController.fetchPatientConsultations
);

module.exports = router; 