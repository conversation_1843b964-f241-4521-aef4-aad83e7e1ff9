const pool = require('../config/database');

class Order {
    /**
     * 创建新订单
     */
    static async createOrder(orderData) {
        const connection = await pool.getConnection();
        
        try {
            await connection.beginTransaction();
            
            const {
                user_id,
                demographic_no,
                items, // [{ product_id, quantity, price }]
                total_amount,
                payment_method,
                billing_address,
                notes
            } = orderData;

            // 生成订单号
            const order_number = `MMC${Date.now()}${Math.floor(Math.random() * 1000)}`;

            // 创建主订单
            const [orderResult] = await connection.query(`
                INSERT INTO orders (
                    order_number, user_id, demographic_no, total_amount,
                    payment_method, billing_address, notes, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', NOW())
            `, [
                order_number, user_id, demographic_no, total_amount,
                payment_method, JSON.stringify(billing_address), notes
            ]);

            const orderId = orderResult.insertId;

            // 创建订单项
            for (const item of items) {
                await connection.query(`
                    INSERT INTO order_items (
                        order_id, product_id, quantity, unit_price, total_price
                    ) VALUES (?, ?, ?, ?, ?)
                `, [
                    orderId, item.product_id, item.quantity, 
                    item.price, item.quantity * item.price
                ]);
            }

            await connection.commit();
            
            return { 
                success: true, 
                orderId, 
                orderNumber: order_number 
            };
        } catch (error) {
            await connection.rollback();
            console.error('Error creating order:', error);
            return { 
                success: false, 
                message: 'Failed to create order', 
                error: error.message 
            };
        } finally {
            connection.release();
        }
    }

    /**
     * 获取用户订单列表
     */
    static async getUserOrders(userId, filters = {}) {
        try {
            let query = `
                SELECT 
                    o.*,
                    COUNT(oi.id) as item_count
                FROM orders o
                LEFT JOIN order_items oi ON o.id = oi.order_id
                WHERE o.user_id = ?
            `;
            
            const params = [userId];
            
            if (filters.status) {
                query += ' AND o.status = ?';
                params.push(filters.status);
            }
            
            query += ' GROUP BY o.id ORDER BY o.created_at DESC';
            
            if (filters.limit) {
                query += ' LIMIT ?';
                params.push(parseInt(filters.limit));
            }

            const [orders] = await pool.query(query, params);
            return { success: true, orders };
        } catch (error) {
            console.error('Error fetching user orders:', error);
            return { success: false, message: 'Failed to fetch orders', error: error.message };
        }
    }

    /**
     * 获取订单详情
     */
    static async getOrderById(orderId, userId = null) {
        try {
            let query = `
                SELECT 
                    o.*,
                    oi.id as item_id,
                    oi.product_id,
                    oi.quantity,
                    oi.unit_price,
                    oi.total_price,
                    p.name_en as product_name_en,
                    p.name_zh as product_name_zh,
                    p.image_url as product_image
                FROM orders o
                LEFT JOIN order_items oi ON o.id = oi.order_id
                LEFT JOIN products p ON oi.product_id = p.id
                WHERE o.id = ?
            `;
            
            const params = [orderId];
            
            if (userId) {
                query += ' AND o.user_id = ?';
                params.push(userId);
            }

            const [rows] = await pool.query(query, params);
            
            if (rows.length === 0) {
                return { success: false, message: 'Order not found' };
            }

            // 组织订单数据
            const order = {
                id: rows[0].id,
                order_number: rows[0].order_number,
                user_id: rows[0].user_id,
                demographic_no: rows[0].demographic_no,
                total_amount: rows[0].total_amount,
                payment_method: rows[0].payment_method,
                billing_address: JSON.parse(rows[0].billing_address || '{}'),
                notes: rows[0].notes,
                status: rows[0].status,
                payment_status: rows[0].payment_status,
                created_at: rows[0].created_at,
                updated_at: rows[0].updated_at,
                items: []
            };

            // 添加订单项
            rows.forEach(row => {
                if (row.item_id) {
                    order.items.push({
                        id: row.item_id,
                        product_id: row.product_id,
                        product_name_en: row.product_name_en,
                        product_name_zh: row.product_name_zh,
                        product_image: row.product_image,
                        quantity: row.quantity,
                        unit_price: row.unit_price,
                        total_price: row.total_price
                    });
                }
            });

            return { success: true, order };
        } catch (error) {
            console.error('Error fetching order:', error);
            return { success: false, message: 'Failed to fetch order', error: error.message };
        }
    }

    /**
     * 更新订单状态
     */
    static async updateOrderStatus(orderId, status, paymentStatus = null) {
        try {
            let query = 'UPDATE orders SET status = ?, updated_at = NOW()';
            const params = [status];
            
            if (paymentStatus) {
                query += ', payment_status = ?';
                params.push(paymentStatus);
            }
            
            query += ' WHERE id = ?';
            params.push(orderId);

            const [result] = await pool.query(query, params);
            
            if (result.affectedRows === 0) {
                return { success: false, message: 'Order not found' };
            }

            return { success: true };
        } catch (error) {
            console.error('Error updating order status:', error);
            return { success: false, message: 'Failed to update order status', error: error.message };
        }
    }

    /**
     * 获取所有订单 (管理员功能)
     */
    static async getAllOrders(filters = {}) {
        try {
            let query = `
                SELECT 
                    o.*,
                    u.email as user_email,
                    d.first_name,
                    d.last_name,
                    COUNT(oi.id) as item_count
                FROM orders o
                LEFT JOIN user_auth u ON o.user_id = u.id
                LEFT JOIN demographic d ON o.demographic_no = d.demographic_no
                LEFT JOIN order_items oi ON o.id = oi.order_id
                WHERE 1=1
            `;
            
            const params = [];
            
            if (filters.status) {
                query += ' AND o.status = ?';
                params.push(filters.status);
            }
            
            if (filters.payment_status) {
                query += ' AND o.payment_status = ?';
                params.push(filters.payment_status);
            }
            
            if (filters.date_from) {
                query += ' AND DATE(o.created_at) >= ?';
                params.push(filters.date_from);
            }
            
            if (filters.date_to) {
                query += ' AND DATE(o.created_at) <= ?';
                params.push(filters.date_to);
            }
            
            query += ' GROUP BY o.id ORDER BY o.created_at DESC';
            
            if (filters.limit) {
                query += ' LIMIT ?';
                params.push(parseInt(filters.limit));
            }

            const [orders] = await pool.query(query, params);
            return { success: true, orders };
        } catch (error) {
            console.error('Error fetching all orders:', error);
            return { success: false, message: 'Failed to fetch orders', error: error.message };
        }
    }
}

module.exports = Order; 