CREATE TABLE appointment_reason_images (
    id INT AUTO_INCREMENT PRIMARY KEY,
    appointment_no_fk INT, -- Assuming appointment.appointment_no is INT. Adjust if different.
    reason_image_path VARCHAR(1024) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_appointment_no_fk (appointment_no_fk)
    -- If you have a direct way to set up foreign key constraints, you can add:
    -- CONSTRAINT fk_appointment_reason_image FOREIGN KEY (appointment_no_fk) REFERENCES appointment(appointment_no) ON DELETE SET NULL ON UPDATE CASCADE
    -- However, direct FK constraints might be complex with existing systems or if not managed by a tool.
    -- An index is generally safe and beneficial for lookups.
);

-- Optional: Add a comment to the table or columns if your DB supports it and you find it useful
-- COMMENT ON TABLE appointment_reason_images IS 'Stores paths to images uploaded as reasons for appointments.';
