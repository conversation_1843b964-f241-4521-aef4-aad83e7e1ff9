const mysql = require('mysql2/promise');

async function clearAIStats() {
    let connection;
    try {
        connection = await mysql.createConnection({
            host: process.env.MAIN_DB_HOST || '**************',
            user: process.env.MAIN_DB_USER || 'root',
            password: process.env.MAIN_DB_PASSWORD || 'Z2Rh6VGr7DE=',
            database: process.env.MAIN_DB_NAME || 'oscar'
        });

        console.log('Connected to database');

        // 清空AI使用统计表
        console.log('Clearing ai_usage_stats table...');
        await connection.execute('DELETE FROM ai_usage_stats');
        
        console.log('Clearing ai_error_logs table...');
        await connection.execute('DELETE FROM ai_error_logs');

        console.log('✅ AI stats tables cleared successfully!');

    } catch (error) {
        console.error('❌ Error clearing AI stats:', error);
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

clearAIStats(); 