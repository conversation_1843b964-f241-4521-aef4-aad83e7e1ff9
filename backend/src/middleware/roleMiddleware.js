/**
 * 角色权限中间件
 * 检查用户是否有指定的角色权限
 */

/**
 * 创建角色检查中间件
 * @param {string|Array} allowedRoles - 允许的角色，可以是字符串或数组
 * @returns {Function} 中间件函数
 */
const roleMiddleware = (allowedRoles) => {
    return (req, res, next) => {
        try {
            // 确保用户已通过身份验证
            if (!req.user) {
                return res.status(401).json({
                    success: false,
                    message: '用户未认证'
                });
            }

            // 获取用户角色
            const userRole = req.user.role;
            
            if (!userRole) {
                return res.status(403).json({
                    success: false,
                    message: '用户角色未定义'
                });
            }

            // 将单个角色转换为数组处理
            const rolesArray = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles];
            
            // 检查用户是否具有所需角色
            if (!rolesArray.includes(userRole)) {
                console.log(`[RoleMiddleware] Access denied for user ${req.user.id}. Required: ${rolesArray.join(', ')}, Got: ${userRole}`);
                return res.status(403).json({
                    success: false,
                    message: `权限不足。需要以下角色之一: ${rolesArray.join(', ')}`
                });
            }

            // 用户有正确的角色，继续处理请求
            console.log(`[RoleMiddleware] Access granted for user ${req.user.id} with role ${userRole}`);
            next();
        } catch (error) {
            console.error('[RoleMiddleware] Error in role check:', error);
            return res.status(500).json({
                success: false,
                message: '角色验证出错'
            });
        }
    };
};

module.exports = roleMiddleware; 