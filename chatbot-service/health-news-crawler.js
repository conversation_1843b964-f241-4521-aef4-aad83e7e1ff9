const cron = require('node-cron');
const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
require('dotenv').config();

// --- Helper Function for Sanitizing Filenames ---
function sanitizeFilename(title) {
    if (!title) return `untitled-article-${Date.now()}`;
    // Convert to lowercase, replace non-alphanumeric with hyphen, trim hyphens
    const sanitized = title
        .toLowerCase()
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/[^a-z0-9-]/g, '') // Remove non-alphanumeric characters except hyphens
        .replace(/-+/g, '-') // Replace multiple hyphens with single
        .replace(/^-+|-+$/g, ''); // Trim leading/trailing hyphens
    return sanitized || `article-${Date.now()}`; // Fallback if empty after sanitization
}

// 从环境变量加载配置
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
const GEMINI_MODEL = process.env.GEMINI_MODEL || 'gemini-2.0-flash';

// Expanded and diversified health topics
const HEALTH_TOPICS = [
    // Core Topics
    "最新的营养学研究进展",
    "改善睡眠质量的科学方法",
    "心理健康与压力管理技巧",
    "常见慢性病（如高血压、糖尿病）的预防与管理",
    "运动与健身的最新指南",
    "疫苗接种的重要性与最新建议",
    "癌症预防与早期筛查",
    "心脏健康维护",
    "消化系统健康与常见问题",
    "骨骼与关节健康",
    "皮肤健康与护理",
    // Trends & Popular Knowledge
    "近期流行的健康饮食趋势分析 (例如：间歇性禁食、生酮饮食)",
    "益生菌与肠道健康",
    "正念冥想对健康的益处",
    "可穿戴健康设备的应用与解读",
    "常见维生素和补充剂的科学依据",
    "公众关注的传染病预防知识 (例如：流感、COVID-19变种)",
    // Celebrity/Current Events (Ethically handled)
    "从近期公开报道的名人健康事件中可以学到的健康教训 (强调隐私和普遍性)",
    "当前季节常见的健康问题及预防 (例如：春季过敏、夏季中暑)",
    // General Knowledge & Prevention
    "如何读懂体检报告中的关键指标",
    "家庭急救常识",
    "健康生活方式对长寿的影响",
    "不同年龄段的健康检查建议",
    "避免常见健康误区",
    "提高免疫力的实用方法"
];

// 创建数据库连接池
const pool = mysql.createPool({
    host: process.env.DB_HOST || 'database',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME || 'oscar',
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
});

// 保存文章到文件系统
async function saveArticle(title, content, abstract, category) {
    try {
        const date = new Date();
        const sanitizedTitle = sanitizeFilename(title);
        const safeCategory = category ? sanitizeFilename(category) : 'general-health';
        const filename = `${sanitizedTitle}.md`;
        const targetDir = path.join('/usr/src/backend/documents', safeCategory);
        const filePath = path.join(targetDir, filename);

        // 确保目录存在
        if (!fs.existsSync(targetDir)) {
            fs.mkdirSync(targetDir, { recursive: true });
            console.log(`Created directory: ${targetDir}`);
        }

        // 创建Markdown格式内容, use actual category
        const markdown = `---
title: ${title}
category: ${safeCategory}
tags: ${safeCategory}, health, tips, research
abstract: ${abstract || ''}
featured: true
date: ${date.toISOString()}
---

${content}

---
**免责声明:** 本文由 MMC 健康管理中心提供，仅供一般健康信息参考。内容不能替代专业的医疗建议、诊断或治疗。如有任何健康问题，请务必咨询医生或其他合格的医疗保健提供者。您可以考虑联系 MMC 健康管理中心获取专业问诊服务。
`;

        fs.writeFileSync(filePath, markdown);
        console.log(`Article saved to ${filePath}`);

        // 同时存储到数据库中
        await saveArticleToDatabase(title, content, abstract, safeCategory);

        return filePath;
    } catch (error) {
        console.error('Error saving article:', error);
    }
}

// 保存文章到数据库
async function saveArticleToDatabase(title, content, abstract, category) {
    try {
        // 检查并添加 abstract 列
        try {
            await pool.query('ALTER TABLE health_articles ADD COLUMN abstract TEXT');
            console.log("Checked/Added 'abstract' column to health_articles table.");
        } catch (alterError) {
            if (alterError.code !== 'ER_DUP_FIELDNAME') {
                console.warn("Could not add 'abstract' column (might already exist):", alterError.code);
            }
        }

        // 检查并添加 category 列
        try {
            await pool.query('ALTER TABLE health_articles ADD COLUMN category VARCHAR(100)');
            console.log("Added 'category' column to health_articles table.");
        } catch (alterError) {
            if (alterError.code !== 'ER_DUP_FIELDNAME') {
                console.warn("Could not add 'category' column (might already exist):", alterError.code);
            }
        }

        // 确保表存在
        await pool.query(`
      CREATE TABLE IF NOT EXISTS health_articles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        abstract TEXT,
        category VARCHAR(100),
        sources TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

        // 插入文章
        await pool.query(
            'INSERT INTO health_articles (title, content, abstract, category, sources) VALUES (?, ?, ?, ?, ?)',
            [title, content, abstract, category, JSON.stringify([{ title: "Generated by Gemini AI", url: "https://mmcwellness.ca" }])]
        );

        console.log('Article saved to database');
    } catch (error) {
        console.error('Error saving article to database:', error);
    }
}

// Import AI usage tracker
let aiUsageTracker;
try {
    aiUsageTracker = require('./aiUsageTracker');
    console.log('AI usage tracker loaded for health news crawler');
} catch (error) {
    console.warn('AI usage tracker not available:', error.message);
    aiUsageTracker = {
        recordUsage: async () => {},
        recordError: async () => {}
    };
}

// Import AI usage types for detailed classification
const aiUsageTypes = require('./aiUsageTypes');

// Import AI Service Manager for intelligent provider switching
let aiServiceManager;
try {
    aiServiceManager = require('./aiServiceManager');
    console.log('Health News Crawler AI Service Manager loaded successfully');
} catch (error) {
    console.warn('AI Service Manager not available for health news crawler:', error.message);
    aiServiceManager = null;
}

// 使用Gemini API直接生成健康内容
async function generateHealthContent() {
    const startTime = Date.now();
    
    try {
        const randomTopic = HEALTH_TOPICS[Math.floor(Math.random() * HEALTH_TOPICS.length)];
        console.log(`Generating health content for topic: ${randomTopic}`);

        // Determine content style (50% Research/In-depth, 50% Popular/Preventive)
        const isPopularStyle = Math.random() < 0.5;
        const styleInstructions = isPopularStyle
            ? "请以通俗易懂、贴近生活的语言撰写，侧重于实用建议、预防措施和破除常见误区。风格活泼有趣，适合大众阅读。"
            : "请以专业、严谨的风格撰写，侧重于科学依据、研究进展和深入分析。内容详实可靠，适合对健康有较高关注度的人群。";

        // Updated prompt incorporating diversification and style
        const prompt = `请围绕选定主题 "${randomTopic}" 撰写一篇健康科普文章，语言为简体中文。

文章要求：
1.  **风格:** ${styleInstructions}
2.  **标题:** 起一个吸引人且信息丰富的标题，符合选定风格。
3.  **摘要 (Abstract):** 在文章最开始提供一个 50-100 字的简短摘要，总结文章核心内容。请使用 "摘要:" (冒号为英文冒号) 作为标签开始摘要内容。
4.  **分类 (Category):** 在摘要之后，单独一行，使用 "Category:" (冒号为英文冒号) 标签，根据内容建议一个最相关的英文分类名称 (例如 "Nutrition", "Mental-Health", "Fitness", "Preventive-Care", "Disease-Info", "Health-Trends", "General-Knowledge")。
5.  **引言:** 简要介绍该主题的重要性或趣味性。
6.  **正文:** 
    *   包含准确信息和实用建议。
    *   结构清晰，逻辑连贯，使用 Markdown 格式组织章节和小标题 (例如 ##, ###)。
    *   如果涉及名人健康事件，请务必匿名化处理或聚焦于普遍的健康教训，严格遵守隐私和伦理规范。
7.  **结论:** 总结要点，并提供可行的建议或启发。
8.  **参考文献 (可选):** 如果引用了具体研究或数据，请酌情添加。
9.  **语言:** 简体中文。
10. **长度:** 大约 800 - 1500 字。

请确保内容可靠、积极，并符合所选风格。`;

        // Try AI Service Manager first for intelligent provider switching
        if (aiServiceManager) {
            console.log('Using AI Service Manager for health content generation');
            
            try {
                const messages = [
                    { role: 'user', content: prompt }
                ];
                
                const result = await aiServiceManager.smartCall(messages, {
                    maxTokens: 4000,
                    temperature: 0.7
                });
                
                const responseTime = Date.now() - startTime;
                
                if (result && result.content) {
                    const generatedContent = result.content;
                    
                    const titleMatch = generatedContent.match(/^# (.+?)(\n|$)/m);
                    const title = titleMatch ? titleMatch[1].trim() : `健康指南: ${randomTopic} - ${new Date().toLocaleDateString('zh-CN')}`;

                    const abstractMatch = generatedContent.match(/^摘要:\s*([\s\S]+?)(\n\n|\n#|\nCategory:|$)/m);
                    const abstract = abstractMatch ? abstractMatch[1].trim().replace(/\n/g, ' ') : '摘要未生成';

                    // Extract Category
                    const categoryMatch = generatedContent.match(/^Category:\s*(.+?)(\n|$)/im);
                    const category = categoryMatch ? categoryMatch[1].trim() : 'General-Health';

                    // Remove metadata (Title, Abstract, Category) from the main content
                    let finalContent = generatedContent;
                    if (categoryMatch) {
                        finalContent = finalContent.replace(categoryMatch[0], '');
                    }
                    if (abstractMatch) {
                        finalContent = finalContent.replace(abstractMatch[0], '');
                    }
                    if (titleMatch) {
                        finalContent = finalContent.replace(titleMatch[0], '');
                    }
                    finalContent = finalContent.trim();

                    console.log(`Health content generated successfully via AI Service Manager in ${responseTime}ms`);

                    return {
                        title,
                        content: finalContent,
                        abstract,
                        category
                    };
                } else {
                    throw new Error('No content returned from AI Service Manager');
                }
            } catch (serviceManagerError) {
                console.warn('AI Service Manager failed for health content, falling back to direct Gemini:', serviceManagerError.message);
                // Fall through to direct Gemini implementation
            }
        }
        
        // Fallback: Direct Gemini API call
        console.log('Using direct Gemini API for health content generation');
        const geminiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_MODEL}:generateContent?key=${GEMINI_API_KEY}`;
        
        const response = await fetch(geminiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{ text: prompt }]
                }]
            })
        });

        const responseTime = Date.now() - startTime;
        const data = await response.json();

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Gemini API error: ${response.status} - ${errorText}`);
        }

        if (data.candidates && data.candidates.length > 0 && data.candidates[0].content && data.candidates[0].content.parts) {
            const generatedContent = data.candidates[0].content.parts[0].text;

            // Record successful AI usage with detailed classification
            const detailedServiceType = 'news_health_crawler';
            const serviceCategory = aiUsageTypes.getServiceCategory(detailedServiceType);
            await aiUsageTracker.recordUsage({
                provider: 'gemini',
                serviceType: detailedServiceType,
                serviceCategory: serviceCategory,
                endpoint: geminiUrl,
                modelName: GEMINI_MODEL,
                success: true,
                responseTimeMs: responseTime,
                cacheHit: false,
                estimatedCostUsd: 0,
                userType: 'system',
                useCase: 'automated_task',
                contentType: 'text',
                language: 'zh',
                featureUsed: `health_content_${category}`
            });

            const titleMatch = generatedContent.match(/^# (.+?)(\n|$)/m);
            const title = titleMatch ? titleMatch[1].trim() : `健康指南: ${randomTopic} - ${new Date().toLocaleDateString('zh-CN')}`;

            const abstractMatch = generatedContent.match(/^摘要:\s*([\s\S]+?)(\n\n|\n#|\nCategory:|$)/m);
            const abstract = abstractMatch ? abstractMatch[1].trim().replace(/\n/g, ' ') : '摘要未生成';

            // Extract Category
            const categoryMatch = generatedContent.match(/^Category:\s*(.+?)(\n|$)/im);
            const category = categoryMatch ? categoryMatch[1].trim() : 'General-Health';

            // Remove metadata (Title, Abstract, Category) from the main content
            let finalContent = generatedContent;
            if (categoryMatch) {
                finalContent = finalContent.replace(categoryMatch[0], '');
            }
            if (abstractMatch) {
                finalContent = finalContent.replace(abstractMatch[0], '');
            }
            if (titleMatch) {
                finalContent = finalContent.replace(titleMatch[0], '');
            }
            finalContent = finalContent.trim();

            console.log(`Health content generated successfully in ${responseTime}ms`);

            return {
                title,
                content: finalContent,
                abstract,
                category
            };
        } else {
            console.error('Invalid response from Gemini API:', JSON.stringify(data, null, 2));
            if (data.error) console.error('Gemini API Error Details:', data.error.message);
            
            // Record failed AI usage with detailed classification
            const detailedServiceType = 'news_health_crawler';
            const serviceCategory = aiUsageTypes.getServiceCategory(detailedServiceType);
            await aiUsageTracker.recordUsage({
                provider: 'gemini',
                serviceType: detailedServiceType,
                serviceCategory: serviceCategory,
                endpoint: geminiUrl,
                modelName: GEMINI_MODEL,
                success: false,
                responseTimeMs: responseTime,
                cacheHit: false,
                userType: 'system',
                useCase: 'automated_task',
                contentType: 'text',
                language: 'zh'
            });

            return null;
        }
    } catch (error) {
        const responseTime = Date.now() - startTime;
        console.error('Error generating health content:', error);

        // Record failed AI usage with detailed classification
        const detailedServiceType = 'news_health_crawler';
        const serviceCategory = aiUsageTypes.getServiceCategory(detailedServiceType);
        await aiUsageTracker.recordUsage({
            provider: 'gemini',
            serviceType: detailedServiceType,
            serviceCategory: serviceCategory,
            endpoint: geminiUrl || `https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_MODEL}:generateContent`,
            modelName: GEMINI_MODEL,
            success: false,
            responseTimeMs: responseTime,
            cacheHit: false,
            userType: 'system',
            useCase: 'automated_task',
            contentType: 'text',
            language: 'zh'
        });

        // Record error details with detailed classification
        await aiUsageTracker.recordError({
            provider: 'gemini',
            serviceType: detailedServiceType,
            errorType: 'API_ERROR',
            errorMessage: error.message,
            errorCode: error.status?.toString(),
            requestSummary: `Health content generation for topic: ${randomTopic}, Type: ${detailedServiceType}`,
            responseSummary: error.stack
        });

        return null;
    }
}

// 主函数：直接生成健康内容并保存
async function processHealthContent() {
    try {
        console.log('Starting health content generation process...');

        const article = await generateHealthContent();

        if (article) {
            await saveArticle(article.title, article.content, article.abstract, article.category);
            console.log('Health article created successfully');
        } else {
            console.log('Failed to generate article');
        }
    } catch (error) {
        console.error('Error in processHealthContent:', error);
    }
}

// 每天早上7点执行生成任务（服务器时区）
cron.schedule('0 7 * * *', () => {
    console.log('Running scheduled health content generator...');
    processHealthContent().catch(err => console.error('Error in scheduled task:', err));
});

// 导出函数以便手动调用
module.exports = {
    processHealthContent
};

// 如果直接执行此文件，立即运行一次
if (require.main === module) {
    processHealthContent().catch(err => console.error('Error running process:', err));
} 