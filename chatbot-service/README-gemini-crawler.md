# 基于Gemini API的健康新闻生成器

这是为MMC Wellness应用开发的健康新闻爬虫的Gemini版本，使用Google Gemini API进行网络搜索和文章生成，无需Custom Search API。

## 相比原版的优势

- **不需要Google Custom Search API**：直接使用Gemini API搜索网络内容
- **节省费用**：Gemini API免费额度每月足够使用
- **更智能的搜索**：Gemini能理解搜索意图并提供更相关的结果
- **更自然的文章总结**：生成的文章质量更高，更自然

## 功能

- 使用Gemini API搜索最新健康资讯(每天1-3条)
- 自动提取文章摘要
- 使用Gemini Pro生成综合性健康文章
- 将生成的文章以Markdown格式保存到文件系统
- 同时将文章存储到数据库中
- 定时任务自动运行，无需人工干预

## 安装

1. 确保已安装Node.js (v14+)和npm

2. 安装依赖:
   ```bash
   cd chatbot-service
   npm install
   ```

3. 配置环境变量:
   - 复制`health-crawler-gemini.env`文件到`.env`或添加到现有的`.env`文件中
   - 添加Gemini API密钥

## 必须的API和配置

### Google Gemini API

1. 访问 https://ai.google.dev/ 并创建账号
2. 获取API密钥
3. 设置环境变量:
   ```
   GEMINI_API_KEY=your_api_key_here
   ```

## 使用方法

### 手动运行爬虫

使用脚本运行:
```bash
./run-gemini-crawler.sh
```

或直接运行Node脚本:
```bash
export GEMINI_API_KEY=your_api_key_here
node health-news-crawler-gemini.js
```

### Docker运行

构建并运行Docker容器:
```bash
docker build -f Dockerfile.gemini -t mmcwebapp-gemini-crawler .
docker run --rm -v $(pwd)/../documents:/usr/src/app/documents -e GEMINI_API_KEY=your_api_key_here -e DB_HOST=${DB_HOST} -e DB_PORT=${DB_PORT} -e DB_USER=${DB_USER} -e DB_PASSWORD=${DB_PASSWORD} -e DB_NAME=${DB_NAME} mmcwebapp-gemini-crawler
```

### 自动调度

爬虫默认配置为每天早上7点自动运行。您可以在`health-news-crawler-gemini.js`文件中修改cron表达式来更改时间：

```javascript
// 例如，配置为每天中午12点运行
cron.schedule('0 12 * * *', () => {
  console.log('Running scheduled health news crawler...');
  processHealthNews();
});
```

## 输出

生成的文章会被保存到:

1. **文件系统**: `../documents/health/health-summary-YYYYMMDD.md`
2. **数据库**: `health_articles`表中

## 如何在应用中使用生成的文章

生成的文章可以通过多种方式集成到MMC Wellness应用中:

1. **网站文章展示**: 自动发布到健康资讯部分
2. **邮件通讯**: 作为每日/每周健康资讯发送给用户
3. **应用内通知**: 向应用用户推送最新健康内容

## 故障排除

如果爬虫运行失败，请检查:

1. Gemini API密钥是否正确设置
2. Gemini API额度是否已用完
3. 网络连接是否正常
4. 数据库连接是否正常

查看日志获取详细错误信息。

## 自定义

您可以根据需要修改以下部分:

1. **健康主题**: 修改`HEALTH_TOPICS`数组添加更多相关主题
2. **搜索提示**: 调整`fetchHealthNews`函数中的搜索提示
3. **文章格式**: 调整`generateArticleSummary`函数中的提示
4. **定时安排**: 修改cron表达式更改运行时间或频率 