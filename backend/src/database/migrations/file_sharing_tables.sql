-- 创建患者文件表
CREATE TABLE IF NOT EXISTS `patient_files` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `demographic_no` int(10) NOT NULL,
  `original_filename` varchar(255) NOT NULL,
  `stored_filename` varchar(255) NOT NULL,
  `file_size` int(10) unsigned NOT NULL,
  `file_type` varchar(100) NOT NULL,
  `upload_date` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `demographic_no_index` (`demographic_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建文件共享表
CREATE TABLE IF NOT EXISTS `shared_files` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `file_id` int(10) unsigned NOT NULL,
  `shared_by` int(10) NOT NULL,
  `shared_to` int(10) NOT NULL,
  `shared_date` datetime NOT NULL,
  PRIMARY KEY (`id`),
  <PERSON>EY `file_id_index` (`file_id`),
  KEY `shared_to_index` (`shared_to`),
  CONSTRAINT `fk_shared_files_file_id` FOREIGN KEY (`file_id`) REFERENCES `patient_files` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4; 