# MMC Wellness Portal - API & Feature Documentation

**Version:** (Specify Date/Version if applicable)

## Overview

This document serves as the primary technical documentation for the MMC Wellness patient portal application. It covers backend API endpoints, key frontend components, and their interactions.

## Backend API Endpoints

Base URL: `https://app-backend.mmcwellness.ca` (or local equivalent)
Authentication: Most endpoints require a `Bearer <token>` in the `Authorization` header.

### Authentication & User (`/api/auth`)

Handles user registration, login, account linking, password management, and profile/membership data retrieval.

- **`POST /register`**: Register new user. Requires prior email verification. Returns user object and token.
- **`POST /login`**: Authenticate user. Returns user object and token.
- **`POST /link`**: Link existing OSCAR patient record to user account (requires email verification).
- **`POST /check-email`**: Check if an email exists in `user_auth` or `demographic` tables.
- **`POST /send-verification-code`**: Send a 6-digit verification code to an email.
- **`POST /verify-email-code`**: Verify the 6-digit code for an email.
- **`GET /verify-email/:token`**: Verify email using a token sent in an email link.
- **`POST /resend-verification`**: Request a new verification email/code to be sent.
- **`POST /link-oscar-patient`**: Create a `user_auth` record linked to an existing OSCAR patient (requires verified email).
- **`POST /forgot-password`**: Initiate password reset process for an email.
- **`GET /verify-reset-token/:token`**: Check if a password reset token is valid.
- **`POST /reset-password`**: Reset password using a valid token.
- **`POST /reset-password-direct`**: Reset password directly (used after email code verification).
- **`GET /profile`**: (Auth Required) Get profile info for the **currently authenticated user** (demographics, family, membership).
- **`GET /profile/:demographicNo`**: (Auth Required) Get **basic demographic info** for a specific patient (self or family member).
- **`GET /membership/:demographicNo`**: (Auth Required) Get **membership details** (current & history) for a specific patient (self or family).
  - *Note:* `current.bill_amount` shows the actual amount paid for that period from `billingmaster`. `current.service_date` is derived from `billingmaster.createdate`.
- **`POST /renew-membership`**: (Auth Required) Renew or purchase membership for the authenticated user.

### Membership Types (`/api/membership`)

Provides information about available membership plans.

- **`GET /types`**: (Auth Required) Get list of allowed membership types and their **standard prices** (from `billingservice`).
- **`GET /types/:code`**: (Auth Required) Get details for a specific membership type code.

### Prescriptions (`/api/prescriptions`)

Manages patient prescription history.

- **`GET /:demographicNo`**: (Auth Required) Get prescription history for a specific patient (self or family).
  - *Note:* `details` field attempts to parse `textView` based on `\n\n`. Provider name is joined from `provider` table.

### Immunizations (`/api/immunizations`)

Manages patient immunization history.

- **`GET /:demographicNo`**: (Auth Required) Get immunization history (from `preventions` table) for a specific patient (self or family).

### Lab Results & Documents (`/api/lab-results`)

Handles fetching and viewing patient documents (lab reports, consults) and measurement data.

- **`GET /reports`**: (Auth Required) Get document list (labs, consults) for the **authenticated user**.
- **`GET /reports/demographic/:demographicNo`**: (Auth Required) Get document list for a **specific patient** (self or family).
- **`GET /report/:reportId`**: (Auth Required) Get the content of a specific document (requires token in query param `?token=...` for direct access/download).
- **`GET /measurements`**: (Auth Required) Get measurement data for the **authenticated user**.
- **`GET /measurements/demographic/:demographicNo`**: (Auth Required) Get measurement data for a **specific patient** (self or family).

### Appointments (`/api/appointments`)

Manages appointment booking, viewing, and status updates.

- **`GET /`**: (Auth Required) Get appointments (upcoming/past) for the **authenticated user**.
- **`GET /:demographicNo`**: (Auth Required) Get appointments for a **specific patient** (self or family).
- **`POST /book`**: (Auth Required) Request a new appointment booking (expects `providerNo`, `appointmentDate`, `appointmentTime`, `reason`). Changed from `POST /`.
- **`PUT /:appointmentId`**: (Auth Required) Update the status/details of an appointment.
- **`DELETE /:appointmentId`**: (Auth Required) Delete an appointment request.
- **`POST /cancel/:id`**: (Auth Required) Cancel an existing appointment (sets status to 'C'). Note: Appointments must be canceled at least two days in advance.
- **`GET /guest/:demographicNo`**: Look up appointment history by demographic number (no auth required).
- **`POST /lookup`**: Look up appointments by last name and phone number (no auth required).

### Providers (`/api/providers`)

Provides information about clinic providers and their availability.

- **`GET /`**: (Auth Required) Get all active providers (doctors and staff).
- **`GET /doctors`**: (Auth Required) Get only active doctors (filtered for booking usually).
- **`GET /:providerNo/availability`**: (Auth Required) Get available time slots for a **specific provider**. Expects `startDate` query parameter (YYYY-MM-DD). Uses provider's schedule templates and checks against existing appointments.
- **`GET /consultation-types`**: (Auth Required) **NEW:** Get a list of distinct, bookable consultation service types (derived from active schedule template names).
- **`GET /availability/by-type`**: (Auth Required) **NEW:** Get available time slots for a **specific consultation type**. Expects `type` (template name) and `startDate` query parameters. Aggregates availability across all providers offering that service type on that date. Returns slots including `provider_no` and `providerName`.

### Medical Records (`/api/medical-records`)

Provides access to specific parts of the patient's medical record.

- **`GET /:demographicNo`**: (Auth Required) Get basic patient info and case management notes (`casemgmt_note`) for a specific patient.

### Store Management (`/api/store`)

Handles e-commerce functionality including products, orders, and payments.

#### Public Routes (No Authentication)
- **`GET /products`**: Get all available products with optional filters.
- **`GET /products/:productId`**: Get detailed information for a specific product.
- **`GET /categories`**: Get list of product categories.
- **`POST /webhook/stripe`**: Stripe payment webhook handler (raw body required).

#### Authenticated Routes
- **`POST /orders`**: (Auth Required) Create a new order.
- **`GET /orders`**: (Auth Required) Get order history for authenticated user.
- **`GET /orders/:orderId`**: (Auth Required) Get details for a specific order.
- **`POST /payment/create-intent`**: (Auth Required) Create Stripe payment intent.

#### Admin Routes
- **`GET /admin/orders`**: (Auth Required) Get all orders (admin only).
- **`PUT /admin/orders/:orderId/status`**: (Auth Required) Update order status (admin only).
- **`POST /products`**: (Auth Required) Create new product (admin only).
- **`PUT /products/:productId`**: (Auth Required) Update existing product (admin only).
- **`DELETE /products/:productId`**: (Auth Required) Delete product (admin only).

### File Management (`/api/files`)

Handles file upload, sharing, and management functionality.

- **`GET /`**: (Auth Required) Get user's uploaded files list.
- **`GET /shared`**: (Auth Required) Get files shared with the user.
- **`POST /upload`**: (Auth Required) Upload a file (max 10MB).
- **`GET /:fileId/download`**: (Auth Required) Download a specific file.
- **`DELETE /:fileId`**: (Auth Required) Delete a file.
- **`POST /:fileId/share`**: (Auth Required) Share a file with other users.
- **`GET /:fileId/shares`**: (Auth Required) Get sharing information for a file.
- **`POST /upload/product-image`**: (Auth Required) Upload product image for store.
- **`DELETE /product-image/:filename`**: (Auth Required) Delete product image.

### Health Tips (`/api/tips`)

Manages health tips and educational content.

#### Public Routes
- **`GET /categories`**: Get list of tip categories (no auth required).
- **`GET /assets/:assetPath`**: Get static assets for tips (no auth required).

#### Authenticated Routes
- **`GET /`**: (Auth Required) Get list of all health tips.
- **`GET /category/:category`**: (Auth Required) Get tips filtered by category.
- **`GET /:filename`**: (Auth Required) Get content of a specific tip.

### Documents (`/api/documents`)

Handles document management and retrieval from OSCAR system.

- **`GET /:demographicNo`**: (Auth Required) Get all documents for a specific patient. Returns document list with ID, title, type, content type, dates, and filename.

### YouTube Videos (`/api/youtube`)

Manages YouTube video content for the platform.

#### Public Routes
- **`GET /videos`**: Get all YouTube videos (no auth required).
- **`GET /videos/category/:category`**: Get videos filtered by category (no auth required).

#### Admin Routes
- **`GET /videos/admin`**: (Auth Required, Admin Only) Get all videos with admin privileges.
- **`POST /videos`**: (Auth Required, Admin Only) Add a new video.
- **`PUT /videos/:id`**: (Auth Required, Admin Only) Update existing video.
- **`DELETE /videos/:id`**: (Auth Required, Admin Only) Delete a video.

### Demographics (`/api/demographics`)

Handles demographic information and avatar management.

- **`POST /:demographic_no/avatar`**: Upload/update avatar for a specific demographic record.
- **`GET /:demographic_no/avatar`**: Get avatar URL for a specific demographic record.
- **`DELETE /:demographic_no/avatar`**: Delete avatar for a specific demographic record.

### Dietician Services (`/api/formDietician`)

Manages dietician-related services and comments.

- **`GET /:demographic_no`**: (Auth Required) Get dietician comments for a specific patient.
- **`GET /summary/:commentId`**: (Auth Required) Get AI summary for a specific dietician comment.

### Articles (`/api/articles`)

Manages health and medical articles.

- **`GET /`**: (Auth Required) List all available articles.
- **`GET /categories`**: (Auth Required) Get list of article categories.
- **`GET /:filename`**: (Auth Required) Get content of a specific article.
- **`GET /assets/*`**: (Auth Required) Get article-related assets (images, etc.).

### Consultations (`/api/consultations`)

Handles consultation requests and management.

- **`GET /:demographicNo`**: (Auth Required) Get consultation history for a specific patient.

### Referral System (`/api/referrals` - via main router)

Manages referral codes and tracking.

- **`GET /code`**: (Auth Required) Get current user's referral code.
- **`GET /code/:demographicNo`**: (Auth Required) Get specific user's referral code (admin only).
- **`GET /list`**: (Auth Required) Get current user's referrals.
- **`GET /list/:demographicNo`**: (Auth Required) Get specific user's referrals (admin only).
- **`GET /validate/:code`**: Validate a referral code (no auth required).

### User Management (`/api/users` - via main router)

Handles user-specific operations like avatar management.

- **`POST /avatar`**: (Auth Required) Upload/update user avatar image (max 5MB).

### Relationships (`/api/relationships`)

Manages family member relationships and permissions.

- **`POST /switch-view/initiate`**: (Auth Required) Initiate the family member view switch verification process.
- **`POST /switch-view/verify`**: (Auth Required) Verify the switch view code to gain access to family member data.
- **`GET /check-access/:targetDemographicNo`**: (Auth Required) Check if access to a family member was recently granted (within 30 minutes).
- **`GET /family`**: (Auth Required) Get the list of family members for the authenticated user.

### Health Check & Test Endpoints

- **`GET /health`**: (No Auth) Simple health check endpoint. Returns `{ status: 'healthy' }`.
- **`GET /test-files`**: (No Auth) Test endpoint to check local document files.
- **`GET /test/verification-codes`**: (No Auth) Development endpoint to view verification codes.
- **`GET /test/verified-emails`**: (No Auth) Development endpoint to view verified emails.

### Public Routes

- **`GET /api/public/categories`**: Get tip categories without authentication.

## Frontend Features & Notes

Key components and logic in the `frontend/src/components` directory.

- **`App.js`**: Main application component managing routing.
  - **Default Route**: Authenticated users are redirected from `/` to `/profile`.
- **`ViewContext.js`**: Manages global state: `loggedInUser`, `viewingDemographicNo` (currently viewed patient), `familyMembers`, `isLoading`, `error`. Provides `setViewAs` function to switch between viewing self and family members. Fetches initial `/api/auth/profile` data.
- **`Header.js`**: Top navigation bar. Content defined in `navItems` array. Includes mobile drawer and user profile menu (logout).
- **`Profile.js` (`/profile`)**: Displays demographic info for `viewingDemographicNo`. Allows switching view via family member cards. Initial load depends on `ViewContext` and `/api/auth/profile/:demographicNo`. May show "Retry Initial Load" on context fetch errors.
- **`Membership.js` (`/membership`)**: Displays membership status. Shows viewed user first, then family. Uses `createdate` for start date. Shows actual paid amount (`billingmaster.bill_amount`) on card, standard prices (`billingservice.value`) in info table. *Contains temporary simulation code for HUBERY SONG's history.*
- **`Appointments.js` (`/appointments`)**: Displays upcoming/past appointments for `viewingDemographicNo`.
- **`BookingPage.js` (`/booking`)**: **UPDATED:** Allows users to book appointments either by selecting a specific Doctor first OR by selecting a Service Type first. Fetches availability accordingly and handles the booking submission.
- **`LabReports.js` (`/lab-reports`)**: Displays documents (labs, consults) and measurements for `viewingDemographicNo`.
- **`Prescriptions.js` (`/prescriptions`)**: Displays prescription history for `viewingDemographicNo`. Attempts to parse medication details. Shows provider name.
- **`Immunizations.js` (`/immunizations`)**: Displays immunization history for `viewingDemographicNo`.
- **`Store.js` (`/store`)**: E-commerce interface for browsing and purchasing products.
- **`StoreAdmin.js` (`/store-admin`)**: Administrative interface for managing products and orders.
- **`HealthTips.js` (`/health-tips`)**: Displays health tips and educational content with category filtering.
- **`YouTubeVideos.js` (`/youtube`)**: Manages YouTube video content with categorization.
- **`Chatbot.js` (`/chat`)**: AI-powered chatbot interface for health consultations.
- **`Settings.js` (`/settings`)**: User settings and preferences management.
- **Login/Register/Password**: Standard authentication flow components.

## General Notes

- **Permissions**: Backend endpoints generally check if the authenticated user has permission to view data for the requested `demographicNo` (either self or linked family member via `relationships` table).
- **Error Handling**: Standard HTTP status codes (400, 401, 403, 404, 500) are used with JSON error messages (e.g., `{ "success": false, "message": "Error details" }`).
- **Database Tables**: Key tables involved include `user_auth`, `demographic`, `billingmaster`, `billingservice`, `prescription`, `preventions`, `document`, `ctl_document`, `measurements`, `appointment`, `provider`, `relationships`, `casemgmt_note`, `store_products`, `store_categories`, `store_orders`, `files`, `referrals`. 
- **File Upload**: Various endpoints support file uploads with different size limits and file type restrictions.
- **Static File Serving**: Product images are served from `/uploads` directory.
- **Cron Jobs**: Backend includes automated tasks for daily health tip generation.

## Route Mounting Structure

The application uses a dual routing structure:
1. **Direct mounting** in `index.js` for core medical functionality
2. **API router mounting** at `/api` for additional features like store, files, tips, documents, referrals, etc.

This ensures both legacy compatibility and organized feature separation. 

## API Endpoints Summary

The MMC Wellness Portal provides a comprehensive REST API with **over 70 endpoints** organized into the following categories:

### Core Medical Features (11 categories)
1. **Authentication & User Management** (14 endpoints) - Login, registration, password reset, profile
2. **Medical Records** (1 endpoint) - Patient medical record access  
3. **Prescriptions** (1 endpoint) - Prescription history with AI explanations
4. **Immunizations** (1 endpoint) - Vaccination records with AI explanations
5. **Lab Results & Documents** (5 endpoints) - Lab reports, measurements, document access
6. **Appointments** (7 endpoints) - Booking, viewing, cancellation with guest lookup
7. **Providers** (5 endpoints) - Doctor information and availability scheduling
8. **Membership** (2 endpoints) - Membership plans and renewal
9. **Documents** (1 endpoint) - OSCAR document retrieval
10. **Consultations** (1 endpoint) - Consultation request history
11. **Relationships** (4 endpoints) - Family member management and access control

### Extended Platform Features (7 categories)
1. **E-commerce Store** (11 endpoints) - Products, orders, payments, admin management
2. **File Management** (8 endpoints) - Upload, sharing, download with product images
3. **Health Tips** (5 endpoints) - Educational content with category filtering
4. **YouTube Videos** (6 endpoints) - Video content management with admin controls
5. **Articles** (4 endpoints) - Health articles and educational resources
6. **Referral System** (5 endpoints) - Referral code management and tracking
7. **Demographics** (3 endpoints) - Patient demographic data and avatar management

### Support & Development (5 endpoints)
- Health check, file testing, and development verification endpoints

### Access Control
- **Public Access**: 8 endpoints (health check, store products, YouTube videos, etc.)
- **Authenticated Access**: 60+ endpoints requiring valid Bearer token
- **Admin Only**: 8 endpoints for administrative functions
- **Family Access**: Special permission system for family member data access

### Technical Features
- **File Upload Support**: Multiple endpoints with varying size limits (5MB-10MB)
- **Payment Integration**: Stripe webhook and payment intent creation
- **AI Integration**: Prescription, immunization, and dietician comment explanations
- **Real-time Features**: Appointment availability checking
- **Static File Serving**: Product images and document assets
- **Automated Tasks**: Cron jobs for daily health tip generation

This comprehensive API architecture supports both the core medical management functionality and extended wellness platform features, providing a complete digital health ecosystem. 