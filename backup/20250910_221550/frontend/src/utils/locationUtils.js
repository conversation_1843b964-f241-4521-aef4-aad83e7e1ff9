// 地址显示工具函数
export const getLocationDisplay = (location, t) => {
    if (location === 'In Person' || location === 'In Clinic') {
        return '#130 8780 Blundell Rd, Richmond, BC. (Midtown Medical Clinic)';
    }
    if (location === 'online/phone') {
        return t('online_phone') || 'By phone';
    }
    return location;
};

// 地址选项配置
export const locationOptions = [
    {
        value: 'In Person',
        labelKey: 'in_person_clinic'
    },
    {
        value: 'online/phone', 
        labelKey: 'online_phone'
    }
];
