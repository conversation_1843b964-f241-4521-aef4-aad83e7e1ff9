# 🚀 部署文档

系统部署、安装和配置相关文档

## 📋 文档列表

### Docker容器化部署
- [**Docker脚本使用指南**](./DOCKER_SCRIPTS_README.md) - Docker维护脚本详细使用说明
  - 快速重建脚本
  - 高级重建选项
  - 完整维护脚本
  - 故障排除指南

### 功能模块部署
- [**在线商店设置指南**](./STORE_SETUP.md) - 电商功能部署配置
  - 商店初始化
  - 支付系统配置
  - 产品管理设置

## 🔧 部署流程

### 快速部署
1. 确保Docker和Docker Compose已安装
2. 配置环境变量文件 `.env`
3. 运行快速重建脚本: `./quick_rebuild.sh`
4. 验证服务运行状态

### 生产部署
1. 参考 [Docker脚本使用指南](./DOCKER_SCRIPTS_README.md)
2. 使用 `./rebuild_docker.sh -c` 进行完整重建
3. 配置商店功能 (如需要)
4. 设置监控和日志

## 🔗 相关链接

- [返回文档中心](../)
- [开发文档](../development/)
- [运维文档](../operations/)
- [API文档](../api/)

---

*最后更新: 2025-05-28* 