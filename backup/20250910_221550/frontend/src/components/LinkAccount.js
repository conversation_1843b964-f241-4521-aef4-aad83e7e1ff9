import React, { useState, useEffect } from 'react';
import { API_URL } from '../utils/env';
import { useLocation, useNavigate } from 'react-router-dom';
import {
    Container,
    Paper,
    TextField,
    Button,
    Typography,
    Link,
    Box,
    Alert,
    CircularProgress,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogContentText,
    DialogActions,
    RadioGroup,
    Radio,
    FormControlLabel,
} from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledPaper = styled(Paper)(({ theme }) => ({
    marginTop: theme.spacing(8),
    padding: theme.spacing(4),
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
}));

const StyledForm = styled('form')(({ theme }) => ({
    width: '100%',
    marginTop: theme.spacing(1),
}));

const StyledButton = styled(Button)(({ theme }) => ({
    margin: theme.spacing(3, 0, 2),
}));

const LinkAccount = () => {
    const [formData, setFormData] = useState({
        email: '',
        password: '',
        confirmPassword: '',
    });
    const [error, setError] = useState('');
    const [loading, setLoading] = useState(false);
    const [patientRecords, setPatientRecords] = useState([]);
    const [selectedPatientId, setSelectedPatientId] = useState('');
    const [multipleRecordsStep, setMultipleRecordsStep] = useState(false);
    const [success, setSuccess] = useState(false);

    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const searchParams = new URLSearchParams(location.search);
        const email = searchParams.get('email');
        if (email) {
            setFormData(prev => ({ ...prev, email }));
            checkExistingPatientRecords(email); // Check only once on load
        } else {
            navigate('/email-verification?mode=link');
        }
    }, [location, navigate]); // Removed dependency on checkExistingPatientRecords

    const checkExistingPatientRecords = async (email) => {
        setLoading(true);
        try {
            const apiUrl = API_URL || 'https://app-backend.mmcwellness.ca';
            const response = await fetch(`${apiUrl}/api/auth/check-email`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email }),
            });
            const data = await response.json();
            if (response.ok && data.demographics && data.demographics.length > 0) {
                if (data.demographics.length > 1) {
                    console.log('LinkAccount: Multiple records found, showing selection.');
                    setPatientRecords(data.demographics);
                    setMultipleRecordsStep(true); // Show selection step first
                } else {
                    // Only one record found, store its ID automatically
                    console.log('LinkAccount: Single record found, storing demographic_no.');
                    setSelectedPatientId(data.demographics[0].demographic_no.toString());
                    setMultipleRecordsStep(false); // Ensure selection step is hidden
                }
            } else if (!response.ok) {
                throw new Error(data.message || 'Failed to check email');
            } else {
                // No demographics found for this email
                setError('No patient record found linked to this verified email. Please contact the clinic.');
                setMultipleRecordsStep(false); // Hide selection step
            }
        } catch (err) {
            console.error('Error checking patient records:', err);
            setError(err.message || 'Error retrieving patient records.');
        } finally {
            setLoading(false);
        }
    };

    const handleChange = (e) => {
        setFormData({ ...formData, [e.target.name]: e.target.value });
    };

    const handlePatientSelect = (e) => {
        setSelectedPatientId(e.target.value);
    };

    // This function now just hides the selection step to reveal the password form
    const handleProceedToPassword = () => {
        if (!selectedPatientId) {
            setError('Please select a patient record to link.');
            return;
        }
        setError(''); // Clear selection error
        setMultipleRecordsStep(false);
        console.log('LinkAccount: Proceeding to password entry for selected patient:', selectedPatientId);
    };

    // This is the primary submit action (for the password form)
    const handleSubmit = async (e) => {
        e.preventDefault();
        setError('');

        if (formData.password !== formData.confirmPassword) {
            setError('Passwords do not match');
            return;
        }
        if (formData.password.length < 8) {
            setError('Password must be at least 8 characters');
            return;
        }
        // Ensure a demographic number is associated (either single auto-selected or chosen from multiple)
        if (!selectedPatientId) {
            setError('Could not determine the patient record to link. Please try the email verification again or contact support.');
            return;
        }

        setLoading(true);

        try {
            const apiUrl = API_URL || 'https://app-backend.mmcwellness.ca';
            // REMOVED the redundant check-email call here

            console.log(`LinkAccount: Calling link-oscar-patient for email: ${formData.email}, demoNo: ${selectedPatientId}`);
            const response = await fetch(`${apiUrl}/api/auth/link-oscar-patient`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    email: formData.email,
                    password: formData.password,
                    demographicNo: selectedPatientId, // Use the stored ID
                }),
            });

            const data = await response.json();

            if (response.ok) {
                console.log('LinkAccount: Link successful. Storing token and navigating.');
                localStorage.setItem('token', data.token);
                localStorage.setItem('user', JSON.stringify(data.user));
                setSuccess(true);
                // Navigate immediately after setting success state
                navigate('/profile');
                // REMOVED setTimeout
            } else {
                throw new Error(data.message || 'Failed to link account');
            }
        } catch (err) {
            console.error('Link account error:', err);
            setError(err.message || 'An error occurred. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    // REMOVED handleContinueWithSelectedPatient function as its logic is merged/changed

    // --- Render Logic ---
    if (loading && !multipleRecordsStep) { // Show general loading only if not in selection step
        return (
            <Container component="main" maxWidth="xs">
                <StyledPaper elevation={3} sx={{ p: 5, display: 'flex', justifyContent: 'center' }}>
                    <CircularProgress />
                </StyledPaper>
            </Container>
        );
    }

    if (success) {
        // Show success message briefly before navigation (navigation happens immediately in handleSubmit)
        return (
            <Container component="main" maxWidth="xs">
                <StyledPaper elevation={3}>
                    <Typography component="h1" variant="h5">Account Linked Successfully</Typography>
                    <Alert severity="success" sx={{ mt: 2, width: '100%' }}>
                        Your account has been successfully linked. Redirecting...
                    </Alert>
                    <CircularProgress sx={{ mt: 2 }} />
                </StyledPaper>
            </Container>
        );
    }

    // Show patient selection step if needed
    if (multipleRecordsStep) {
        return (
            <Container component="main" maxWidth="xs">
                <StyledPaper elevation={3}>
                    <Typography component="h1" variant="h5">Select Your Patient Record</Typography>
                    <Typography variant="subtitle1" sx={{ mt: 2, mb: 2, textAlign: 'center' }}>
                        Multiple patient records found with this email. Please select your record.
                    </Typography>
                    {error && <Alert severity="error" sx={{ width: '100%', mb: 2 }}>{error}</Alert>}
                    <RadioGroup
                        aria-label="patient-record"
                        name="patient-record"
                        value={selectedPatientId}
                        onChange={handlePatientSelect}
                        sx={{ width: '100%', mt: 2 }}
                    >
                        {patientRecords.map((record) => (
                            <FormControlLabel
                                key={record.demographic_no}
                                value={record.demographic_no.toString()}
                                control={<Radio />}
                                label={`${record.first_name} ${record.last_name} (${record.address || 'No address'})`}
                                sx={{ mb: 1 }}
                            />
                        ))}
                    </RadioGroup>
                    <Box sx={{ display: 'flex', justifyContent: 'flex-end', width: '100%', mt: 3 }}>
                        {/* Changed Button to call handleProceedToPassword */}
                        <Button
                            variant="contained"
                            color="primary"
                            onClick={handleProceedToPassword}
                            disabled={!selectedPatientId}
                        >
                            Proceed to Set Password
                        </Button>
                    </Box>
                </StyledPaper>
            </Container>
        );
    }

    // Show password form (default view after email check or after patient selection)
    return (
        <Container component="main" maxWidth="xs">
            <StyledPaper elevation={3}>
                <Typography component="h1" variant="h5">Link Your Patient Account</Typography>
                <Typography variant="subtitle1" sx={{ mt: 2, mb: 2, textAlign: 'center' }}>
                    Create a password to access your patient record
                </Typography>
                {error && <Alert severity="error" sx={{ width: '100%', mb: 2 }}>{error}</Alert>}
                <StyledForm onSubmit={handleSubmit}>
                    <TextField
                        variant="outlined"
                        margin="normal"
                        required
                        fullWidth
                        id="email"
                        label="Email Address"
                        name="email"
                        value={formData.email}
                        disabled // 邮箱已经过验证，不允许修改
                    />
                    <TextField
                        variant="outlined"
                        margin="normal"
                        required
                        fullWidth
                        name="password"
                        label="Password"
                        type="password"
                        id="password"
                        value={formData.password}
                        onChange={handleChange}
                        autoComplete="new-password"
                    />
                    <TextField
                        variant="outlined"
                        margin="normal"
                        required
                        fullWidth
                        name="confirmPassword"
                        label="Confirm Password"
                        type="password"
                        id="confirmPassword"
                        value={formData.confirmPassword}
                        onChange={handleChange}
                        autoComplete="new-password"
                    />
                    <StyledButton
                        type="submit"
                        fullWidth
                        variant="contained"
                        color="primary"
                        disabled={loading}
                    >
                        {loading ? <CircularProgress size={24} /> : 'Create Account'}
                    </StyledButton>
                </StyledForm>
                <Box mt={2}>
                    <Typography variant="body2" align="center">
                        Already have an account?{' '}
                        <Link href="/login" variant="body2">
                            Sign in
                        </Link>
                    </Typography>
                </Box>
            </StyledPaper>
        </Container>
    );
};

export default LinkAccount; 