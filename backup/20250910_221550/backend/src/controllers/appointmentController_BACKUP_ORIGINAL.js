const pool = require('../config/database');
const { createLogger } = require('../utils/logger');
const { AppError, ValidationError, NotFoundError } = require('../middleware/errorHandler');
const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc');
const timezone = require('dayjs/plugin/timezone');
const customParseFormat = require('dayjs/plugin/customParseFormat');

// 配置 dayjs
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(customParseFormat);

// 设置默认时区为温哥华
const TIMEZONE = 'America/Vancouver';

// 创建日志记录器
const logger = createLogger('AppointmentController');

// Helper to handle errors consistently for appointment operations
const handleAppointmentError = (res, error, message = 'Server error') => {
    logger.error('Appointment operation error', {
        message: error.message,
        stack: error.stack
    });
    
    if (error instanceof AppError) {
        return res.status(error.statusCode).json({
            success: false,
            error: {
                message: error.message,
                code: error.code
            }
        });
    }
    
    res.status(500).json({
        success: false,
        error: {
            message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        }
    });
};

/**
 * Get user's appointments
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} JSON response with upcoming and past appointments
 */
exports.getUserAppointments = async (req, res) => {
    try {
        const userId = req.user.id;
        logger.info('Getting user appointments', { userId });

        // First get the demographic_no from the user_auth table
        const userQuery = `
            SELECT demographic_no
            FROM user_auth
            WHERE id = ?
        `;

        const [userResult] = await pool.query(userQuery, [userId]);

        if (!userResult || userResult.length === 0 || !userResult[0].demographic_no) {
            throw new NotFoundError('User not linked to an Oscar account');
        }

        const demographicNo = userResult[0].demographic_no;

        // Get appointment data from the appointment table
        const appointmentQuery = `
            SELECT
                a.appointment_no,
                a.appointment_date,
                a.start_time,
                a.end_time,
                a.reason,
                a.status,
                a.location,
                a.notes,
                p.first_name as provider_first_name,
                p.last_name as provider_last_name,
                p.provider_type
            FROM appointment a
            LEFT JOIN provider p ON a.provider_no = p.provider_no
            WHERE a.demographic_no = ?
            ORDER BY a.appointment_date DESC, a.start_time DESC
        `;

        const [appointments] = await pool.query(appointmentQuery, [demographicNo]);

        // Format and organize appointments
        const currentDate = dayjs().tz(TIMEZONE).startOf('day');
        logger.debug('Current date', { currentDate: currentDate.format('YYYY-MM-DD') });

        const formattedAppointments = appointments.map(appointment => {
            const appointmentDateStr = appointment.appointment_date.toISOString().split('T')[0];
            const appointmentDate = dayjs.tz(appointmentDateStr, TIMEZONE);

            // Format time from time format "HH:MM:SS" to "HH:MM AM/PM"
            let timeStr = appointment.start_time.toString();
            let [hours, minutes] = timeStr.split(':').map(Number);

            const ampm = hours >= 12 ? 'PM' : 'AM';
            hours = hours % 12 || 12;
            const formattedTime = `${hours}:${minutes.toString().padStart(2, '0')} ${ampm}`;

            return {
                id: appointment.appointment_no,
                date: appointmentDateStr,
                time: formattedTime,
                doctor: `${appointment.provider_type === 'doctor' ? 'Dr. ' : ''}${appointment.provider_first_name} ${appointment.provider_last_name}`,
                location: appointment.location || 'MMC Wellness Clinic',
                status: getAppointmentStatus(appointment.status, appointmentDate, currentDate),
                reason: appointment.reason || 'General Consultation',
                notes: appointment.notes || ''
            };
        });

        // Separate into upcoming and past appointments
        const upcoming = formattedAppointments.filter(appt => {
            const apptDate = dayjs.tz(appt.date, TIMEZONE);
            return apptDate.isAfter(currentDate) || apptDate.isSame(currentDate, 'day') && appt.status !== 'cancelled';
        });

        const past = formattedAppointments.filter(appt => {
            const apptDate = dayjs.tz(appt.date, TIMEZONE);
            return apptDate.isBefore(currentDate) && !apptDate.isSame(currentDate, 'day') || appt.status === 'cancelled';
        });

        logger.info('Retrieved appointments', { 
            upcoming: upcoming.length, 
            past: past.length,
            total: appointments.length 
        });

        return res.json({
            success: true,
            data: {
                upcoming,
                past
            }
        });

    } catch (error) {
        handleAppointmentError(res, error, 'Failed to retrieve appointments');
    }
};

/**
 * Create a new appointment request
 * @param {Object} req - Request object with appointment details
 * @param {Object} res - Response object
 * @returns {Object} JSON response with created appointment details
 */
exports.createAppointmentRequest = async (req, res) => {
    try {
        const userId = req.user.id;
        const { providerId, date, time, reason } = req.body;

        // Validate required fields
        if (!providerId || !date || !time || !reason) {
            throw new ValidationError('Please provide all required appointment details', {
                required: ['providerId', 'date', 'time', 'reason']
            });
        }

        logger.info('Creating appointment request', { 
            userId, 
            providerId, 
            date, 
            time, 
            reason 
        });

        // Get demographic_no from user_auth
        const userQuery = `SELECT demographic_no FROM user_auth WHERE id = ?`;
        const [userResult] = await pool.query(userQuery, [userId]);

        if (!userResult || userResult.length === 0 || !userResult[0].demographic_no) {
            throw new NotFoundError('User not linked to an Oscar account');
        }

        const demographicNo = userResult[0].demographic_no;

        // Parse time and calculate end time
        const formattedTime = convertTimeToOscarFormat(time);
        const endTime = calculateEndTimeSync(formattedTime);

        const conn = await pool.getConnection();
        try {
            // Insert appointment request
            const appointmentQuery = `
                INSERT INTO appointment (
                    provider_no,
                    demographic_no,
                    appointment_date,
                    start_time,
                    end_time,
                    reason,
                    status,
                    notes,
                    location,
                    creator_security_no,
                    booking_source
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;

            const [result] = await conn.query(appointmentQuery, [
                providerId,
                demographicNo,
                date,
                formattedTime,
                endTime,
                reason,
                'pending',
                'Created via patient portal',
                'MMC Wellness Clinic',
                999998,
                'patient_portal'
            ]);

            logger.info('Appointment request created', { appointmentId: result.insertId });

            res.status(201).json({
                success: true,
                message: 'Appointment request submitted successfully',
                data: {
                    appointmentId: result.insertId,
                    status: 'pending'
                }
            });

        } finally {
            conn.release();
        }

    } catch (error) {
        handleAppointmentError(res, error, 'Failed to create appointment request');
    }
};

/**
 * Update appointment status
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} JSON response with updated appointment
 */
exports.updateAppointmentStatus = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        const { status } = req.body;
        const userId = req.user.id;

        if (!appointmentId || !status) {
            throw new ValidationError('Appointment ID and status are required');
        }

        logger.info('Updating appointment status', { appointmentId, status, userId });

        // Verify user owns this appointment
        const verifyQuery = `
            SELECT a.appointment_no, a.demographic_no, u.demographic_no as user_demo_no
            FROM appointment a
            JOIN user_auth u ON a.demographic_no = u.demographic_no
            WHERE a.appointment_no = ? AND u.id = ?
        `;

        const [verifyResult] = await pool.query(verifyQuery, [appointmentId, userId]);

        if (!verifyResult || verifyResult.length === 0) {
            throw new NotFoundError('Appointment not found or access denied');
        }

        // Update appointment status
        const updateQuery = `
            UPDATE appointment 
            SET status = ?, last_update_date = NOW() 
            WHERE appointment_no = ?
        `;

        await pool.query(updateQuery, [mapStatusToOscarCode(status), appointmentId]);

        logger.info('Appointment status updated', { appointmentId, newStatus: status });

        res.json({
            success: true,
            message: 'Appointment status updated successfully',
            data: {
                appointmentId,
                status
            }
        });

    } catch (error) {
        handleAppointmentError(res, error, 'Failed to update appointment status');
    }
};

/**
 * Delete appointment request
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} JSON response
 */
exports.deleteAppointmentRequest = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        const userId = req.user.id;

        if (!appointmentId) {
            throw new ValidationError('Appointment ID is required');
        }

        logger.info('Deleting appointment request', { appointmentId, userId });

        // Verify user owns this appointment
        const verifyQuery = `
            SELECT a.appointment_no 
            FROM appointment a
            JOIN user_auth u ON a.demographic_no = u.demographic_no
            WHERE a.appointment_no = ? AND u.id = ?
        `;

        const [verifyResult] = await pool.query(verifyQuery, [appointmentId, userId]);

        if (!verifyResult || verifyResult.length === 0) {
            throw new NotFoundError('Appointment not found or access denied');
        }

        // Delete appointment
        const deleteQuery = `DELETE FROM appointment WHERE appointment_no = ?`;
        await pool.query(deleteQuery, [appointmentId]);

        logger.info('Appointment deleted', { appointmentId });

        res.json({
            success: true,
            message: 'Appointment request deleted successfully'
        });

    } catch (error) {
        handleAppointmentError(res, error, 'Failed to delete appointment request');
    }
};

/**
 * Get doctor availability (simplified version)
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} JSON response with availability
 */
exports.getDoctorAvailability = async (req, res) => {
    try {
        const { providerId, date } = req.query;

        if (!providerId || !date) {
            throw new ValidationError('Provider ID and date are required');
        }

        logger.info('Getting doctor availability', { providerId, date });

        // Get existing appointments for the provider on the given date
        const appointmentQuery = `
            SELECT start_time, end_time
            FROM appointment
            WHERE provider_no = ? AND appointment_date = ? AND status != 'cancelled'
            ORDER BY start_time
        `;

        const [existingAppointments] = await pool.query(appointmentQuery, [providerId, date]);

        // Generate available time slots (simplified logic)
        const availableSlots = generateAvailableTimeSlots(existingAppointments);

        res.json({
            success: true,
            data: {
                date,
                providerId,
                availableSlots
            }
        });

    } catch (error) {
        handleAppointmentError(res, error, 'Failed to get doctor availability');
    }
};

// Helper functions
function getAppointmentStatus(dbStatus, appointmentDate, currentDate) {
    if (dbStatus === 'cancelled' || dbStatus === 'C') {
        return 'cancelled';
    }
    
    if (appointmentDate.isBefore(currentDate)) {
        return 'completed';
    }
    
    if (appointmentDate.isSame(currentDate, 'day')) {
        return 'today';
    }
    
    return 'scheduled';
}

function convertTimeToOscarFormat(timeStr) {
    const [time, period] = timeStr.split(' ');
    let [hours, minutes] = time.split(':').map(Number);
    
    if (period === 'PM' && hours !== 12) hours += 12;
    if (period === 'AM' && hours === 12) hours = 0;
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00`;
}

function calculateEndTimeSync(startTime) {
    const [hours, minutes] = startTime.split(':').map(Number);
    const endMinutes = minutes + 30; // Default 30-minute appointments
    const endHours = hours + Math.floor(endMinutes / 60);
    const finalMinutes = endMinutes % 60;
    
    return `${endHours.toString().padStart(2, '0')}:${finalMinutes.toString().padStart(2, '0')}:00`;
}

function mapStatusToOscarCode(status) {
    const statusMap = {
        'pending': 'pending',
        'confirmed': 'confirmed',
        'cancelled': 'C',
        'completed': 'completed'
    };
    
    return statusMap[status] || status;
}

function generateAvailableTimeSlots(existingAppointments) {
    // Simplified time slot generation
    const slots = [];
    const startHour = 9; // 9 AM
    const endHour = 17; // 5 PM
    
    for (let hour = startHour; hour < endHour; hour++) {
        for (let minute = 0; minute < 60; minute += 30) {
            const timeSlot = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:00`;
            
            // Check if this slot conflicts with existing appointments
            const isAvailable = !existingAppointments.some(appt => {
                const startTime = appt.start_time;
                const endTime = appt.end_time;
                return timeSlot >= startTime && timeSlot < endTime;
            });
            
            if (isAvailable) {
                const displayTime = formatTimeForDisplay(timeSlot);
                slots.push({
                    time: timeSlot,
                    displayTime,
                    available: true
                });
            }
        }
    }
    
    return slots;
}

function formatTimeForDisplay(timeStr) {
    const [hours, minutes] = timeStr.split(':').map(Number);
    const ampm = hours >= 12 ? 'PM' : 'AM';
    const displayHours = hours % 12 || 12;
    return `${displayHours}:${minutes.toString().padStart(2, '0')} ${ampm}`;
}

module.exports = exports;