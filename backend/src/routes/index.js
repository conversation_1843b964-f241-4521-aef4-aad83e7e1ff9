const express = require('express');
const authRoutes = require('./auth');
const relationshipRoutes = require('./relationshipRoutes');
const appointmentRoutes = require('./appointmentRoutes');
const prescriptionRoutes = require('./prescriptionRoutes');
const consultationRoutes = require('./consultationRoutes'); // Import consultation routes
const demographicRoutes = require('./demographicRoutes'); // Import demographic routes
const formDieticianRoutes = require('./formDieticianRoutes'); // Import formDietician routes
const youtubeRoutes = require('./youtubeRoutes'); // Import youtube routes
const storeRoutes = require('./storeRoutes'); // Import store routes
const healthGuideRoutes = require('./healthGuideRoutes'); // Import health guide routes
const aiMonitorRoutes = require('./ai-monitor'); // Import AI monitoring routes
const fs = require('fs');
const path = require('path');

const router = express.Router();

router.use('/auth', authRoutes);
router.use('/relationships', relationshipRoutes);
router.use('/appointments', appointmentRoutes);
router.use('/prescriptions', prescriptionRoutes);
router.use('/consultations', consultationRoutes); // Register consultation routes
router.use('/demographics', demographicRoutes); // Register demographic routes
router.use('/formDietician', formDieticianRoutes); // Register formDietician routes
router.use('/youtube', youtubeRoutes); // Register youtube routes
router.use('/store', storeRoutes); // Register store routes
router.use('/health-guides', healthGuideRoutes); // Register health guide routes
router.use('/ai-monitor', aiMonitorRoutes); // Register AI monitoring routes

// 注册文档路由
const documentRoutes = require('./documentRoutes');
router.use('/documents', documentRoutes);

// 测试路由，列出本地文件
router.get('/test-local-files', (req, res) => {
    const docsPath = process.env.LOCAL_TIPS_PATH || '/app/documents';

    try {
        if (fs.existsSync(docsPath)) {
            const files = fs.readdirSync(docsPath)
                .filter(filename => filename.endsWith('.md') && !filename.startsWith('.'));

            res.json({
                path: docsPath,
                exists: true,
                files
            });
        } else {
            res.json({
                path: docsPath,
                exists: false,
                error: 'Path does not exist'
            });
        }
    } catch (error) {
        res.status(500).json({
            path: docsPath,
            error: error.message
        });
    }
});

module.exports = router; 