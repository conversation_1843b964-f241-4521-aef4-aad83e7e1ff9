# 健康新闻爬虫

这是为MMC Wellness应用开发的健康新闻爬虫，用于自动获取最新的健康资讯，使用AI进行总结，并生成高质量的健康文章。

## 功能

- 从Google搜索自动获取最新健康资讯(每天1-3条)
- 爬取文章内容，提取正文
- 使用OpenRouter API (基于deepseek/deepseek-chat-v3-0324:free模型)生成综合性文章
- 将生成的文章以Markdown格式保存到文件系统
- 同时将文章存储到数据库中
- 定时任务自动运行，无需人工干预

## 安装

1. 确保已安装Node.js (v14+)和npm

2. 安装依赖:
   ```bash
   cd chatbot-service
   npm install
   ```

3. 配置环境变量:
   - 复制`health-crawler.env`文件到`.env`或添加到现有的`.env`文件中
   - 确保环境变量已配置好(OpenRouter API密钥和Google搜索API)

## 必须的API和配置

1. **Google API 配置**:
   - 已配置好Google Custom Search Engine (cx=5168e6a7d492644e1)
   - 已配置好Google API Key
   - 系统中可使用以下代码嵌入搜索框：
     ```html
     <script async src="https://cse.google.com/cse.js?cx=5168e6a7d492644e1"></script>
     <div class="gcse-search"></div>
     ```

2. **OpenRouter API**:
   - 使用系统中已配置的OPENROUTER_API_KEY环境变量
   - 使用deepseek/deepseek-chat-v3-0324:free模型(免费版)

## 使用方法

### 手动运行爬虫

```bash
npm run crawler
```

或者

```bash
node health-news-crawler.js
```

### 自动调度

爬虫默认配置为每天早上7点自动运行。您可以在`health-news-crawler.js`文件中修改cron表达式来更改时间：

```javascript
// 例如，配置为每天中午12点运行
cron.schedule('0 12 * * *', () => {
  console.log('Running scheduled health news crawler...');
  processHealthNews();
});
```

## 输出

生成的文章会被保存到:

1. **文件系统**: `../documents/health/health-summary-YYYYMMDD.md`
2. **数据库**: `health_articles`表中

## 如何在应用中使用生成的文章

生成的文章可以通过多种方式集成到MMC Wellness应用中:

1. **网站文章展示**: 自动发布到健康资讯部分
2. **邮件通讯**: 作为每日/每周健康资讯发送给用户
3. **应用内通知**: 向应用用户推送最新健康内容

## 故障排除

如果爬虫运行失败，请检查:

1. 环境变量是否正确配置
2. Google API限额是否已用完(每天免费100次查询)
3. OpenRouter API是否可用
4. 数据库连接是否正常

查看日志获取详细错误信息。

## 自定义

您可以根据需要修改以下部分:

1. **健康主题**: 修改`HEALTH_TOPICS`数组添加更多相关主题
2. **文章格式**: 调整`generateArticleSummary`函数中的prompt
3. **定时安排**: 修改cron表达式更改运行时间或频率

---

# Health News Crawler

This is a health news crawler developed for the MMC Wellness application, designed to automatically fetch the latest health information, summarize it using AI, and generate high-quality health articles.

## Features

- Automatically fetches the latest health news from Google Search (1-3 items daily)
- Crawls and extracts article content
- Uses OpenRouter API (based on deepseek/deepseek-chat-v3-0324:free model) to generate comprehensive articles
- Saves articles in Markdown format to the file system
- Also stores articles in the database
- Scheduled tasks run automatically, requiring no manual intervention

## Installation

1. Ensure Node.js (v14+) and npm are installed

2. Install dependencies:
   ```bash
   cd chatbot-service
   npm install
   ```

3. Configure environment variables:
   - Copy the `health-crawler.env` file to `.env` or add to your existing `.env` file
   - Make sure the environment variables are properly configured (OpenRouter API key and Google Search API)

## Required APIs and Configuration

1. **Google API Configuration**:
   - Google Custom Search Engine is configured (cx=5168e6a7d492644e1)
   - Google API Key is configured
   - You can embed the search box in the system using the following code:
     ```html
     <script async src="https://cse.google.com/cse.js?cx=5168e6a7d492644e1"></script>
     <div class="gcse-search"></div>
     ```

2. **OpenRouter API**:
   - Uses the OPENROUTER_API_KEY environment variable already configured in the system
   - Uses the deepseek/deepseek-chat-v3-0324:free model (free version)

## Usage

### Manually Run the Crawler

```bash
npm run crawler
```

Or

```bash
node health-news-crawler.js
```

### Automatic Scheduling

The crawler is configured to run automatically at 7 AM daily. You can modify the cron expression in the `health-news-crawler.js` file to change the timing:

```javascript
// For example, to run at noon daily
cron.schedule('0 12 * * *', () => {
  console.log('Running scheduled health news crawler...');
  processHealthNews();
});
```

## Output

Generated articles are saved to:

1. **File System**: `../documents/health/health-summary-YYYYMMDD.md`
2. **Database**: `health_articles` table

## How to Use Generated Articles in the Application

Generated articles can be integrated into the MMC Wellness application in various ways:

1. **Website Article Display**: Automatically publish to the health information section
2. **Email Newsletter**: Send as daily/weekly health information to users
3. **In-App Notifications**: Push the latest health content to application users

## Troubleshooting

If the crawler fails to run, check:

1. If environment variables are correctly configured
2. If Google API quota has been exhausted (free 100 queries per day)
3. If OpenRouter API is available
4. If database connection is normal

View logs for detailed error information.

## Customization

You can modify the following parts as needed:

1. **Health Topics**: Modify the `HEALTH_TOPICS` array to add more relevant topics
2. **Article Format**: Adjust the prompt in the `generateArticleSummary` function
3. **Scheduling**: Modify the cron expression to change run time or frequency 