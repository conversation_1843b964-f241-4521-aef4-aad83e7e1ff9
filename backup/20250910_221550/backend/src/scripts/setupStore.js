const pool = require('../config/database');
const fs = require('fs');
const path = require('path');

async function setupStore() {
    try {
        console.log('Setting up MMC Health Store database tables...');
        
        // 读取迁移文件
        const migrationPath = path.join(__dirname, '../database/migrations/create_store_tables.sql');
        const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
        
        // 分割SQL语句（按分号分割）
        const statements = migrationSQL
            .split(';')
            .map(stmt => stmt.trim())
            .filter(stmt => stmt.length > 0);
        
        // 执行每个SQL语句
        for (const statement of statements) {
            if (statement.trim()) {
                console.log(`Executing: ${statement.substring(0, 50)}...`);
                await pool.query(statement);
            }
        }
        
        console.log('✅ Store database setup completed successfully!');
        
        // 验证表是否创建成功
        console.log('\nVerifying tables...');
        const tables = [
            'product_categories',
            'products', 
            'orders',
            'order_items',
            'cart_items',
            'user_services'
        ];
        
        for (const table of tables) {
            const [rows] = await pool.query(`SHOW TABLES LIKE '${table}'`);
            if (rows.length > 0) {
                console.log(`✅ Table '${table}' exists`);
                
                // 显示表结构
                const [structure] = await pool.query(`DESCRIBE ${table}`);
                console.log(`   Columns: ${structure.map(col => col.Field).join(', ')}`);
            } else {
                console.log(`❌ Table '${table}' not found`);
            }
        }
        
        // 检查示例数据
        console.log('\nChecking sample data...');
        const [categories] = await pool.query('SELECT COUNT(*) as count FROM product_categories');
        const [products] = await pool.query('SELECT COUNT(*) as count FROM products');
        
        console.log(`Categories: ${categories[0].count}`);
        console.log(`Products: ${products[0].count}`);
        
        if (categories[0].count > 0 && products[0].count > 0) {
            console.log('✅ Sample data loaded successfully!');
        } else {
            console.log('⚠️  No sample data found. You may need to add products manually.');
        }
        
    } catch (error) {
        console.error('❌ Error setting up store:', error);
        throw error;
    } finally {
        await pool.end();
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    setupStore()
        .then(() => {
            console.log('\n🎉 MMC Health Store setup completed!');
            process.exit(0);
        })
        .catch((error) => {
            console.error('\n💥 Setup failed:', error);
            process.exit(1);
        });
}

module.exports = setupStore; 