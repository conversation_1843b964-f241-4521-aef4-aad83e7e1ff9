const express = require('express');
const router = express.Router();
const tipsController = require('../controllers/tipsController');
const auth = require('../middleware/auth');
const { requireMember } = require('../middleware/userTypeAuth');

// Make GET /categories public (no auth required)
// Important: Define this specific route BEFORE any parameterized routes
router.get('/categories', tipsController.listCategories);

// Public route for assets
router.get('/assets/:assetPath', tipsController.getAsset);

// Member-only routes - 需要有效membership才能访问健康小贴士
router.get('/', auth, requireMember, tipsController.listTips);
router.get('/category/:category', auth, requireMember, tipsController.listTipsByCategory);
router.get('/:filename', auth, requireMember, tipsController.getTip);

module.exports = router; 