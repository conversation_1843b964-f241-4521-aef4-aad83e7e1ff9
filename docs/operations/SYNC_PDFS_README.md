# PDF文档同步脚本使用说明

## 概述

`sync-pdfs.sh` 是一个增强版的PDF文档同步脚本，用于从远程Oscar服务器同步医疗文档到本地系统。该脚本包含详细的日志记录、错误处理和多种运行模式。

## 功能特性

### 🔍 核心功能
- **智能同步**: 使用 rsync 进行高效的增量同步
- **连接检查**: 自动检测远程服务器连接状态
- **权限管理**: 自动修正本地文件和目录权限
- **统计报告**: 提供详细的同步前后统计信息

### 📝 日志系统
- **彩色输出**: 不同级别的日志使用不同颜色显示
- **文件记录**: 所有操作记录到日志文件
- **日志轮转**: 自动管理日志文件大小和数量
- **详细统计**: 记录传输文件数量、大小等信息

### 🛡️ 错误处理
- **连接验证**: 检查网络连接和SSH配置
- **目录验证**: 确认远程和本地目录状态
- **优雅退出**: 错误时提供详细信息并安全退出
- **临时文件清理**: 自动清理临时文件

## 使用方法

### 基本语法
```bash
./sync-pdfs.sh [选项]
```

### 可用选项

| 选项 | 说明 |
|------|------|
| `-h, --help` | 显示帮助信息 |
| `-v, --verbose` | 详细输出模式 |
| `--dry-run` | 模拟运行，不实际同步文件 |
| `--no-delete` | 不删除本地多余文件 |
| `--stats-only` | 仅显示统计信息，不执行同步 |

### 使用示例

#### 1. 正常同步
```bash
./sync-pdfs.sh
```
执行完整的文档同步操作。

#### 2. 模拟运行
```bash
./sync-pdfs.sh --dry-run
```
模拟同步过程，查看会进行的操作但不实际传输文件。

#### 3. 仅查看统计
```bash
./sync-pdfs.sh --stats-only
```
只显示本地和远程的文件统计信息，不执行同步。

#### 4. 详细模式
```bash
./sync-pdfs.sh -v
```
显示更详细的操作信息。

#### 5. 保留本地文件
```bash
./sync-pdfs.sh --no-delete
```
同步新文件但不删除本地的额外文件。

## 配置说明

### 服务器配置
```bash
REMOTE_USER=mmc                    # 远程用户名
REMOTE_HOST=**************         # 远程服务器IP
REMOTE_DIR=/home/<USER>/open-osp/volumes/OscarDocument/oscar  # 远程目录
LOCAL_DIR=/home/<USER>/apps/mmcwebapp/local-documents/oscar  # 本地目录
```

### 日志配置
```bash
LOG_DIR="/home/<USER>/apps/mmcwebapp/logs"  # 日志目录
LOG_FILE="$LOG_DIR/sync-pdfs.log"            # 日志文件
MAX_LOG_SIZE=10485760                         # 最大日志文件大小 (10MB)
MAX_LOG_FILES=5                               # 保留的日志文件数量
```

## 日志系统

### 日志级别
- **INFO** (蓝色): 一般信息
- **SUCCESS** (绿色): 成功操作
- **WARNING** (黄色): 警告信息
- **ERROR** (红色): 错误信息
- **DEBUG** (紫色): 调试信息

### 日志文件位置
```
/home/<USER>/apps/mmcwebapp/logs/sync-pdfs.log
```

### 日志轮转
- 当日志文件超过 10MB 时自动轮转
- 保留最近 5 个日志文件
- 旧文件自动重命名为 `.1`, `.2` 等

### 查看日志
```bash
# 查看最新日志
tail -f /home/<USER>/apps/mmcwebapp/logs/sync-pdfs.log

# 查看今天的日志
grep "$(date '+%Y-%m-%d')" /home/<USER>/apps/mmcwebapp/logs/sync-pdfs.log

# 查看错误日志
grep "ERROR" /home/<USER>/apps/mmcwebapp/logs/sync-pdfs.log
```

## 前置要求

### 1. SSH配置
确保SSH密钥已配置，可以无密码连接到远程服务器：
```bash
# 测试SSH连接
ssh mmc@************** "echo 'Connection test successful'"
```

### 2. 网络连接
确保可以ping通远程服务器：
```bash
ping -c 1 **************
```

### 3. 目录权限
确保本地目录有写权限：
```bash
mkdir -p /home/<USER>/apps/mmcwebapp/local-documents/oscar
mkdir -p /home/<USER>/apps/mmcwebapp/logs
```

## 故障排除

### 常见问题

#### 1. SSH连接失败
**错误**: `SSH连接失败: mmc@**************`

**解决方案**:
- 检查SSH密钥配置
- 确认远程服务器状态
- 验证网络连接

#### 2. 权限问题
**错误**: `Permission denied`

**解决方案**:
```bash
# 检查脚本权限
chmod +x sync-pdfs.sh

# 检查目录权限
chmod 755 /home/<USER>/apps/mmcwebapp/logs
```

#### 3. 磁盘空间不足
**错误**: `No space left on device`

**解决方案**:
```bash
# 检查磁盘空间
df -h

# 清理旧日志
rm -f /home/<USER>/apps/mmcwebapp/logs/*.log.*
```

### 调试模式

启用详细输出查看详细信息：
```bash
./sync-pdfs.sh -v
```

查看实时日志：
```bash
tail -f /home/<USER>/apps/mmcwebapp/logs/sync-pdfs.log
```

## 定时任务设置

### 设置每日自动同步
```bash
# 编辑crontab
crontab -e

# 添加以下行 (每天凌晨2点执行)
0 2 * * * /home/<USER>/apps/mmcwebapp/sync-pdfs.sh >> /home/<USER>/apps/mmcwebapp/logs/cron-sync.log 2>&1
```

### 设置每小时同步
```bash
# 每小时的第0分钟执行
0 * * * * /home/<USER>/apps/mmcwebapp/sync-pdfs.sh >> /home/<USER>/apps/mmcwebapp/logs/cron-sync.log 2>&1
```

## 性能优化

### rsync 优化选项
脚本已包含以下优化：
- `--delete`: 删除本地多余文件
- `--exclude='*.tmp'`: 排除临时文件
- `--exclude='.*'`: 排除隐藏文件
- `--progress`: 显示传输进度
- `--stats`: 显示传输统计

### 网络优化
```bash
# 如果网络较慢，可以添加压缩选项
rsync -avz --compress-level=9 ...

# 如果网络不稳定，可以添加重试选项
rsync -avz --partial --timeout=300 ...
```

## 安全考虑

### 1. SSH密钥安全
- 使用强密钥算法（RSA 4096位或ED25519）
- 定期轮换SSH密钥
- 限制密钥的使用权限

### 2. 网络安全
- 确保SSH连接加密
- 考虑使用VPN或专用网络
- 监控异常连接

### 3. 数据安全
- 定期备份同步的数据
- 验证数据完整性
- 控制访问权限

## 更新说明

### v2.0 (当前版本)
- ✅ 添加详细日志系统
- ✅ 实现彩色输出
- ✅ 增加错误处理
- ✅ 支持多种运行模式
- ✅ 自动日志轮转
- ✅ 连接状态检查
- ✅ 统计信息报告

### 计划改进
- 支持配置文件
- 增加邮件通知
- 实现并行传输
- 添加Web界面监控

## 联系信息

如有问题或建议，请联系系统管理员。

---

**注意**: 请在生产环境使用前充分测试脚本功能。 