const path = require('path');
const { CronJob } = require('cron');
const { execSync } = require('child_process');
const fs = require('fs');

// 获取生成脚本的路径
const generateDailyTipPath = path.join(__dirname, 'scripts/generateDailyTip.js');

console.log(`[${new Date().toISOString()}] 设置1分钟后自动生成健康贴士测试...`);

// 确认脚本存在
if (!fs.existsSync(generateDailyTipPath)) {
    console.error(`生成脚本不存在: ${generateDailyTipPath}`);
    process.exit(1);
}

// 计算1分钟后的时间
const now = new Date();
const oneMinuteLater = new Date(now.getTime() + 60000);
const hours = oneMinuteLater.getHours();
const minutes = oneMinuteLater.getMinutes();

console.log(`当前时间: ${now.toLocaleTimeString()}`);
console.log(`计划执行时间: ${oneMinuteLater.toLocaleTimeString()}`);

// 创建一个在1分钟后执行的cron作业
const cronExpression = `${minutes} ${hours} * * *`;
console.log(`使用cron表达式: ${cronExpression}`);

const oneMinuteJob = new CronJob(cronExpression, function () {
    console.log(`[${new Date().toISOString()}] 开始生成健康小贴士...`);

    try {
        const output = execSync(`node ${generateDailyTipPath}`, { encoding: 'utf8' });
        console.log(`健康小贴士生成结果: ${output}`);

        // 检查是否生成成功
        const documentsPath = process.env.LOCAL_TIPS_PATH || path.join(__dirname, '../documents');
        console.log(`检查目录: ${documentsPath}`);

        // 获取当前日期格式(YYYYMMDD)
        const today = new Date().toISOString().split('T')[0].replace(/-/g, '');

        // 递归搜索所有目录找到可能新生成的文件
        console.log('正在搜索新生成的文件...');
        const foundFiles = [];

        function searchFiles(dir) {
            const entries = fs.readdirSync(dir, { withFileTypes: true });
            for (const entry of entries) {
                const fullPath = path.join(dir, entry.name);
                if (entry.isDirectory()) {
                    searchFiles(fullPath);
                } else if (entry.isFile() && entry.name.includes(today)) {
                    // 检查文件的创建时间是在cron任务执行之后
                    const stats = fs.statSync(fullPath);
                    if (stats.mtime > now) {
                        foundFiles.push(fullPath);
                    }
                }
            }
        }

        try {
            searchFiles(documentsPath);
            if (foundFiles.length > 0) {
                console.log(`成功! 找到新生成的文件:`);
                foundFiles.forEach(file => console.log(`- ${file}`));
            } else {
                console.log('未找到新生成的文件，生成可能失败');
            }
        } catch (searchErr) {
            console.error(`搜索文件时出错: ${searchErr.message}`);
        }

        // 测试完成后退出程序
        console.log('测试完成，1分钟后将自动退出...');
        setTimeout(() => process.exit(0), 60000);

    } catch (error) {
        console.error(`生成健康小贴士出错: ${error.message}`);
        if (error.stdout) console.log(`输出: ${error.stdout}`);
        if (error.stderr) console.error(`错误输出: ${error.stderr}`);

        // 发生错误也退出程序
        setTimeout(() => process.exit(1), 5000);
    }
}, null, true, 'America/Vancouver');

// 启动cron任务
oneMinuteJob.start();
console.log('任务已设置，请等待1分钟...');

// 确保程序不会立即退出
console.log('按Ctrl+C终止程序'); 