const appointmentNoteService = require('../services/appointmentNoteService');
const pool = require('../config/database');
const User = require('../models/user'); // 导入User模型

/**
 * 获取特定预约的医疗笔记
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @returns {Object} JSON响应包含笔记内容
 */
exports.getAppointmentNote = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        const userId = req.user.id;

        console.log(`Getting appointment note for appointmentId: ${appointmentId}, userId: ${userId}`);

        // 处理查看当前登录用户或其家庭成员的笔记
        const result = await getAppointmentNoteWithPermissionCheck(appointmentId, userId);

        if (!result.success) {
            console.log(`Failed to get note: ${result.message}`);
            return res.status(result.statusCode || 404).json({ message: result.message });
        }

        return res.json(result);
    } catch (error) {
        console.error('Error in getAppointmentNote controller:', error);
        return res.status(500).json({ message: 'Server error', error: error.message });
    }
};

/**
 * 获取特定预约的医疗笔记并用AI总结 (带缓存)
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @returns {Object} JSON响应包含笔记内容和AI总结
 */
exports.getAppointmentNoteSummary = async (req, res) => {
    console.log('<<<<< ENTERING getAppointmentNoteSummary CONTROLLER >>>>>');
    try {
        const { appointmentId } = req.params;
        const userId = req.user.id;
        const language = req.query.lang || req.headers['accept-language']?.substring(0, 2) || 'en';

        console.log(`[Note Summary Cache] Getting summary for appointmentId: ${appointmentId}, userId: ${userId}, language: ${language}`);

        // --- 1. 获取笔记数据 (包含 noteId) ---
        const noteResult = await getAppointmentNoteWithPermissionCheck(appointmentId, userId);

        if (!noteResult.success || !noteResult.note || !noteResult.note.noteId) {
            console.log(`[Note Summary Cache] Failed to get note or noteId missing: ${noteResult.message}`);
            return res.status(noteResult.statusCode || 404).json({
                success: false,
                message: noteResult.message || 'Failed to retrieve note details.'
            });
        }

        const noteData = noteResult.note;
        const noteId = noteData.noteId;
        console.log(`[Note Summary Cache] Got note data with noteId: ${noteId}`);

        // --- 2. 检查缓存 --- 
        const cacheQuery = `
            SELECT generated_summary 
            FROM appointment_note_summaries 
            WHERE note_id = ? AND language = ?
        `;
        try {
            const [cacheResult] = await pool.query(cacheQuery, [noteId, language]);

            if (cacheResult.length > 0) {
                const cachedSummary = cacheResult[0].generated_summary;
                console.log(`[Note Summary Cache] Cache hit for noteId ${noteId}, language ${language}. Returning cached summary.`);
                return res.json({
                    success: true,
                    note: noteData,
                    summary: cachedSummary,
                    source: 'cache',
                    language: language
                });
            }
            console.log(`[Note Summary Cache] Cache miss for noteId ${noteId}, language ${language}. Generating...`);

        } catch (cacheReadError) {
            console.error(`[Note Summary Cache] Error reading cache for noteId ${noteId}:`, cacheReadError);
            // 不中断流程，继续尝试生成
        }

        // --- 3. 生成 AI 摘要 (如果缓存未命中) ---
        const summaryResult = await appointmentNoteService.summarizeNoteWithAI(noteData, language);

        // --- 4. 存储到缓存 (如果生成成功) ---
        if (summaryResult.success && summaryResult.summary) {
            const upsertCacheQuery = `
                INSERT INTO appointment_note_summaries (note_id, language, generated_summary) 
                VALUES (?, ?, ?) 
                ON DUPLICATE KEY UPDATE 
                generated_summary = VALUES(generated_summary),
                last_updated = NOW()
            `;
            try {
                console.log(`[Note Summary Cache] Attempting to write to cache. SQL: ${upsertCacheQuery.replace(/\s+/g, ' ')}`);
                console.log(`[Note Summary Cache] Parameters: [noteId: ${noteId} (${typeof noteId}), language: ${language} (${typeof language}), summary: ${summaryResult.summary.substring(0, 50)}... (${typeof summaryResult.summary}, length: ${summaryResult.summary.length})]`);

                const [result] = await pool.query(upsertCacheQuery, [noteId, language, summaryResult.summary]);

                console.log(`[Note Summary Cache] DB write result: ${JSON.stringify(result)}`);
                if (result.affectedRows > 0 || result.warningStatus === 0) {
                    console.log(`[Note Summary Cache] Successfully wrote/updated cache for noteId ${noteId}, language ${language}.`);
                } else {
                    console.warn(`[Note Summary Cache] DB write for noteId ${noteId} completed but reported 0 affected rows. Result: ${JSON.stringify(result)}`);
                }

            } catch (cacheWriteError) {
                console.error(`[Note Summary Cache] FATAL: Error writing cache for noteId ${noteId}, lang ${language}. Error Code: ${cacheWriteError.code}, SQL State: ${cacheWriteError.sqlState}, Message: ${cacheWriteError.message}`);
                console.error(`[Note Summary Cache] Failing SQL parameters: [noteId: ${noteId}, language: ${language}, summary length: ${summaryResult.summary?.length}]`);
            }
        } else {
            console.log(`[Note Summary Cache] Skipped caching for noteId ${noteId}: success=${summaryResult.success}, summary exists=${!!summaryResult.summary}`);
        }

        // --- 5. 返回结果 --- 
        return res.json({
            success: true,
            note: noteData,
            summary: summaryResult.success ? summaryResult.summary : null,
            summaryError: !summaryResult.success ? summaryResult.message : null,
            source: 'generated',
            language: language
        });

    } catch (error) {
        console.error('Error in getAppointmentNoteSummary controller:', error);
        return res.status(500).json({
            success: false,
            message: 'Server error processing appointment note summary.',
            error: error.message
        });
    }
};

/**
 * 检查用户访问预约笔记的权限并获取笔记数据
 * @param {Number} appointmentId - 预约ID
 * @param {Number} userId - 当前登录用户ID
 * @returns {Object} 包含笔记内容或错误信息的对象
 */
async function getAppointmentNoteWithPermissionCheck(appointmentId, userId) {
    try {
        console.log(`Starting permission check for appointmentId: ${appointmentId}, userId: ${userId}`);

        // 查询用户的demographic_no
        const [userResult] = await pool.query(
            'SELECT demographic_no FROM user_auth WHERE id = ?',
            [userId]
        );

        if (!userResult || userResult.length === 0 || !userResult[0].demographic_no) {
            console.log('User not linked to an Oscar account');
            return {
                success: false,
                statusCode: 404,
                message: 'User not linked to an Oscar account'
            };
        }

        const userDemographicNo = userResult[0].demographic_no;
        console.log(`User demographic_no: ${userDemographicNo}`);

        // 首先检查预约是否属于当前用户
        const [appointmentResult] = await pool.query(
            'SELECT demographic_no FROM appointment WHERE appointment_no = ?',
            [appointmentId]
        );

        if (!appointmentResult || appointmentResult.length === 0) {
            console.log(`Appointment ${appointmentId} not found`);
            return {
                success: false,
                statusCode: 404,
                message: 'Appointment not found'
            };
        }

        const appointmentDemographicNo = appointmentResult[0].demographic_no;
        console.log(`Appointment demographic_no: ${appointmentDemographicNo}`);

        // 如果预约属于当前用户，直接获取笔记
        const userDemoInt = parseInt(userDemographicNo);
        const apptDemoInt = parseInt(appointmentDemographicNo);

        console.log(`Comparing user demographic ${userDemoInt} with appointment demographic ${apptDemoInt}`);

        if (userDemoInt === apptDemoInt) {
            console.log(`User ${userId} authorized to view their own appointment ${appointmentId}`);
            const result = await appointmentNoteService.getAppointmentNote(appointmentId, userDemographicNo);

            if (!result.success) {
                console.log(`Failed to get note: ${result.message}`);
            }

            return result;
        }

        console.log(`Appointment ${appointmentId} doesn't belong to user ${userId}, checking relationships`);

        // 检查医疗提供者权限
        try {
            console.log(`Checking for provider-patient relationship`);

            // 检查是否是医生或医疗提供者
            const [providerResult] = await pool.query(`
                SELECT provider_no FROM provider 
                WHERE provider_no IN (
                    SELECT provider_no FROM security 
                    WHERE user_name = (SELECT email FROM user_auth WHERE id = ?)
                )
            `, [userId]);

            if (providerResult && providerResult.length > 0) {
                console.log(`User ${userId} is a provider with provider_no: ${providerResult[0].provider_no}`);
                // 医疗提供者可以查看任何预约笔记
                const result = await appointmentNoteService.getAppointmentNote(appointmentId, appointmentDemographicNo);

                if (!result.success) {
                    console.log(`Provider failed to get note: ${result.message}`);
                } else {
                    console.log(`Provider successfully retrieved note`);
                }

                return result;
            }

            // 检查家庭成员关系 - 使用User.getFamilyMembers方法
            console.log(`Checking family relationships from OSCAR relationships table`);
            const familyMembers = await User.getFamilyMembers(userDemographicNo, userId);
            console.log(`Found ${familyMembers.length} family members for user ${userId}`);

            // 查看所有家庭成员信息和要匹配的预约信息
            console.log(`Looking for appointment demographic_no: ${appointmentDemographicNo} (${typeof appointmentDemographicNo})`);
            if (familyMembers.length > 0) {
                console.log(`Family members: ${JSON.stringify(familyMembers.map(m => ({
                    id: m.relative_demographic_no,
                    type: typeof m.relative_demographic_no,
                    name: `${m.relative_first_name} ${m.relative_last_name}`
                })))}`);
            }

            // 查找预约患者是否在家庭成员列表中 - 使用字符串比较
            const familyMember = familyMembers.find(member =>
                String(member.relative_demographic_no) === String(appointmentDemographicNo)
            );

            if (familyMember) {
                console.log(`Found family relationship: ${familyMember.relationship_type}, allowing access`);
                const result = await appointmentNoteService.getAppointmentNote(appointmentId, appointmentDemographicNo);

                if (!result.success) {
                    console.log(`Failed to get note via family relationship: ${result.message}`);
                } else {
                    console.log(`Successfully retrieved family member's note`);
                }

                return result;
            }

            console.log(`No family relationship found to patient ${appointmentDemographicNo}`);

            // 没有找到关系，返回未授权错误
            return {
                success: false,
                statusCode: 403,
                message: 'Not authorized to view this appointment note'
            };
        } catch (relationError) {
            console.error('Error checking relationships:', relationError);
            // 如果检查表时出错，返回未授权错误
            return {
                success: false,
                statusCode: 403,
                message: 'Not authorized to view this appointment note'
            };
        }
    } catch (error) {
        console.error('Error in getAppointmentNoteWithPermissionCheck:', error);
        return {
            success: false,
            statusCode: 500,
            message: 'Failed to check permission or retrieve appointment note',
            error: error.message
        };
    }
} 