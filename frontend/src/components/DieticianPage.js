import React, { useEffect, useState } from 'react';
import Typography from '@mui/material/Typography';
import Container from '@mui/material/Container';
import Paper from '@mui/material/Paper';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Box from '@mui/material/Box';
import ToggleButtonGroup from '@mui/material/ToggleButtonGroup';
import ToggleButton from '@mui/material/ToggleButton';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import RestaurantIcon from '@mui/icons-material/Restaurant';
import { useLanguage } from '../context/LanguageContext';
import { useView } from '../context/ViewContext';
import { getDieticianComments, getDieticianCommentSummary } from '../services/dieticianService';
import axios from 'axios'; // Import axios to check its defaults
import AIDisclaimer from './AIDisclaimer';

// 增强的Markdown渲染
const renderMarkdown = (text) => {
    if (!text) return '';

    // 清理markdown代码块标记
    let cleanText = text.replace(/```markdown\s+/g, '').replace(/```\s*$/g, '');

    // 替换标题
    let formatted = cleanText.replace(/^# (.*$)/gm, '<h1>$1</h1>');
    formatted = formatted.replace(/^## (.*$)/gm, '<h2>$1</h2>');
    formatted = formatted.replace(/^### (.*$)/gm, '<h3>$1</h3>');

    // 替换列表
    formatted = formatted.replace(/^\* (.*$)/gm, '<li>$1</li>');
    formatted = formatted.replace(/^\*\* (.*$)/gm, '<li>$1</li>'); // 处理双星号开头的列表项
    formatted = formatted.replace(/^(\d+)\. (.*$)/gm, '<li>$2</li>');
    formatted = formatted.replace(/<\/li>\n<li>/g, '</li><li>');
    formatted = formatted.replace(/(?:<li>.*<\/li>\n)+/gs, function (match) {
        return match.includes('<li>1') || match.includes('<li>2')
            ? '<ol>' + match + '</ol>'
            : '<ul>' + match + '</ul>';
    });

    // 替换粗体和斜体
    formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>');

    // 替换段落
    formatted = formatted.replace(/(?:^|\n)(?!\<h|\<ul|\<ol|\<li)(.+)/g, '<p>$1</p>');

    return formatted;
};

const DieticianPage = () => {
    const { t, language } = useLanguage();
    const { viewingDemographicNo } = useView();
    const [comments, setComments] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    
    // Dialog state for AI summary
    const [dialogOpen, setDialogOpen] = useState(false);
    const [selectedComment, setSelectedComment] = useState(null);
    const [summaryData, setSummaryData] = useState(null);
    const [summaryLoading, setSummaryLoading] = useState(false);
    const [summaryError, setSummaryError] = useState(null);
    const [summaryLanguage, setSummaryLanguage] = useState(language || 'zh');

    useEffect(() => {
        const fetchComments = async () => {
            if (!viewingDemographicNo) {
                setError(t('invalid_patient_id'));
                setLoading(false);
                return;
            }
            try {
                setLoading(true);
                setError(null);
                console.log('Axios baseURL before dietician call:', axios.defaults.baseURL); // Log baseURL
                const response = await getDieticianComments(viewingDemographicNo);
                if (response.success && response.data) {
                    setComments(response.data);
                } else {
                    setComments([]);
                    // Optionally set an error if success is false or data is missing
                    if(!response.success) setError(response.message || t('fetch_failed') );
                }
            } catch (err) {
                console.error("Error fetching dietician comments:", err);
                setError(err.message || t('fetch_error'));
                setComments([]);
            } finally {
                setLoading(false);
            }
        };

        fetchComments();
    }, [viewingDemographicNo, t]);

    // Update summary language when user language changes
    useEffect(() => {
        setSummaryLanguage(language || 'zh');
    }, [language]);

    const handleViewSummary = async (comment) => {
        setSelectedComment(comment);
        setDialogOpen(true);
        setSummaryData(null);
        setSummaryError(null);
        // Automatically fetch summary when dialog opens
        await fetchSummary(comment, summaryLanguage);
    };

    const handleLanguageChange = async (event, newLanguage) => {
        if (newLanguage && newLanguage !== summaryLanguage && selectedComment) {
            setSummaryLanguage(newLanguage);
            await fetchSummary(selectedComment, newLanguage);
        }
    };

    const fetchSummary = async (comment, lang = summaryLanguage) => {
        if (!comment) return;
        
        setSummaryLoading(true);
        setSummaryError(null);
        
        try {
            const response = await getDieticianCommentSummary(comment.dietician_comment_id, lang);
            if (response.success) {
                setSummaryData(response);
            } else {
                setSummaryError(response.message || t('summary_fetch_failed'));
            }
        } catch (error) {
            console.error('Error fetching dietician comment summary:', error);
            setSummaryError(error.message || t('summary_fetch_error'));
        } finally {
            setSummaryLoading(false);
        }
    };

    const handleCloseDialog = () => {
        setDialogOpen(false);
        setSelectedComment(null);
        setSummaryData(null);
        setSummaryError(null);
    };

    const formatDate = (dateString) => {
        if (!dateString) return '';
        const options = { year: 'numeric', month: 'long', day: 'numeric' };
        return new Date(dateString).toLocaleDateString(undefined, options);
    };

    return (
        <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
            <Typography variant="h4" gutterBottom>
                {t('dietician')}
            </Typography>

            {loading && <CircularProgress sx={{ display: 'block', margin: 'auto' }} />}

            {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                    {error}
                </Alert>
            )}

            {!loading && !error && comments.length === 0 && (
                <Typography variant="body1">
                    {t('no_dietician_comments')}
                </Typography>
            )}

            {!loading && !error && comments.length > 0 && (
                comments.map((comment, index) => (
                    <Paper key={index} elevation={2} sx={{ p: 3, mb: 2 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                            <Box sx={{ flex: 1 }}>
                        {comment.entry_date && (
                                    <Typography variant="caption" display="block" gutterBottom color="text.secondary">
                                        {formatDate(comment.entry_date)}
                            </Typography>
                        )}
                                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                    {t('dietician_consultation_available')}
                        </Typography>
                            </Box>
                            
                            <Button 
                                variant="contained" 
                                size="small"
                                startIcon={<AutoAwesomeIcon />}
                                onClick={() => handleViewSummary(comment)}
                                sx={{ ml: 2 }}
                            >
                                {t('view_summary')}
                            </Button>
                        </Box>
                    </Paper>
                ))
            )}

            {/* AI Summary Dialog */}
            <Dialog 
                open={dialogOpen} 
                onClose={handleCloseDialog}
                maxWidth="md"
                fullWidth
            >
                <DialogTitle sx={{ display: 'flex', alignItems: 'center' }}>
                    <RestaurantIcon sx={{ mr: 1 }} />
                    {t('dietician_consultation_summary')}
                </DialogTitle>
                <DialogContent dividers>
                    {selectedComment && (
                        <Box sx={{ mb: 3 }}>
                            <Typography variant="h6" gutterBottom>
                                {t('consultation_details')}
                            </Typography>
                            <Typography variant="body1">
                                <strong>{t('date')}:</strong> {formatDate(selectedComment.entry_date)}
                            </Typography>
                        </Box>
                    )}

                    {/* Language Toggle */}
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                        <Typography variant="h6">
                            {t('nutrition_summary')}
                        </Typography>
                        <ToggleButtonGroup
                            value={summaryLanguage}
                            exclusive
                            onChange={handleLanguageChange}
                            aria-label="summary language"
                            size="small"
                        >
                            <ToggleButton value="zh" aria-label="Chinese">
                                中文
                            </ToggleButton>
                            <ToggleButton value="en" aria-label="English">
                                English
                            </ToggleButton>
                        </ToggleButtonGroup>
                    </Box>
                    
                    {summaryLoading && (
                        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 4 }}>
                            <CircularProgress sx={{ mr: 2 }} />
                            <Typography>
                                {t('generating_summary')}
                            </Typography>
                        </Box>
                    )}
                    
                    {summaryError && (
                        <Alert severity="error" sx={{ mt: 2 }}>
                            {summaryError}
                        </Alert>
                    )}
                    
                    {summaryData && summaryData.summary && !summaryLoading && (
                        <Box sx={{ mt: 2 }}>
                            <AIDisclaimer sx={{ mb: 2 }} />
                            <Paper elevation={1} sx={{ p: 3, backgroundColor: 'background.default' }}>
                                <Box 
                                    dangerouslySetInnerHTML={{ __html: renderMarkdown(summaryData.summary) }}
                                    sx={{
                                        '& h1, & h2, & h3': {
                                            color: 'primary.main',
                                            marginTop: 2,
                                            marginBottom: 1,
                                        },
                                        '& h1': { fontSize: '1.5rem' },
                                        '& h2': { fontSize: '1.3rem' },
                                        '& h3': { fontSize: '1.1rem' },
                                        '& p': {
                                            marginBottom: 1,
                                            lineHeight: 1.6,
                                        },
                                        '& ul, & ol': {
                                            paddingLeft: 2,
                                            marginBottom: 1,
                                        },
                                        '& li': {
                                            marginBottom: 0.5,
                                        },
                                        '& strong': {
                                            fontWeight: 600,
                                            color: 'text.primary',
                                        },
                                        '& em': {
                                            fontStyle: 'italic',
                                            color: 'text.secondary',
                                        },
                                    }}
                                />
                            </Paper>
                        </Box>
                    )}

                    {!summaryData && !summaryLoading && !summaryError && (
                        <Box sx={{ textAlign: 'center', py: 4 }}>
                            <Typography color="text.secondary">
                                {t('summary_will_be_generated')}
                            </Typography>
                        </Box>
                    )}
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDialog}>
                        {t('close')}
                    </Button>
                </DialogActions>
            </Dialog>
        </Container>
    );
};

export default DieticianPage; 