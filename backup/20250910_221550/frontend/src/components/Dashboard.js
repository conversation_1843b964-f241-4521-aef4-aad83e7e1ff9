import React, { useContext } from 'react';
import { useView } from '../context/ViewContext';
import { Typography, Container, Paper, Box } from '@mui/material';
import { useLanguage } from '../context/LanguageContext';
import Appointments from './Appointments';
import Prescriptions from './Prescriptions';
import LabReports from './LabReports';
import Immunizations from './Immunizations';
import Consultations from './Consultations';

const Dashboard = ({ viewedDemographic }) => {
    const { loggedInUser } = useView();
    const { t } = useLanguage();

    // Use viewedDemographic if available (viewing family member), otherwise use logged-in user's demographic
    const currentDemographic = viewedDemographic || loggedInUser?.demographic;

    if (!currentDemographic) {
        return <Typography>Please link your account to a patient record or select a family member.</Typography>;
    }

    return (
        <Box sx={{ mt: 2 }}>
            <Typography variant="h5" gutterBottom>
                {currentDemographic.first_name} {currentDemographic.last_name}'s Dashboard
            </Typography>
            {/* Render existing components */}
            <Appointments viewedDemographic={currentDemographic} />
            <Prescriptions viewedDemographic={currentDemographic} />
            <Consultations viewedDemographic={currentDemographic} />
            <LabReports viewedDemographic={currentDemographic} />
            <Immunizations viewedDemographic={currentDemographic} />
            {/* Add other dashboard components as needed */}
        </Box>
    );
};

export default Dashboard; 