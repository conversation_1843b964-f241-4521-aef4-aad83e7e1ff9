const aiService = require('../utils/aiService');
const MedicalPromptTemplates = require('../prompts/medicalPrompts');
const PrescriptionExplanationDAO = require('../dao/prescriptionExplanationDAO');
const PrescriptionService = require('./prescriptionService');
const { createLogger } = require('../utils/logger');
const { ValidationError, AuthorizationError } = require('../utils/errors');

const logger = createLogger('PrescriptionExplanationService');

class PrescriptionExplanationService {
    /**
     * 使用AI解释处方内容 - 重构版本
     * @param {Object} prescription - 处方对象
     * @param {string} language - 语言 ('zh' 或 'en')
     * @returns {Promise<Object>} 包含AI解释的对象
     */
    static async explainWithAI(prescription, language = 'zh') {
        try {
            logger.info(`[AI Service] Starting prescription explanation for prescription ${prescription.id}`);

            // 构建提示数据
            const promptData = {
                id: prescription.id,
                datePrescribed: prescription.datePrescribed,
                providerName: prescription.providerName || '未知医生',
                details: prescription.details || '无详细信息',
                comments: prescription.comments || '无备注'
            };

            // 使用模板构建提示词
            const prompt = MedicalPromptTemplates.buildPrompt('prescription', language, promptData);

            // 调用统一AI服务
            const result = await aiService.generatePrescriptionExplanation(prompt, prescription.id, language);

            if (result.success) {
                logger.info(`[AI Service] Successfully generated explanation for prescription ${prescription.id}`);
                
                // 保存到数据库
                await PrescriptionExplanationDAO.createOrUpdate(
                    prescription.id,
                    result.content,
                    language
                );

                return { success: true, explanation: result.content };
            } else {
                logger.error(`[AI Service] Failed to generate explanation for prescription ${prescription.id}:`, result.error);
                return { success: false, message: result.error };
            }

        } catch (error) {
            logger.error('[AI Service] Error in explainWithAI:', error);
            return { success: false, message: 'Failed to generate AI explanation', error: error.message };
        }
    }

    /**
     * 获取处方解释，如果不存在则生成
     * @param {Object} prescription - 处方对象
     * @param {string} language - 语言 ('zh' 或 'en')
     * @returns {Promise<Object>} 处方解释对象
     */
    static async getOrCreateExplanation(prescription, language = 'zh') {
        try {
            // 先查找是否有已有的解释
            const existingExplanation = await PrescriptionExplanationDAO.getByScriptNo(prescription.id, language);

            if (existingExplanation) {
                logger.info(`Found existing explanation for prescription ${prescription.id} in ${language}`);
                return { success: true, explanation: existingExplanation.explanation };
            }

            // 如果没有，生成新的解释
            logger.info(`No existing explanation found, generating new one for prescription ${prescription.id}`);
            return await this.explainWithAI(prescription, language);
        } catch (error) {
            logger.error('Error in getOrCreateExplanation:', error);
            return { success: false, message: 'Failed to get or create explanation', error: error.message };
        }
    }
}

module.exports = PrescriptionExplanationService; 