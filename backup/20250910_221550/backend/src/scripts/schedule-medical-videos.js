/**
 * 定时调度程序 - 自动搜索医学视频
 * 
 * 此脚本会定期执行auto-fetch-medical-videos.js脚本，
 * 实现自动搜索和添加高质量医学科普视频的功能
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const cron = require('node-cron');

// 脚本路径
const scriptPath = path.join(__dirname, 'auto-fetch-medical-videos.js');

// 日志目录
const logDir = path.join(__dirname, '../../../logs');
if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
}

/**
 * 运行自动搜索视频脚本
 */
function runAutoFetchScript() {
    console.log('开始执行自动搜索医学视频脚本...');

    // 创建日志文件
    const date = new Date();
    const logFileName = `medical-videos-${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}-${String(date.getHours()).padStart(2, '0')}-${String(date.getMinutes()).padStart(2, '0')}.log`;
    const logFilePath = path.join(logDir, logFileName);
    const logStream = fs.createWriteStream(logFilePath, { flags: 'a' });

    // 写入日志头
    logStream.write(`===== 自动搜索医学视频任务开始 - ${new Date().toISOString()} =====\n`);

    // 运行子进程
    const child = spawn('node', [scriptPath], {
        env: process.env
    });

    // 输出重定向到日志
    child.stdout.pipe(logStream);
    child.stderr.pipe(logStream);

    // 进程事件处理
    child.on('exit', (code) => {
        const message = `\n===== 自动搜索医学视频任务结束 - ${new Date().toISOString()} - 退出代码: ${code} =====\n\n`;
        logStream.write(message);
        logStream.end();

        if (code === 0) {
            console.log(`自动搜索医学视频任务完成，退出代码 ${code}，日志保存到 ${logFilePath}`);
        } else {
            console.error(`自动搜索医学视频任务失败，退出代码 ${code}，查看日志 ${logFilePath}`);
        }
    });

    // 错误处理
    child.on('error', (err) => {
        logStream.write(`\n===== 进程启动错误: ${err.message} =====\n`);
        logStream.end();
        console.error(`启动子进程错误: ${err.message}`);
    });
}

/**
 * 主函数 - 设置定时任务
 */
function main() {
    console.log('启动医学视频自动搜索调度器...');

    // 设置定时任务 - 每天凌晨3点执行
    cron.schedule('0 3 * * *', () => {
        console.log(`定时任务触发 - ${new Date().toISOString()}`);
        runAutoFetchScript();
    });

    console.log('调度器已启动，等待定时任务执行...');
    console.log('下次执行时间将是今晚凌晨3点');

    // 初次运行 - 立即执行一次
    console.log('立即执行一次初始化搜索...');
    runAutoFetchScript();
}

// 执行主函数
if (require.main === module) {
    main();
}

module.exports = { runAutoFetchScript }; 