import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useLanguage } from '../context/LanguageContext';
import {
    Container,
    Paper,
    TextField,
    Button,
    Typography,
    Link,
    Grid,
    Box,
    Divider,
    Alert,
    Collapse,
    IconButton,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogContentText,
    DialogActions,
    InputAdornment,
    CircularProgress,
    RadioGroup,
    Radio,
    FormControlLabel,
    Snackbar,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import CloseIcon from '@mui/icons-material/Close';
import SearchIcon from '@mui/icons-material/Search';
import axios from 'axios';

const StyledPaper = styled(Paper)(({ theme }) => ({
    marginTop: theme.spacing(8),
    padding: theme.spacing(4),
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
}));

const StyledForm = styled('form')(({ theme }) => ({
    width: '100%',
    marginTop: theme.spacing(1),
}));

const StyledButton = styled(Button)(({ theme }) => ({
    margin: theme.spacing(3, 0, 2),
}));

const Login = () => {
    const [formData, setFormData] = useState({
        email: '',
        password: '',
        verificationCode: '',
    });
    const [error, setError] = useState('');
    const [infoMessage, setInfoMessage] = useState('');
    const [showInfoAlert, setShowInfoAlert] = useState(false);
    const [isCheckingEmail, setIsCheckingEmail] = useState(false);
    const [foundExistingPatient, setFoundExistingPatient] = useState(false);
    const [linkDialogOpen, setLinkDialogOpen] = useState(false);
    const [linkPassword, setLinkPassword] = useState('');
    const [confirmLinkPassword, setConfirmLinkPassword] = useState('');
    const [linkError, setLinkError] = useState('');
    const [patientRecords, setPatientRecords] = useState([]);
    const [selectedPatientId, setSelectedPatientId] = useState('');
    const [multipleRecordsStep, setMultipleRecordsStep] = useState(false);
    const [codeSent, setCodeSent] = useState(false);
    const [isVerified, setIsVerified] = useState(false);
    const [sendingCode, setSendingCode] = useState(false);
    const [verifyingCode, setVerifyingCode] = useState(false);
    const [showCodeAlert, setShowCodeAlert] = useState(false);
    const [codeAlertMessage, setCodeAlertMessage] = useState('');
    const navigate = useNavigate();
    const location = useLocation();
    const { t } = useLanguage();

    const handleChange = (e) => {
        setFormData({
            ...formData,
            [e.target.name]: e.target.value,
        });
    };

    const sendVerificationCode = async (email) => {
        setSendingCode(true);
        try {
            const response = await axios.post('/api/auth/send-verification-code', { email });
            const data = response.data;

            setCodeSent(true);
            setCodeAlertMessage('Verification code sent to your email');
            setShowCodeAlert(true);
            return true;
        } catch (err) {
            console.error('Send verification code error:', err);
            setError(`Error sending verification code: ${err.response?.data?.message || err.message}`);
            return false;
        } finally {
            setSendingCode(false);
        }
    };

    const verifyEmailCode = async () => {
        if (!formData.verificationCode) {
            setError('Please enter the verification code');
            return false;
        }

        setVerifyingCode(true);
        try {
            const response = await axios.post('/api/auth/verify-email-code', {
                email: formData.email,
                code: formData.verificationCode
            });

            const data = response.data;

            setIsVerified(true);
            setCodeAlertMessage('Email verified successfully');
            setShowCodeAlert(true);
            return true;
        } catch (err) {
            console.error('Verify code error:', err);
            setError(`Error verifying code: ${err.response?.data?.message || err.message}`);
            return false;
        } finally {
            setVerifyingCode(false);
        }
    };

    const handleEmailCheck = async () => {
        if (!formData.email || !formData.email.includes('@')) {
            setError('Please enter a valid email address');
            return;
        }

        setIsCheckingEmail(true);
        setError('');
        setShowInfoAlert(false);
        setPatientRecords([]);
        setSelectedPatientId('');
        setMultipleRecordsStep(false);
        setCodeSent(false);
        setIsVerified(false);

        try {
            console.log('Checking email:', formData.email);
            console.log('Using API URL:', axios.defaults.baseURL);

            const response = await axios.post('/api/auth/check-email', { email: formData.email });

            console.log('Response status:', response.status);
            const data = response.data;
            console.log('Response data:', data);

            console.log('Email check result - exists:', data.exists, 'existsInOscar:', data.existsInOscar);

            const codeSent = await sendVerificationCode(formData.email);

            if (codeSent) {
                if (data.exists) {
                    console.log('Email exists in user_auth - proceeding with normal login');
                    setFoundExistingPatient(false);
                    setInfoMessage('');
                    setShowInfoAlert(false);
                } else if (data.existsInOscar) {
                    console.log('Email exists in Oscar but not in user_auth - offering to create account');
                    setFoundExistingPatient(true);

                    if (data.patientCount > 1 && data.patientRecords) {
                        console.log('Multiple patient records found:', data.patientCount);
                        setPatientRecords(data.patientRecords);
                        setMultipleRecordsStep(true);
                        setInfoMessage('Multiple patient records found with this email. Please select which patient you want to create an account for.');
                        setShowInfoAlert(true);
                    } else {
                        if (data.patientRecords && data.patientRecords.length > 0) {
                            setSelectedPatientId(data.patientRecords[0].demographic_no);
                        }
                        setInfoMessage('We found your email in our patient records. Would you like to create an online account?');
                        setShowInfoAlert(true);
                    }
                } else {
                    console.log('Email not found anywhere - suggesting to register');
                    setInfoMessage('Email not found. Please register if you are a new patient.');
                    setShowInfoAlert(true);
                }
            }
        } catch (err) {
            console.error('Email check error:', err);
            setError(`Error checking email: ${err.response?.data?.message || err.message}`);
        } finally {
            setIsCheckingEmail(false);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setError('');

        if (foundExistingPatient) {
            if (!isVerified) {
                if (codeSent) {
                    setError('Please verify your email before creating an account');
                    return;
                } else if (formData.email) {
                    const codeSent = await sendVerificationCode(formData.email);
                    if (!codeSent) {
                        return;
                    }
                    setError('Please verify your email before creating an account');
                    return;
                } else {
                    setError('Please enter your email address');
                    return;
                }
            }

            setLinkDialogOpen(true);
            return;
        }

        try {
            console.log('API URL:', process.env.REACT_APP_API_URL);
            console.log('Form data:', { ...formData, password: '[HIDDEN]' });

            const apiUrl = `${process.env.REACT_APP_API_URL}/api/auth/login`;
            console.log('Sending request to:', apiUrl);

            const response = await axios.post('/api/auth/login', formData);

            console.log('Response status:', response.status);
            const data = response.data;
            console.log('Response data:', { ...data, token: data.token ? '[HIDDEN]' : null });

            localStorage.setItem('token', data.token);
            localStorage.setItem('user', JSON.stringify({
                id: data.user.id,
                email: data.user.email,
                firstName: data.user.demographicInfo?.first_name,
                lastName: data.user.demographicInfo?.last_name,
                demographic_no: data.user.demographic_no,
                role: data.user.role
            }));
            console.log('Data stored in localStorage');

            console.log('Navigating to profile page...');
            navigate('/profile');
        } catch (err) {
            console.error('Login error:', err);
            setError(err.response?.data?.message || err.message || 'Failed to login. Please check your credentials and try again.');
        }
    };

    const handleLinkAccount = async () => {
        if (!isVerified) {
            if (!codeSent && formData.email) {
                await sendVerificationCode(formData.email);
            }
            setLinkError('Please verify your email before creating an account');
            return;
        }

        if (!linkPassword) {
            setLinkError('Password is required');
            return;
        }

        if (linkPassword !== confirmLinkPassword) {
            setLinkError('Passwords do not match');
            return;
        }

        setLinkError('');
        try {
            const payload = {
                email: formData.email,
                password: linkPassword
            };

            if (selectedPatientId) {
                payload.demographicNo = selectedPatientId;
            }

            console.log('Linking account with payload:', { ...payload, password: '[HIDDEN]' });

            const response = await axios.post('/api/auth/link-oscar-patient', payload);
            const data = response.data;

            localStorage.setItem('token', data.token);
            localStorage.setItem('user', JSON.stringify(data.user));

            setLinkDialogOpen(false);
            navigate('/profile');
        } catch (err) {
            console.error('Link account error:', err);
            setLinkError(err.response?.data?.message || err.message);
        }
    };

    const handlePatientSelect = (e) => {
        setSelectedPatientId(e.target.value);
    };

    const handleContinueWithSelectedPatient = () => {
        if (!selectedPatientId) {
            setError('Please select a patient record');
            return;
        }

        if (!isVerified) {
            if (!codeSent && formData.email) {
                sendVerificationCode(formData.email);
            }
            setError('Please verify your email before proceeding');
            return;
        }

        setMultipleRecordsStep(false);
        setLinkDialogOpen(true);
    };

    const checkVerificationStatus = () => {
        if (!isVerified && formData.email) {
            setError('Please verify your email before proceeding');
            if (!codeSent) {
                sendVerificationCode(formData.email);
            }
            return false;
        }
        return true;
    };

    return (
        <Container component="main" maxWidth="xs">
            <StyledPaper elevation={3}>
                <Typography component="h1" variant="h5">
                    Sign in
                </Typography>

                <Collapse in={showInfoAlert}>
                    <Alert
                        severity={foundExistingPatient ? "info" : "warning"}
                        action={
                            <IconButton
                                color="inherit"
                                size="small"
                                onClick={() => setShowInfoAlert(false)}
                            >
                                <CloseIcon fontSize="inherit" />
                            </IconButton>
                        }
                        sx={{ mt: 2, width: '100%' }}
                    >
                        {infoMessage}
                        {!foundExistingPatient && infoMessage.includes('register') && (
                            <Box sx={{ mt: 1 }}>
                                <Button
                                    size="small"
                                    variant="outlined"
                                    onClick={() => navigate('/register')}
                                >
                                    Register Now
                                </Button>
                            </Box>
                        )}
                    </Alert>
                </Collapse>

                {error && (
                    <Typography color="error" align="center" sx={{ mt: 2 }}>
                        {error}
                    </Typography>
                )}

                <StyledForm onSubmit={handleSubmit}>
                    <TextField
                        variant="outlined"
                        margin="normal"
                        required
                        fullWidth
                        id="email"
                        label="Email Address"
                        name="email"
                        autoComplete="email"
                        autoFocus
                        value={formData.email}
                        onChange={handleChange}
                    />

                    {codeSent && !isVerified && (
                        <TextField
                            variant="outlined"
                            margin="normal"
                            required
                            fullWidth
                            id="verificationCode"
                            label="Verification Code"
                            name="verificationCode"
                            value={formData.verificationCode}
                            onChange={handleChange}
                            helperText="A verification code has been sent to your email"
                            InputProps={{
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <Button
                                            onClick={verifyEmailCode}
                                            disabled={verifyingCode || !formData.verificationCode}
                                            color="primary"
                                            sx={{ minWidth: 'auto', p: '4px' }}
                                        >
                                            {verifyingCode ? <CircularProgress size={20} /> : 'Verify'}
                                        </Button>
                                    </InputAdornment>
                                ),
                            }}
                        />
                    )}

                    {isVerified && (
                        <Alert severity="success" sx={{ mt: 2, mb: 2 }}>
                            Email verified successfully
                        </Alert>
                    )}

                    {!foundExistingPatient && (
                        <TextField
                            variant="outlined"
                            margin="normal"
                            required
                            fullWidth
                            name="password"
                            label="Password"
                            type="password"
                            id="password"
                            autoComplete="current-password"
                            value={formData.password}
                            onChange={handleChange}
                        />
                    )}

                    <StyledButton
                        type="submit"
                        fullWidth
                        variant="contained"
                        color="primary"
                        disabled={isCheckingEmail || (codeSent && !isVerified)}
                    >
                        {foundExistingPatient ? 'Create Account' : 'Sign In'}
                    </StyledButton>

                    <StyledButton
                        fullWidth
                        variant="outlined"
                        color="secondary"
                        onClick={() => {
                            navigate('/email-verification?mode=link');
                        }}
                        sx={{ mt: 2 }}
                    >
                        Already Patient at MMC, Link Your Account
                    </StyledButton>

                    <StyledButton
                        fullWidth
                        variant="outlined"
                        color="primary"
                        onClick={() => {
                            navigate('/register');
                        }}
                        sx={{ mt: 2 }}
                    >
                        Register for New Patient
                    </StyledButton>

                    <Grid container justifyContent="flex-end" sx={{ mt: 2 }}>
                        <Grid item>
                            <Link href="/forgot-password" variant="body2">
                                Forgot password?
                            </Link>
                        </Grid>
                    </Grid>
                </StyledForm>
            </StyledPaper>

            <Snackbar
                open={showCodeAlert}
                autoHideDuration={6000}
                onClose={() => setShowCodeAlert(false)}
                message={codeAlertMessage}
            />

            {multipleRecordsStep && patientRecords.length > 0 && (
                <Dialog open={true} onClose={() => setMultipleRecordsStep(false)} fullWidth maxWidth="sm">
                    <DialogTitle>Select Your Patient Record</DialogTitle>
                    <DialogContent>
                        <DialogContentText>
                            We found multiple patient records with your email. Please select which one you want to create an account for:
                        </DialogContentText>
                        <RadioGroup
                            aria-label="patient-select"
                            name="patient-select"
                            value={selectedPatientId}
                            onChange={handlePatientSelect}
                        >
                            {patientRecords.map((patient) => (
                                <FormControlLabel
                                    key={patient.demographic_no}
                                    value={patient.demographic_no.toString()}
                                    control={<Radio />}
                                    label={`${patient.title || ''} ${patient.first_name} ${patient.last_name} (${patient.year_of_birth}-${patient.month_of_birth}-${patient.date_of_birth})`}
                                />
                            ))}
                        </RadioGroup>
                    </DialogContent>
                    <DialogActions>
                        <Button onClick={() => setMultipleRecordsStep(false)}>{t('cancel')}</Button>
                        <Button onClick={handleContinueWithSelectedPatient} variant="contained" color="primary">
                            Continue
                        </Button>
                    </DialogActions>
                </Dialog>
            )}

            <Dialog open={linkDialogOpen} onClose={() => setLinkDialogOpen(false)}>
                <DialogTitle>Create Your Online Account</DialogTitle>
                <DialogContent>
                    <DialogContentText>
                        We found your information in our medical records system. Please set a password to create your online account.
                    </DialogContentText>
                    <TextField
                        autoFocus
                        margin="dense"
                        id="linkPassword"
                        label="Password"
                        type="password"
                        fullWidth
                        variant="outlined"
                        value={linkPassword}
                        onChange={(e) => setLinkPassword(e.target.value)}
                        error={!!linkError}
                    />
                    <TextField
                        margin="dense"
                        id="confirmLinkPassword"
                        label="Confirm Password"
                        type="password"
                        fullWidth
                        variant="outlined"
                        value={confirmLinkPassword}
                        onChange={(e) => setConfirmLinkPassword(e.target.value)}
                        error={!!linkError}
                        helperText={linkError}
                    />
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setLinkDialogOpen(false)}>{t('cancel')}</Button>
                    <Button
                        onClick={handleLinkAccount}
                        variant="contained"
                        color="primary"
                        disabled={!isVerified}
                    >
                        Create Account
                    </Button>
                </DialogActions>
            </Dialog>
        </Container>
    );
};

export default Login; 