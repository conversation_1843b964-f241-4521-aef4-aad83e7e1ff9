import React, { useState, useEffect } from 'react';
import { API_URL } from '../utils/env';
import {
    Container,
    Paper,
    Typography,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Button,
    CircularProgress,
    Alert,
    Box,
    Grid,
    Card,
    CardContent,
    Chip,
    Divider,
    IconButton,
    Tooltip,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import VaccinesIcon from '@mui/icons-material/Vaccines';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import InfoIcon from '@mui/icons-material/Info';
import DownloadIcon from '@mui/icons-material/Download';
import ScheduleIcon from '@mui/icons-material/Schedule';
import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import CloseIcon from '@mui/icons-material/Close';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import { useView } from '../context/ViewContext';
import { useLanguage } from '../context/LanguageContext';
import {
    List,
    ListItem,
    ListItemText,
} from '@mui/material';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import MedicalServicesIcon from '@mui/icons-material/MedicalServices'; // Immunization type icon
import PersonIcon from '@mui/icons-material/Person'; // Provider icon
import EventIcon from '@mui/icons-material/Event'; // Next Due Date icon
import SwitchAccountIcon from '@mui/icons-material/SwitchAccount';
import { format } from 'date-fns';
import AIDisclaimer from './AIDisclaimer';

const StyledPaper = styled(Paper)(({ theme }) => ({
    marginTop: theme.spacing(3),
    padding: theme.spacing(3),
}));

// 简单的Markdown渲染
const renderMarkdown = (text) => {
    if (!text) return '';

    // 清理markdown代码块标记
    let cleanText = text.replace(/```markdown\s+/g, '').replace(/```\s*$/g, '');

    // 替换标题
    let formatted = cleanText.replace(/^# (.*$)/gm, '<h1>$1</h1>');
    formatted = formatted.replace(/^## (.*$)/gm, '<h2>$1</h2>');
    formatted = formatted.replace(/^### (.*$)/gm, '<h3>$1</h3>');

    // 替换列表
    formatted = formatted.replace(/^\* (.*$)/gm, '<li>$1</li>');
    formatted = formatted.replace(/^(\d+)\. (.*$)/gm, '<li>$2</li>');
    formatted = formatted.replace(/<\/li>\n<li>/g, '</li><li>');
    formatted = formatted.replace(/(?:<li>.*<\/li>\n)+/gs, function (match) {
        return match.startsWith('<li>1') || match.startsWith('<li>2')
            ? '<ol>' + match + '</ol>'
            : '<ul>' + match + '</ul>';
    });

    // 替换粗体和斜体
    formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>');

    // 替换段落
    formatted = formatted.replace(/(?:^|\n)(?!\<h|\<ul|\<ol)(.+)/g, '<p>$1</p>');

    return formatted;
};

const InfoItem = ({ icon, primary, secondary }) => (
    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
        {icon && <Box sx={{ mr: 1.5, color: 'text.secondary' }}>{icon}</Box>}
        <Box>
            {primary && <Typography variant="body2" color="text.secondary">{primary}</Typography>}
            {secondary && <Typography variant="body1" sx={{ fontWeight: 500, whiteSpace: 'pre-wrap' }}>{secondary}</Typography>}
        </Box>
    </Box>
);

const Immunizations = () => {
    const { t } = useLanguage();
    const {
        loggedInUser,
        viewingDemographicNo,
        setViewAs,
        isViewingOwnProfile,
        isLoading: contextLoading,
        error: contextError
    } = useView();

    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [immunizations, setImmunizations] = useState([]);

    // 添加疫苗解释相关状态
    const [selectedImm, setSelectedImm] = useState(null);
    const [explanationOpen, setExplanationOpen] = useState(false);
    const [explanationLoading, setExplanationLoading] = useState(false);
    const [explanationError, setExplanationError] = useState('');
    const [explanation, setExplanation] = useState('');
    const [explanations, setExplanations] = useState({});  // 存储所有疫苗的解释
    const [fetchingAllExplanations, setFetchingAllExplanations] = useState(false);

    // Format date string (handles full datetime or just date)
    const formatDate = (dateString) => {
        if (!dateString) return t('not_available');
        try {
            // Check if it's just a date (YYYY-MM-DD) or a full datetime
            const date = new Date(dateString);
            if (dateString.length === 10) { // Likely YYYY-MM-DD
                return format(date, 'yyyy-MM-dd');
            }
            // Assume full datetime string
            return format(date, 'yyyy-MM-dd HH:mm'); // Or just yyyy-MM-dd if time is not needed
        } catch (e) {
            console.error("Error formatting date:", dateString, e);
            return dateString; // Return original if formatting fails
        }
    };

    // 获取疫苗解释的函数
    const fetchExplanation = async (immunizationId) => {
        const token = localStorage.getItem('token');
        if (!token) {
            console.error('No authentication token');
            return null;
        }

        try {
            const response = await fetch(`${API_URL}/api/immunizations/explanation/${immunizationId}?language=${t.language || 'zh'}`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });

            const data = await response.json();

            if (!response.ok) {
                console.error('Error fetching explanation:', data.message);
                return null;
            }

            if (data.success) {
                return data.explanation;
            } else {
                console.error('API error:', data.message);
                return null;
            }
        } catch (err) {
            console.error('Exception fetching explanation:', err);
            return null;
        }
    };

    // 获取所有疫苗解释
    const fetchAllExplanations = async (immList) => {
        if (!immList || immList.length === 0 || fetchingAllExplanations) return;

        setFetchingAllExplanations(true);

        try {
            // 并行请求所有疫苗的解释
            const explanationPromises = immList.map(imm => fetchExplanation(imm.id));
            const explanationResults = await Promise.all(explanationPromises);

            // 将结果整合到一个对象中
            const newExplanations = {};
            immList.forEach((imm, index) => {
                if (explanationResults[index]) {
                    newExplanations[imm.id] = explanationResults[index];
                }
            });

            setExplanations(newExplanations);
        } catch (err) {
            console.error('Error fetching all explanations:', err);
        } finally {
            setFetchingAllExplanations(false);
        }
    };

    useEffect(() => {
        const fetchImmunizations = async () => {
            if (!viewingDemographicNo) {
                console.log('Immunizations: Waiting for viewingDemographicNo...');
                setLoading(contextLoading); // Reflect context loading state
                return;
            }

            console.log(`Immunizations: Fetching data for demographic_no: ${viewingDemographicNo}`);
            setLoading(true);
            setError('');
            setImmunizations([]); // Clear previous results
            setExplanations({}); // 清除之前的解释

            const token = localStorage.getItem('token');
            if (!token) {
                setError(t('not_authenticated'));
                setLoading(false);
                return;
            }

            try {
                const response = await fetch(`${API_URL}/api/immunizations/${viewingDemographicNo}`, {
                    headers: {
                        Authorization: `Bearer ${token}`,
                    },
                });

                const data = await response.json();

                if (!response.ok) {
                    // Handle permission denied specifically
                    if (response.status === 403 && loggedInUser) {
                        setViewAs(loggedInUser.demographic_no); // Switch back to self
                    }
                    throw new Error(data.message || t('fetch_failed'));
                }

                if (data.success) {
                    console.log('Immunizations data received:', data.immunizations);
                    const immList = data.immunizations || [];
                    setImmunizations(immList);

                    // 先显示疫苗列表，然后立即开始获取解释
                    setLoading(false);

                    // 异步加载所有解释
                    fetchAllExplanations(immList);
                } else {
                    throw new Error(data.message || t('api_error'));
                }

            } catch (err) {
                console.error('Error fetching immunizations:', err);
                setError(err.message || t('fetch_error'));
                setLoading(false);
            }
        };

        fetchImmunizations();
    }, [viewingDemographicNo, contextLoading, loggedInUser, setViewAs, t]);

    const handleSwitchBack = () => {
        if (loggedInUser?.demographic_no) {
            setViewAs(loggedInUser.demographic_no);
        }
    }

    // 处理查看疫苗解释的请求
    const handleViewExplanation = async (imm) => {
        setSelectedImm(imm);
        setExplanationOpen(true);

        // 如果已经有缓存的解释，直接显示
        if (explanations[imm.id]) {
            setExplanation(explanations[imm.id]);
            return;
        }

        // 否则加载解释
        setExplanationLoading(true);
        setExplanationError('');
        setExplanation('');

        const token = localStorage.getItem('token');
        if (!token) {
            setExplanationError(t('not_authenticated'));
            setExplanationLoading(false);
            return;
        }

        try {
            // 获取疫苗解释
            const response = await fetch(`${API_URL}/api/immunizations/explanation/${imm.id}?language=${t.language || 'zh'}`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || t('fetch_failed'));
            }

            if (data.success) {
                setExplanation(data.explanation);
                // 更新缓存
                setExplanations(prev => ({
                    ...prev,
                    [imm.id]: data.explanation
                }));
            } else {
                throw new Error(data.message || t('api_error'));
            }
        } catch (err) {
            console.error('Error fetching immunization explanation:', err);
            setExplanationError(err.message || t('fetch_error'));
        } finally {
            setExplanationLoading(false);
        }
    };

    // 关闭解释对话框
    const handleCloseExplanation = () => {
        setExplanationOpen(false);
        setSelectedImm(null);
    };

    // Combine loading and error states
    const isLoading = loading || contextLoading;
    const displayError = error || contextError;

    return (
        <Container maxWidth="lg">
            {!isViewingOwnProfile && loggedInUser && (
                <Alert
                    severity="info"
                    sx={{ mt: 2, mb: 1 }}
                    action={
                        <Button
                            color="inherit"
                            size="small"
                            onClick={handleSwitchBack}
                            startIcon={<SwitchAccountIcon />}
                        >
                            {t('switch_back')}
                        </Button>
                    }
                >
                    {t('viewing_family_immunizations')}
                </Alert>
            )}

            <StyledPaper elevation={3}>
                <Typography variant="h4" gutterBottom align="center">
                    {t('immunizations')}
                </Typography>

                {contextLoading && (
                    <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
                        <CircularProgress />
                    </Box>
                )}

                {displayError && (
                    <Alert severity="error" sx={{ my: 2 }}>
                        {typeof displayError === 'string' ? displayError : JSON.stringify(displayError)}
                    </Alert>
                )}

                {!contextLoading && !displayError && (
                    <List disablePadding>
                        {loading ? (
                            <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
                                <CircularProgress />
                            </Box>
                        ) : immunizations.length > 0 ? (
                            immunizations.map((imm) => (
                                <React.Fragment key={imm.id}>
                                    <Card variant="outlined" sx={{ my: 1.5 }}>
                                        <CardContent>
                                            <InfoItem
                                                icon={<MedicalServicesIcon fontSize="small" />}
                                                primary={t('vaccine_type')}
                                                secondary={imm.type || t('not_available')}
                                            />
                                            <InfoItem
                                                icon={<CalendarMonthIcon fontSize="small" />}
                                                primary={t('date_administered')}
                                                secondary={formatDate(imm.immunizationDate)}
                                            />
                                            {imm.providerName && (
                                                <InfoItem
                                                    icon={<PersonIcon fontSize="small" />}
                                                    primary={t('administered_by')}
                                                    secondary={imm.providerName}
                                                />
                                            )}
                                            {imm.nextDueDate && (
                                                <InfoItem
                                                    icon={<EventIcon fontSize="small" />}
                                                    primary={t('next_due_date')}
                                                    secondary={formatDate(imm.nextDueDate)}
                                                />
                                            )}

                                            {/* 显示AI解释（如果有） */}
                                            {explanations[imm.id] && (
                                                <Box sx={{ mt: 2, pt: 2, borderTop: '1px solid #eee' }}>
                                                    <AIDisclaimer compact={true} sx={{ mb: 1 }} />
                                                    <Typography variant="subtitle2" sx={{ mb: 1, color: 'text.secondary' }}>
                                                        {t('ai_generated_explanation') || 'AI生成的解释（仅供参考）'}
                                                    </Typography>
                                                    <Typography
                                                        variant="body1"
                                                        component="div"
                                                        sx={{
                                                            whiteSpace: 'pre-wrap',
                                                            lineHeight: 1.6,
                                                            '& h1': { fontSize: '1.4rem', mb: 1.5, fontWeight: 600 },
                                                            '& h2': { fontSize: '1.2rem', mb: 1, mt: 1.5, fontWeight: 600 },
                                                            '& h3': { fontSize: '1.1rem', mb: 0.8, mt: 1.2, fontWeight: 600 },
                                                            '& p': { mb: 1 },
                                                            '& ul, & ol': { mb: 1.5, pl: 2 },
                                                            '& li': { mb: 0.5 },
                                                            '& strong': { fontWeight: 600 },
                                                            '& em': { fontStyle: 'italic' }
                                                        }}
                                                        dangerouslySetInnerHTML={{ __html: renderMarkdown(explanations[imm.id]) }}
                                                    />
                                                </Box>
                                            )}

                                            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 1 }}>
                                                <Button
                                                    variant="outlined"
                                                    size="small"
                                                    startIcon={<SmartToyIcon />}
                                                    onClick={() => handleViewExplanation(imm)}
                                                >
                                                    {explanations[imm.id] ? (t('view_full_explanation') || '查看完整解释') : (t('view_explanation') || '查看解释')}
                                                </Button>
                                            </Box>
                                        </CardContent>
                                    </Card>
                                </React.Fragment>
                            ))
                        ) : (
                            <Typography variant="body1" align="center" sx={{ my: 3 }}>
                                {t('no_immunizations')}
                            </Typography>
                        )}
                    </List>
                )}
            </StyledPaper>

            {/* 疫苗解释对话框 */}
            <Dialog
                open={explanationOpen}
                onClose={handleCloseExplanation}
                fullWidth
                maxWidth="md"
            >
                <DialogTitle sx={{ m: 0, p: 2 }}>
                    {t('immunization_explanation') || '疫苗解释'}
                    <IconButton
                        aria-label="close"
                        onClick={handleCloseExplanation}
                        sx={{
                            position: 'absolute',
                            right: 8,
                            top: 8,
                            color: (theme) => theme.palette.grey[500],
                        }}
                    >
                        <CloseIcon />
                    </IconButton>
                </DialogTitle>

                <DialogContent dividers>
                    <AIDisclaimer sx={{ mb: 3 }} />
                    {explanationLoading ? (
                        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 4 }}>
                            <CircularProgress sx={{ mr: 2 }} />
                            <Typography>
                                {t('loading_explanation') || '正在加载解释...'}
                            </Typography>
                        </Box>
                    ) : explanationError ? (
                        <Alert severity="error" sx={{ mt: 2 }}>
                            {explanationError}
                        </Alert>
                    ) : explanation ? (
                        <Box
                            sx={{
                                whiteSpace: 'pre-wrap',
                                lineHeight: 1.6,
                                '& h1': { fontSize: '1.4rem', mb: 1.5, fontWeight: 600 },
                                '& h2': { fontSize: '1.2rem', mb: 1, mt: 1.5, fontWeight: 600 },
                                '& h3': { fontSize: '1.1rem', mb: 0.8, mt: 1.2, fontWeight: 600 },
                                '& p': { mb: 1 },
                                '& ul, & ol': { mb: 1.5, pl: 2 },
                                '& li': { mb: 0.5 },
                                '& strong': { fontWeight: 600 },
                                '& em': { fontStyle: 'italic' }
                            }}
                            dangerouslySetInnerHTML={{ __html: renderMarkdown(explanation) }}
                        />
                    ) : (
                        <Typography variant="body1" color="text.secondary">
                            {t('no_explanation_available') || '暂无解释'}
                        </Typography>
                    )}
                </DialogContent>

                <DialogActions>
                    <Button onClick={handleCloseExplanation}>
                        {t('close') || '关闭'}
                    </Button>
                </DialogActions>
            </Dialog>
        </Container>
    );
};

export default Immunizations; 