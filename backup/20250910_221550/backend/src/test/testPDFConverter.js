const { spawn } = require('child_process');
const fs = require('fs').promises;
const path = require('path');

async function testPyMuPDF4LLM() {
    console.log('Testing PyMuPDF4LLM installation...');
    
    try {
        // 测试Python和PyMuPDF4LLM是否正确安装
        const pythonScript = `
import sys
import pymupdf4llm

try:
    print("PyMuPDF4LLM version:", pymupdf4llm.__version__ if hasattr(pymupdf4llm, '__version__') else 'Unknown')
    print("Python version:", sys.version)
    print("PyMuPDF4LLM import successful!")
except Exception as e:
    print(f"Error: {str(e)}", file=sys.stderr)
    sys.exit(1)
`;

        const scriptPath = '/tmp/test_pymupdf.py';
        await fs.writeFile(scriptPath, pythonScript);

        const result = await executePythonScript(scriptPath);
        console.log('Test result:', result);
        
        // 清理
        await fs.unlink(scriptPath).catch(() => {});
        
        return true;
    } catch (error) {
        console.error('Test failed:', error.message);
        return false;
    }
}

function executePythonScript(scriptPath) {
    return new Promise((resolve, reject) => {
        const python = spawn('python3', [scriptPath]);
        
        let stdout = '';
        let stderr = '';

        python.stdout.on('data', (data) => {
            stdout += data.toString();
        });

        python.stderr.on('data', (data) => {
            stderr += data.toString();
        });

        python.on('close', (code) => {
            if (code !== 0) {
                reject(new Error(`Python script failed: ${stderr}`));
            } else {
                resolve(stdout.trim());
            }
        });

        python.on('error', (error) => {
            reject(new Error(`Failed to spawn Python process: ${error.message}`));
        });
    });
}

// 如果直接运行此脚本
if (require.main === module) {
    testPyMuPDF4LLM().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = { testPyMuPDF4LLM }; 