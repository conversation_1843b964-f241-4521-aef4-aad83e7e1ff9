const express = require('express');
const router = express.Router();
const formDieticianController = require('../controllers/formDieticianController');
const auth = require('../middleware/auth');

// GET /api/formDietician/summary/:commentId - AI summary for specific comment (must be before dynamic route)
router.get('/summary/:commentId', auth, formDieticianController.getDieticianCommentSummary);

// GET /api/formDietician/:demographic_no
router.get('/:demographic_no', auth, formDieticianController.getDieticianCommentsByDemographicNo);

module.exports = router; 