const path = require('path');
const fs = require('fs');

console.log('正在测试健康小贴士生成功能...');

// 测试环境变量是否配置
const envFile = path.join(__dirname, '../../.env');
if (!fs.existsSync(envFile)) {
    console.log('警告: .env文件不存在，创建默认配置文件');
    fs.writeFileSync(
        envFile,
        `OPENROUTER_API_KEY=your_api_key_here
LOCAL_TIPS_PATH=${path.join(__dirname, '../../documents')}
`
    );
    console.log(`请编辑 ${envFile} 文件并设置有效的OPENROUTER_API_KEY`);
    process.exit(1);
}

// 检查生成脚本是否存在
const generateScriptPath = path.join(__dirname, 'generateDailyTip.js');
if (!fs.existsSync(generateScriptPath)) {
    console.error(`错误: 生成脚本不存在: ${generateScriptPath}`);
    process.exit(1);
}

// 尝试生成小贴士
console.log('调用生成脚本...');
const { exec } = require('child_process');
exec(`node ${generateScriptPath}`, (error, stdout, stderr) => {
    if (error) {
        console.error(`执行错误: ${error.message}`);
        console.log('请检查您的OPENROUTER_API_KEY是否正确设置');
        process.exit(1);
    }
    if (stderr) {
        console.error(`stderr: ${stderr}`);
    }
    console.log(`生成结果: ${stdout}`);

    // 验证文件是否创建
    const docsPath = process.env.LOCAL_TIPS_PATH || path.join(__dirname, '../../documents');
    const files = fs.readdirSync(docsPath);
    const dailyTipFiles = files.filter(f => f.startsWith('daily-tip-'));

    if (dailyTipFiles.length > 0) {
        console.log('成功! 以下文件已创建:');
        dailyTipFiles.forEach(file => console.log(`- ${file}`));

        // 显示最新文件内容
        const latestFile = dailyTipFiles.sort().pop();
        const filePath = path.join(docsPath, latestFile);
        console.log(`\n最新小贴士内容 (${latestFile}):`);
        console.log('-----------------------------------');
        console.log(fs.readFileSync(filePath, 'utf8').substring(0, 500) + '...');
        console.log('-----------------------------------');
    } else {
        console.error('错误: 没有找到生成的文件');
        process.exit(1);
    }

    console.log('\n测试完成!');
    console.log('如要配置自动生成，请运行: npm run setup-daily-tips');
}); 