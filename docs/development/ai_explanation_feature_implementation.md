# AI 解释功能实现流程

本文档记录了为处方（Prescriptions）和疫苗接种（Immunizations）添加 AI 解释功能的完整实现流程，方便未来参考和扩展到其他功能模块。

## 功能概述

AI 解释功能允许系统自动生成医疗信息（如处方和疫苗接种）的通俗解释，帮助患者更好地理解专业医疗信息。主要特点：

- 使用 Google Gemini AI 生成解释内容
- 支持中英文双语解释
- 解释内容以 Markdown 格式呈现
- 解释结果缓存在数据库中避免重复生成
- 用户界面默认显示解释内容

## 1. 数据库设计

### 1.1 处方解释表（prescription_explanations）

```sql
CREATE TABLE prescription_explanations (
    id INT(11) NOT NULL AUTO_INCREMENT,
    script_no INT(11) NOT NULL,
    explanation TEXT NOT NULL,
    language VARCHAR(10) NOT NULL DEFAULT 'zh',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY unique_script_language (script_no, language)
) ENGINE=Aria DEFAULT CHARSET=utf8mb4;
```

### 1.2 疫苗接种解释表（immunization_explanations）

```sql
CREATE TABLE immunization_explanations (
    id INT(11) NOT NULL AUTO_INCREMENT,
    immunization_id INT(11) NOT NULL,
    explanation TEXT NOT NULL,
    language VARCHAR(10) NOT NULL DEFAULT 'zh',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY unique_immunization_language (immunization_id, language)
) ENGINE=Aria DEFAULT CHARSET=utf8mb4;
```

## 2. 后端实现

### 2.1 数据访问对象（DAO）

创建 DAO 类来处理数据库操作：

- `prescriptionExplanationDAO.js`
- `immunizationExplanationDAO.js`

DAO 类包含以下方法：
- `getByXXXId(id, language)` - 根据ID和语言获取解释
- `createOrUpdate(id, explanation, language)` - 创建或更新解释
- `delete(id, language)` - 删除解释

### 2.2 服务层（Service）

创建服务类处理业务逻辑：

- `prescriptionExplanationService.js`
- `immunizationExplanationService.js`

服务类包含以下方法：
- `explainWithAI(item, language)` - 调用AI API生成解释
- `getOrCreateExplanation(item, language)` - 获取已有解释或生成新解释

AI 解释实现细节：
- 使用 Gemini API 生成解释
- 根据语言选择不同的提示语
- 将医疗专业信息转换为通俗易懂的语言
- 处理API错误和异常情况

### 2.3 控制器（Controller）

在控制器中添加解释相关端点：

- `prescriptionController.js` 添加 `getPrescriptionExplanation` 方法
- `immunizationController.js` 添加 `getImmunizationExplanation` 方法

控制器负责：
- 验证请求参数
- 检查用户权限
- 调用服务层获取解释
- 返回适当的响应

### 2.4 路由（Routes）

定义API端点路由：

```javascript
// 处方解释路由
router.get('/explanation/:scriptNo', authenticate, prescriptionController.getPrescriptionExplanation);

// 疫苗接种解释路由
router.get('/explanation/:immunizationId', authenticate, immunizationController.getImmunizationExplanation);
```

## 3. 前端实现

### 3.1 UI组件扩展

修改相关组件添加解释功能：

- `Prescriptions.js`
- `Immunizations.js`

主要更改：
- 添加状态变量存储解释内容
- 添加对话框组件显示完整解释
- 实现Markdown渲染函数
- 调整UI布局在信息卡片中显示解释内容

### 3.2 Markdown渲染函数

```javascript
const renderMarkdown = (text) => {
    if (!text) return '';

    // 清理markdown代码块标记
    let cleanText = text.replace(/```markdown\s+/g, '').replace(/```\s*$/g, '');

    // 替换标题
    let formatted = cleanText.replace(/^# (.*$)/gm, '<h1>$1</h1>');
    formatted = formatted.replace(/^## (.*$)/gm, '<h2>$1</h2>');
    formatted = formatted.replace(/^### (.*$)/gm, '<h3>$1</h3>');

    // 替换列表
    formatted = formatted.replace(/^\* (.*$)/gm, '<li>$1</li>');
    formatted = formatted.replace(/^(\d+)\. (.*$)/gm, '<li>$2</li>');
    formatted = formatted.replace(/<\/li>\n<li>/g, '</li><li>');
    formatted = formatted.replace(/(?:<li>.*<\/li>\n)+/gs, function (match) {
        return match.startsWith('<li>1') || match.startsWith('<li>2')
            ? '<ol>' + match + '</ol>'
            : '<ul>' + match + '</ul>';
    });

    // 替换粗体和斜体
    formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>');

    // 替换段落
    formatted = formatted.replace(/(?:^|\n)(?!\<h|\<ul|\<ol)(.+)/g, '<p>$1</p>');

    return formatted;
};
```

### 3.3 获取解释逻辑

```javascript
// 获取解释函数
const fetchExplanation = async (id) => {
    const token = localStorage.getItem('token');
    if (!token) {
        console.error('No authentication token');
        return null;
    }

    try {
        const response = await fetch(`${API_URL}/api/[resource_type]/explanation/${id}?language=${t.language || 'zh'}`, {
            headers: {
                Authorization: `Bearer ${token}`,
            },
        });

        const data = await response.json();

        if (!response.ok) {
            console.error('Error fetching explanation:', data.message);
            return null;
        }

        if (data.success) {
            return data.explanation;
        } else {
            console.error('API error:', data.message);
            return null;
        }
    } catch (err) {
        console.error('Exception fetching explanation:', err);
        return null;
    }
};
```

### 3.4 加载时获取所有解释

```javascript
useEffect(() => {
    const fetchData = async () => {
        // ... 获取主要数据（处方或疫苗接种）...
        
        if (data.success) {
            const itemList = data.items || [];
            setItems(itemList);
            
            // 为每个项目获取解释
            const explanationsMap = {};
            for (const item of itemList) {
                const explanation = await fetchExplanation(item.id);
                if (explanation) {
                    explanationsMap[item.id] = explanation;
                }
            }
            setExplanations(explanationsMap);
        }
    };

    fetchData();
}, [demographicNo, language]);
```

## 4. 部署流程

### 4.1 数据库迁移

1. 创建SQL迁移文件
2. 将SQL文件复制到数据库容器
3. 执行SQL脚本创建表

```bash
# 将SQL文件复制到数据库容器
docker cp ./path/to/migration.sql [db_container]:/tmp/

# 在容器中执行SQL
docker exec -it [db_container] bash -c "mysql -u[user] -p[password] [db_name] < /tmp/migration.sql"

# 验证表是否创建成功
docker exec -it [db_container] bash -c "mysql -u[user] -p[password] [db_name] -e 'SHOW TABLES LIKE \"table_name\"'"
```

### 4.2 应用更新

1. 更新后端代码
2. 更新前端代码
3. 重启相关服务

```bash
# 重启后端服务应用更改
docker-compose restart backend
```

## 5. 扩展到其他功能

要将AI解释功能扩展到其他医疗信息模块，需要：

1. 创建新的数据库表存储解释内容
2. 实现相应的DAO、Service和Controller类
3. 添加新的API路由
4. 修改前端组件展示解释内容

## 6. 注意事项

- 确保有适当的权限检查，避免用户访问非自己的医疗解释
- 处理API调用限制和错误情况
- 考虑缓存策略减少AI调用成本
- 确保解释内容的准确性和安全性
- 明确标识AI生成内容，避免用户混淆

## 7. 环境配置

需要在环境变量中配置：

```
GEMINI_API_KEY=your_api_key_here
GEMINI_MODEL=gemini-2.0-flash  # 或其他适合的模型
```

---

此文档提供了AI解释功能的完整实现流程参考，可以用于维护现有功能或扩展到其他医疗信息模块。 