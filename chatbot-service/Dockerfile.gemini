# Use an official Node.js runtime as a parent image
FROM node:20-alpine

# Set the working directory in the container
WORKDIR /usr/src/app

# Copy package.json and package-lock.json (if available)
COPY package*.json ./

# Install app dependencies
RUN npm install

# Create directory for health articles
RUN mkdir -p /usr/src/app/documents/health

# Bundle app source
COPY . .

# Run the Gemini crawler
CMD ["node", "health-news-crawler-gemini.js"] 