html, body, #root {
  /* width: 100%; Let CssBaseline and MUI handle this */
  /* height: 100%; Let CssBaseline and MUI handle this */
  margin: 0;
  padding: 0;
  /* box-sizing: border-box; CssBaseline handles this */
  overflow-x: hidden; /* This is still useful as a strong guard */
  /* background: #f5f5f7; CssBaseline will set this from theme.palette.background.default */
}

/* A common addition to ensure all elements inherit box-sizing */
*,
*::before,
*::after {
  box-sizing: inherit;
} 