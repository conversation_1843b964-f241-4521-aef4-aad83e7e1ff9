import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import {
    AppBar,
    Box,
    Toolbar,
    IconButton,
    Typography,
    Menu,
    Container,
    Avatar,
    Button,
    Tooltip,
    MenuItem,
    Drawer,
    List,
    ListItem,
    ListItemButton,
    ListItemIcon,
    ListItemText,
    Divider,
    Badge,
    useTheme,
    alpha,
    SwipeableDrawer
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import PersonIcon from '@mui/icons-material/Person';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import DescriptionIcon from '@mui/icons-material/Description';
import CardMembershipIcon from '@mui/icons-material/CardMembership';
import LogoutIcon from '@mui/icons-material/Logout';
import DashboardIcon from '@mui/icons-material/Dashboard';
import SettingsIcon from '@mui/icons-material/Settings';
import CloseIcon from '@mui/icons-material/Close';
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import AssignmentIndIcon from '@mui/icons-material/AssignmentInd';
import VaccinesIcon from '@mui/icons-material/Vaccines';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import HealthAndSafetyIcon from '@mui/icons-material/HealthAndSafety';
import FamilyRestroomIcon from '@mui/icons-material/FamilyRestroom';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import RestaurantIcon from '@mui/icons-material/Restaurant';
import MedicalServicesIcon from '@mui/icons-material/MedicalServices';
import ExpandLess from '@mui/icons-material/ExpandLess';
import ExpandMore from '@mui/icons-material/ExpandMore';
import Collapse from '@mui/material/Collapse';
import { useLanguage } from '../context/LanguageContext';
import { getAvatarUrl } from '../services/demographicService';

function Header() {
    const { t } = useLanguage();
    const [anchorElUser, setAnchorElUser] = useState(null);
    const [drawerOpen, setDrawerOpen] = useState(false);
    const [user, setUser] = useState(null);
    const [avatarUrl, setAvatarUrl] = useState(null);
    const navigate = useNavigate();
    const location = useLocation();
    const theme = useTheme();
    const [healthRecordsOpen, setHealthRecordsOpen] = useState(false);

    const handleHealthRecordsClick = () => {
        setHealthRecordsOpen(!healthRecordsOpen);
    };

    // Navigation items with translation keys
    const navItems = [
        { label: t('profile'), icon: <PersonIcon />, path: '/profile' },
        { label: t('appointments'), icon: <CalendarMonthIcon />, path: '/appointments' },
        { label: t('booking'), icon: <AddCircleOutlineIcon />, path: '/booking' },
        { 
            label: t('health_records') || '健康记录', 
            icon: <MedicalServicesIcon />, 
            onClick: handleHealthRecordsClick,
            open: healthRecordsOpen,
            subItems: [
                { label: t('lab_reports'), icon: <DescriptionIcon />, path: '/lab-reports' },
                { label: t('prescriptions'), icon: <AssignmentIndIcon />, path: '/prescriptions' },
                { label: t('immunizations'), icon: <VaccinesIcon />, path: '/immunizations' },
                { label: t('nutritionist_notes') || '营养师', icon: <RestaurantIcon />, path: '/profile' }
            ]
        },
        { label: t('membership'), icon: <CardMembershipIcon />, path: '/membership' },
        { label: t('settings'), icon: <SettingsIcon />, path: '/settings' },
        { label: t('chatbot') || 'Chatbot', icon: <SmartToyIcon />, path: '/chatbot' },
    ];

    useEffect(() => {
        // Load user info from localStorage
        const userJson = localStorage.getItem('user');
        if (userJson) {
            try {
                const userData = JSON.parse(userJson);
                setUser(userData);
                // Fetch avatar if demographicNo is available
                if (userData && userData.demographicNo) {
                    fetchAvatar(userData.demographicNo);
                }
            } catch (e) {
                console.error('Error parsing user data', e);
            }
        }
    }, []);

    const fetchAvatar = async (demographicNo) => {
        const url = await getAvatarUrl(demographicNo);
        if (url) {
            setAvatarUrl(url);
        }
    };

    const handleOpenUserMenu = (event) => {
        setAnchorElUser(event.currentTarget);
    };

    const handleCloseUserMenu = () => {
        setAnchorElUser(null);
    };

    const toggleDrawer = (open) => (event) => {
        if (event &&
            event.type === 'keydown' &&
            (event.key === 'Tab' || event.key === 'Shift')
        ) {
            return;
        }
        setDrawerOpen(open);
    };

    const handleLogout = () => {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        navigate('/login');
    };

    const handleNavigation = (path) => {
        console.log('Navigating to path:', path);
        navigate(path);
        setDrawerOpen(false);
        handleCloseUserMenu();
    };

    // Check if user is authenticated
    const isAuthenticated = !!localStorage.getItem('token');

    // Don't show header on login and register pages
    const hideHeaderPaths = ['/login', '/register', '/forgot-password', '/reset-password', '/verify-email', '/email-verification'];
    if (hideHeaderPaths.includes(location.pathname)) {
        return null;
    }

    // If not authenticated, redirect to login
    if (!isAuthenticated) {
        return null;
    }

    const getInitials = () => {
        if (!user) return '?';
        if (user.firstName && user.lastName) {
            return `${user.firstName[0]}${user.lastName[0]}`;
        }
        if (user.email) {
            return user.email[0].toUpperCase();
        }
        return '?';
    };

    const isActive = (path) => {
        // Special handling for membership-related paths
        if (path === '/membership' && (location.pathname === '/membership' || location.pathname === '/renew-membership')) {
            return true;
        }
        return location.pathname === path;
    };

    // Add a helper to check admin status from localStorage (if available)
    function isAdminUser() {
        try {
            const user = JSON.parse(localStorage.getItem('user'));
            return user && user.role === 'admin';
        } catch {
            return false;
        }
    }

    return (
        <>
            <AppBar
                position="fixed"
                sx={{
                    zIndex: (theme) => theme.zIndex.drawer + 1,
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)'
                }}
            >
                <Container maxWidth="xl">
                    <Toolbar disableGutters sx={{ minHeight: { xs: '64px' } }}>
                        {/* Mobile view */}
                        <Box sx={{ flexGrow: 0, display: { xs: 'flex', md: 'none' } }}>
                            <IconButton
                                size="large"
                                aria-label="open drawer"
                                edge="start"
                                color="inherit"
                                onClick={toggleDrawer(true)}
                                sx={{
                                    mr: 1,
                                    background: alpha(theme.palette.primary.light, 0.1),
                                    borderRadius: '12px',
                                    padding: '8px',
                                    '&:hover': {
                                        background: alpha(theme.palette.primary.light, 0.2)
                                    }
                                }}
                            >
                                <MenuIcon fontSize="medium" />
                            </IconButton>
                        </Box>

                        {/* Logo/Title */}
                        <Typography
                            variant="h6"
                            noWrap
                            component="div"
                            sx={{
                                flexGrow: 1,
                                display: 'flex',
                                fontWeight: 700,
                                letterSpacing: '.1rem',
                                color: 'inherit',
                                textDecoration: 'none',
                                cursor: 'pointer',
                                fontSize: { xs: '1.1rem', md: '1.25rem' }
                            }}
                            onClick={() => navigate('/membership')}
                        >
                            {t('app_title')}
                        </Typography>

                        {/* Desktop Navigation */}
                        <Box sx={{ flexGrow: 1, display: { xs: 'none', md: 'flex' }, justifyContent: 'center' }}>
                            {navItems.map((item) => (
                                <Button
                                    key={item.label}
                                    onClick={() => handleNavigation(item.path)}
                                    sx={{
                                        my: 2,
                                        color: 'white',
                                        display: 'flex',
                                        alignItems: 'center',
                                        mx: 1,
                                        opacity: isActive(item.path) ? 1 : 0.8,
                                        fontWeight: isActive(item.path) ? 'bold' : 'normal',
                                        position: 'relative',
                                        '&:hover': {
                                            opacity: 1
                                        },
                                        '&::after': isActive(item.path) ? {
                                            content: '""',
                                            position: 'absolute',
                                            bottom: '6px',
                                            left: '50%',
                                            width: '40%',
                                            height: '3px',
                                            background: '#fff',
                                            transform: 'translateX(-50%)',
                                            borderRadius: '2px'
                                        } : {}
                                    }}
                                    startIcon={item.icon}
                                >
                                    {item.label}
                                </Button>
                            ))}
                        </Box>

                        {/* Avatar & User Menu */}
                        <Box sx={{ flexGrow: 0 }}>
                            <Tooltip title={t('settings')}>
                                <IconButton
                                    onClick={handleOpenUserMenu}
                                    sx={{
                                        p: 0,
                                        border: '2px solid rgba(255,255,255,0.3)',
                                        borderRadius: '50%',
                                        ml: { xs: 1, md: 2 },
                                        width: { xs: 40, md: 42 },
                                        height: { xs: 40, md: 42 }
                                    }}
                                >
                                    <Avatar
                                        alt={user?.name || "User"}
                                        src={avatarUrl}
                                        sx={{
                                            width: { xs: 36, md: 38 },
                                            height: { xs: 36, md: 38 },
                                            bgcolor: avatarUrl ? 'transparent' : theme.palette.secondary.main
                                        }}
                                    >
                                        {!avatarUrl && getInitials()}
                                    </Avatar>
                                </IconButton>
                            </Tooltip>
                            <Menu
                                sx={{ mt: '45px' }}
                                id="menu-appbar"
                                anchorEl={anchorElUser}
                                anchorOrigin={{
                                    vertical: 'top',
                                    horizontal: 'right',
                                }}
                                keepMounted
                                transformOrigin={{
                                    vertical: 'top',
                                    horizontal: 'right',
                                }}
                                open={Boolean(anchorElUser)}
                                onClose={handleCloseUserMenu}
                                PaperProps={{
                                    elevation: 3,
                                    sx: {
                                        overflow: 'visible',
                                        filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.1))',
                                        mt: 1.5,
                                        borderRadius: 3,
                                        '&:before': {
                                            content: '""',
                                            display: 'block',
                                            position: 'absolute',
                                            top: 0,
                                            right: 14,
                                            width: 10,
                                            height: 10,
                                            bgcolor: 'background.paper',
                                            transform: 'translateY(-50%) rotate(45deg)',
                                            zIndex: 0,
                                        },
                                    },
                                }}
                            >
                                <MenuItem onClick={() => handleNavigation('/profile')}>
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        <PersonIcon fontSize="small" sx={{ mr: 1.5, color: theme.palette.primary.main }} />
                                        {t('profile')}
                                    </Box>
                                </MenuItem>
                                <MenuItem onClick={() => handleNavigation('/settings')}>
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        <SettingsIcon fontSize="small" sx={{ mr: 1.5, color: theme.palette.primary.main }} />
                                        {t('settings')}
                                    </Box>
                                </MenuItem>
                                <Divider sx={{ my: 1 }} />
                                <MenuItem onClick={handleLogout}>
                                    <Box sx={{ display: 'flex', alignItems: 'center', color: theme.palette.error.main }}>
                                        <LogoutIcon fontSize="small" sx={{ mr: 1.5 }} />
                                        {t('logout')}
                                    </Box>
                                </MenuItem>
                                {isAdminUser() && (
                                    <MenuItem onClick={() => navigate('/admin-client-search')}>
                                        <ListItemIcon>
                                            <PersonIcon fontSize="small" />
                                        </ListItemIcon>
                                        Admin Client Search
                                    </MenuItem>
                                )}
                            </Menu>
                        </Box>
                    </Toolbar>
                </Container>
            </AppBar>

            {/* Empty toolbar for spacing since AppBar is fixed */}
            <Toolbar />

            {/* Mobile Navigation Drawer */}
            <SwipeableDrawer
                anchor="left"
                open={drawerOpen}
                onClose={toggleDrawer(false)}
                onOpen={toggleDrawer(true)}
                disableBackdropTransition={false}
                disableDiscovery={false}
                SwipeAreaProps={{
                    style: {
                        width: drawerOpen ? 0 : 20,
                        height: '100%'
                    }
                }}
                sx={{
                    '& .MuiDrawer-paper': {
                        width: '85%',
                        maxWidth: 320,
                        boxSizing: 'border-box',
                        borderTopRightRadius: 16,
                        borderBottomRightRadius: 16,
                    },
                }}
            >
                <Box
                    sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        p: 2,
                        bgcolor: 'primary.main',
                        color: 'white',
                        borderTopRightRadius: 16
                    }}
                >
                    <Typography variant="h6" sx={{ fontWeight: 700, color: theme.palette.primary.main }}>
                        {t('app_title')}
                    </Typography>
                    <IconButton onClick={toggleDrawer(false)}>
                        <CloseIcon />
                    </IconButton>
                </Box>

                <Divider />

                <Box sx={{ p: 2 }}>
                    <Box
                        sx={{
                            display: 'flex',
                            alignItems: 'center',
                            p: 1.5,
                            mb: 1,
                            bgcolor: alpha(theme.palette.primary.main, 0.08),
                            borderRadius: '12px'
                        }}
                    >
                        <Avatar
                            sx={{
                                width: 45,
                                height: 45,
                                bgcolor: avatarUrl ? 'transparent' : theme.palette.primary.main,
                            }}
                            src={avatarUrl}
                        >
                            {!avatarUrl && getInitials()}
                        </Avatar>
                        <Box sx={{ ml: 2 }}>
                            <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                                {user?.name || t('user')}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                                {user?.email || '<EMAIL>'}
                            </Typography>
                        </Box>
                    </Box>
                </Box>

                <List component="nav" sx={{ px: 1, py: 1.5 }}>
                    {navItems.map((item) => (
                        item.subItems ? (
                            <React.Fragment key={item.label}>
                                <ListItemButton onClick={item.onClick} sx={{ py: 1.5, px: 2, borderRadius: '12px', mb: 0.5 }}>
                                    <ListItemIcon sx={{ minWidth: 44 }}>{item.icon}</ListItemIcon>
                                    <ListItemText primary={item.label} />
                                    {item.open ? <ExpandLess /> : <ExpandMore />}
                                </ListItemButton>
                                <Collapse in={item.open} timeout="auto" unmountOnExit>
                                    <List component="div" disablePadding sx={{ pl: 2 }}>
                                        {item.subItems.map((subItem) => (
                                            <ListItem key={subItem.label} disablePadding sx={{ mb: 0.5 }}>
                                                <ListItemButton
                                                    onClick={() => handleNavigation(subItem.path)}
                                                    selected={isActive(subItem.path)}
                                                    sx={{
                                                        py: 1.5,
                                                        px: 2,
                                                        borderRadius: '12px',
                                                        '&.Mui-selected': {
                                                            backgroundColor: alpha(theme.palette.primary.main, 0.1),
                                                            '&:hover': {
                                                                backgroundColor: alpha(theme.palette.primary.main, 0.15),
                                                            },
                                                            '& .MuiListItemIcon-root': { color: theme.palette.primary.main },
                                                            '& .MuiListItemText-primary': { fontWeight: 600, color: theme.palette.primary.main }
                                                        }
                                                    }}
                                                >
                                                    <ListItemIcon sx={{ minWidth: 44 }}>{subItem.icon}</ListItemIcon>
                                                    <ListItemText primary={subItem.label} />
                                                     <KeyboardArrowRightIcon fontSize="small" sx={{ opacity: 0.6, color: isActive(subItem.path) ? theme.palette.primary.main : 'inherit' }} />
                                                </ListItemButton>
                                            </ListItem>
                                        ))}
                                    </List>
                                </Collapse>
                            </React.Fragment>
                        ) : (
                            <ListItem key={item.label} disablePadding sx={{ mb: 0.5 }}>
                                <ListItemButton
                                    onClick={() => handleNavigation(item.path)}
                                    selected={isActive(item.path)}
                                    sx={{
                                        py: 1.5,
                                        px: 2,
                                        borderRadius: '12px',
                                        '&.Mui-selected': {
                                            backgroundColor: alpha(theme.palette.primary.main, 0.1),
                                            '&:hover': {
                                                backgroundColor: alpha(theme.palette.primary.main, 0.15),
                                            },
                                            '& .MuiListItemIcon-root': { color: theme.palette.primary.main },
                                            '& .MuiListItemText-primary': { fontWeight: 600, color: theme.palette.primary.main }
                                        }
                                    }}
                                >
                                    <ListItemIcon sx={{ minWidth: 44 }}>{item.icon}</ListItemIcon>
                                    <ListItemText primary={item.label} />
                                    <KeyboardArrowRightIcon fontSize="small" sx={{ opacity: 0.6, color: isActive(item.path) ? theme.palette.primary.main : 'inherit' }} />
                                </ListItemButton>
                            </ListItem>
                        )
                    ))}
                </List>

                <Divider sx={{ my: 1 }} />

                <List sx={{ px: 1, py: 1 }}>
                    <ListItem disablePadding>
                        <ListItemButton
                            onClick={handleLogout}
                            sx={{
                                borderRadius: '12px',
                                py: 1.5,
                                px: 2,
                                color: theme.palette.error.main,
                                '&:hover': {
                                    backgroundColor: alpha(theme.palette.error.main, 0.08),
                                }
                            }}
                        >
                            <ListItemIcon sx={{ minWidth: 44, color: theme.palette.error.main }}>
                                <LogoutIcon />
                            </ListItemIcon>
                            <ListItemText
                                primary={t('logout')}
                                primaryTypographyProps={{
                                    fontWeight: 500,
                                    color: theme.palette.error.main
                                }}
                            />
                        </ListItemButton>
                    </ListItem>
                </List>

                <Box sx={{
                    mt: 'auto',
                    p: 2.5,
                    borderTop: '1px solid rgba(0,0,0,0.08)',
                    textAlign: 'center'
                }}>
                    <Typography variant="caption" color="text.secondary">
                        © 2025 MMC Wellness
                    </Typography>
                </Box>
            </SwipeableDrawer>
        </>
    );
}

export default Header; 