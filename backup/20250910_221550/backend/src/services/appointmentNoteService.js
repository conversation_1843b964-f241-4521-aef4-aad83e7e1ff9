const pool = require('../config/database');
const crypto = require('crypto');
const aiService = require('../utils/aiService');
const MedicalPromptTemplates = require('../prompts/medicalPrompts');

// Helper function to clean and format provider names
function formatProviderName(firstName, lastName, providerType = null) {
    // Clean the first name and last name by removing _number pattern
    const cleanFirstName = firstName ? firstName.replace(/_\d+/g, '').trim() : '';
    const cleanLastName = lastName ? lastName.replace(/_\d+/g, '').trim() : '';
    
    // Filter out Ken Sun completely
    if ((cleanFirstName.toLowerCase() === 'ken' && cleanLastName.toLowerCase() === 'sun') ||
        (cleanFirstName.toLowerCase().includes('ken') && cleanLastName.toLowerCase() === 'sun')) {
        return null; // Return null to indicate this provider should not be displayed
    }
    
    // Format the name
    let formattedName = `${cleanFirstName} ${cleanLastName}`.trim();
    
    // Add Dr. prefix if it's a doctor
    if (providerType === 'doctor' && formattedName) {
        formattedName = `Dr. ${formattedName}`;
    }
    
    return formattedName || 'Unknown Provider';
}

/**
 * 获取特定预约的医疗笔记
 * @param {Number} appointmentNo - 预约编号
 * @param {Number} demographicNo - 患者编号
 * @returns {Object} 包含笔记内容和元数据的对象
 */
exports.getAppointmentNote = async (appointmentNo, demographicNo) => {
    try {
        console.log(`Getting appointment note for appointmentNo: ${appointmentNo}, demographicNo: ${demographicNo}`);

        // 查询与该预约相关的笔记
        const noteQuery = `
            SELECT 
                n.note_id, 
                n.observation_date, 
                n.update_date,
                n.provider_no, 
                n.note, 
                n.encounter_type,
                p.first_name as provider_first_name,
                p.last_name as provider_last_name
            FROM 
                casemgmt_note n
            LEFT JOIN 
                provider p ON n.provider_no = p.provider_no 
            WHERE 
                n.appointmentNo = ? 
                AND n.demographic_no = ?
                AND n.note is not null
                AND n.note != ''
                AND n.observation_date >= '2023-07-01'
            ORDER BY 
                n.observation_date DESC
            LIMIT 1
        `;

        console.log(`Executing note query with appointmentNo: ${appointmentNo}, demographicNo: ${demographicNo}`);
        let [noteResults] = await pool.query(noteQuery, [appointmentNo, demographicNo]);
        console.log(`Note query returned ${noteResults ? noteResults.length : 0} results`);

        if (!noteResults || noteResults.length === 0) {
            // 如果找不到直接关联到预约的笔记，尝试找最近的笔记
            console.log(`No direct appointment notes found, trying to find recent notes`);

            const recentNoteQuery = `
                SELECT 
                    n.note_id, 
                    n.observation_date, 
                    n.update_date,
                    n.provider_no, 
                    n.note, 
                    n.encounter_type,
                    p.first_name as provider_first_name,
                    p.last_name as provider_last_name
                FROM 
                    casemgmt_note n
                LEFT JOIN 
                    provider p ON n.provider_no = p.provider_no 
                WHERE 
                    n.demographic_no = ?
                    AND n.observation_date >= '2023-07-01'
                ORDER BY 
                    n.observation_date DESC
                LIMIT 1
            `;

            const [recentResults] = await pool.query(recentNoteQuery, [demographicNo]);

            if (!recentResults || recentResults.length === 0) {
                console.log(`No medical notes found for patient ${demographicNo}`);
                return { success: false, message: 'No medical notes found for this patient' };
            }

            console.log(`Found a recent note for patient ${demographicNo}`);

            // 使用找到的最近笔记，但标记为非直接预约笔记
            noteResults = recentResults;
        }

        // 获取相关的问题(issues)
        console.log(`Getting issues for note_id: ${noteResults[0].note_id}`);
        const issueQuery = `
            SELECT 
                i.code, 
                i.description 
            FROM 
                casemgmt_issue_notes cin
            JOIN 
                casemgmt_issue ci ON cin.id = ci.id
            JOIN 
                issue i ON ci.issue_id = i.issue_id
            WHERE 
                cin.note_id = ?
        `;

        const [issueResults] = await pool.query(issueQuery, [noteResults[0].note_id]);
        console.log(`Found ${issueResults ? issueResults.length : 0} issues`);

        // 获取约诊信息
        console.log(`Getting appointment info for appointmentNo: ${appointmentNo}, demographicNo: ${demographicNo}`);
        const appointmentQuery = `
            SELECT 
                appointment_date, 
                start_time, 
                end_time, 
                reason, 
                status, 
                location
            FROM 
                appointment 
            WHERE 
                appointment_no = ? 
                AND demographic_no = ?
                AND appointment_date >= '2023-07-01'
        `;

        const [appointmentResults] = await pool.query(appointmentQuery, [appointmentNo, demographicNo]);
        console.log(`Found ${appointmentResults ? appointmentResults.length : 0} appointment results`);

        // 获取患者基本信息
        console.log(`Getting patient info for demographicNo: ${demographicNo}`);
        const patientQuery = `
            SELECT 
                last_name,
                first_name,
                sex,
                CONCAT(year_of_birth, '-', month_of_birth, '-', date_of_birth) as dob
            FROM 
                demographic 
            WHERE 
                demographic_no = ?
        `;

        const [patientResults] = await pool.query(patientQuery, [demographicNo]);
        console.log(`Found ${patientResults ? patientResults.length : 0} patient results`);

        // 获取扩展字段
        console.log(`Getting note extensions for note_id: ${noteResults[0].note_id}`);
        const extQuery = `
            SELECT key_val, value 
            FROM casemgmt_note_ext 
            WHERE note_id = ?
        `;

        const [extResults] = await pool.query(extQuery, [noteResults[0].note_id]);
        console.log(`Found ${extResults ? extResults.length : 0} extension results`);

        // 构建完整的笔记对象
        const note = {
            noteId: noteResults[0].note_id,
            observationDate: noteResults[0].observation_date,
            updateDate: noteResults[0].update_date,
            providerId: noteResults[0].provider_no,
            providerName: formatProviderName(noteResults[0].provider_first_name, noteResults[0].provider_last_name) || 'Unknown Provider',
            content: noteResults[0].note,
            encounterType: noteResults[0].encounter_type,
            issues: issueResults,
            appointment: appointmentResults.length > 0 ? appointmentResults[0] : null,
            patient: patientResults.length > 0 ? patientResults[0] : null,
            extensions: extResults
        };

        console.log(`Successfully built note object with ID: ${note.noteId}`);
        return { success: true, note };
    } catch (error) {
        console.error('Error retrieving appointment note:', error);
        return { success: false, message: 'Failed to retrieve appointment note', error: error.message };
    }
};

/**
 * 使用AI总结【单个】医疗笔记 - 重构版本
 * @param {Object} noteData - 包含笔记信息的对象
 * @param {String} language - 用户偏好的语言 ('en' 或 'zh')
 * @returns {Object} 包含AI总结的对象
 */
exports.summarizeNoteWithAI = async (noteData, language = 'en') => {
    try {
        console.log(`[AI Service] Starting medical note summary for note ${noteData.noteId}`);

        // 准备患者基本信息
        const patientInfo = noteData.patient
            ? `Patient: ${noteData.patient.first_name} ${noteData.patient.last_name}, ${noteData.patient.sex}, DOB: ${noteData.patient.dob}`
            : '';

        // 准备约诊信息
        const appointmentInfo = noteData.appointment
            ? `Appointment Date: ${noteData.appointment.appointment_date}, Reason: ${noteData.appointment.reason || 'Not specified'}`
            : '';

        // 构建提示数据
        const promptData = {
            patientInfo,
            appointmentInfo,
            providerName: noteData.providerName,
            observationDate: noteData.observationDate,
            content: noteData.content
        };

        // 使用模板构建提示词
        const prompt = MedicalPromptTemplates.buildPrompt('medicalNotes', language, promptData);

        // 调用统一AI服务
        const result = await aiService.generateMedicalNoteSummary(prompt, noteData.noteId, language);

        if (result.success) {
            console.log(`[AI Service] Successfully generated summary for note ${noteData.noteId}`);
            return { success: true, summary: result.content };
        } else {
            console.error(`[AI Service] Failed to generate summary for note ${noteData.noteId}:`, result.error);
            return { success: false, message: result.error };
        }

    } catch (error) {
        console.error('[AI Service] Error in summarizeNoteWithAI:', error);
        return { success: false, message: 'Failed to generate AI summary', error: error.message };
    }
};

/**
 * 使用AI总结【历史】医疗笔记 - 重构版本
 * @param {String} historyText - 包含多条笔记的文本
 * @param {String} language - 用户偏好的语言 ('en' 或 'zh')
 * @param {String} summaryType - 'half-year' 或 'final' 用于调整提示
 * @returns {Object} 包含AI总结的对象
 */
exports.summarizeHistoryWithAI = async (historyText, language = 'en', summaryType = 'final') => {
    try {
        console.log(`[AI Service] Starting medical history summary - Type: ${summaryType}, Language: ${language}`);

        // 构建提示数据
        const promptData = {
            historyText,
            summaryType
        };

        // 使用模板构建提示词
        const prompt = MedicalPromptTemplates.buildPrompt('medicalHistory', language, promptData);

        // 生成缓存键
        const cacheKey = `medical_history_${summaryType}_${language}_${crypto.createHash('md5').update(historyText).digest('hex').substring(0, 8)}`;

        // 调用统一AI服务
        const result = await aiService.generateContent(prompt, {
            service: 'medicalNotes',
            cacheKey: cacheKey,
            context: `medicalHistorySummary-${summaryType}`
        });

        if (result.success) {
            console.log(`[AI Service] Successfully generated ${summaryType} history summary`);
            return { success: true, summary: result.content };
        } else {
            console.error(`[AI Service] Failed to generate ${summaryType} history summary:`, result.error);
            return { success: false, message: result.error };
        }

    } catch (error) {
        console.error('[AI Service] Error in summarizeHistoryWithAI:', error);
        return { success: false, message: 'Failed to generate AI history summary', error: error.message };
    }
}; 