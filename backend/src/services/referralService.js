const pool = require('../config/database');
const crypto = require('crypto');
const { createLogger } = require('../utils/logger');
const logger = createLogger('referralService');

class ReferralService {
    /**
     * Generate a unique referral code for a user
     * @param {number} demographicNo - The demographic number of the user
     * @returns {Promise<string>} - The generated referral code
     */
    static async generateReferralCode(demographicNo) {
        try {
            // Check if user already has a code
            const [existingCodes] = await pool.query(
                'SELECT referral_code FROM referral_codes WHERE demographic_no = ? AND is_active = TRUE',
                [demographicNo]
            );

            if (existingCodes.length > 0) {
                return existingCodes[0].referral_code;
            }

            // Generate a new code
            let code, isUnique = false;
            while (!isUnique) {
                // Generate a 6-character alphanumeric code
                code = crypto.randomBytes(3).toString('hex').toUpperCase();
                
                // Check if code already exists
                const [existing] = await pool.query(
                    'SELECT id FROM referral_codes WHERE referral_code = ?', 
                    [code]
                );
                
                isUnique = existing.length === 0;
            }

            // Save the code
            await pool.query(
                'INSERT INTO referral_codes (demographic_no, referral_code) VALUES (?, ?)',
                [demographicNo, code]
            );

            return code;
        } catch (error) {
            logger.error('Error generating referral code:', error);
            throw error;
        }
    }

    /**
     * Validate a referral code
     * @param {string} code - The referral code to validate
     * @returns {Promise<number|null>} - The demographic number of the referrer if valid, null otherwise
     */
    static async validateReferralCode(code) {
        try {
            const [results] = await pool.query(
                'SELECT demographic_no FROM referral_codes WHERE referral_code = ? AND is_active = TRUE',
                [code]
            );

            return results.length > 0 ? results[0].demographic_no : null;
        } catch (error) {
            logger.error('Error validating referral code:', error);
            throw error;
        }
    }

    /**
     * Record a referral when a new user registers
     * @param {number} referrerDemographicNo - The demographic number of the referring user
     * @param {number} referredDemographicNo - The demographic number of the referred (new) user
     * @returns {Promise<boolean>} - Whether the referral was recorded successfully
     */
    static async recordReferral(referrerDemographicNo, referredDemographicNo) {
        try {
            await pool.query(
                'INSERT INTO referrals (referrer_demographic_no, referred_demographic_no) VALUES (?, ?)',
                [referrerDemographicNo, referredDemographicNo]
            );
            return true;
        } catch (error) {
            logger.error('Error recording referral:', error);
            return false;
        }
    }

    /**
     * Add reward points to a user
     * @param {number} demographicNo - The demographic number of the user to reward
     * @param {number} points - The number of points to award
     * @param {string} description - Description of why points were awarded
     * @returns {Promise<boolean>} - Whether the points were added successfully
     */
    static async addRewardPoints(demographicNo, points, description) {
        try {
            await pool.query(
                'INSERT INTO reward_points (demographic_no, points, description) VALUES (?, ?, ?)',
                [demographicNo, points, description]
            );
            
            // Update the referral status to rewarded
            if (description.includes('referral')) {
                await pool.query(
                    'UPDATE referrals SET status = "rewarded", reward_points = ? WHERE referrer_demographic_no = ? AND status = "completed"',
                    [points, demographicNo]
                );
            }
            
            return true;
        } catch (error) {
            logger.error('Error adding reward points:', error);
            return false;
        }
    }

    /**
     * Get total reward points for a user
     * @param {number} demographicNo - The demographic number of the user
     * @returns {Promise<number>} - The total reward points
     */
    static async getTotalRewardPoints(demographicNo) {
        try {
            const [results] = await pool.query(
                'SELECT SUM(points) as total FROM reward_points WHERE demographic_no = ?',
                [demographicNo]
            );
            
            return results[0].total || 0;
        } catch (error) {
            logger.error('Error getting total reward points:', error);
            return 0;
        }
    }

    /**
     * Get all referrals made by a user
     * @param {number} demographicNo - The demographic number of the user
     * @returns {Promise<Array>} - Array of referrals
     */
    static async getUserReferrals(demographicNo) {
        try {
            const [referrals] = await pool.query(`
                SELECT r.*, 
                       d.first_name, d.last_name, 
                       DATE_FORMAT(r.referral_date, '%Y-%m-%d') as formatted_date
                FROM referrals r
                JOIN demographic d ON r.referred_demographic_no = d.demographic_no
                WHERE r.referrer_demographic_no = ?
                ORDER BY r.referral_date DESC
            `, [demographicNo]);
            
            return referrals;
        } catch (error) {
            logger.error('Error getting user referrals:', error);
            return [];
        }
    }

    /**
     * Get referral code for a user
     * @param {number} demographicNo - The demographic number of the user
     * @returns {Promise<string|null>} - The referral code or null if not found
     */
    static async getReferralCode(demographicNo) {
        try {
            const [codes] = await pool.query(
                'SELECT referral_code FROM referral_codes WHERE demographic_no = ? AND is_active = TRUE',
                [demographicNo]
            );
            
            if (codes.length === 0) {
                // Generate a new code if one doesn't exist
                return await this.generateReferralCode(demographicNo);
            }
            
            return codes[0].referral_code;
        } catch (error) {
            logger.error('Error getting referral code:', error);
            return null;
        }
    }
}

module.exports = ReferralService; 