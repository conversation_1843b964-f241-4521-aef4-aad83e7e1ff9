import React, { useState, useEffect } from 'react';
import { API_URL } from '../utils/env';
import { useNavigate } from 'react-router-dom';
import {
    Container,
    Paper,
    Typography,
    Button,
    Grid,
    Divider,
    Card,
    CardContent,
    Box,
    Chip,
    CircularProgress,
    useTheme,
    useMediaQuery,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Select,
    MenuItem,
    FormControl,
    InputLabel,
    Alert,
    IconButton,
    Accordion,
    AccordionSummary,
    AccordionDetails,
    Tabs,
    Tab,
    List,
    ListItem,
    ListItemAvatar,
    ListItemText,
    Avatar
} from '@mui/material';
import { styled, alpha } from '@mui/material/styles';
import CreditCardIcon from '@mui/icons-material/CreditCard';
import HistoryIcon from '@mui/icons-material/History';
import PaymentIcon from '@mui/icons-material/Payment';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import AutorenewIcon from '@mui/icons-material/Autorenew';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import InfoIcon from '@mui/icons-material/Info';
import PersonIcon from '@mui/icons-material/Person';
import HistoryToggleOffIcon from '@mui/icons-material/HistoryToggleOff';
import FamilyRestroomIcon from '@mui/icons-material/FamilyRestroom';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import GroupAddIcon from '@mui/icons-material/GroupAdd';
import PeopleIcon from '@mui/icons-material/People';
import RedeemIcon from '@mui/icons-material/Redeem';
import ShareIcon from '@mui/icons-material/Share';
import Tooltip from '@mui/material/Tooltip';
import { useView } from '../context/ViewContext';
import { useLanguage } from '../context/LanguageContext';
import CardContainer from './CardContainer';

const StyledPaper = styled(Paper)(({ theme }) => ({
    marginTop: theme.spacing(2),
    marginBottom: theme.spacing(4),
    padding: theme.spacing(3),
    [theme.breakpoints.up('md')]: {
        marginTop: theme.spacing(4),
        padding: theme.spacing(4),
    },
    [theme.breakpoints.down('sm')]: {
        marginLeft: 0,
        marginRight: 0,
        width: '100%',
        borderRadius: 0,
    },
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
}));

const StyledButton = styled(Button)(({ theme }) => ({
    margin: theme.spacing(2, 0, 1),
}));

const InfoItem = ({ icon, primary, secondary }) => {
    const theme = useTheme();

    return (
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box sx={{
                mr: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '50%',
                bgcolor: theme.palette.primary.main + '14', // 8% opacity
                p: 1,
                minWidth: 40,
                height: 40
            }}>
                {React.cloneElement(icon, { color: "primary" })}
            </Box>
            <Box>
                <Typography variant="body2" color="text.secondary">{primary}</Typography>
                <Typography variant="body1" sx={{ fontWeight: 500 }}>{secondary}</Typography>
            </Box>
        </Box>
    );
};

// 会员卡片组件
const MembershipCard = ({ membership, name, relationship, formatPrice, formatDate, getMembershipTypeLabel, canRenew = false, onRenew }) => {
    const theme = useTheme();
    const [showHistory, setShowHistory] = useState(false);
    const { t } = useLanguage();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const isActive = membership.is_active; // from fetched data
    const membershipTypeLabel = getMembershipTypeLabel(membership.membership_type_code);

    if (!membership || !membership.current) {
        return (
            <Card
                elevation={isActive ? 4 : 1}
                sx={{
                    p: isMobile ? 1.5 : 2.5, // Internal padding
                    borderLeft: `5px solid ${isActive ? theme.palette.success.main : theme.palette.grey[400]}`,
                    bgcolor: isActive ? alpha(theme.palette.success.main, 0.03) : theme.palette.background.paper,
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between',
                    borderRadius: '12px',
                    boxShadow: isActive ? theme.shadows[3] : theme.shadows[1],
                    transition: 'all 0.3s ease',
                    '&:hover': {
                        transform: 'translateY(-3px)',
                        boxShadow: theme.shadows[isActive ? 6 : 3],
                    }
                }}
            >
                <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                        <Typography variant="h6">
                        {name}
                        {relationship && (
                            <Chip
                                size="small"
                                label={relationship}
                                sx={{ ml: 1, textTransform: 'capitalize' }}
                            />
                        )}
                    </Typography>
                        <Chip
                            label={isActive ? t('active') : t('expired')}
                            color={isActive ? 'success' : 'error'}
                            sx={{ fontWeight: 'bold' }}
                        />
                    </Box>

                    <Divider sx={{ mb: 2 }} />

                    <Grid container spacing={2}>
                        <Grid item xs={6}>
                            <InfoItem
                                icon={<CalendarMonthIcon />}
                                primary={t('start_date')}
                                secondary={formatDate(membership.current.service_date)}
                            />
                        </Grid>
                        <Grid item xs={6}>
                            <InfoItem
                                icon={<CalendarMonthIcon />}
                                primary={t('end_date')}
                                secondary={formatDate(membership.current.endDate)}
                            />
                        </Grid>
                        <Grid item xs={6}>
                            <InfoItem
                                icon={<CreditCardIcon />}
                                primary={t('membership_type')}
                                secondary={membershipTypeLabel}
                            />
                        </Grid>
                        <Grid item xs={6}>
                            <InfoItem
                                icon={<PaymentIcon />}
                                primary={t('payment_amount')}
                                secondary={formatPrice(membership.current.bill_amount)}
                            />
                        </Grid>
                    </Grid>

                        {canRenew && (
                        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                            <Button
                                variant={isActive ? "contained" : "outlined"}
                                color="primary"
                                startIcon={<AutorenewIcon />}
                                onClick={onRenew}
                            >
                                {isActive ? t('renew_early') : t('renew_membership')}
                            </Button>
                        </Box>
                    )}

                    {membership.history && membership.history.length > 0 && (
                        <>
                            <Box sx={{ mt: 3, mb: 1 }}>
                                <Button
                                    size="small"
                                    startIcon={<HistoryToggleOffIcon />}
                                    onClick={() => setShowHistory(!showHistory)}
                                    color="primary"
                                >
                                    {showHistory ? t('hide_history') : t('view_history')}
                                </Button>
                    </Box>

                            {showHistory && (
                                <>
                                    <Divider sx={{ mb: 2 }} />
                                    <Typography variant="subtitle2" gutterBottom>
                                        {t('membership_history')}
                                    </Typography>
                                    <TableContainer component={Paper} variant="outlined" sx={{ mt: 1 }}>
                                        <Table size="small">
                                            <TableHead>
                                                <TableRow>
                                                    <TableCell>{t('date')}</TableCell>
                                                    <TableCell>{t('type')}</TableCell>
                                                    <TableCell>{t('amount')}</TableCell>
                                                    <TableCell>{t('status')}</TableCell>
                                                </TableRow>
                                            </TableHead>
                                            <TableBody>
                                                {membership.history.map((record, index) => (
                                                    <TableRow key={index}>
                                                        <TableCell>{formatDate(record.service_date)}</TableCell>
                                                        <TableCell>{getMembershipTypeLabel(record.billing_code)}</TableCell>
                                                        <TableCell>{formatPrice(record.bill_amount)}</TableCell>
                                                        <TableCell>
                                                            <Chip
                                                                size="small"
                                                                label={record.billingstatus === 'A' ? t('completed') : record.billingstatus}
                                                                color={record.billingstatus === 'A' ? 'success' : 'default'}
                                                            />
                                                        </TableCell>
                                                    </TableRow>
                                                ))}
                                            </TableBody>
                                        </Table>
                                    </TableContainer>
                                </>
                            )}
                        </>
                    )}
                </CardContent>
            </Card>
        );
    }

    const isExpired = membership.current.isExpired;

    return (
        <Card
            elevation={isActive ? 4 : 1}
            sx={{
                p: isMobile ? 1.5 : 2.5, // Internal padding
                borderLeft: `5px solid ${isActive ? theme.palette.success.main : theme.palette.grey[400]}`,
                bgcolor: isActive ? alpha(theme.palette.success.main, 0.03) : theme.palette.background.paper,
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between',
                borderRadius: '12px',
                boxShadow: isActive ? theme.shadows[3] : theme.shadows[1],
                transition: 'all 0.3s ease',
                '&:hover': {
                    transform: 'translateY(-3px)',
                    boxShadow: theme.shadows[isActive ? 6 : 3],
                }
            }}
        >
            <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                    <Typography variant="h6">
                        {name}
                        {relationship && (
                            <Chip
                                size="small"
                                label={relationship}
                                sx={{ ml: 1, textTransform: 'capitalize' }}
                            />
                        )}
                    </Typography>
                    <Chip
                        label={isExpired ? t('expired') : t('active')}
                        color={isExpired ? 'error' : 'success'}
                        sx={{ fontWeight: 'bold' }}
                    />
                </Box>

                <Divider sx={{ mb: 2 }} />

                <Grid container spacing={2}>
                    <Grid item xs={6}>
                        <InfoItem
                            icon={<CalendarMonthIcon />}
                            primary={t('start_date')}
                            secondary={formatDate(membership.current.service_date)}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <InfoItem
                            icon={<CalendarMonthIcon />}
                            primary={t('end_date')}
                            secondary={formatDate(membership.current.endDate)}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <InfoItem
                            icon={<CreditCardIcon />}
                            primary={t('membership_type')}
                            secondary={membershipTypeLabel}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <InfoItem
                            icon={<PaymentIcon />}
                            primary={t('payment_amount')}
                            secondary={formatPrice(membership.current.bill_amount)}
                        />
                    </Grid>
                </Grid>

                {canRenew && (
                    <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                        <Button
                            variant={isExpired ? "contained" : "outlined"}
                            color="primary"
                            startIcon={<AutorenewIcon />}
                            onClick={onRenew}
                        >
                            {isExpired ? t('renew_membership') : t('renew_early')}
                        </Button>
                    </Box>
                )}

                {membership.history && membership.history.length > 0 && (
                    <>
                        <Box sx={{ mt: 3, mb: 1 }}>
                            <Button
                                size="small"
                                startIcon={<HistoryToggleOffIcon />}
                                onClick={() => setShowHistory(!showHistory)}
                                color="primary"
                            >
                                {showHistory ? t('hide_history') : t('view_history')}
                            </Button>
                        </Box>

                        {showHistory && (
                            <>
                                <Divider sx={{ mb: 2 }} />
                                <Typography variant="subtitle2" gutterBottom>
                                    {t('membership_history')}
                                </Typography>
                                <TableContainer component={Paper} variant="outlined" sx={{ mt: 1 }}>
                                    <Table size="small">
                                        <TableHead>
                                            <TableRow>
                                                <TableCell>{t('date')}</TableCell>
                                                <TableCell>{t('type')}</TableCell>
                                                <TableCell>{t('amount')}</TableCell>
                                                <TableCell>{t('status')}</TableCell>
                                            </TableRow>
                                        </TableHead>
                                        <TableBody>
                                            {membership.history.map((record, index) => (
                                                <TableRow key={index}>
                                                    <TableCell>{formatDate(record.service_date)}</TableCell>
                                                    <TableCell>{getMembershipTypeLabel(record.billing_code)}</TableCell>
                                                    <TableCell>{formatPrice(record.bill_amount)}</TableCell>
                                                    <TableCell>
                                                        <Chip
                                                            size="small"
                                                            label={record.billingstatus === 'A' ? t('completed') : record.billingstatus}
                                                            color={record.billingstatus === 'A' ? 'success' : 'default'}
                                                        />
                                                    </TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>
                                </TableContainer>
                            </>
                        )}
                    </>
                )}
            </CardContent>
        </Card>
    );
};

const Membership = () => {
    console.log("DEBUG: Membership component rendering");
    const {
        loggedInUser,
        viewingDemographicNo,
        isViewingOwnProfile,
        isLoading: contextLoading,
        error: contextError
    } = useView();
    const { t } = useLanguage();

    const [membershipData, setMembershipData] = useState(null);
    const [familyMemberships, setFamilyMemberships] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [membershipTypes, setMembershipTypes] = useState([]);
    const [typesLoading, setTypesLoading] = useState(true);

    // For renewal dialog
    const [renewDialogOpen, setRenewDialogOpen] = useState(false);
    const [selectedType, setSelectedType] = useState('');
    const [renewError, setRenewError] = useState('');
    const [renewSuccess, setRenewSuccess] = useState(false);

    // 为卡片视图添加状态
    const [cardViewMode, setCardViewMode] = useState('grid'); // 默认网格视图

    // 定义是否可以续订会员资格
    const canRenew = true; // 默认允许所有用户购买或续订

    const navigate = useNavigate();
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

    const [referralCode, setReferralCode] = useState('');
    const [referrals, setReferrals] = useState([]);
    const [totalPoints, setTotalPoints] = useState(0);
    const [referralTabValue, setReferralTabValue] = useState(0);
    const [copied, setCopied] = useState(false);

    const fetchMembershipInfo = async () => {
        console.log("DEBUG: Starting fetchMembershipInfo in Membership component");
        console.log("DEBUG: viewingDemographicNo =", viewingDemographicNo);
        console.log("DEBUG: isViewingOwnProfile =", isViewingOwnProfile);
        console.log("DEBUG: loggedInUser =", loggedInUser);

        if (!viewingDemographicNo) {
            console.log('Membership: Waiting for viewingDemographicNo...');
            setLoading(contextLoading); 
            return;
        }

        const demoNo = parseInt(viewingDemographicNo, 10);
        if (isNaN(demoNo) || demoNo <= 0) {
            console.error(`Membership: Invalid demographic number: ${viewingDemographicNo}`);
            setError(t('invalid_patient_id'));
            setLoading(false);
            return;
        }

        console.log(`Membership: Using data for demographic_no: ${demoNo}`);
        setLoading(true);
        setError('');

        const token = localStorage.getItem('token');
        if (!token) {
            console.log("DEBUG: No token found, redirecting to login");
            navigate('/login');
            return;
        }

        let retries = 0;
        const maxRetries = 2;

        const attemptFetch = async () => {
            try {
                console.log(`DEBUG: Fetching membership data from API (attempt ${retries + 1})`);
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 10000);

                const response = await fetch(`${API_URL}/api/auth/membership/${demoNo}`, {
                    headers: { 'Authorization': `Bearer ${token}` },
                    signal: controller.signal
                });
                clearTimeout(timeoutId);
                console.log(`DEBUG: API response status: ${response.status}`);

                if (response.status === 401 || response.status === 403) {
                    console.error(`Membership: Authentication error (${response.status})`);
                    localStorage.removeItem('token');
                    navigate('/login');
                    throw new Error(t('authentication_failed'));
                }

                let data;
                try {
                    data = await response.json();
                    console.log("DEBUG: API response data:", data);
                } catch (jsonError) {
                    const errorText = await response.text();
                    console.error("DEBUG: API error response (not JSON):", errorText);
                    throw new Error(t('server_invalid_format'));
                }

                if (!response.ok) {
                    throw new Error(data.message || `${t('fetch_membership_failed')} (${t('status_code')}: ${response.status})`);
                }

                if (data.success) {
                    console.log('Membership: Data received successfully');
                    console.log('[DEBUG] Received membership data:', data.membershipInfo);
                    console.log('[DEBUG] Received family memberships:', data.familyMemberships);
                    setMembershipData(data.membershipInfo);
                    let processedFamilyMemberships = data.familyMemberships || [];
                    // 移除硬编码的模拟数据，使用后端返回的真实数据
                    console.log('Using real membership data from backend for all family members');
                    console.log('Membership: Final Family data being set:', processedFamilyMemberships);
                    setFamilyMemberships(processedFamilyMemberships);
                    return false; 
                } else {
                    throw new Error(data.message || t('server_invalid_structure'));
                }
            } catch (err) {
                console.error('Membership fetch error:', err);
                if (err.name === 'AbortError') {
                    console.error('Membership: Request timed out');
                    if (retries < maxRetries) {
                        retries++;
                        console.log(`Retrying membership fetch (${retries}/${maxRetries})...`);
                        return true; 
                    } else {
                        setError(t('request_timeout'));
                    }
                } else if (err.message.includes(t('authentication_failed'))) {
                    setError(err.message);
                    return false; 
                } else if (retries < maxRetries) {
                    retries++;
                    console.log(`Retrying membership fetch (${retries}/${maxRetries})...`);
                    return true; 
                } else {
                    setError(err.message || t('fetch_membership_failed_retry'));
                }
                return false; 
            }
        };
         await attemptFetch(); 
         setLoading(false); 
    };

    const fetchMembershipTypes = async () => {
        setTypesLoading(true);
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                navigate('/login');
                return;
            }
            const response = await fetch(`${API_URL}/api/membership/types`, {
                headers: { 'Authorization': `Bearer ${token}` },
            });
            if (!response.ok) {
                const text = await response.text();
                console.error('Failed to fetch membership types:', text);
                throw new Error(t('fetch_membership_types_failed'));
            }
            const data = await response.json();
            if (data.success && Array.isArray(data.types)) {
                const formattedTypes = data.types.map(type => ({
                    value: type.service_code,
                    label: type.description,
                    price: parseInt(type.value),
                    description: type.description
                }));
                console.log('Membership types loaded:', formattedTypes);
                setMembershipTypes(formattedTypes);
            } else {
                console.warn('Using default membership types due to API error or invalid data format');
                setMembershipTypes([
                    { value: 'AHM_EM', label: 'Individual Membership', price: 2350 },
                    { value: 'AHM_EM_KD', label: 'Child Membership', price: 850 },
                    { value: 'AHM_SIG', label: 'Family Membership', price: 5500 },
                    { value: 'AHM_SIG_KD', label: 'Family Child Membership', price: 850 },
                ]);
            }
        } catch (err) {
            console.error('Error fetching membership types:', err);
            setMembershipTypes([
                { value: 'AHM_EM', label: 'Individual Membership', price: 2350 },
                { value: 'AHM_EM_KD', label: 'Child Membership', price: 850 },
                { value: 'AHM_SIG', label: 'Family Membership', price: 5500 },
                { value: 'AHM_SIG_KD', label: 'Family Child Membership', price: 850 },
            ]);
        } finally {
            setTypesLoading(false);
        }
    };

    const fetchReferralInfo = async () => {
        try {
            const token = localStorage.getItem('token');
            if (!token) return;

            const codeResponse = await fetch(`${API_URL}/api/referrals/code`, {
                headers: { 'Authorization': `Bearer ${token}` }
            });
            if (codeResponse.ok) {
                const codeData = await codeResponse.json();
                if (codeData.success) {
                    setReferralCode(codeData.referralCode);
                }
            }
            const referralsResponse = await fetch(`${API_URL}/api/referrals/list`, {
                headers: { 'Authorization': `Bearer ${token}` }
            });
            if (referralsResponse.ok) {
                const referralsData = await referralsResponse.json();
                if (referralsData.success) {
                    setReferrals(referralsData.referrals);
                    setTotalPoints(referralsData.totalPoints);
                }
            }
        } catch (error) {
            console.error('Error fetching referral info:', error);
        }
    };

    // useEffect for initial data fetching
    useEffect(() => {
        if (viewingDemographicNo) {
             fetchMembershipInfo();
             // fetchMembershipTypes(); // This call was problematic if fetchMembershipTypes was defined in another useEffect
        }
        if (isViewingOwnProfile) {
            fetchReferralInfo();
        }
    }, [viewingDemographicNo, contextLoading, isViewingOwnProfile, loggedInUser, navigate, t]);

    // useEffect specifically for fetching membership types (can be combined or kept separate)
    useEffect(() => {
        // Define fetchMembershipTypes here or move its definition to component scope if called from multiple useEffects
        // For now, assuming it's only called here or fetchMembershipTypes is now at component scope
        fetchMembershipTypes(); 
    }, [navigate, t]); // Add t to dependencies if used inside fetchMembershipTypes for translations

    // useEffect that was causing the problem by calling functions defined in other useEffects
    useEffect(() => {
        if (viewingDemographicNo) {
            fetchMembershipInfo();
            fetchMembershipTypes(); // This should now work as fetchMembershipTypes is component-scoped
            fetchReferralInfo();
        }
    }, [viewingDemographicNo]); // Removed other dependencies if they are not directly used for these calls

    // Format date string
    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        try {
            let date;
            
            // 检查是否是 yyyymmdd 格式
            if (typeof dateString === 'string' && dateString.length === 8 && /^\d{8}$/.test(dateString)) {
                const year = parseInt(dateString.substring(0, 4));
                const month = parseInt(dateString.substring(4, 6)) - 1; // JavaScript月份从0开始
                const day = parseInt(dateString.substring(6, 8));
                date = new Date(year, month, day);
            } else {
                // 标准日期格式
                date = new Date(dateString);
            }
            
            if (isNaN(date.getTime())) {
                return dateString; // 如果无法解析，返回原始字符串
            }
            
            return date.toLocaleDateString(undefined, {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        } catch (e) {
            return dateString;
        }
    };

    // Get membership type label
    const getMembershipTypeLabel = (code) => {
        const types = {
            'AHM_EM': '个人会员',
            'AHM_EM_KD': '儿童会员',
            'AHM_SIG': '家庭会员',
            'AHM_SIG_KD': '家庭儿童会员',
            'AHM_VIP': 'VIP会员',
            'AHM_VIP_FM': 'VIP家庭会员',
            'AHM_7/24': '7/24会员',
            'AHM_MT20': '按摩会员'
        };
        return types[code] || code;
    };

    // Format price to currency
    const formatPrice = (price) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'CAD',
            minimumFractionDigits: 2
        }).format(price);
    };

    // Renewal dialog functions
    const handleOpenRenewDialog = () => {
        setRenewDialogOpen(true);
        setSelectedType('');
        setRenewError('');
        setRenewSuccess(false);
    };

    const handleCloseRenewDialog = () => {
        setRenewDialogOpen(false);
    };

    const handleTypeChange = (event) => {
        setSelectedType(event.target.value);
    };

    const handleRenewMembership = async () => {
        if (!selectedType) {
            setRenewError('Please select a membership type');
            return;
        }

        try {
            setRenewError('');
            const token = localStorage.getItem('token');
            const response = await fetch(`${API_URL}/api/auth/renew-membership`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`,
                },
                body: JSON.stringify({ membershipType: selectedType }),
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'Failed to renew membership');
            }

            setRenewSuccess(true);
            setTimeout(() => {
                setRenewDialogOpen(false);
                // Refresh the page to update membership info
                window.location.reload();
            }, 1500);
        } catch (err) {
            console.error('Renewal error:', err);
            setRenewError(err.message);
        }
    };

    const handleCopyReferralCode = () => {
        navigator.clipboard.writeText(referralCode)
            .then(() => {
                setCopied(true);
                setTimeout(() => setCopied(false), 2000);
            })
            .catch(err => console.error('Failed to copy: ', err));
    };

    const handleReferralTabChange = (event, newValue) => {
        setReferralTabValue(newValue);
    };

    // Function to navigate to store for membership purchase
    const handleNavigateToStore = () => {
        navigate('/store');
    };

    // Combined loading/error states
    const isLoading = loading || contextLoading;
    const displayError = error || contextError;

    // 检测是否为网络连接问题
    const isNetworkError = displayError && (
        displayError.includes('Failed to fetch') ||
        displayError.includes('Network Error') ||
        displayError.includes('timeout') ||
        displayError.includes('超时') ||
        displayError.includes('请求超时')
    );

    if (isLoading) {
        return (
            <Container component="main" maxWidth="lg">
                <StyledPaper elevation={3} sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
                    <CircularProgress />
                </StyledPaper>
            </Container>
        );
    }

    if (displayError && !isNetworkError) {
        return (
            <Container component="main" maxWidth="lg">
                <StyledPaper elevation={3}>
                    <Typography color="error" variant="h6">加载会员数据时出错</Typography>
                    <Alert severity="error" sx={{ mt: 2, width: '100%' }}>{displayError}</Alert>
                </StyledPaper>
            </Container>
        );
    }

    // User's full name
    const userName = loggedInUser?.demographicInfo ?
        `${loggedInUser.demographicInfo.first_name || ''} ${loggedInUser.demographicInfo.last_name || ''}` :
        '当前用户';

    return (
        <Container component="main" maxWidth="lg" sx={{ pb: 4 }}>
            {isNetworkError && (
                <Paper
                    sx={{
                        mt: 2,
                        p: 2,
                        border: '1px solid #ffcc80',
                        bgcolor: '#fff8e1',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexDirection: 'column',
                        textAlign: 'center',
                        mb: 2
                    }}
                >
                    <Typography variant="h6" color="error" gutterBottom>
                        网络连接问题
                    </Typography>
                    <Typography variant="body1" gutterBottom>
                        无法连接到服务器，请检查您的网络连接并刷新页面
                    </Typography>
                    <Button
                        variant="contained"
                        color="primary"
                        onClick={() => window.location.reload()}
                        sx={{ mt: 2 }}
                    >
                        刷新页面
                    </Button>
                </Paper>
            )}

            <StyledPaper elevation={3} sx={{
                mx: { xs: 0 }, // 移动设备上移除水平边距
                width: { xs: '100%' }, // 移动设备上使用100%宽度
                borderRadius: { xs: 0, sm: 2 }, // 移动设备上移除边框圆角
                boxShadow: { xs: 'none', sm: 3 }, // 移动设备上移除阴影
                p: { xs: 2, sm: 3 }, // 移动设备上减少内边距
                border: 'none' // 移除边框
            }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h4" component="h1" gutterBottom>
                        {t('membership_info')}
                    </Typography>
                    {loggedInUser && isViewingOwnProfile && (
                        <Button
                            variant="contained"
                            color="primary"
                            onClick={canRenew ? handleNavigateToStore : handleOpenRenewDialog}
                            disabled={loading}
                            startIcon={<AutorenewIcon />}
                        >
                            {canRenew ? t('purchase_membership') : t('renew_early')}
                        </Button>
                    )}
                </Box>

                {error && (
                    <Alert severity="error" sx={{ width: '100%', mb: 2 }}>
                        {error}
                    </Alert>
                )}

                {renewSuccess && (
                    <Alert severity="success" sx={{ width: '100%', mb: 2 }}>
                        {t('renewal_success')}
                    </Alert>
                )}

                {isLoading ? (
                    <Box sx={{ py: 5, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                        <CircularProgress size={50} />
                        <Typography variant="h6" sx={{ mt: 2 }}>
                            {t('loading_membership')}
                        </Typography>
                    </Box>
                ) : (
                    <Box sx={{ width: '100%' }}>
                        <Box sx={{ mb: 3 }}>
                            <Typography variant="h6" component="div" sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                <FamilyRestroomIcon sx={{ mr: 1 }} /> {t('family_members_status')}
                            </Typography>

                            <Grid container spacing={isMobile ? 1 : 2}>
                                {/* 先显示自己的会员信息 */}
                                <Grid item xs={12} md={6}>
                                    <Card
                                        variant="outlined"
                                        sx={{
                                            width: '100%',
                                            height: '100%',
                                            border: isMobile ? 'none' : undefined,
                                            borderRadius: isMobile ? 1 : undefined,
                                            boxShadow: isMobile ? 1 : undefined,
                                            mb: 2,
                                            display: 'flex',
                                            flexDirection: 'column'
                                        }}
                                    >
                                        <CardContent sx={{ 
                                            p: isMobile ? 2 : undefined,
                                            flex: 1,
                                            display: 'flex',
                                            flexDirection: 'column'
                                        }}>
                                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                                                <Typography variant="h6">
                                                    {loggedInUser?.first_name && loggedInUser?.last_name
                                                        ? `${loggedInUser.first_name} ${loggedInUser.last_name}`
                                                        : loggedInUser?.demographicInfo?.first_name && loggedInUser?.demographicInfo?.last_name
                                                            ? `${loggedInUser.demographicInfo.first_name} ${loggedInUser.demographicInfo.last_name}`
                                                            : t('your_membership')}
                                                    <Chip
                                                        size="small"
                                                        label={t('self')}
                                                        sx={{ ml: 1, textTransform: 'capitalize' }}
                                                    />
                                                </Typography>
                                                {membershipData?.current && (
                                                    <Chip
                                                        label={membershipData.current.isExpired ? t('expired') : t('active')}
                                                        color={membershipData.current.isExpired ? 'error' : 'success'}
                                                        sx={{ fontWeight: 'bold' }}
                                                    />
                                                )}
                                            </Box>

                                            {!membershipData || !membershipData.current ? (
                                                <>
                                                    <Divider sx={{ mb: 2 }} />
                                                    
                                                    {/* 为无会员信息的卡片创建相同的网格布局结构 */}
                                                    <Grid container spacing={2}>
                                                        <Grid item xs={12}>
                                                            <Box sx={{ 
                                                                textAlign: 'center', 
                                                                py: 4,
                                                                display: 'flex',
                                                                flexDirection: 'column',
                                                                alignItems: 'center',
                                                                justifyContent: 'center',
                                                                minHeight: 120 // 确保最小高度与有会员信息的卡片相似
                                                            }}>
                                                                <PersonIcon sx={{ 
                                                                    fontSize: 48, 
                                                                    color: 'text.secondary', 
                                                                    mb: 1 
                                                                }} />
                                                                <Typography variant="body1" color="text.secondary" gutterBottom>
                                                                    {t('no_membership')}
                                                                </Typography>
                                                                <Typography variant="body2" color="text.secondary">
                                                                    {t('click_below_to_purchase')}
                                                                </Typography>
                                                            </Box>
                                                        </Grid>
                                                    </Grid>
                                                    
                                                    {/* 添加一个占位的底部空间，保持卡片高度一致 */}
                                                    <Box sx={{ mt: 'auto', pt: 2 }}>
                                                        <Button
                                                            variant="contained"
                                                            color="primary"
                                                            fullWidth
                                                            startIcon={<CreditCardIcon />}
                                                            onClick={handleNavigateToStore}
                                                        >
                                                            {t('purchase_membership')}
                                                        </Button>
                                                    </Box>
                                                </>
                                            ) : (
                                                <>
                                                    <Divider sx={{ mb: 2 }} />

                                                    <Grid container spacing={2}>
                                                        <Grid item xs={6}>
                                                            <InfoItem
                                                                icon={<CalendarMonthIcon />}
                                                                primary={t('start_date')}
                                                                secondary={formatDate(membershipData.current.service_date)}
                                                            />
                                                        </Grid>
                                                        <Grid item xs={6}>
                                                            <InfoItem
                                                                icon={<CalendarMonthIcon />}
                                                                primary={t('end_date')}
                                                                secondary={formatDate(membershipData.current.endDate)}
                                                            />
                                                        </Grid>
                                                        <Grid item xs={6}>
                                                            <InfoItem
                                                                icon={<CreditCardIcon />}
                                                                primary={t('membership_type')}
                                                                secondary={getMembershipTypeLabel(membershipData.current.billing_code)}
                                                            />
                                                        </Grid>
                                                        <Grid item xs={6}>
                                                            <InfoItem
                                                                icon={<PaymentIcon />}
                                                                primary={t('payment_amount')}
                                                                secondary={formatPrice(membershipData.current.bill_amount)}
                                                            />
                                                        </Grid>
                                                    </Grid>

                                                    <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                                                        <Button
                                                            variant={membershipData.current.isExpired ? "contained" : "outlined"}
                                                            color="primary"
                                                            startIcon={<AutorenewIcon />}
                                                            onClick={handleOpenRenewDialog}
                                                        >
                                                            {membershipData.current.isExpired ? t('renew_membership') : t('renew_early')}
                                                        </Button>
                                                    </Box>
                                                </>
                                            )}
                                        </CardContent>
                                    </Card>
                                </Grid>

                                {/* 然后是家庭成员 */}
                                {familyMemberships.map((member) => (
                                    <Grid item xs={12} md={6} key={member.demographic_no}>
                                        <Card
                                            variant="outlined"
                                            sx={{
                                                width: '100%',
                                                height: '100%',
                                                border: isMobile ? 'none' : undefined,
                                                borderRadius: isMobile ? 1 : undefined,
                                                boxShadow: isMobile ? 1 : undefined,
                                                mb: 2,
                                                display: 'flex',
                                                flexDirection: 'column'
                                            }}
                                        >
                                            <CardContent sx={{ 
                                                p: isMobile ? 2 : undefined,
                                                flex: 1,
                                                display: 'flex',
                                                flexDirection: 'column'
                                            }}>
                                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                                                    <Typography variant="h6">
                                                        {member.first_name} {member.last_name}
                                                        <Chip
                                                            size="small"
                                                            label={member.relationship_type || t('family_member')}
                                                            sx={{ ml: 1, textTransform: 'capitalize' }}
                                                        />
                                                    </Typography>
                                                    {member.membership?.current && (
                                                        <Chip
                                                            label={member.membership.current.isExpired ? t('expired') : t('active')}
                                                            color={member.membership.current.isExpired ? 'error' : 'success'}
                                                            sx={{ fontWeight: 'bold' }}
                                                        />
                                                    )}
                                                </Box>

                                                {!member.membership || !member.membership.current ? (
                                                    <>
                                                        <Divider sx={{ mb: 2 }} />
                                                        
                                                        {/* 为无会员信息的卡片创建相同的网格布局结构 */}
                                                        <Grid container spacing={2}>
                                                            <Grid item xs={12}>
                                                                <Box sx={{ 
                                                                    textAlign: 'center', 
                                                                    py: 4,
                                                                    display: 'flex',
                                                                    flexDirection: 'column',
                                                                    alignItems: 'center',
                                                                    justifyContent: 'center',
                                                                    minHeight: 120 // 确保最小高度与有会员信息的卡片相似
                                                                }}>
                                                                    <PersonIcon sx={{ 
                                                                        fontSize: 48, 
                                                                        color: 'text.secondary', 
                                                                        mb: 1 
                                                                    }} />
                                                                    <Typography variant="body1" color="text.secondary" gutterBottom>
                                                                        {t('no_membership')}
                                                                    </Typography>
                                                                    <Typography variant="body2" color="text.secondary">
                                                                        {t('contact_to_add_membership')}
                                                                    </Typography>
                                                                </Box>
                                                            </Grid>
                                                        </Grid>
                                                        
                                                        {/* 添加一个占位的底部空间，保持卡片高度一致 */}
                                                        <Box sx={{ mt: 'auto', pt: 2 }}>
                                                            <Button
                                                                variant="outlined"
                                                                color="primary"
                                                                fullWidth
                                                                startIcon={<CreditCardIcon />}
                                                                onClick={handleNavigateToStore}
                                                                sx={{ opacity: 0.7 }}
                                                            >
                                                                {t('add_membership')}
                                                            </Button>
                                                        </Box>
                                                    </>
                                                ) : (
                                                    <>
                                                        <Divider sx={{ mb: 2 }} />

                                                        <Grid container spacing={2}>
                                                            <Grid item xs={6}>
                                                                <InfoItem
                                                                    icon={<CalendarMonthIcon />}
                                                                    primary={t('start_date')}
                                                                    secondary={formatDate(member.membership.current.service_date)}
                                                                />
                                                            </Grid>
                                                            <Grid item xs={6}>
                                                                <InfoItem
                                                                    icon={<CalendarMonthIcon />}
                                                                    primary={t('end_date')}
                                                                    secondary={formatDate(member.membership.current.endDate)}
                                                                />
                                                            </Grid>
                                                            <Grid item xs={6}>
                                                                <InfoItem
                                                                    icon={<CreditCardIcon />}
                                                                    primary={t('membership_type')}
                                                                    secondary={getMembershipTypeLabel(member.membership.current.billing_code)}
                                                                />
                                                            </Grid>
                                                            <Grid item xs={6}>
                                                                <InfoItem
                                                                    icon={<PaymentIcon />}
                                                                    primary={t('payment_amount')}
                                                                    secondary={formatPrice(member.membership.current.bill_amount)}
                                                                />
                                                            </Grid>
                                                        </Grid>
                                                    </>
                                                )}
                                            </CardContent>
                                        </Card>
                                    </Grid>
                                ))}

                                {familyMemberships.length === 0 && !membershipData && (
                                    <Grid item xs={12}>
                                        <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'background.default' }}>
                                            <Typography color="text.secondary">
                                                {t('no_membership_records')}
                                            </Typography>
                                        </Paper>
                                    </Grid>
                                )}
                            </Grid>
                        </Box>

                        {/* Membership details accordion - Hidden per user request */}
                        {false && (
                            <Accordion sx={{
                                boxShadow: isMobile ? 'none' : undefined,
                                border: isMobile ? 'none' : '1px solid rgba(0, 0, 0, 0.12)',
                                borderRadius: isMobile ? 0 : 1,
                                '&:before': {
                                    display: 'none', // 移除默认的上边框
                                },
                                width: isMobile ? '100%' : undefined,
                                mt: 2
                            }}>
                                <AccordionSummary
                                    expandIcon={<ExpandMoreIcon />}
                                    aria-controls="membership-info-content"
                                    id="membership-info-header"
                                    sx={{
                                        background: isMobile ? 'white' : undefined,
                                        borderRadius: isMobile ? 1 : undefined
                                    }}
                                >
                                    <Typography sx={{ display: 'flex', alignItems: 'center' }}>
                                        <InfoIcon sx={{ mr: 1, color: 'primary.main' }} />
                                        {t('membership_details')}
                                    </Typography>
                                </AccordionSummary>
                                <AccordionDetails>
                                    <Box sx={{ mb: 2 }}>
                                        <Typography variant="h6" gutterBottom>
                                            {t('available_membership_types')}:
                                        </Typography>

                                        <TableContainer component={Paper} variant="outlined">
                                            <Table aria-label="membership types table" size="small">
                                                <TableHead>
                                                    <TableRow>
                                                        <TableCell>{t('membership_type')}</TableCell>
                                                        <TableCell align="right">{t('price')}</TableCell>
                                                    </TableRow>
                                                </TableHead>
                                                <TableBody>
                                                    {typesLoading ? (
                                                        <TableRow>
                                                            <TableCell colSpan={2} align="center">
                                                                <CircularProgress size={24} />
                                                            </TableCell>
                                                        </TableRow>
                                                    ) : membershipTypes.length > 0 ? (
                                                        membershipTypes.map((type) => (
                                                            <TableRow key={type.value}>
                                                                <TableCell component="th" scope="row">
                                                                    {type.label}
                                                                </TableCell>
                                                                <TableCell align="right">
                                                                    {formatPrice(type.price)}
                                                                </TableCell>
                                                            </TableRow>
                                                        ))
                                                    ) : (
                                                        <TableRow>
                                                            <TableCell colSpan={2} align="center">
                                                                {t('no_membership_types')}
                                                            </TableCell>
                                                        </TableRow>
                                                    )}
                                                </TableBody>
                                            </Table>
                                        </TableContainer>
                                    </Box>

                                    <Typography variant="body2" color="text.secondary">
                                        {t('membership_benefits_note')}
                                    </Typography>
                                </AccordionDetails>
                            </Accordion>
                        )}
                    </Box>
                )}
            </StyledPaper>



            {/* Add a new card for Referrals */}
            <CardContainer>
                <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
                    <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
                        <Tabs
                            value={referralTabValue}
                            onChange={handleReferralTabChange}
                            aria-label="referral tabs"
                            centered
                            variant={isMobile ? "fullWidth" : "standard"}
                        >
                            <Tab 
                                label={t('your_referral_code')} 
                                icon={<RedeemIcon />} 
                                iconPosition="start"
                            />
                            <Tab 
                                label={t('referred_users')} 
                                icon={<PeopleIcon />} 
                                iconPosition="start"
                            />
                        </Tabs>
                    </Box>

                    {/* Referral Code Tab */}
                    <Box role="tabpanel" hidden={referralTabValue !== 0} id="referral-tabpanel-0">
                        {referralTabValue === 0 && (
                            <Box sx={{ p: 2 }}>
                                <Typography variant="h6" gutterBottom>
                                    {t('referral_code_title')}
                                </Typography>
                                
                                <Typography variant="body1" color="text.secondary" paragraph>
                                    {t('referral_code_description')}
                                </Typography>
                                
                                <Box 
                                    sx={{ 
                                        display: 'flex', 
                                        alignItems: 'center', 
                                        justifyContent: 'center',
                                        my: 3
                                    }}
                                >
                                    <Paper
                                        elevation={2}
                                        sx={{
                                            p: 2,
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'space-between',
                                            width: '100%',
                                            maxWidth: 300,
                                            border: '2px dashed',
                                            borderColor: 'primary.main',
                                            bgcolor: 'background.paper'
                                        }}
                                    >
                                        <Typography 
                                            variant="h5" 
                                            sx={{ 
                                                fontWeight: 'bold',
                                                letterSpacing: 1,
                                                color: 'primary.main',
                                                fontFamily: 'monospace'
                                            }}
                                        >
                                            {referralCode || '...'}
                                        </Typography>
                                        
                                        <Tooltip title={copied ? t('copied') : t('copy_code')} arrow>
                                            <IconButton 
                                                color="primary" 
                                                onClick={handleCopyReferralCode}
                                                sx={{ ml: 1 }}
                                            >
                                                <ContentCopyIcon />
                                            </IconButton>
                                        </Tooltip>
                                    </Paper>
                                </Box>
                                
                                <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center' }}>
                                    <Button
                                        variant="contained"
                                        startIcon={<ShareIcon />}
                                        onClick={() => {
                                            const text = t('share_referral_text').replace('{code}', referralCode);
                                            if (navigator.share) {
                                                navigator.share({
                                                    title: t('share_referral_title'),
                                                    text: text,
                                                    url: window.location.origin
                                                }).catch(err => console.error('Error sharing:', err));
                                            } else {
                                                handleCopyReferralCode();
                                            }
                                        }}
                                    >
                                        {t('share_referral')}
                                    </Button>
                                </Box>
                                
                                <Box sx={{ mt: 4, textAlign: 'center' }}>
                                    <Chip 
                                        icon={<RedeemIcon />} 
                                        label={`${t('total_reward_points')}: ${totalPoints}`}
                                        color="primary"
                                        variant="outlined"
                                    />
                                </Box>
                            </Box>
                        )}
                    </Box>

                    {/* Referred Users Tab */}
                    <Box role="tabpanel" hidden={referralTabValue !== 1} id="referral-tabpanel-1">
                        {referralTabValue === 1 && (
                            <Box sx={{ p: 2 }}>
                                <Typography variant="h6" gutterBottom sx={{ mb: 3 }}>
                                    {t('referred_users_title')}
                                </Typography>
                                
                                {referrals.length > 0 ? (
                                    <List>
                                        {referrals.map((referral) => (
                                            <ListItem
                                                key={referral.id}
                                                divider
                                                sx={{ 
                                                    mb: 1, 
                                                    borderRadius: 1,
                                                    bgcolor: 'background.paper',
                                                    boxShadow: 1
                                                }}
                                            >
                                                <ListItemAvatar>
                                                    <Avatar sx={{ bgcolor: 'primary.main' }}>
                                                        <PersonIcon />
                                                    </Avatar>
                                                </ListItemAvatar>
                                                <ListItemText
                                                    primary={`${referral.first_name} ${referral.last_name}`}
                                                    secondary={t('referred_on', { date: formatDate(referral.formatted_date) })}
                                                />
                                                <Chip
                                                    label={referral.status === 'rewarded' 
                                                        ? t('status_rewarded', { points: referral.reward_points })
                                                        : t(`status_${referral.status}`)}
                                                    color={referral.status === 'rewarded' ? 'success' : 'primary'}
                                                    variant={referral.status === 'rewarded' ? 'default' : 'outlined'}
                                                    size="small"
                                                />
                                            </ListItem>
                                        ))}
                                    </List>
                                ) : (
                                    <Box sx={{ textAlign: 'center', py: 4 }}>
                                        <GroupAddIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
                                        <Typography variant="body1" color="text.secondary">
                                            {t('no_referrals_yet')}
                                        </Typography>
                                        <Button
                                            variant="text"
                                            onClick={() => setReferralTabValue(0)}
                                            sx={{ mt: 2 }}
                                        >
                                            {t('get_your_referral_code')}
                                        </Button>
                                    </Box>
                                )}
                            </Box>
                        )}
                    </Box>
                </Paper>
            </CardContainer>

            {/* 会员续订对话框 */}
            <Dialog open={renewDialogOpen} onClose={handleCloseRenewDialog} fullWidth maxWidth="sm">
                <DialogTitle>{t('renew_membership')}</DialogTitle>
                <DialogContent>
                    {renewError && (
                        <Alert severity="error" sx={{ mb: 2 }}>{renewError}</Alert>
                    )}

                    <Typography variant="body1" gutterBottom sx={{ mb: 3 }}>
                        {t('select_membership_type')}:
                    </Typography>

                    <FormControl fullWidth variant="outlined" sx={{ mb: 3 }}>
                        <InputLabel id="membership-type-label">{t('membership_type')}</InputLabel>
                        <Select
                            labelId="membership-type-label"
                            id="membership-type"
                            value={selectedType}
                            onChange={handleTypeChange}
                            label={t('membership_type')}
                            disabled={typesLoading}
                        >
                            {typesLoading ? (
                                <MenuItem value="" disabled>
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        <CircularProgress size={20} sx={{ mr: 1 }} />
                                        {t('loading')}...
                                    </Box>
                                </MenuItem>
                            ) : membershipTypes.length > 0 ? (
                                membershipTypes.map((type) => (
                                    <MenuItem key={type.value} value={type.value}>
                                        {type.label} - {formatPrice(type.price)}
                                    </MenuItem>
                                ))
                            ) : (
                                <MenuItem value="" disabled>
                                    {t('no_membership_types')}
                                </MenuItem>
                            )}
                        </Select>
                    </FormControl>

                    <Typography variant="body2" color="text.secondary">
                        {t('payment_process_note')}
                    </Typography>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseRenewDialog}>{t('cancel')}</Button>
                    <Button
                        onClick={handleRenewMembership}
                        variant="contained"
                        color="primary"
                        disabled={!selectedType}
                    >
                        {t('proceed_to_payment')}
                    </Button>
                </DialogActions>
            </Dialog>
        </Container>
    );
};

export default Membership; 