# 会员权限控制系统实施指南

## 概述

本文档描述了MMC Wellness应用中实施的会员权限控制系统，该系统确保特定功能和服务仅对有效会员开放，同时保持健康商城对所有用户开放。

## 实施的功能

### 1. 会员服务权限控制

以下服务现在需要有效会员权限才能访问：

#### 受保护的服务（需要会员权限）：
- **健康指南** (`/health-guide`) - 专业医疗内容和健康指导
- **健康小贴士** (`/health-tips`) - 个性化健康建议
- **实验室报告** (`/lab-reports`) - 医疗检测结果查看
- **处方记录** (`/prescriptions`) - 药物处方历史
- **免疫接种记录** (`/immunizations`) - 疫苗接种历史
- **会员资源** (`/member-resources`) - 专属会员内容
- **YouTube视频** (`/youtube-videos`) - 健康教育视频
- **营养师服务** (`/dietician`) - 专业营养咨询

#### 开放服务（所有用户可访问）：
- **健康商城** (`/store`) - 产品购买和浏览
- **个人资料** (`/profile`) - 基本用户信息
- **预约管理** (`/appointments`) - 预约查看（基础功能）
- **会员状态** (`/membership`) - 会员信息查看
- **设置** (`/settings`) - 账户设置

### 2. Dr. Miao预约权限控制

#### 特殊预约限制：
- **Dr. Miao的预约时段**仅对有效会员开放
- 非会员用户在预约界面将看不到Dr. Miao的可用时间段
- 如果所有可用时段都是Dr. Miao的，非会员将看到"所选医生需要会员权限访问"的提示

#### 实现机制：
- **第一层过滤**：后端在`getConsultationTypes` API中过滤Dr. Miao专属的服务类型
- **第二层过滤**：后端在`getAvailabilityByType` API中过滤Dr. Miao的时间段
- **第三层过滤**：前端在显示时间段时进行三重过滤
- **智能识别**：基于医生姓名包含"miao"进行识别
- **优化的用户体验**：非会员在第一步就看不到Dr. Miao专属的服务类型

## 技术实现

### 1. 后端实现

#### 会员权限中间件
- 使用现有的用户类型系统（`user`, `member`, `admin`）
- 在`providerController.js`中添加Dr. Miao权限检查
- 路由级别的权限控制已通过现有中间件实现

#### Dr. Miao预约过滤

**1. 服务类型级别过滤 (getConsultationTypes)**
```javascript
// 在 providerController.js 中 - 第一层过滤
const userRole = req.user.role;
const isNonMember = userRole !== 'member' && userRole !== 'admin';

if (isNonMember) {
    for (const type of consultationTypes) {
        // 检查该服务类型的所有医生
        const [providers] = await pool.query(providerQuery, [checkStartDate, checkEndDate, type]);
        
        // 如果该服务类型只有Dr. Miao提供，则过滤掉
        const allProvidersMiao = providers.every(provider => {
            const fullName = `${provider.first_name} ${provider.last_name}`;
            return fullName.toLowerCase().includes('miao');
        });
        
        if (!allProvidersMiao || providers.length === 0) {
            filteredTypes.push(type);
        }
    }
}
```

**2. 时间段级别过滤 (getAvailabilityByType)**
```javascript
// 在 providerController.js 中 - 第二层过滤
const isDrMiaoRestricted = (providerName) => {
    return providerName && providerName.toLowerCase().includes('miao');
};

// 过滤非会员用户的Dr. Miao时段
if (isDrMiaoRestricted(providerName) && userRole !== 'member' && userRole !== 'admin') {
    continue; // 跳过此医生的时段
}
```

### 2. 前端实现

#### 用户类型Hook
创建了`useUserType` Hook来检查用户权限：
```javascript
// frontend/src/hooks/useUserType.js
export const useUserType = () => {
    const [userType, setUserType] = useState(null);
    // ... 实现用户类型检查逻辑
    return { userType, isMember, isAdmin, loading, error };
};
```

#### 会员权限守卫组件
创建了`MemberGuard`组件来保护需要会员权限的页面：
```javascript
// frontend/src/components/MemberGuard.js
const MemberGuard = ({ children, requireMember = true, fallbackMessage = null }) => {
    // ... 权限检查和友好的权限提示界面
};
```

#### 预约页面权限控制
在`BookingPage.js`中实现了Dr. Miao时段的前端过滤：
```javascript
// 过滤Dr. Miao的时段
const filteredAvailabilityByType = availabilityByType.filter(slot => {
    if (isDrMiao(slot.providerName) && !isMember) {
        return false;
    }
    return true;
});
```

### 3. 路由保护

在`App.js`中为需要会员权限的路由添加了`MemberGuard`包装：
```javascript
<Route path="/health-guide" element={<MemberGuard><HealthGuide /></MemberGuard>} />
<Route path="/health-tips" element={<MemberGuard><HealthTips /></MemberGuard>} />
// ... 其他受保护的路由
```

## 用户体验

### 1. 非会员用户体验
- 访问受保护功能时看到友好的会员权限提示页面
- 提示页面包含：
  - 清晰的权限说明
  - 会员专享服务列表
  - 查看会员状态和续费的快捷按钮
  - 美观的UI设计

### 2. 会员用户体验
- 无缝访问所有功能
- 可以预约包括Dr. Miao在内的所有医生
- 享受完整的平台服务

### 3. 预约功能体验
- **会员**：可以看到所有医生的可用时段
- **非会员**：只能看到除Dr. Miao外的医生时段
- 如果某个时间只有Dr. Miao可用，非会员会看到权限提示

## 会员判定逻辑

系统使用以下标准判定用户是否为有效会员：

1. **数据库查询**：检查`billingmaster`表中的最新记录
2. **会员代码**：`billing_code LIKE 'AHM%'`
3. **状态检查**：`billingstatus = 'A'`（有效状态）
4. **有效期**：从`service_date`开始计算，有效期一年
5. **自动更新**：用户登录时自动检查并更新用户类型

## 安全考虑

1. **双重验证**：后端和前端都进行权限检查
2. **API保护**：所有受保护的API端点都有中间件验证
3. **实时检查**：每次请求都验证用户权限
4. **优雅降级**：权限不足时提供友好的用户界面

## 维护和扩展

### 添加新的受保护功能
1. 在路由中添加`MemberGuard`包装
2. 确保相关API使用`requireMember`中间件
3. 测试权限控制是否正常工作

### 添加新的医生权限控制
1. 在`providerController.js`中修改医生识别逻辑
2. 在前端`BookingPage.js`中更新过滤条件
3. 测试预约功能的权限控制

### 自定义权限提示
可以为不同功能提供自定义的权限提示消息：
```javascript
<MemberGuard fallbackMessage="此高级功能需要会员权限">
    <AdvancedFeature />
</MemberGuard>
```

## 测试建议

1. **会员权限测试**：
   - 使用有效会员账户测试所有功能
   - 使用非会员账户验证权限限制

2. **预约功能测试**：
   - 测试Dr. Miao时段的权限控制
   - 验证其他医生的预约不受影响

3. **用户体验测试**：
   - 确认权限提示页面的友好性
   - 测试会员状态和续费按钮的功能

## 总结

本次实施成功建立了完整的会员权限控制系统，确保：
- 会员专享服务得到有效保护
- 健康商城保持对所有用户开放
- Dr. Miao的预约服务仅限会员访问
- 提供友好的用户体验和清晰的权限提示
- 系统具有良好的可维护性和扩展性 