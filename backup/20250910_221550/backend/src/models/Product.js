const pool = require('../config/database');

class Product {
    /**
     * 获取所有商品
     */
    static async getAllProducts(filters = {}) {
        try {
            let query = `
                SELECT 
                    p.*,
                    pc.name_en as category_name_en,
                    pc.name_zh as category_name_zh
                FROM products p
                LEFT JOIN product_categories pc ON p.category_id = pc.id
                WHERE p.status = 'active'
            `;
            
            const params = [];
            
            if (filters.category_id) {
                query += ' AND p.category_id = ?';
                params.push(filters.category_id);
            }
            
            if (filters.search) {
                query += ' AND (p.name_en LIKE ? OR p.name_zh LIKE ? OR p.description_en LIKE ? OR p.description_zh LIKE ?)';
                const searchTerm = `%${filters.search}%`;
                params.push(searchTerm, searchTerm, searchTerm, searchTerm);
            }
            
            query += ' ORDER BY p.sort_order ASC, p.created_at DESC';
            
            if (filters.limit) {
                query += ' LIMIT ?';
                params.push(parseInt(filters.limit));
            }
            
            const [products] = await pool.query(query, params);
            return { success: true, products };
        } catch (error) {
            console.error('Error fetching products:', error);
            return { success: false, message: 'Failed to fetch products', error: error.message };
        }
    }

    /**
     * 根据ID获取商品详情
     */
    static async getProductById(productId) {
        try {
            const [products] = await pool.query(`
                SELECT 
                    p.*,
                    pc.name_en as category_name_en,
                    pc.name_zh as category_name_zh
                FROM products p
                LEFT JOIN product_categories pc ON p.category_id = pc.id
                WHERE p.id = ? AND p.status = 'active'
            `, [productId]);
            
            if (products.length === 0) {
                return { success: false, message: 'Product not found' };
            }
            
            return { success: true, product: products[0] };
        } catch (error) {
            console.error('Error fetching product:', error);
            return { success: false, message: 'Failed to fetch product', error: error.message };
        }
    }

    /**
     * 获取商品分类
     */
    static async getCategories() {
        try {
            const [categories] = await pool.query(`
                SELECT * FROM product_categories 
                WHERE status = 'active' 
                ORDER BY sort_order ASC
            `);
            
            return { success: true, categories };
        } catch (error) {
            console.error('Error fetching categories:', error);
            return { success: false, message: 'Failed to fetch categories', error: error.message };
        }
    }

    /**
     * 创建新商品 (管理员功能)
     */
    static async createProduct(productData) {
        try {
            const {
                name_en, name_zh, description_en, description_zh,
                price, category_id, image_url, features_en, features_zh,
                duration_days, is_subscription, status = 'active'
            } = productData;

            const [result] = await pool.query(`
                INSERT INTO products (
                    name_en, name_zh, description_en, description_zh,
                    price, category_id, image_url, features_en, features_zh,
                    duration_days, is_subscription, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            `, [
                name_en, name_zh, description_en, description_zh,
                price, category_id, image_url, features_en, features_zh,
                duration_days, is_subscription, status
            ]);

            return { success: true, productId: result.insertId };
        } catch (error) {
            console.error('Error creating product:', error);
            return { success: false, message: 'Failed to create product', error: error.message };
        }
    }

    /**
     * 更新商品 (管理员功能)
     */
    static async updateProduct(productId, productData) {
        try {
            const {
                name_en, name_zh, description_en, description_zh,
                price, category_id, image_url, features_en, features_zh,
                duration_days, is_subscription, status
            } = productData;

            const [result] = await pool.query(`
                UPDATE products SET
                    name_en = ?, name_zh = ?, description_en = ?, description_zh = ?,
                    price = ?, category_id = ?, image_url = ?, features_en = ?, features_zh = ?,
                    duration_days = ?, is_subscription = ?, status = ?, updated_at = NOW()
                WHERE id = ?
            `, [
                name_en, name_zh, description_en, description_zh,
                price, category_id, image_url, features_en, features_zh,
                duration_days, is_subscription, status, productId
            ]);

            if (result.affectedRows === 0) {
                return { success: false, message: 'Product not found' };
            }

            return { success: true };
        } catch (error) {
            console.error('Error updating product:', error);
            return { success: false, message: 'Failed to update product', error: error.message };
        }
    }

    /**
     * 删除商品 (软删除)
     */
    static async deleteProduct(productId) {
        try {
            const [result] = await pool.query(`
                UPDATE products SET status = 'deleted', updated_at = NOW()
                WHERE id = ?
            `, [productId]);

            if (result.affectedRows === 0) {
                return { success: false, message: 'Product not found' };
            }

            return { success: true };
        } catch (error) {
            console.error('Error deleting product:', error);
            return { success: false, message: 'Failed to delete product', error: error.message };
        }
    }
}

module.exports = Product; 