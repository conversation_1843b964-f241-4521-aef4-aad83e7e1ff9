import { useState, useEffect } from 'react';
import { API_URL } from '../utils/env';

export const useUserType = () => {
    const [userType, setUserType] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        const fetchUserType = async () => {
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    setUserType('user'); // Default for non-authenticated users
                    setLoading(false);
                    return;
                }

                const response = await fetch(`${API_URL}/api/auth/user-type`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    setUserType(data.userType);
                } else {
                    setUserType('user'); // Default on error
                }
            } catch (err) {
                console.error('Error fetching user type:', err);
                setUserType('user'); // Default on error
                setError(err.message);
            } finally {
                setLoading(false);
            }
        };

        fetchUserType();
    }, []);

    const isMember = userType === 'member' || userType === 'admin';
    const isAdmin = userType === 'admin';

    return {
        userType,
        isMember,
        isAdmin,
        loading,
        error
    };
}; 