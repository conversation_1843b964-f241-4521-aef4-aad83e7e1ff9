const express = require('express');
const router = express.Router();
const membershipController = require('../controllers/membershipController');
const auth = require('../middleware/auth');

/**
 * @route   GET /api/membership/types
 * @desc    Get all membership types
 * @access  Private
 */
router.get('/types', auth, membershipController.getMembershipTypes);

/**
 * @route   GET /api/membership/types/:code
 * @desc    Get membership type by code
 * @access  Private
 */
router.get('/types/:code', auth, membershipController.getMembershipTypeByCode);

module.exports = router; 