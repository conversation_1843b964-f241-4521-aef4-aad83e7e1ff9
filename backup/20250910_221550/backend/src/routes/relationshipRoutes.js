const express = require('express');
const relationshipController = require('../controllers/relationshipController');
const authenticate = require('../middleware/authenticate'); // Corrected import path and name

const router = express.Router();

// Route to initiate the switch view verification process
// Requires authentication
router.post(
    '/switch-view/initiate',
    authenticate, // Use the correct middleware function
    relationshipController.initiateSwitchView
);

// Route to verify the switch view code
// Requires authentication
router.post(
    '/switch-view/verify',
    authenticate, // Use the correct middleware function
    relationshipController.verifySwitchViewCode
);

// Route to check if access was recently granted (within 30 mins)
// Requires authentication
router.get(
    '/check-access/:targetDemographicNo',
    authenticate,
    relationshipController.checkSwitchViewAccess
);

// NEW ROUTE: Get the list of family members for the logged-in user
// Requires authentication
router.get(
    '/family', // Endpoint defined here
    authenticate, // Ensure user is logged in
    relationshipController.getFamily // Map to the new controller function
);

// Example placeholder route for getting relationships (if needed)
// router.get('/', protect, relationshipController.getRelationships);

module.exports = router; 