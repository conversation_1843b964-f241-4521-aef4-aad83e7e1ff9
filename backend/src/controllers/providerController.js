const pool = require('../config/database');
const dayjs = require('dayjs'); // Import dayjs for easier date manipulation
const utc = require('dayjs/plugin/utc');
const timezone = require('dayjs/plugin/timezone'); // dependent on utc plugin

dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * Get all active providers (doctors and staff)
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} JSON response with providers list
 */
exports.getAllProviders = async (req, res) => {
    try {
        // Query to get all active providers (doctors and staff)
        const providersQuery = `
            SELECT 
                provider_no,
                last_name,
                first_name,
                provider_type,
                specialty,
                title,
                email,
                phone,
                status
            FROM provider
            WHERE status = '1' 
            ORDER BY provider_type, last_name, first_name
        `;

        const [providers] = await pool.query(providersQuery);

        const formattedProviders = providers.map(provider => ({
            id: provider.provider_no,
            lastName: provider.last_name,
            firstName: provider.first_name,
            type: provider.provider_type,
            specialty: provider.specialty,
            title: provider.title,
            email: provider.email,
            phone: provider.phone,
            fullName: `${provider.provider_type === 'doctor' ? 'Dr. ' : ''}${provider.first_name} ${provider.last_name}`
        }));

        return res.json({
            success: true,
            providers: formattedProviders
        });

    } catch (error) {
        console.error('Error retrieving providers:', error);
        return res.status(500).json({ message: 'Server error' });
    }
};

/**
 * Get all doctors (filtered by doctor provider_type)
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} JSON response with doctors list
 */
exports.getDoctors = async (req, res) => {
    try {
        // Query to get active doctors who have at least one schedule template
        const doctorsQuery = `
            SELECT DISTINCT
                p.provider_no,
                p.last_name,
                p.first_name
            FROM provider p
            JOIN scheduletemplate st ON p.provider_no = st.provider_no
            WHERE p.provider_type = 'doctor' AND p.status = '1'
            ORDER BY p.last_name, p.first_name
        `;

        const [doctors] = await pool.query(doctorsQuery);

        const formattedDoctors = doctors.map(doctor => ({
            id: doctor.provider_no,
            name: `Dr. ${doctor.first_name} ${doctor.last_name}`
        }));

        return res.json({
            success: true,
            doctors: formattedDoctors
        });

    } catch (error) {
        console.error('Error retrieving doctors:', error);
        return res.status(500).json({ message: 'Server error' });
    }
};

/**
 * @desc    Get available appointment slots for a specific provider
 * @route   GET /api/providers/:providerNo/availability
 * @access  Private
 * @query   startDate (optional, YYYY-MM-DD, defaults to day after tomorrow) - *Note: Frontend currently sends this, but logic uses a date range based on it.*
 * @query   duration (optional, number of days to check, defaults to 7) - *Currently unused, checks a fixed range.*
 */
exports.getProviderAvailability = async (req, res) => {
    const { providerNo } = req.params;
    const requestedStartDate = req.query.startDate; // Get requested start date if provided
    const serviceType = req.query.serviceType; // Get service type query parameter

    // 1. Validate Provider
    if (!providerNo) {
        return res.status(400).json({ success: false, message: 'Provider number is required.' });
    }
    // Consider fetching bookable providers dynamically instead of hardcoding
    // const allowedProviders = ['818', '830', '8000'];
    // if (!allowedProviders.includes(providerNo)) {
    //     return res.status(404).json({ success: false, message: 'Provider not found or not available for booking.' });
    // }

    try {
        // 2. Determine Date Range to Check
        const appTimeZone = 'America/Vancouver'; // Assuming server/database times relate to this timezone
        const today = dayjs().tz(appTimeZone);
        // Default start date is 2 days from now if not provided or invalid
        let checkStartDate = today.add(2, 'day');
        if (requestedStartDate && dayjs(requestedStartDate).isValid() && dayjs(requestedStartDate).isAfter(today.add(1, 'day'))) {
            // Use provided start date if it's valid and at least day after tomorrow
            checkStartDate = dayjs(requestedStartDate).tz(appTimeZone);
        }
        const checkEndDate = checkStartDate.add(14, 'day'); // Check for the next 14 days from the start date

        // 3. Fetch Active Schedule Dates and Template Names for the range
        const scheduleDateQuery = `
            SELECT
                DATE_FORMAT(sdate, '%Y-%m-%d') AS scheduleDate,
                hour AS templateName 
            FROM scheduledate
            WHERE provider_no = ?
              AND sdate BETWEEN ? AND ?
              AND status = 'A' 
              AND available = '1'
              ${serviceType ? 'AND hour = ?' : ''} -- Add filter for serviceType (templateName/hour)
        `;

        const queryParams = [
            providerNo,
            checkStartDate.format('YYYY-MM-DD'),
            checkEndDate.format('YYYY-MM-DD')
        ];
        if (serviceType) {
            queryParams.push(serviceType);
        }

        const [scheduleDates] = await pool.query(scheduleDateQuery, queryParams);

        if (scheduleDates.length === 0) {
            console.log(`No active schedule found for provider ${providerNo} ${serviceType ? 'and service ' + serviceType : ''} between ${checkStartDate.format('YYYY-MM-DD')} and ${checkEndDate.format('YYYY-MM-DD')}`);
            return res.json({ success: true, availability: [] });
        }

        // 4. Fetch Corresponding Schedule Template Timecodes
        const templateName = serviceType; // If serviceType is provided, that's the template we need
        if (!templateName) {
            // This should not happen if serviceType filter was applied correctly in scheduleDateQuery
            // but handle defensively.
            console.error("Error: Service type (template name) is missing after filtering schedule dates.");
            return res.status(500).json({ success: false, message: 'Internal error: Service type missing.' });
        }

        const templateQuery = `
            SELECT name, timecode
            FROM scheduletemplate
            WHERE provider_no = ?
              AND name = ? 
        `;
        const [templates] = await pool.query(templateQuery, [providerNo, templateName]);

        if (templates.length === 0) {
            console.log(`No schedule template found for provider ${providerNo}, service ${templateName}`);
            return res.json({ success: true, availability: [] });
        }

        const template = templates[0];
        const timecode = template.timecode;
        const timecodeLength = timecode ? timecode.length : 0;

        // --- Determine slot duration based on the fetched template --- 
        let slotDuration = 15; // Default to 15 mins (length 96)
        if (timecodeLength === 48) {
            slotDuration = 30;
        } else if (timecodeLength === 96) {
            slotDuration = 15;
        } else {
            console.warn(`Provider ${providerNo}, Service ${templateName}: Unexpected timecode length (${timecodeLength}). Assuming 15 min slots.`);
            // Proceed with default 15min if length is unexpected but timecode exists
            if (!timecode) {
                console.error(`Provider ${providerNo}, Service ${templateName}: Missing timecode data.`);
                return res.json({ success: true, availability: [] }); // Cannot generate slots without timecode
            }
        }
        const slotsPerHour = 60 / slotDuration;
        console.log(`[getProviderAvailability] Provider: ${providerNo}, Service: ${templateName}, Date: ${requestedStartDate}, Timecode Length: ${timecodeLength}, Slot Duration: ${slotDuration} min`);
        // --- End Duration Determination ---

        // 5. Fetch Existing Appointments for the Date Range
        const appointmentQuery = `
            SELECT
                DATE_FORMAT(appointment_date, '%Y-%m-%d') AS apptDate,
                TIME_FORMAT(start_time, '%H:%i') AS startTime
            FROM appointment
            WHERE provider_no = ?
              AND appointment_date BETWEEN ? AND ?
            /* Add filtering by status if certain statuses mean the slot IS available (e.g., cancelled)
              AND status NOT IN ('CA', 'NS', ...) */
        `;
        const [appointments] = await pool.query(appointmentQuery, [
            providerNo,
            checkStartDate.format('YYYY-MM-DD'),
            checkEndDate.format('YYYY-MM-DD')
        ]);

        // Create a set of booked slots for efficient lookup: "YYYY-MM-DD HH:MM"
        const bookedSlots = new Set(
            appointments.map(appt => `${appt.apptDate} ${appt.startTime}`)
        );

        // 6. Generate and Filter Available Slots
        const availableSlots = [];
        // Use dynamic slotsPerHour determined above

        // Iterate through the scheduleDates which are already filtered for the specific serviceType (templateName)
        for (const schedule of scheduleDates) {
            const dateStr = schedule.scheduleDate;
            // const templateName = schedule.templateName; // Already known
            // const timecode = templateMap[templateName]; // Already fetched as 'timecode'

            // Validate timecode length against determined expectation (48 or 96)
            if (!timecode || ![48, 96].includes(timecode.length)) {
                console.warn(`Invalid or unexpected timecode length ${timecode?.length} for provider ${providerNo}, date ${dateStr}, template ${templateName}. Skipping date.`);
                continue;
            }

            // Ensure the determined slot duration matches the actual timecode length for safety
            if ((timecode.length === 48 && slotDuration !== 30) || (timecode.length === 96 && slotDuration !== 15)) {
                console.warn(`Mismatch between determined slot duration (${slotDuration}) and timecode length (${timecode.length}) for ${dateStr}, ${templateName}. Skipping.`);
                continue;
            }

            const scheduleDay = dayjs.tz(dateStr, appTimeZone);
            console.log(`[getProviderAvailability] Processing date: ${dateStr} for template: ${templateName}`);

            for (let hour = 0; hour < 24; hour++) {
                for (let minuteIndex = 0; minuteIndex < slotsPerHour; minuteIndex++) {
                    const timecodeIndex = hour * slotsPerHour + minuteIndex;
                    const slotCode = timecode.charAt(timecodeIndex);

                    if (slotCode !== '_') { // Modified: Check if the code is NOT an underscore (assuming '_' means unavailable)
                        const minute = minuteIndex * slotDuration;
                        const slotTime = dayjs(scheduleDay).hour(hour).minute(minute).second(0);
                        const slotTimeStr = slotTime.format('HH:mm');
                        const slotDateTimeStr = `${dateStr} ${slotTimeStr}`;

                        // Check if slot is in the future (respecting the 2-day minimum) and not booked
                        if (slotTime.isAfter(today.add(1, 'day').endOf('day')) && !bookedSlots.has(slotDateTimeStr)) {
                            availableSlots.push({
                                date: dateStr,
                                time: slotTimeStr
                            });
                        }
                    }
                }
            }
        }

        // Sort the results chronologically
        availableSlots.sort((a, b) => {
            const dateTimeA = dayjs(`${a.date} ${a.time}`);
            const dateTimeB = dayjs(`${b.date} ${b.time}`);
            return dateTimeA.diff(dateTimeB);
        });

        res.json({ success: true, availability: availableSlots });

    } catch (error) {
        console.error(`Error fetching availability for provider ${providerNo}:`, error);
        res.status(500).json({ success: false, message: 'Server error fetching availability' });
    }
};

/**
 * @desc    Get distinct consultation types (schedule template names) that are currently bookable.
 * @route   GET /api/providers/consultation-types
 * @access  Private
 */
exports.getConsultationTypes = async (req, res) => {
    try {
        const appTimeZone = 'America/Vancouver';
        const today = dayjs().tz(appTimeZone);
        const checkStartDate = today.add(2, 'day').format('YYYY-MM-DD');
        const checkEndDate = today.add(32, 'day').format('YYYY-MM-DD');

        // Modified query: Remove strict length=96 check,
        // keep check for non-underscore chars, ensure status is active
        const query = `
            SELECT DISTINCT sd.hour AS consultationType
            FROM scheduledate sd
            JOIN scheduletemplate st ON sd.provider_no = st.provider_no AND sd.hour = st.name
            WHERE sd.status = 'A' 
              AND sd.sdate BETWEEN ? AND ?
              -- Ensure template timecode is either 48 or 96 length (common lengths)
              AND LENGTH(st.timecode) IN (48, 96) 
              -- Ensure timecode has at least one non-underscore (bookable) slot type
              AND st.timecode REGEXP '[^_]' 
            ORDER BY consultationType ASC;
        `;

        const [results] = await pool.query(query, [checkStartDate, checkEndDate]);

        const consultationTypes = results.map(row => row.consultationType);

        // Filter consultation types based on membership for Dr. Miao exclusive services
        const userRole = req.user.role;
        const isNonMember = userRole !== 'member' && userRole !== 'admin';

        if (isNonMember) {
            // For each consultation type, check if it's Dr. Miao exclusive
            const filteredTypes = [];
            
            for (const type of consultationTypes) {
                // Get all providers for this consultation type
                const providerQuery = `
                    SELECT DISTINCT p.provider_no, p.first_name, p.last_name
                    FROM scheduledate sd
                    JOIN scheduletemplate st ON sd.provider_no = st.provider_no AND sd.hour = st.name
                    JOIN provider p ON sd.provider_no = p.provider_no
                    WHERE sd.status = 'A' 
                      AND sd.sdate BETWEEN ? AND ?
                      AND sd.hour = ?
                      AND LENGTH(st.timecode) IN (48, 96) 
                      AND st.timecode REGEXP '[^_]'
                `;
                
                const [providers] = await pool.query(providerQuery, [checkStartDate, checkEndDate, type]);
                
                // Check if all providers for this type are Dr. Miao
                const allProvidersMiao = providers.every(provider => {
                    const fullName = `${provider.first_name} ${provider.last_name}`;
                    return fullName.toLowerCase().includes('miao');
                });
                
                // If not all providers are Dr. Miao, or there are no providers, include the type
                if (!allProvidersMiao || providers.length === 0) {
                    filteredTypes.push(type);
                } else {
                    console.log(`[Member Access Control] Filtering out Dr. Miao exclusive consultation type: ${type} for user role: ${userRole}`);
                }
            }
            
            res.json({ success: true, consultationTypes: filteredTypes });
        } else {
            res.json({ success: true, consultationTypes });
        }

    } catch (error) {
        console.error('Error fetching consultation types:', error);
        res.status(500).json({ success: false, message: 'Server error fetching consultation types' });
    }
};

/**
 * @desc    Get available appointment slots for a specific consultation type (template name)
 * @route   GET /api/providers/availability/by-type
 * @access  Private
 * @query   type (required, the schedule template name)
 * @query   startDate (optional, YYYY-MM-DD, defaults to day after tomorrow)
 */
exports.getAvailabilityByType = async (req, res) => {
    const { type } = req.query;
    const requestedStartDate = req.query.startDate;

    if (!type) {
        return res.status(400).json({ success: false, message: 'Consultation type parameter is required.' });
    }

    try {
        const appTimeZone = 'America/Vancouver';
        const today = dayjs().tz(appTimeZone);
        let checkStartDate = today.add(2, 'day');
        if (requestedStartDate && dayjs(requestedStartDate).isValid() && dayjs(requestedStartDate).isAfter(today.add(1, 'day'))) {
            checkStartDate = dayjs(requestedStartDate).tz(appTimeZone);
        }
        const checkEndDate = checkStartDate; // Check only the selected date
        const dateStr = checkStartDate.format('YYYY-MM-DD');
        const scheduleDay = dayjs.tz(dateStr, appTimeZone);

        // 2. Find Providers and Templates for the requested Type with VALID timecodes (48 or 96 length)
        const templateQuery = `
            SELECT provider_no, timecode
            FROM scheduletemplate
            WHERE name = ?
              AND LENGTH(timecode) IN (48, 96) -- Allow 48 or 96 length
        `;
        const [validTemplates] = await pool.query(templateQuery, [type]);

        if (validTemplates.length === 0) {
            console.log(`No valid templates (length 48 or 96) found for consultation type: ${type}`);
            return res.json({ success: true, availability: [] });
        }

        // --- Determine slot duration based on the FIRST valid template found --- 
        // (Assumption: All templates for a given 'type' name should have the same duration/length)
        const timecodeSample = validTemplates[0].timecode;
        const timecodeLength = timecodeSample.length;
        let slotDuration = 15; // Default
        if (timecodeLength === 48) {
            slotDuration = 30;
        } else if (timecodeLength !== 96) {
            console.warn(`Unexpected timecode length (${timecodeLength}) for type ${type}. Defaulting to 15 min slots.`);
        }
        const slotsPerHour = 60 / slotDuration;
        // --- End Duration Determination ---

        const providerIds = validTemplates.map(t => t.provider_no);
        const providerTemplateMap = validTemplates.reduce((acc, t) => {
            acc[t.provider_no] = t.timecode; // Store the actual timecode
            return acc;
        }, {});

        // 3. Fetch Active Schedule Dates for these providers and the specific type/date
        // Keep the check for available='1' here, as it pertains to the specific day's schedule instance
        const scheduleDateQuery = `
            SELECT provider_no
            FROM scheduledate
            WHERE provider_no IN (?)
              AND hour = ? 
              AND status = 'A' 
              AND available = '1' 
              AND sdate = ?
        `;
        const [scheduleDates] = await pool.query(scheduleDateQuery, [
            providerIds,
            type,
            dateStr
        ]);

        if (scheduleDates.length === 0) {
            console.log(`No active/available schedule found for type ${type} on ${dateStr} for providers ${providerIds.join(', ')}`);
            return res.json({ success: true, availability: [] });
        }

        const providerScheduleMap = scheduleDates.reduce((acc, sd) => {
            acc[sd.provider_no] = true;
            return acc;
        }, {});

        // 4. Fetch Existing Appointments
        const appointmentQuery = `
            SELECT provider_no, TIME_FORMAT(start_time, '%H:%i') AS startTime
            FROM appointment
            WHERE provider_no IN (?)
              AND appointment_date = ?
        `;
        const [appointments] = await pool.query(appointmentQuery, [providerIds, dateStr]);

        const bookedSlots = new Set(
            appointments.map(appt => `${appt.provider_no}:${appt.startTime}`)
        );

        // Fetch provider names
        const providerNameQuery = `SELECT provider_no, first_name, last_name FROM provider WHERE provider_no IN (?)`;
        const [providerDetails] = await pool.query(providerNameQuery, [providerIds]);
        const providerNameMap = providerDetails.reduce((acc, p) => {
            acc[p.provider_no] = `Dr. ${p.first_name} ${p.last_name}`;
            return acc;
        }, {});

        // Check user's membership status for Dr. Miao access control
        const userRole = req.user.role;
        const isDrMiaoRestricted = (providerName) => {
            // Check if this is Dr. Miao's appointment
            return providerName && providerName.toLowerCase().includes('miao');
        };

        // 5. Generate and Filter Available Slots using DYNAMIC duration
        const allAvailableSlots = [];

        for (const providerId of providerIds) {
            if (!providerScheduleMap[providerId]) continue; // Skip if no active schedule date for this provider

            const providerName = providerNameMap[providerId] || `Provider ${providerId}`;
            
            // Check if this is Dr. Miao and user is not a member
            if (isDrMiaoRestricted(providerName) && userRole !== 'member' && userRole !== 'admin') {
                console.log(`[Member Access Control] Skipping Dr. Miao slots for user role: ${userRole}`);
                continue; // Skip this provider's slots for non-members
            }

            const timecode = providerTemplateMap[providerId];
            if (!timecode || timecode.length !== timecodeLength) continue; // Ensure consistent length for this provider

            // --- DEBUG LOGGING for 30-min slots ---
            const isDebuggingThisProvider = (providerId === '830' && timecodeLength === 48); // Example: Log only for provider 830 with 30-min slots
            if (isDebuggingThisProvider) {
                console.log(`[Debug ${dateStr} ${providerId} ${type}] Timecode (len ${timecodeLength}): ${timecode}`);
            }
            // --- END DEBUG LOGGING ---

            for (let hour = 0; hour < 24; hour++) {
                // Use dynamic slotsPerHour based on determined duration
                for (let minuteIndex = 0; minuteIndex < slotsPerHour; minuteIndex++) {
                    const timecodeIndex = hour * slotsPerHour + minuteIndex;

                    // Boundary check for safety, although length check should prevent this
                    if (timecodeIndex >= timecode.length) continue;

                    const slotCode = timecode.charAt(timecodeIndex);

                    // --- DEBUG LOGGING --- 
                    let debugSlotTime = null;
                    let debugMinute = null;
                    // --- END DEBUG LOGGING ---

                    if (slotCode !== '_') {
                        // Use dynamic slotDuration
                        const minute = minuteIndex * slotDuration;
                        const slotTime = dayjs(scheduleDay).hour(hour).minute(minute).second(0);
                        const slotTimeStr = slotTime.format('HH:mm');
                        const slotProviderTimeStr = `${providerId}:${slotTimeStr}`;

                        // --- DEBUG LOGGING ---
                        debugSlotTime = slotTime.format('YYYY-MM-DD HH:mm'); // Store for logging
                        debugMinute = minute;
                        const isBooked = bookedSlots.has(slotProviderTimeStr);
                        const isAfterCutoff = slotTime.isAfter(today.add(2, 'day').endOf('day'));
                        const passesFilters = isAfterCutoff && !isBooked;
                        // --- END DEBUG LOGGING ---

                        // Check future and booked status
                        // Booking must be at least 3 days in advance
                        if (passesFilters) { // Use the calculated boolean
                            allAvailableSlots.push({
                                date: dateStr,
                                time: slotTimeStr,
                                provider_no: providerId,
                                providerName: providerName
                            });
                        }
                    }
                    // --- DEBUG LOGGING (Log details for 30-min slots of the target provider) ---
                    if (isDebuggingThisProvider) {
                        console.log(`  [Debug ${dateStr} ${providerId}] Hour=${hour}, minuteIndex=${minuteIndex} (timecodeIdx=${timecodeIndex}) -> slotCode='${slotCode}', calculatedMinute=${debugMinute !== null ? debugMinute : 'N/A'}, slotTime=${debugSlotTime || 'N/A'}, isBooked=${bookedSlots.has(`${providerId}:${debugSlotTime ? debugSlotTime.split(' ')[1] : ''}`) || 'N/A'}, passesDateRule=${debugSlotTime ? dayjs(debugSlotTime).isAfter(today.add(2, 'day').endOf('day')) : 'N/A'}`);
                    }
                    // --- END DEBUG LOGGING ---
                }
            }
        }

        // Sort results
        allAvailableSlots.sort((a, b) => {
            if (a.time < b.time) return -1;
            if (a.time > b.time) return 1;
            if (a.providerName < b.providerName) return -1;
            if (a.providerName > b.providerName) return 1;
            return 0;
        });

        res.json({ success: true, availability: allAvailableSlots });

    } catch (error) {
        console.error(`Error fetching availability for type ${type}:`, error);
        res.status(500).json({ success: false, message: 'Server error fetching availability by type' });
    }
};

/**
 * @desc    Get available dates for a specific consultation type (template name)
 * @route   GET /api/providers/availability/by-type/dates
 * @access  Private
 * @query   type (required, the schedule template name)
 * @query   startDate (optional, YYYY-MM-DD, defaults to day after tomorrow)
 * @query   endDate (optional, YYYY-MM-DD, defaults to 30 days after startDate)
 */
exports.getAvailableDatesByType = async (req, res) => {
    const { type } = req.query;
    const requestedStartDate = req.query.startDate;
    const requestedEndDate = req.query.endDate;

    if (!type) {
        return res.status(400).json({ success: false, message: 'Consultation type parameter is required.' });
    }

    try {
        // 1. Determine Date Range to Check
        const appTimeZone = 'America/Vancouver';
        const today = dayjs().tz(appTimeZone);

        let checkStartDate = today.add(3, 'day'); // Adjusted to match 3-day rule
        if (requestedStartDate && dayjs(requestedStartDate).isValid() && dayjs(requestedStartDate).isAfter(today.add(2, 'day'))) { // Ensure start date is at least 3 days out
            checkStartDate = dayjs(requestedStartDate).tz(appTimeZone);
        }

        let checkEndDate = checkStartDate.add(30, 'day');
        if (requestedEndDate && dayjs(requestedEndDate).isValid() && dayjs(requestedEndDate).isAfter(checkStartDate)) {
            checkEndDate = dayjs(requestedEndDate).tz(appTimeZone);
        }

        console.log(`[getAvailableDatesByType] Service Type: ${type}`);
        console.log(`[getAvailableDatesByType] Checking Date Range: ${checkStartDate.format('YYYY-MM-DD')} to ${checkEndDate.format('YYYY-MM-DD')}`);

        // Query providers for the service type
        // Removed LENGTH check for broader debugging
        const providersQuery = `
            SELECT DISTINCT st.provider_no
            FROM scheduletemplate st
            WHERE st.name = ?
        `;
        const [validProviders] = await pool.query(providersQuery, [type]);

        if (validProviders.length === 0) {
            console.log(`[getAvailableDatesByType] No providers found for type: ${type}`);
            return res.json({ success: true, availableDates: [] });
        }

        const providerIds = validProviders.map(p => p.provider_no);
        console.log(`[getAvailableDatesByType] Found Provider IDs: ${providerIds.join(', ')}`);

        // Query available dates based on scheduledate
        const scheduleDatesQuery = `
            SELECT DISTINCT DATE_FORMAT(sdate, '%Y-%m-%d') AS availableDate
            FROM scheduledate
            WHERE provider_no IN (?)
              AND hour = ?
              AND status = 'A' 
              AND available = '1'
              AND sdate BETWEEN ? AND ?
            ORDER BY availableDate
        `;

        const [scheduleDates] = await pool.query(scheduleDatesQuery, [
            providerIds,
            type,
            checkStartDate.format('YYYY-MM-DD'),
            checkEndDate.format('YYYY-MM-DD')
        ]);

        // Extract the dates
        const availableDates = scheduleDates.map(date => date.availableDate);
        console.log(`[getAvailableDatesByType] Found available dates (before final check): ${availableDates.join(', ')}`);

        // Potential future enhancement: 
        // Add a check here to ensure each returned date ACTUALLY has slots 
        // that meet the 3-day rule by calling a simplified version of the slot checking logic.
        // For now, we just return dates based on schedule entries.

        res.json({ success: true, availableDates });

    } catch (error) {
        console.error(`[getAvailableDatesByType] Error fetching available dates for type ${type}:`, error);
        res.status(500).json({ success: false, message: 'Server error fetching available dates' });
    }
};

// --- NEW CONTROLLER FUNCTION --- 
/**
 * @desc    Get distinct consultation types (schedule template names) offered by a specific provider.
 * @route   GET /api/providers/:providerNo/services
 * @access  Private
 */
exports.getProviderServices = async (req, res) => {
    const { providerNo } = req.params;
    if (!providerNo) {
        return res.status(400).json({ success: false, message: 'Provider number is required.' });
    }

    try {
        // Query scheduletemplate directly for distinct names associated with the provider
        const query = `
            SELECT DISTINCT name
            FROM scheduletemplate
            WHERE provider_no = ? 
              -- Optional: Add checks similar to getConsultationTypes if needed
              -- AND LENGTH(timecode) IN (48, 96) 
              -- AND timecode REGEXP '[^_]'
            ORDER BY name ASC;
        `;

        const [results] = await pool.query(query, [providerNo]);
        const services = results.map(row => row.name);

        res.json({ success: true, services });

    } catch (error) {
        console.error(`Error fetching services for provider ${providerNo}:`, error);
        res.status(500).json({ success: false, message: 'Server error fetching provider services' });
    }
};
// -------------------------------- 

/**
 * @desc    Get available DATES for a specific provider and service type.
 * @route   GET /api/providers/:providerNo/availability/dates
 * @access  Private
 * @query   serviceType (required)
 * @query   startDate (optional, YYYY-MM-DD, defaults to 3 days from now)
 * @query   daysToCheck (optional, number, defaults to 60)
 */
exports.getProviderAvailableDates = async (req, res) => {
    const { providerNo } = req.params;
    const { serviceType, startDate, daysToCheck } = req.query;

    if (!providerNo || !serviceType) {
        return res.status(400).json({ success: false, message: 'Provider number and service type are required.' });
    }

    try {
        const appTimeZone = 'America/Vancouver';
        const today = dayjs().tz(appTimeZone);
        const numDaysToCheck = parseInt(daysToCheck) || 60;

        let checkStartDate = today.add(3, 'day'); // Ensure minimum 3 days ahead
        if (startDate && dayjs(startDate).isValid() && dayjs(startDate).isAfter(today.add(2, 'day'))) {
            checkStartDate = dayjs(startDate).tz(appTimeZone);
        }
        const checkEndDate = checkStartDate.add(numDaysToCheck, 'day');

        console.log(`[getProviderAvailableDates] Provider: ${providerNo}, Service: ${serviceType}, Range: ${checkStartDate.format('YYYY-MM-DD')} to ${checkEndDate.format('YYYY-MM-DD')}`);

        // 1. Get the template for the service
        const templateQuery = `SELECT timecode FROM scheduletemplate WHERE provider_no = ? AND name = ?`;
        const [templates] = await pool.query(templateQuery, [providerNo, serviceType]);
        if (templates.length === 0 || !templates[0].timecode) {
            console.log(`[getProviderAvailableDates] No valid template found.`);
            return res.json({ success: true, availableDates: [] });
        }
        const timecode = templates[0].timecode;
        const timecodeLength = timecode.length;
        let slotDuration = 15;
        if (timecodeLength === 48) slotDuration = 30;
        else if (timecodeLength !== 96) {
            console.warn(`[getProviderAvailableDates] Unexpected timecode length ${timecodeLength}. Assuming 15min.`);
        }
        const slotsPerHour = 60 / slotDuration;

        // 2. Get schedule dates within the range
        const scheduleDateQuery = `            SELECT DATE_FORMAT(sdate, '%Y-%m-%d') AS scheduleDate
            FROM scheduledate
            WHERE provider_no = ? AND hour = ? AND status = 'A' AND available = '1'
              AND sdate BETWEEN ? AND ?
        `;
        const [scheduleDates] = await pool.query(scheduleDateQuery, [
            providerNo, serviceType,
            checkStartDate.format('YYYY-MM-DD'),
            checkEndDate.format('YYYY-MM-DD')
        ]);
        const possibleDates = scheduleDates.map(sd => sd.scheduleDate);
        if (possibleDates.length === 0) {
            console.log(`[getProviderAvailableDates] No schedule dates found in range.`);
            return res.json({ success: true, availableDates: [] });
        }

        // 3. Get booked appointments for these dates
        const appointmentQuery = `
            SELECT DATE_FORMAT(appointment_date, '%Y-%m-%d') AS apptDate, 
                   TIME_FORMAT(start_time, '%H:%i') AS startTime
            FROM appointment
            WHERE provider_no = ? AND appointment_date IN (?)
        `;
        const [appointments] = await pool.query(appointmentQuery, [providerNo, possibleDates]);
        const bookedSlots = new Set(appointments.map(appt => `${appt.apptDate}:${appt.startTime}`));

        // 4. Check each possible date for *any* available slot
        const availableDatesList = [];
        for (const dateStr of possibleDates) {
            const scheduleDay = dayjs.tz(dateStr, appTimeZone);
            let hasAvailableSlot = false;
            for (let hour = 0; hour < 24; hour++) {
                for (let minuteIndex = 0; minuteIndex < slotsPerHour; minuteIndex++) {
                    const timecodeIndex = hour * slotsPerHour + minuteIndex;
                    if (timecodeIndex >= timecode.length) continue; // Safety check
                    const slotCode = timecode.charAt(timecodeIndex);
                    if (slotCode !== '_') {
                        const minute = minuteIndex * slotDuration;
                        const slotTime = dayjs(scheduleDay).hour(hour).minute(minute).second(0);
                        const slotTimeStr = slotTime.format('HH:mm');
                        const slotDateTimeKey = `${dateStr}:${slotTimeStr}`;
                        // Check future (at least 3 days) and not booked
                        if (slotTime.isAfter(today.add(2, 'day').endOf('day')) && !bookedSlots.has(slotDateTimeKey)) {
                            hasAvailableSlot = true;
                            break; // Found one slot, no need to check further for this date
                        }
                    }
                }
                if (hasAvailableSlot) break;
            }
            if (hasAvailableSlot) {
                availableDatesList.push(dateStr);
            }
        }

        console.log(`[getProviderAvailableDates] Found available dates: ${availableDatesList.join(', ')}`);
        res.json({ success: true, availableDates: availableDatesList });

    } catch (error) {
        console.error(`Error fetching available dates for provider ${providerNo}, service ${serviceType}:`, error);
        res.status(500).json({ success: false, message: 'Server error fetching available dates' });
    }
};
// -------------------------------- 
