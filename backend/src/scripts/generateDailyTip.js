const fs = require('fs');
const path = require('path');
const axios = require('axios');
const dotenv = require('dotenv');
const { format } = require('date-fns');

// 加载环境变量
dotenv.config();

// OpenRouter API密钥
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;

// 健康小贴士的主题类别
const categories = [
    { name: 'nutrition', chinese: '营养' },
    { name: 'fitness', chinese: '健身' },
    { name: 'wellness', chinese: '健康' },
    { name: 'mental-health', chinese: '心理健康' },
    { name: 'sleep', chinese: '睡眠' },
    { name: 'preventive-care', chinese: '预防保健' }
];

// 预定义的模板内容
const tipTemplates = {
    'nutrition': `# 营养健康小贴士：均衡饮食的力量

## 每日膳食基础

* 保持"彩虹餐盘"原则，每餐包含多种颜色的蔬果
* 确保适量蛋白质摄入，选择豆类、鱼类或瘦肉
* 优先选择全谷物，如糙米、藜麦和全麦面包
* 控制精制糖和盐的摄入量，选择天然调味方式

## 智慧饮食习惯

1. 认真咀嚼每口食物，至少20-30次
2. 使用小盘子和小碗，帮助控制份量
3. 遵循8分饱原则，避免过度进食
4. 规律进餐，避免长时间不吃然后暴饮暴食

## 明智食物选择

* 选择富含抗氧化物的食物，如蓝莓、胡萝卜和绿茶
* 增加膳食纤维摄入，促进消化健康和饱腹感
* 健康脂肪(如鳄梨、橄榄油)比精制碳水更重要
* 减少加工食品，选择新鲜、当季的天然食材

## 营养补充策略

1. 饮食多样化优于过度依赖补充剂
2. 考虑适量维生素D补充，特别是冬季或室内工作者
3. 关注Omega-3脂肪酸摄入，可来自鱼类或亚麻籽
4. 与医生或营养师咨询个人化的补充方案`,

    'fitness': `# 健身运动小贴士：科学锻炼指南

## 有效锻炼原则

* 遵循"热身-主运动-放松"的完整锻炼结构
* 循序渐进提高强度，避免突然增加训练量
* 结合有氧、力量和柔韧性训练获得全面健康
* 注意正确姿势和技术，质量胜于数量

## 持续动力维持

1. 设定具体、可测量、可实现的健身目标
2. 尝试不同运动形式，找到您真正喜欢的活动
3. 寻找运动伙伴或加入团体课程增加责任感
4. 记录并庆祝进步，无论大小，保持积极心态

## 日常活动增加

* 选择步行或骑车代替短途驾车
* 使用楼梯而非电梯
* 工作时每小时起身活动5分钟
* 将家务劳动视为额外的活动机会

## 特殊人群锻炼指南

1. 初学者应从轻度活动开始，如步行或温和瑜伽
2. 老年人重点关注平衡性和功能性锻炼
3. 慢性疾病患者在医生指导下选择适合的运动
4. 忙碌工作者善用高效的间歇训练方法`,

    'wellness': `# 整体健康小贴士：平衡生活的艺术

## 身心平衡策略

* 每天留出"断连"时间，远离电子设备和社交媒体
* 培养一项创造性爱好，如绘画、园艺或音乐
* 定期接触自然环境，增强身心健康
* 保持社交连接和有意义的关系

## 压力管理技巧

1. 练习深呼吸技巧，每天3-5分钟
2. 学习识别压力触发因素并提前制定应对计划
3. 建立健康边界，学会适时说"不"
4. 尝试正念冥想，从五分钟开始逐渐延长

## 生活习惯优化

* 创建规律的晨间和睡前仪式
* 优先考虑睡眠质量，保持一致的作息时间
* 定期进行数字排毒，暂时远离电子设备
* 保持生活空间整洁有序，减少环境压力

## 预防保健措施

1. 按计划进行定期健康检查
2. 保持最新疫苗接种状态
3. 了解自己的健康风险因素并针对性预防
4. 倾听身体信号，不忽视小症状`,

    'mental-health': `# 心理健康小贴士：滋养内心平静

## 情绪管理基础

* 学习识别和命名情绪，增强情绪意识
* 接受所有情绪是正常的，无需评判
* 建立健康的情绪表达方式，避免压抑
* 在需要时寻求专业帮助，这是力量而非弱点

## 思维模式调整

1. 注意并挑战消极自动思维
2. 培养感恩意识，每天记录三件感恩事项
3. 练习积极重构，寻找事件的建设性角度
4. 避免完美主义陷阱，接受"足够好"

## 心理韧性建设

* 将挑战视为成长机会而非威胁
* 培养解决问题的能力和适应性思维
* 建立支持网络，知道何时何处寻求帮助
* 发展个人优势和兴趣爱好

## 日常心理保健

1. 每天安排"心理休息时间"，哪怕只有10分钟
2. 减少信息过载，设定媒体使用界限
3. 练习自我同情，对自己和他人保持友善
4. 寻找意义和目的，与更大的事物连接`,

    'sleep': `# 睡眠健康小贴士：优质休息指南

## 睡眠环境优化

* 保持卧室凉爽、黑暗和安静
* 投资高质量的床垫和枕头，满足个人需求
* 使用舒适透气的床上用品
* 减少卧室电子设备，特别是电视

## 睡前习惯调整

1. 创建放松的睡前仪式，如温和拉伸或阅读
2. 睡前1-2小时避免屏幕使用
3. 限制傍晚后的咖啡因和酒精摄入
4. 尝试睡前放松技巧，如深呼吸或冥想

## 睡眠规律建立

* 每天保持相同的睡眠和起床时间，包括周末
* 根据个人需求调整睡眠时长，大多数成人需要7-9小时
* 如果15分钟内无法入睡，起床做些放松活动
* 避免频繁日间小睡，若需要应控制在30分钟以内

## 特殊情况应对

1. 使用旅行眼罩和耳塞应对新环境
2. 调整作息时间减轻时差反应
3. 压力大时更加重视睡眠卫生
4. 持续睡眠问题应咨询医疗专业人士`,

    'preventive-care': `# 预防保健小贴士：主动健康管理

## 定期检查计划

* 建立个人健康档案，记录家族病史
* 按年龄和风险因素安排体检时间表
* 定期监测血压、血糖和胆固醇水平
* 遵循医生建议的筛查计划，如结肠镜检查

## 免疫系统增强

1. 保持足够水分摄入，每天至少8杯水
2. 摄入富含抗氧化物的食物，如浆果和绿叶蔬菜
3. 确保充足的维生素D，通过适度阳光和饮食
4. 管理压力水平，长期压力会削弱免疫系统

## 慢性病风险降低

* 保持健康体重，减少腹部脂肪
* 控制血压和血糖在健康范围
* 限制加工食品和反式脂肪摄入
* 避免吸烟和过量饮酒

## 日常预防习惯

1. 正确洗手，特别是在公共场所后
2. 根据季节使用防晒霜和保湿产品
3. 保持良好姿势，特别是长时间使用电脑时
4. 定期进行口腔护理，包括牙线使用`
};

// 随机选择一个类别
const getRandomCategory = () => {
    const randomIndex = Math.floor(Math.random() * categories.length);
    return categories[randomIndex];
};

// 生成当前日期字符串
const getFormattedDate = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}年${month}月${day}日`;
};

// 读取AI写作规则
function getAIWritingRules() {
    try {
        const rulesPath = path.join(__dirname, '../config/ai_writing_rules.md');
        if (fs.existsSync(rulesPath)) {
            return fs.readFileSync(rulesPath, 'utf8');
        } else {
            console.log('AI写作规则文件不存在，使用默认规则');
            return '请基于最新医疗新闻创建健康小贴士，避免使用通用摘要如"探索健康生活的小技巧"';
        }
    } catch (error) {
        console.error('读取AI写作规则时出错:', error);
        return '请基于最新医疗新闻创建健康小贴士，避免使用通用摘要如"探索健康生活的小技巧"';
    }
}

// 获取最新医疗新闻作为主题参考
async function getLatestMedicalNews() {
    try {
        console.log('获取最新医疗新闻...');

        // 尝试从多个来源获取医疗新闻
        const sources = [
            { url: 'https://api.pubmed.gov/api/news', queryParam: 'limit=5' },
            { url: 'https://www.who.int/news-room/api/news', queryParam: 'pageSize=5' },
            // 添加其他医疗新闻API来源
        ];

        // 随机选择一个来源
        const selectedSource = sources[Math.floor(Math.random() * sources.length)];

        // 注意：这里是模拟API调用，实际项目中应该调用真实的新闻API
        console.log(`尝试从 ${selectedSource.url} 获取新闻`);

        // 由于这是示例，我们创建模拟新闻数据
        const mockNews = [
            { title: '研究显示地中海饮食可能降低认知衰退风险', category: 'nutrition' },
            { title: '新研究：定期进行肌肉力量训练有助延缓衰老', category: 'fitness' },
            { title: '最新研究揭示睡眠质量与心脏健康的密切关系', category: 'sleep' },
            { title: '研究发现:每日冥想可有效缓解慢性焦虑', category: 'mental-health' },
            { title: '科学家发现肠道菌群对免疫系统功能的新影响', category: 'functional-medicine' }
        ];

        console.log('获取到的模拟医疗新闻:', mockNews);
        return mockNews;
    } catch (error) {
        console.error('获取医疗新闻时出错:', error);
        return [{
            title: '健康生活方式对提升整体健康的重要性',
            category: 'wellness'
        }];
    }
}

// 使用AI生成内容
async function generateContentFromAI(prompt, category, newsTitle) {
    const aiUsageTracker = require('../utils/aiUsageTracker');
    const aiUsageTypes = require('../constants/aiUsageTypes');
    const startTime = Date.now();
    const modelName = process.env.MODEL_NAME || 'deepseek/deepseek-r1-0528:free';
    
    try {
        // 获取API密钥
        const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;

        // 检查是否有API密钥
        if (OPENROUTER_API_KEY && OPENROUTER_API_KEY !== 'your_api_key_here' && OPENROUTER_API_KEY !== 'undefined') {
            console.log('使用OpenRouter API生成内容...');

            // 使用OpenRouter API调用AI
            const response = await axios.post(
                'https://openrouter.ai/api/v1/chat/completions',
                {
                    model: modelName,
                    messages: [{ role: 'user', content: prompt }]
                },
                {
                    headers: {
                        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            const responseTime = Date.now() - startTime;
            
            // 提取生成的内容
            const generatedContent = response.data.choices[0].message.content.trim();
            console.log('AI内容生成成功');
            
            // 记录成功的AI使用，使用详细分类
            const detailedServiceType = 'tips_daily_generation';
            const serviceCategory = aiUsageTypes.getServiceCategory(detailedServiceType);
            await aiUsageTracker.recordUsage({
                provider: 'openrouter',
                serviceType: detailedServiceType,
                serviceCategory: serviceCategory,
                endpoint: 'https://openrouter.ai/api/v1/chat/completions',
                modelName: modelName,
                success: true,
                inputTokens: response.data.usage?.prompt_tokens || 0,
                outputTokens: response.data.usage?.completion_tokens || 0,
                responseTimeMs: responseTime,
                cacheHit: false,
                estimatedCostUsd: 0, // OpenRouter可能不提供成本信息
                userType: 'system',
                useCase: 'automated_task',
                contentType: 'text',
                language: 'zh',
                featureUsed: `daily_tip_${category}`
            });
            
            return generatedContent;
        } else {
            console.log('未找到有效的API密钥，使用预定义模板...');
            return generateFromTemplate(category, newsTitle);
        }
    } catch (error) {
        const responseTime = Date.now() - startTime;
        console.error('使用AI生成内容时出错:', error);
        
        // 记录失败的AI使用，使用详细分类
        const detailedServiceType = 'tips_daily_generation';
        const serviceCategory = aiUsageTypes.getServiceCategory(detailedServiceType);
        await aiUsageTracker.recordUsage({
            provider: 'openrouter',
            serviceType: detailedServiceType,
            serviceCategory: serviceCategory,
            endpoint: 'https://openrouter.ai/api/v1/chat/completions',
            modelName: modelName,
            success: false,
            responseTimeMs: responseTime,
            cacheHit: false,
            userType: 'system',
            useCase: 'automated_task',
            contentType: 'text',
            language: 'zh',
            featureUsed: `daily_tip_${category}`
        });
        
        // 记录错误详情，使用详细分类
        await aiUsageTracker.recordError({
            provider: 'openrouter',
            serviceType: detailedServiceType,
            errorType: error.response ? 'API_ERROR' : 'NETWORK_ERROR',
            errorMessage: error.message,
            errorCode: error.response?.status?.toString(),
            requestSummary: `Model: ${modelName}, Category: ${category}, Type: ${detailedServiceType}`,
            responseSummary: error.response ? `Status: ${error.response.status}` : null
        });
        
        return generateFromTemplate(category, newsTitle);
    }
}

// 使用预定义模板生成内容
function generateFromTemplate(category, newsTitle) {
    // 根据新闻标题和类别创建一个合适的标题
    const title = `# ${newsTitle || getDefaultTitle(category)}`;

    // 根据类别获取摘要
    const summary = getCategorySummary(category);

    // 根据类别构建内容
    const sections = getCategorySections(category);

    // 添加免责声明
    const disclaimer = getDisclaimer();

    // 组合内容
    const template = `${title}

## 摘要
${summary}

## 内容
${sections.join('\n\n')}

${disclaimer}
`;

    console.log(`已使用${category}类别的预定义模板`);
    return template;
}

// 获取默认标题
function getDefaultTitle(category) {
    const titles = {
        'nutrition': '健康饮食指南',
        'fitness': '运动健身小贴士',
        'sleep': '优质睡眠的健康小贴士',
        'mental-health': '心理健康管理技巧',
        'wellness': '健康生活小贴士',
        'functional-medicine': '功能医学健康贴士',
        'seasonal': '季节性健康小贴士'
    };
    return titles[category] || '每日健康小贴士';
}

// 获取类别摘要
function getCategorySummary(category) {
    const summaries = {
        'nutrition': '均衡饮食是健康生活的基础，了解如何合理搭配食物，获取必要的营养素，保持身体健康活力。',
        'fitness': '科学的运动方式能提升体能、塑造体态并改善心理健康，掌握正确的健身知识，让运动成为生活的享受。',
        'wellness': '全面的健康生活方式需要均衡的饮食、适当的运动、充足的休息和积极的心态，掌握这些实用小技巧，构建健康可持续的生活方式。',
        'mental-health': '保持良好的心理健康与身体健康同样重要，了解情绪管理方法、压力应对技巧和心理调适策略，提高心理韧性和幸福感。',
        'sleep': '充足的高质量睡眠对身心健康至关重要，学习如何创造理想的睡眠环境，养成良好的睡前习惯，让您每天醒来精力充沛。',
        'functional-medicine': '功能医学关注疾病根源而非单纯症状治疗，强调个体化医疗和全身系统平衡，了解这些原则帮助您实现最佳健康状态。',
        'seasonal': '不同季节带来不同的健康挑战，从防暑到抵御寒冬，从过敏应对到节假日健康，掌握这些季节性健康知识，全年保持健康状态。'
    };
    return summaries[category] || '探索专业的健康知识和实用建议，提升您的健康水平和生活质量。';
}

// 获取类别内容部分
function getCategorySections(category) {
    // 针对不同类别的内容模板
    const templates = {
        'nutrition': [
            '### 每日营养目标\n\n* 每天至少吃5份水果和蔬菜\n* 选择全谷物而非精制碳水化合物\n* 每餐包含瘦蛋白质\n* 保持充足的水分，每天至少8杯水',
            '### 膳食计划技巧\n\n1. 提前准备餐食\n2. 使用各种色彩鲜艳的食物\n3. 控制份量大小\n4. 限制加工食品和添加糖',
            '### 健康零食选择\n\n* 混合坚果和干果\n* 希腊酸奶配浆果\n* 胡萝卜条配鳄梨酱\n* 全麦吐司配花生酱',
            '### 饮食禁忌\n\n* 避免过量饮酒\n* 减少含糖饮料摄入\n* 控制高盐食品消费\n* 限制反式脂肪的摄入量'
        ],
        'fitness': [
            '### 运动入门指南\n\n* 每周进行至少150分钟中等强度有氧运动\n* 每周进行2-3次力量训练\n* 开始和结束时进行5-10分钟的热身和拉伸\n* 循序渐进增加运动强度',
            '### 最佳运动方式\n\n1. 有氧运动：跑步、游泳、骑自行车\n2. 力量训练：举重、俯卧撑、深蹲\n3. 柔韧性练习：瑜伽、普拉提\n4. 平衡训练：太极、单腿站立练习',
            '### 避免运动损伤技巧\n\n* 使用正确的运动装备\n* 掌握正确的运动姿势\n* 给身体足够的恢复时间\n* 注意听取身体发出的信号',
            '### 健身常见误区\n\n* 只做有氧运动忽视力量训练\n* 认为出汗越多越好\n* 运动前不做热身\n* 忽视饮食在健身中的重要性'
        ],
        'mental-health': [
            '### 情绪管理技巧\n\n* 学会识别和命名情绪\n* 练习深呼吸和冥想\n* 保持规律的作息时间\n* 培养积极的思维模式',
            '### 压力应对策略\n\n1. 制定合理的目标和期望\n2. 学会时间管理和优先级排序\n3. 寻求社会支持和专业帮助\n4. 进行适度的运动和放松活动',
            '### 心理韧性建设\n\n* 培养解决问题的能力\n* 保持乐观和感恩的心态\n* 建立健康的人际关系\n* 持续学习和自我提升',
            '### 日常心理保健\n\n* 每天留出独处和反思的时间\n* 限制负面信息的接触\n* 培养兴趣爱好和创造性活动\n* 定期进行心理健康检查'
        ],
        'sleep': [
            '### 睡眠环境优化\n\n* 保持卧室温度在18-22℃\n* 确保房间黑暗和安静\n* 使用舒适的床垫和枕头\n* 移除电子设备和蓝光源',
            '### 睡前准备习惯\n\n1. 睡前1-2小时停止使用电子设备\n2. 进行放松活动如阅读或泡澡\n3. 避免大餐、咖啡因和酒精\n4. 建立固定的睡前仪式',
            '### 睡眠时间管理\n\n* 每天保持相同的睡眠和起床时间\n* 成人每晚需要7-9小时的睡眠\n* 避免长时间的午睡\n* 如果无法入睡，起床做些安静的活动',
            '### 改善睡眠质量\n\n* 白天保持充足的自然光照射\n* 规律进行适度的运动\n* 管理日常压力和焦虑\n* 如有持续问题应咨询医生'
        ],
        'functional-medicine': [
            '### 整体健康评估\n\n* 关注身体系统间的相互关系\n* 重视营养状态和消化健康\n* 评估激素平衡和代谢功能\n* 考虑环境因素对健康的影响',
            '### 个性化治疗方案\n\n1. 基于基因检测的精准营养\n2. 针对性的营养补充剂使用\n3. 生活方式的全面调整\n4. 压力管理和心理支持',
            '### 根本原因分析\n\n* 识别慢性炎症的来源\n* 检查肠道菌群平衡\n* 评估毒素暴露和解毒能力\n* 分析免疫系统功能状态',
            '### 预防性健康管理\n\n* 定期进行功能性检测\n* 维持最佳的营养状态\n* 建立健康的生活习惯\n* 早期干预潜在健康问题'
        ]
    };

    // 如果有该类别的模板，返回该模板；否则返回通用模板
    return templates[category] || [
        '### 第一部分\n\n* 要点1\n* 要点2\n* 要点3\n* 要点4',
        '### 第二部分\n\n1. 要点1\n2. 要点2\n3. 要点3\n4. 要点4',
        '### 第三部分\n\n* 要点1\n* 要点2\n* 要点3\n* 要点4',
        '### 第四部分\n\n* 要点1\n* 要点2\n* 要点3\n* 要点4'
    ];
}

// 获取免责声明文本
function getDisclaimer() {
    return `---

**免责声明**: 本文由 MMC 健康管理中心提供，仅供一般健康信息参考。内容不能替代专业的医疗建议、诊断或治疗。如有任何健康问题，请务必咨询医生或其他合格的医疗保健提供者。您可以考虑联系 MMC 健康管理中心获取专业问诊服务。`;
}

// 更新生成健康小贴士的函数
async function generateHealthTip() {
    try {
        // 获取日期
        const today = new Date();
        const dateStr = format(today, 'yyyy-MM-dd');
        const aiRules = getAIWritingRules();

        console.log(`开始为 ${dateStr} 生成健康小贴士`);

        // 获取最新医疗新闻作为主题参考
        const latestNews = await getLatestMedicalNews();

        // 随机选择一个新闻主题
        const selectedNews = latestNews[Math.floor(Math.random() * latestNews.length)];
        console.log('选择的新闻主题:', selectedNews);

        const category = selectedNews.category || 'wellness';
        const newsTitle = selectedNews.title || '健康生活方式';

        // 构建提示
        const prompt = `
请根据以下规则和主题创建一篇健康小贴士文章：

${aiRules}

当前选择的主题：${newsTitle}
文章类别：${category}

请提供完整的Markdown格式文章，确保摘要是针对特定主题的，而不是通用的"探索健康生活的小技巧"。
**重要规则：请确保输出的 Markdown 文件的前言（front matter）部分的 tags 字段中不包含 'ai-generated' 这个标签。**
`;

        // 调用OpenRouter API（或其他AI API）
        const content = await generateContentFromAI(prompt, category, newsTitle);

        // 保存生成的内容
        saveGeneratedTip(content, category, dateStr);

        return {
            success: true,
            message: `成功生成${category}类别的健康小贴士`,
            content: content
        };
    } catch (error) {
        console.error('生成健康小贴士时出错:', error);
        return {
            success: false,
            message: `生成健康小贴士时出错: ${error.message}`
        };
    }
}

// 修改保存生成的小贴士的函数
function saveGeneratedTip(content, category, dateStr) {
    try {
        // 根据类别确定子文件夹
        let subFolder = 'tips'; // 默认文件夹

        // 映射类别到相应的子文件夹
        const categoryFolderMap = {
            'nutrition': 'health',
            'fitness': 'health',
            'wellness': 'health',
            'mental-health': 'health',
            'sleep': 'health',
            'preventive-care': 'health',
            // 可以添加更多类别映射
        };

        // 如果类别有对应的子文件夹，则使用它
        if (categoryFolderMap[category]) {
            subFolder = categoryFolderMap[category];
        }

        // 检查内容是否已包含免责声明
        const disclaimer = getDisclaimer();
        if (!content.includes('免责声明')) {
            content = content + '\n\n' + disclaimer;
        }

        const fileName = `daily-tip-${category}-${dateStr}.md`;
        const filePath = path.join(__dirname, '../../documents', subFolder, fileName);

        // 确保目录存在
        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }

        // 从内容中提取摘要（如果没有摘要部分）
        let finalContent = content;
        if (!content.includes('## 摘要') && category) {
            const summary = getCategorySummary(category);
            // 在标题和内容之间插入摘要
            const titleEnd = content.indexOf('\n\n');
            if (titleEnd !== -1) {
                finalContent = content.substring(0, titleEnd + 2) +
                    `## 摘要\n${summary}\n\n` +
                    content.substring(titleEnd + 2);
            }
        }

        // 写入文件
        fs.writeFileSync(filePath, finalContent);
        console.log(`健康小贴士已保存到: ${filePath}`);

        // 更新最新小贴士文件（用于快速访问）
        const latestTipPath = path.join(__dirname, '../../documents', subFolder, `latest-${category}-tip.md`);
        fs.writeFileSync(latestTipPath, finalContent);
        console.log(`最新小贴士已更新: ${latestTipPath}`);

        return { success: true, filePath };
    } catch (error) {
        console.error('保存健康小贴士时出错:', error);
        return { success: false, error: error.message };
    }
}

// 执行脚本
generateHealthTip()
    .then(result => {
        console.log(result.message);
        process.exit(0);
    })
    .catch(err => {
        console.error('执行失败:', err);
        process.exit(1);
    }); 