import React, { useState, useEffect, useCallback } from 'react';
import { API_URL } from '../utils/env';
import ReactMarkdown from 'react-markdown';
import { Container, Typography, List, ListItem, ListItemText, Paper, CircularProgress, Alert, Divider, Box, Button, Grid, Card, CardContent, CardActions, Chip } from '@mui/material';
import { useLanguage } from '../context/LanguageContext'; // Assuming you use this

function InfoPage() {
    const [articles, setArticles] = useState([]);
    const [categories, setCategories] = useState([]); // State for categories
    const [selectedCategory, setSelectedCategory] = useState('all'); // State for selected category
    const [selectedArticle, setSelectedArticle] = useState(null); // Store full article object when viewing details
    const [loadingArticles, setLoadingArticles] = useState(false);
    const [loadingContent, setLoadingContent] = useState(false);
    const [error, setError] = useState('');
    const { t } = useLanguage(); // Use language context if available

    const apiUrl = API_URL || 'https://app-backend.mmcwellness.ca';
    const token = localStorage.getItem('token'); // Get token for authenticated requests

    // Fetch categories on mount
    useEffect(() => {
        setError('');
        // Assuming the endpoint is /api/tips/categories. Change if it's /api/articles/categories
        fetch(`${apiUrl}/api/tips/categories`, {
            headers: { 'Authorization': `Bearer ${token}` }
        })
            .then(async res => {
                if (!res.ok) throw new Error(await res.text());
                return res.json();
            })
            .then(data => {
                // Add an "All" category option
                setCategories([{ slug: 'all', name: t('healthInfo.allCategories') || '全部' }, ...data]);
            })
            .catch(err => {
                console.error("Error fetching categories:", err);
                setError(`Failed to load categories: ${err.message}`);
                setCategories([{ slug: 'all', name: t('healthInfo.allCategories') || '全部' }]); // Provide default
            });
    }, [apiUrl, token, t]);

    // Fetch list of articles based on selected category
    const fetchArticles = useCallback((categorySlug) => {
        setLoadingArticles(true);
        setError('');
        setSelectedArticle(null); // Clear selected article when category changes

        let fetchUrl = `${apiUrl}/api/tips`;
        if (categorySlug && categorySlug !== 'all') {
            fetchUrl = `${apiUrl}/api/tips/category/${categorySlug}`;
        }

        fetch(fetchUrl, {
            headers: { 'Authorization': `Bearer ${token}` }
        })
            .then(async res => {
                if (!res.ok) throw new Error(await res.text());
                return res.json();
            })
            .then(data => {
                setArticles(data || []);
            })
            .catch(err => {
                console.error("Error fetching article list:", err);
                setError(`Failed to load articles: ${err.message}`);
                setArticles([]);
            })
            .finally(() => {
                setLoadingArticles(false);
            });
    }, [apiUrl, token]);

    // Fetch initial articles (all)
    useEffect(() => {
        fetchArticles('all');
    }, [fetchArticles]);

    // Fetch content when an article is selected for detail view
    useEffect(() => {
        if (selectedArticle && selectedArticle.filename && !selectedArticle.content) { // Only fetch if content not already loaded
            setLoadingContent(true);
            setError('');
            fetch(`${apiUrl}/api/tips/${selectedArticle.filename}`, {
                headers: { 'Authorization': `Bearer ${token}` }
            })
                .then(async res => {
                    if (!res.ok) throw new Error(await res.text());
                    return res.json(); // Expecting JSON with content field
                })
                .then(data => {
                    setSelectedArticle(prev => ({ ...prev, content: data.content })); // Update selected article with content
                })
                .catch(err => {
                    console.error("Error fetching article content:", err);
                    setError(`Failed to load article content: ${err.message}`);
                })
                .finally(() => {
                    setLoadingContent(false);
                });
        }
    }, [selectedArticle, apiUrl, token]);

    const handleSelectCategory = (categorySlug) => {
        setSelectedCategory(categorySlug);
        fetchArticles(categorySlug);
    };

    const handleViewDetails = (article) => {
        setSelectedArticle(article); // Set the full article object to trigger detail view and content fetch
    };

    const handleBackToList = () => {
        setSelectedArticle(null); // Go back to the list view
    };

    // Custom renderer for images
    const renderers = {
        img: ({ node, ...props }) => {
            let src = props.src;
            // Assuming assets are served from a relative path or a specific endpoint
            if (src && !src.startsWith('http') && !src.startsWith('/api')) {
                // Adjust the path based on how assets are served by the backend
                // Example: /api/tips/assets/ or /api/assets/
                src = `${apiUrl}/api/tips/assets/${src.startsWith('/') ? src.substring(1) : src}`;
            }
            return <img {...props} src={src} alt={props.alt || ''} style={{ maxWidth: '100%', height: 'auto' }} />;
        },
        // Add other renderers if needed (e.g., for headings, paragraphs)
    };

    return (
        <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
            <Typography variant="h4" component="h1" gutterBottom>
                {t('healthInfo.title') || '健康小贴士'} {/* Changed title slightly */}
            </Typography>

            {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

            {/* Category Filter Buttons */}
            <Box sx={{ mb: 3, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {categories.map(cat => (
                    <Button
                        key={cat.slug}
                        variant={selectedCategory === cat.slug ? 'contained' : 'outlined'}
                        onClick={() => handleSelectCategory(cat.slug)}
                    >
                        {cat.name} {/* Display translated name */}
                    </Button>
                ))}
            </Box>
            <Divider sx={{ mb: 3 }} />

            {/* Conditional Rendering: List View or Detail View */}
            {selectedArticle ? (
                // Detail View
                <Paper elevation={2} sx={{ p: 3 }}>
                    <Button onClick={handleBackToList} sx={{ mb: 2 }}>
                        {t('common.back') || '← 返回列表'}
                    </Button>
                    <Typography variant="h5" component="h2" gutterBottom>
                        {selectedArticle.title}
                    </Typography>
                    <Typography variant="caption" display="block" gutterBottom>
                        {t('healthInfo.category', { category: selectedArticle.categoryName }) || `分类: ${selectedArticle.categoryName}`} | {t('healthInfo.lastModified', { date: new Date(selectedArticle.lastModified).toLocaleDateString() }) || `最后更新: ${new Date(selectedArticle.lastModified).toLocaleDateString()}`}
                    </Typography>
                    {selectedArticle.tags && selectedArticle.tags.length > 0 && (
                        <Box sx={{ mb: 2 }}>
                            {selectedArticle.tags
                                .filter(tag => tag !== 'ai-generated')
                                .map(tag => <Chip key={tag} label={tag} size="small" sx={{ mr: 0.5 }} />)}
                        </Box>
                    )}
                    <Divider sx={{ mb: 2 }} />
                    {loadingContent ? (
                        <Box display="flex" justifyContent="center" sx={{ pt: 4 }}><CircularProgress /></Box>
                    ) : (
                        <Box sx={{ mt: 2, '& img': { maxWidth: '100%', height: 'auto' } }}>
                            {/* Use ReactMarkdown to render content */}
                            <ReactMarkdown components={renderers}>
                                {selectedArticle.content || ''}
                            </ReactMarkdown>
                        </Box>
                    )}
                </Paper>
            ) : (
                // List View
                <Grid container spacing={3}>
                    {loadingArticles ? (
                        <Grid item xs={12}><Box display="flex" justifyContent="center"><CircularProgress /></Box></Grid>
                    ) : articles.length > 0 ? (
                        articles.map(article => (
                            <Grid item xs={12} sm={6} md={4} key={article.filename}>
                                <Card sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
                                    <CardContent sx={{ flexGrow: 1 }}>
                                        {/* Display Category Chip */}
                                        <Chip label={article.categoryName} size="small" sx={{ mb: 1 }} />
                                        <Typography gutterBottom variant="h6" component="div">
                                            {article.title}
                                        </Typography>
                                        {/* Display Abstract */}
                                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                            {article.abstract || t('healthInfo.noAbstract') || '暂无摘要'}
                                        </Typography>
                                        <Typography variant="caption" display="block" color="text.secondary">
                                            {t('healthInfo.lastModifiedShort', { date: new Date(article.lastModified).toLocaleDateString() }) || `更新于: ${new Date(article.lastModified).toLocaleDateString()}`}
                                        </Typography>
                                    </CardContent>
                                    <CardActions>
                                        <Button size="small" onClick={() => handleViewDetails(article)}>
                                            {t('healthInfo.readMore') || '阅读详情'}
                                        </Button>
                                    </CardActions>
                                </Card>
                            </Grid>
                        ))
                    ) : (
                        <Grid item xs={12}>
                            <Typography variant="body1" sx={{ mt: 2, textAlign: 'center' }}>
                                {t('healthInfo.noArticlesInCategory') || '此分类下暂无文章。'}
                            </Typography>
                        </Grid>
                    )}
                </Grid>
            )}
        </Container>
    );
}

export default InfoPage; 