import React, { useState, useEffect } from 'react';
import { API_URL } from '../utils/env';
import { useSearchParams, useNavigate } from 'react-router-dom';
import {
    Container,
    Box,
    Typography,
    Button,
    Paper,
    Alert,
    CircularProgress,
    Divider,
    TextField,
    Link,
    InputAdornment
} from '@mui/material';
import { styled } from '@mui/material/styles';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import { useLanguage } from '../context/LanguageContext';

const StyledPaper = styled(Paper)(({ theme }) => ({
    marginTop: theme.spacing(8),
    padding: theme.spacing(4),
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
}));

const StyledForm = styled('form')(({ theme }) => ({
    width: '100%',
    marginTop: theme.spacing(1),
}));

const StyledButton = styled(Button)(({ theme }) => ({
    margin: theme.spacing(3, 0, 2),
}));

const EmailVerification = () => {
    const [searchParams] = useSearchParams();
    const navigate = useNavigate();
    const [verificationCode, setVerificationCode] = useState('');
    const [email, setEmail] = useState('');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState(false);
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [showPasswordForm, setShowPasswordForm] = useState(false);
    const [mode, setMode] = useState('');
    const [sendingCode, setSendingCode] = useState(false);
    const [codeSent, setCodeSent] = useState(false);

    useEffect(() => {
        const emailParam = searchParams.get('email');
        const code = searchParams.get('code');
        const modeParam = searchParams.get('mode');

        if (emailParam) {
            setEmail(emailParam);
        }

        if (modeParam) {
            setMode(modeParam);
        }

        if (code) {
            setVerificationCode(code);
            // If code is provided in URL, verify immediately
            handleVerify(code, emailParam);
        }
    }, [searchParams]);

    const handleVerify = async (code, emailToVerify) => {
        const codeToUse = code || verificationCode;
        const emailToUse = emailToVerify || email;

        if (!codeToUse) {
            setError('Please enter the verification code');
            return;
        }

        if (!emailToUse) {
            setError('Email is required');
            return;
        }

        setLoading(true);
        setError('');

        try {
            const response = await fetch(`${API_URL}/api/auth/verify-email-code`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    email: emailToUse,
                    code: codeToUse
                }),
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'Failed to verify email');
            }

            if (mode === 'register') {
                console.log(`Email verified for register mode. Navigating to /register for ${emailToUse}`);
                navigate(`/register?email=${encodeURIComponent(emailToUse)}`);
            } else if (mode === 'link') {
                console.log(`Email verified for link mode. Navigating to /link-account for ${emailToUse}`);
                navigate(`/link-account?email=${encodeURIComponent(emailToUse)}`);
            } else {
                console.log(`Email verified for default/password reset mode for ${emailToUse}`);
                setShowPasswordForm(true);
            }
        } catch (err) {
            setError(err.message || 'Something went wrong. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const handlePasswordReset = async (e) => {
        e.preventDefault();

        if (!password || !confirmPassword) {
            setError('Please enter both password fields');
            return;
        }

        if (password !== confirmPassword) {
            setError('Passwords do not match');
            return;
        }

        if (password.length < 8) {
            setError('Password must be at least 8 characters long');
            return;
        }

        setLoading(true);
        setError('');

        try {
            const response = await fetch(`${API_URL}/api/auth/reset-password-direct`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    email,
                    newPassword: password
                }),
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'Failed to set password');
            }

            setSuccess(true);
            setTimeout(() => {
                navigate('/login');
            }, 2000);
        } catch (err) {
            setError(err.message || 'Something went wrong. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const sendVerificationCode = async () => {
        if (!email || !email.includes('@')) {
            setError('Please enter a valid email address');
            return;
        }

        setSendingCode(true);
        setError('');

        try {
            const response = await fetch(`${API_URL}/api/auth/send-verification-code`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email }),
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'Failed to send verification code');
            }

            setCodeSent(true);

        } catch (err) {
            setError(err.message || 'Something went wrong when sending verification code');
        } finally {
            setSendingCode(false);
        }
    };

    return (
        <Container component="main" maxWidth="sm">
            <StyledPaper elevation={3}>
                <Typography component="h1" variant="h5">
                    Email Verification
                </Typography>

                {error && <Alert severity="error" sx={{ mt: 2, width: '100%' }}>{error}</Alert>}

                {!showPasswordForm && (
                    <Box component="form" sx={{ mt: 3, width: '100%' }}>
                        <TextField
                            margin="normal"
                            required
                            fullWidth
                            id="email"
                            label="Email Address"
                            name="email"
                            autoComplete="email"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            disabled={loading || codeSent || searchParams.get('email')}
                            InputProps={{
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <Button
                                            onClick={sendVerificationCode}
                                            disabled={sendingCode || !email || loading || codeSent || searchParams.get('email')}
                                            color="primary"
                                            variant="outlined"
                                            size="small"
                                        >
                                            {sendingCode ? <CircularProgress size={20} /> : (codeSent ? 'Sent' : 'Send Code')}
                                        </Button>
                                    </InputAdornment>
                                ),
                            }}
                        />

                        <TextField
                            margin="normal"
                            required
                            fullWidth
                            id="verificationCode"
                            label="Verification Code"
                            name="verificationCode"
                            value={verificationCode}
                            onChange={(e) => setVerificationCode(e.target.value)}
                            disabled={loading}
                            helperText={codeSent ? "A verification code has been sent to your email" : ""}
                        />

                        <Button
                            fullWidth
                            variant="contained"
                            color="primary"
                            sx={{ mt: 3, mb: 2 }}
                            onClick={() => handleVerify()}
                            disabled={loading}
                        >
                            {loading ? <CircularProgress size={24} /> : 'Verify Email'}
                        </Button>

                        {codeSent && (
                            <Button
                                fullWidth
                                variant="text"
                                color="primary"
                                onClick={sendVerificationCode}
                                disabled={sendingCode}
                                sx={{ mt: 1 }}
                            >
                                {sendingCode ? <CircularProgress size={20} /> : 'Resend Code'}
                            </Button>
                        )}
                    </Box>
                )}

                {showPasswordForm && (
                    <Box component="form" onSubmit={handlePasswordReset} sx={{ mt: 3, width: '100%' }}>
                        <Typography variant="h6" sx={{ mb: 2 }}>
                            Set Your Password
                        </Typography>

                        <TextField
                            margin="normal"
                            required
                            fullWidth
                            name="password"
                            label="New Password"
                            type="password"
                            id="password"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            disabled={loading}
                        />

                        <TextField
                            margin="normal"
                            required
                            fullWidth
                            name="confirmPassword"
                            label="Confirm Password"
                            type="password"
                            id="confirmPassword"
                            value={confirmPassword}
                            onChange={(e) => setConfirmPassword(e.target.value)}
                            disabled={loading}
                        />

                        <Button
                            type="submit"
                            fullWidth
                            variant="contained"
                            color="primary"
                            sx={{ mt: 3, mb: 2 }}
                            disabled={loading}
                        >
                            {loading ? <CircularProgress size={24} /> : 'Set Password & Continue'}
                        </Button>
                    </Box>
                )}
            </StyledPaper>
        </Container>
    );
};

export default EmailVerification; 