{"name": "mmcwebapp-frontend", "version": "1.0.0", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.19", "@mui/lab": "^5.0.0-alpha.153", "@mui/material": "^5.14.20", "@mui/x-date-pickers": "^6.20.2", "@use-gesture/react": "^10.3.1", "axios": "^1.6.2", "chart.js": "^4.4.9", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.0", "react-scripts": "5.0.1", "react-swipeable-views": "^0.14.0", "recharts": "^2.8.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}