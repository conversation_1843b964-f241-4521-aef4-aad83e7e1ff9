const pool = require('../config/database');
const fs = require('fs');
const path = require('path');

async function checkAndCreateDieticianTable() {
    try {
        console.log('Checking if dietician_comment_summaries table exists...');
        
        // Check if table exists
        const [tables] = await pool.query("SHOW TABLES LIKE 'dietician_comment_summaries'");
        
        if (tables.length === 0) {
            console.log('Table does not exist. Creating dietician_comment_summaries table...');
            
            // Read the migration file
            const migrationPath = path.join(__dirname, '../database/migrations/20240716000001_create_dietician_comment_summaries.sql');
            const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
            
            // Execute the migration
            await pool.query(migrationSQL);
            console.log('Successfully created dietician_comment_summaries table');
        } else {
            console.log('Table dietician_comment_summaries already exists');
        }
        
        // Show table structure
        const [structure] = await pool.query("DESCRIBE dietician_comment_summaries");
        console.log('Table structure:');
        console.table(structure);
        
    } catch (error) {
        console.error('Error checking/creating table:', error);
    } finally {
        await pool.end();
    }
}

checkAndCreateDieticianTable(); 