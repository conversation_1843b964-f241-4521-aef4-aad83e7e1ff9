const axios = require('axios');
const crypto = require('crypto');
const aiConfig = require('../config/aiConfig');
const aiUsageTracker = require('./aiUsageTracker');

class GeminiAIService {
    constructor() {
        this.config = aiConfig.gemini;
        this.validateConfig();
        this.httpClient = this.createHttpClient();
        this.metrics = {
            totalCalls: 0,
            successfulCalls: 0,
            failedCalls: 0,
            cacheHits: 0,
            cacheMisses: 0
        };
        this.cache = new Map(); // 简单内存缓存，后续可扩展为Redis
    }

    validateConfig() {
        if (!this.config.apiKey) {
            throw new Error('GEMINI_API_KEY not configured. Please set the environment variable.');
        }
        console.log(`[AI Service] Initialized with model: ${this.config.model}`);
    }

    createHttpClient() {
        return axios.create({
            timeout: this.config.timeout,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'MMC-WebApp/1.0'
            }
        });
    }

    /**
     * 生成缓存键
     * @param {Object} prompt - 提示对象
     * @param {Object} config - 生成配置
     * @returns {string} - 缓存键
     */
    generateCacheKey(prompt, config) {
        const cacheData = {
            prompt: typeof prompt === 'string' ? prompt : JSON.stringify(prompt),
            config: config
        };
        return crypto
            .createHash('sha256')
            .update(JSON.stringify(cacheData))
            .digest('hex');
    }

    /**
     * 获取缓存
     * @param {string} key - 缓存键
     * @returns {Object|null} - 缓存结果
     */
    getCache(key) {
        const cached = this.cache.get(key);
        if (cached && cached.expires > Date.now()) {
            this.metrics.cacheHits++;
            return cached.data;
        }
        if (cached) {
            this.cache.delete(key); // 清除过期缓存
        }
        this.metrics.cacheMisses++;
        return null;
    }

    /**
     * 设置缓存
     * @param {string} key - 缓存键
     * @param {Object} data - 数据
     * @param {number} ttl - 存活时间（秒）
     */
    setCache(key, data, ttl) {
        if (aiConfig.cache.enabled) {
            this.cache.set(key, {
                data: data,
                expires: Date.now() + (ttl * 1000)
            });
        }
    }

    /**
     * 构建请求体
     * @param {Object} prompt - 提示对象 {system: string, user: string} 或简单字符串
     * @param {Object} config - 配置选项
     * @returns {Object} - 请求体
     */
    buildRequestBody(prompt, config = {}) {
        const generationConfig = {
            ...this.config.defaultConfig,
            ...config
        };

        let requestBody = {
            generationConfig: generationConfig
        };

        // 处理不同的提示格式
        if (typeof prompt === 'string') {
            // 简单字符串提示
            requestBody.contents = [{
                parts: [{ text: prompt }]
            }];
        } else if (prompt.system && prompt.user) {
            // 系统+用户提示
            requestBody.systemInstruction = {
                parts: [{ text: prompt.system }]
            };
            requestBody.contents = [{
                parts: [{ text: prompt.user }]
            }];
        } else if (prompt.user) {
            // 仅用户提示
            requestBody.contents = [{
                parts: [{ text: prompt.user }]
            }];
        } else {
            throw new Error('Invalid prompt format. Expected string or {system?, user} object.');
        }

        return requestBody;
    }

    /**
     * 解析API响应
     * @param {Object} response - API响应
     * @returns {string} - 提取的文本内容
     */
    parseResponse(response) {
        if (!response.data) {
            throw new Error('Empty response from AI API');
        }

        const { data } = response;
        
        if (data.candidates && 
            data.candidates[0] && 
            data.candidates[0].content && 
            data.candidates[0].content.parts && 
            data.candidates[0].content.parts[0] && 
            data.candidates[0].content.parts[0].text) {
            
            return data.candidates[0].content.parts[0].text.trim();
        }

        // 记录意外的响应格式用于调试
        console.error('[AI Service] Unexpected API response format:', JSON.stringify(data, null, 2));
        throw new Error('Unexpected AI API response format');
    }

    /**
     * 执行单次API调用
     * @param {Object} requestBody - 请求体
     * @param {string} serviceType - 服务类型
     * @returns {Promise<string>} - AI生成的内容
     */
    async executeAPICall(requestBody, serviceType = 'default') {
        const url = `${this.config.baseURL}/models/${this.config.model}:generateContent?key=${this.config.apiKey}`;
        const startTime = Date.now();
        
        try {
            const response = await this.httpClient.post(url, requestBody);
            const responseTime = Date.now() - startTime;
            const result = this.parseResponse(response);
            
            // 记录成功的API调用
            await aiUsageTracker.recordUsage({
                provider: 'gemini',
                serviceType: serviceType,
                endpoint: url,
                modelName: this.config.model,
                success: true,
                responseTimeMs: responseTime,
                cacheHit: false
            });
            
            return result;
        } catch (error) {
            const responseTime = Date.now() - startTime;
            
            // 记录失败的API调用
            await aiUsageTracker.recordUsage({
                provider: 'gemini',
                serviceType: serviceType,
                endpoint: url,
                modelName: this.config.model,
                success: false,
                responseTimeMs: responseTime,
                cacheHit: false
            });
            
            // 记录错误详情
            await aiUsageTracker.recordError({
                provider: 'gemini',
                serviceType: serviceType,
                errorType: error.response ? 'API_ERROR' : 'NETWORK_ERROR',
                errorMessage: error.message,
                errorCode: error.response?.status?.toString(),
                requestSummary: `Model: ${this.config.model}, URL: ${url}`,
                responseSummary: error.response ? `Status: ${error.response.status}, Data: ${JSON.stringify(error.response.data)}` : null
            });
            
            // 增强错误信息
            if (error.response) {
                const apiError = new Error(`Gemini API Error: ${error.response.status} - ${error.response.statusText}`);
                apiError.status = error.response.status;
                apiError.data = error.response.data;
                throw apiError;
            }
            throw error;
        }
    }

    /**
     * 检查错误是否可重试
     * @param {Error} error - 错误对象
     * @returns {boolean} - 是否可重试
     */
    isRetryableError(error) {
        if (error.code && this.config.retryConfig.retryableErrors.includes(error.code)) {
            return true;
        }
        if (error.status === 429 || error.status === 500 || error.status === 502 || error.status === 503) {
            return true;
        }
        return false;
    }

    /**
     * 延迟执行
     * @param {number} ms - 延迟毫秒数
     * @returns {Promise} - Promise对象
     */
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 带重试的API调用
     * @param {Object} requestBody - 请求体
     * @param {string} context - 调用上下文（用于日志）
     * @param {string} serviceType - 服务类型
     * @returns {Promise<string>} - AI生成的内容
     */
    async executeWithRetry(requestBody, context = 'unknown', serviceType = 'default') {
        const { maxRetries, retryDelay, backoffMultiplier } = this.config.retryConfig;
        let lastError;

        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                this.metrics.totalCalls++;
                const result = await this.executeAPICall(requestBody, serviceType);
                this.metrics.successfulCalls++;
                
                if (attempt > 0) {
                    console.log(`[AI Service] ${context} succeeded on attempt ${attempt + 1}`);
                }
                
                return result;
            } catch (error) {
                lastError = error;
                this.metrics.failedCalls++;

                console.error(`[AI Service] ${context} attempt ${attempt + 1} failed:`, error.message);

                if (attempt === maxRetries || !this.isRetryableError(error)) {
                    break;
                }

                const delayMs = retryDelay * Math.pow(backoffMultiplier, attempt);
                console.log(`[AI Service] Retrying ${context} in ${delayMs}ms...`);
                await this.delay(delayMs);
            }
        }

        throw new Error(`AI Service failed after ${maxRetries + 1} attempts: ${lastError?.message || 'Unknown error'}`);
    }

    /**
     * 主要的内容生成方法
     * @param {Object|string} prompt - 提示内容
     * @param {Object} options - 选项
     * @param {string} options.service - 服务名称（用于获取特定配置）
     * @param {string} options.cacheKey - 自定义缓存键
     * @param {number} options.cacheTTL - 缓存存活时间
     * @param {Object} options.config - 自定义生成配置
     * @param {string} options.context - 调用上下文
     * @returns {Promise<Object>} - 结果对象 {success: boolean, content?: string, error?: string}
     */
    async generateContent(prompt, options = {}) {
        const {
            service = 'default',
            cacheKey,
            cacheTTL,
            config = {},
            context = `generateContent-${service}`
        } = options;

        try {
            // 获取服务特定配置
            const serviceConfig = this.config.serviceConfigs[service] || {};
            const finalConfig = { ...serviceConfig, ...config };

            // 构建请求体
            const requestBody = this.buildRequestBody(prompt, finalConfig);

            // 检查缓存
            let finalCacheKey = cacheKey;
            if (!finalCacheKey && aiConfig.cache.enabled) {
                finalCacheKey = this.generateCacheKey(prompt, finalConfig);
            }

            if (finalCacheKey) {
                const cached = this.getCache(finalCacheKey);
                if (cached) {
                    console.log(`[AI Service] Cache hit for ${context}`);
                    return { success: true, content: cached };
                }
            }

            // 执行API调用
            const content = await this.executeWithRetry(requestBody, context, service);

            // 缓存结果
            if (finalCacheKey) {
                const ttl = cacheTTL || aiConfig.cache.ttl[service] || aiConfig.cache.ttl.default || 3600;
                this.setCache(finalCacheKey, content, ttl);
            }

            return { success: true, content };

        } catch (error) {
            console.error(`[AI Service] ${context} failed:`, error.message);
            return { 
                success: false, 
                error: error.message,
                retryable: this.isRetryableError(error)
            };
        }
    }

    /**
     * 便捷方法：生成医疗笔记总结
     * @param {Object} prompt - 提示对象
     * @param {string} noteId - 笔记ID（用于缓存）
     * @param {string} language - 语言
     * @returns {Promise<Object>} - 结果对象
     */
    async generateMedicalNoteSummary(prompt, noteId, language = 'en') {
        return this.generateContent(prompt, {
            service: 'medicalNotes',
            cacheKey: `medical_note_${noteId}_${language}`,
            context: `medicalNoteSummary-${noteId}`
        });
    }

    /**
     * 便捷方法：生成处方解释
     * @param {Object} prompt - 提示对象
     * @param {string} prescriptionId - 处方ID
     * @param {string} language - 语言
     * @returns {Promise<Object>} - 结果对象
     */
    async generatePrescriptionExplanation(prompt, prescriptionId, language = 'zh') {
        return this.generateContent(prompt, {
            service: 'prescription',
            cacheKey: `prescription_${prescriptionId}_${language}`,
            context: `prescriptionExplanation-${prescriptionId}`
        });
    }

    /**
     * 便捷方法：生成疫苗接种解释
     * @param {Object} prompt - 提示对象
     * @param {string} immunizationId - 疫苗接种ID
     * @param {string} language - 语言
     * @returns {Promise<Object>} - 结果对象
     */
    async generateImmunizationExplanation(prompt, immunizationId, language = 'zh') {
        return this.generateContent(prompt, {
            service: 'immunization',
            cacheKey: `immunization_${immunizationId}_${language}`,
            context: `immunizationExplanation-${immunizationId}`
        });
    }

    /**
     * 便捷方法：生成营养师评论总结
     * @param {Object} prompt - 提示对象
     * @param {string} commentId - 评论ID
     * @param {string} language - 语言
     * @returns {Promise<Object>} - 结果对象
     */
    async generateDieticianSummary(prompt, commentId, language = 'zh') {
        return this.generateContent(prompt, {
            service: 'dietician',
            cacheKey: `dietician_${commentId}_${language}`,
            context: `dieticianSummary-${commentId}`
        });
    }

    /**
     * 获取服务指标
     * @returns {Object} - 指标对象
     */
    getMetrics() {
        return {
            ...this.metrics,
            cacheSize: this.cache.size,
            successRate: this.metrics.totalCalls > 0 
                ? (this.metrics.successfulCalls / this.metrics.totalCalls * 100).toFixed(2) + '%' 
                : '0%',
            cacheHitRate: (this.metrics.cacheHits + this.metrics.cacheMisses) > 0
                ? (this.metrics.cacheHits / (this.metrics.cacheHits + this.metrics.cacheMisses) * 100).toFixed(2) + '%'
                : '0%'
        };
    }

    /**
     * 清理过期缓存
     */
    cleanupCache() {
        const now = Date.now();
        let cleaned = 0;
        
        for (const [key, value] of this.cache.entries()) {
            if (value.expires <= now) {
                this.cache.delete(key);
                cleaned++;
            }
        }
        
        if (cleaned > 0) {
            console.log(`[AI Service] Cleaned up ${cleaned} expired cache entries`);
        }
    }
}

// 创建单例实例
const aiService = new GeminiAIService();

// 定期清理缓存
setInterval(() => {
    aiService.cleanupCache();
}, 300000); // 每5分钟清理一次

module.exports = aiService; 