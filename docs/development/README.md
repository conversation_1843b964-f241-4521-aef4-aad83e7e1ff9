# 🔧 开发文档

开发相关指南、规范和架构文档

## 📋 文档列表

### 开发规范与流程
- [**开发规范指南**](./DEV_GUIDELINES.md) - 代码规范和开发流程
  - 代码风格规范
  - Git工作流程
  - 代码审查标准
  - 测试要求

- [**新功能添加指南**](./add_new_feature_guide.md) - 添加新功能的详细步骤
  - 功能设计流程
  - 代码实现规范
  - 测试与部署

### 架构文档
- [**前端架构总览**](./frontend_summary.md) - 前端技术栈和组件说明
  - React组件结构
  - Material-UI使用
  - 状态管理
  - 路由配置

- [**后端架构总览**](./backend_summary.md) - 后端架构和API设计
  - Node.js + Express架构
  - 数据库设计
  - API结构
  - 安全机制

### 功能实现文档
- [**AI解释功能实现**](./ai_explanation_feature_implementation.md) - AI功能技术实现详解
  - AI集成方案
  - 处方解释系统
  - 免疫接种说明
  - 营养师评论总结

## 🛠️ 开发环境设置

### 前端开发
```bash
cd frontend
npm install
npm start  # 开发模式运行
```

### 后端开发
```bash
cd backend
npm install
npm run dev  # 开发模式运行
```

### 完整开发环境
```bash
# 使用Docker开发环境
./rebuild_docker.sh -s backend  # 只重建后端
./rebuild_docker.sh -s frontend  # 只重建前端
```

## 📚 技术栈

### 前端技术
- **框架**: React 18+
- **UI库**: Material-UI (MUI)
- **路由**: React Router v6
- **状态管理**: Context API
- **HTTP客户端**: Axios

### 后端技术
- **运行时**: Node.js 20+
- **框架**: Express.js
- **数据库**: MySQL 8.0
- **ORM**: Native MySQL2
- **认证**: JWT
- **文件存储**: Supabase

### AI服务
- **聊天机器人**: 独立微服务
- **AI模型**: Google Gemini API
- **数据处理**: 自然语言处理

## 🔄 开发流程

1. **需求分析** - 明确功能需求和技术要求
2. **设计阶段** - API设计和数据库设计
3. **编码实现** - 遵循开发规范
4. **测试验证** - 单元测试和集成测试
5. **代码审查** - 同行评审
6. **部署发布** - 使用Docker部署

## 🔗 相关链接

- [返回文档中心](../)
- [API文档](../api/) - 接口规范
- [部署文档](../deployment/) - 部署指南
- [运维文档](../operations/) - 运维维护

## 📞 开发支持

- 遇到技术问题请查阅相关文档
- 新功能开发请参考开发规范
- 代码提交前请运行测试

---

*最后更新: 2025-05-28* 