// 简化的PaymentService，不依赖Stripe
const Order = require('../models/Order');

class PaymentService {
    /**
     * 检查Stripe是否可用
     */
    static isStripeAvailable() {
        return false; // 暂时返回false，表示Stripe不可用
    }

    /**
     * 创建Stripe支付意图
     */
    static async createPaymentIntent(orderData) {
        return {
            success: false,
            message: 'Payment service is currently unavailable. Stripe is not configured.',
            error: 'STRIPE_NOT_CONFIGURED'
        };
    }

    /**
     * 确认支付
     */
    static async confirmPayment(paymentIntentId) {
        return {
            success: false,
            message: 'Payment service is currently unavailable. Stripe is not configured.',
            error: 'STRIPE_NOT_CONFIGURED'
        };
    }

    /**
     * 处理Stripe Webhook
     */
    static async handleWebhook(event) {
        return {
            success: false,
            message: 'Payment service is currently unavailable. Stripe is not configured.',
            error: 'STRIPE_NOT_CONFIGURED'
        };
    }

    /**
     * 创建退款
     */
    static async createRefund(paymentIntentId, amount = null) {
        return {
            success: false,
            message: 'Payment service is currently unavailable. Stripe is not configured.',
            error: 'STRIPE_NOT_CONFIGURED'
        };
    }

    /**
     * 获取支付方法
     */
    static async getPaymentMethods(customerId) {
        return {
            success: false,
            message: 'Payment service is currently unavailable. Stripe is not configured.',
            error: 'STRIPE_NOT_CONFIGURED'
        };
    }

    /**
     * 创建或获取Stripe客户
     */
    static async createOrGetCustomer(email, name = null) {
        return {
            success: false,
            message: 'Payment service is currently unavailable. Stripe is not configured.',
            error: 'STRIPE_NOT_CONFIGURED'
        };
    }

    /**
     * 验证Webhook签名
     */
    static verifyWebhookSignature(payload, signature, endpointSecret) {
        return {
            success: false,
            message: 'Payment service is currently unavailable. Stripe is not configured.',
            error: 'STRIPE_NOT_CONFIGURED'
        };
    }
}

module.exports = PaymentService; 