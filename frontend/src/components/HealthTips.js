import React, { useState, useEffect } from 'react';
import { API_URL } from '../utils/env';
import {
    Box,
    Typography,
    Card,
    CardContent,
    Grid,
    Button,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    IconButton,
    Divider,
    CircularProgress,
    Alert,
    Chip,
    Stack,
    Paper,
    Badge,
    useTheme,
    FormControlLabel,
    Switch,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import TipsAndUpdatesIcon from '@mui/icons-material/TipsAndUpdates';
import FitnessCenterIcon from '@mui/icons-material/FitnessCenter';
import RestaurantIcon from '@mui/icons-material/Restaurant';
import SpaIcon from '@mui/icons-material/Spa';
import PsychologyIcon from '@mui/icons-material/Psychology';
import StarIcon from '@mui/icons-material/Star';
import LocalOfferIcon from '@mui/icons-material/LocalOffer';
import TranslateIcon from '@mui/icons-material/Translate';
import { useLanguage } from '../context/LanguageContext';
import axios from 'axios';

// 辅助函数：检测中文字符
const containsChinese = (text) => {
    return /[\u4e00-\u9fa5]/.test(text);
};

// 辅助函数：检测英文字符
const containsEnglish = (text) => {
    return /[a-zA-Z]/.test(text);
};

// 模拟健康小贴士数据 - 使用静态数据替代
const MOCK_TIPS = [
    {
        filename: 'healthy-eating.md',
        title: '健康饮食指南',
        category: 'nutrition',
        tags: ['饮食', '营养', '健康饮食'],
        featured: true,
        lastModified: '2023-05-15T10:30:00Z'
    },
    {
        filename: 'stress-management.md',
        title: '压力管理技巧',
        category: 'mental-health',
        tags: ['压力', '焦虑', '心理健康'],
        featured: true,
        lastModified: '2023-06-20T14:45:00Z'
    },
    {
        filename: 'exercise-basics.md',
        title: '运动基础知识',
        category: 'fitness',
        tags: ['锻炼', '健身', '运动'],
        featured: false,
        lastModified: '2023-07-05T09:15:00Z'
    },
    {
        filename: 'sleep-improvement.md',
        title: '改善睡眠习惯',
        category: 'wellness',
        tags: ['睡眠', '休息', '健康'],
        featured: true,
        lastModified: '2023-08-12T16:20:00Z'
    },
    {
        filename: 'meditation-guide.md',
        title: '冥想指南',
        category: 'mental-health',
        tags: ['冥想', '压力', '放松'],
        featured: false,
        lastModified: '2023-09-18T11:30:00Z'
    },
    {
        filename: 'seasonal-allergies.md',
        title: '季节性过敏应对策略',
        category: 'wellness',
        tags: ['过敏', '健康', '季节'],
        featured: false,
        lastModified: '2023-10-05T10:45:00Z'
    }
];

// 预定义内容映射
const MOCK_CONTENTS = {
    'healthy-eating.md': `# 健康饮食指南

## 每日营养目标

* 每天至少吃5份水果和蔬菜
* 选择全谷物而非精制碳水化合物
* 每餐包含瘦蛋白质
* 保持充足的水分，每天至少8杯水

## 膳食计划技巧

1. 提前准备餐食
2. 使用各种色彩鲜艳的食物
3. 控制份量大小
4. 限制加工食品和添加糖`,
    'stress-management.md': `# 压力管理技巧

## 快速缓解压力的方法

* 深呼吸练习
* 渐进式肌肉放松
* 正念冥想
* 短时户外散步

## 长期压力管理

1. 规律的锻炼计划
2. 充足的睡眠（7-9小时）
3. 保持社交联系
4. 必要时寻求专业咨询`,
    'exercise-basics.md': `# 运动基础知识

## 入门指南

* 以5-10分钟热身开始
* 每周目标150分钟中等强度活动
* 每周包括2-3次力量训练
* 始终进行冷却和拉伸

## 找到适合自己的运动

1. 尝试不同活动找到喜欢的
2. 从慢开始，逐渐增加强度
3. 考虑参加团体课程增加动力
4. 记录进展保持动力`,
    'sleep-improvement.md': `# 改善睡眠习惯

## 创造最佳睡眠环境

* 保持卧室凉爽、黑暗和安静
* 使用舒适的床垫和枕头
* 从卧室移除电子设备
* 保持一致的睡眠时间表

## 有益的睡前习惯

1. 睡前避免咖啡因和重餐
2. 练习放松技巧
3. 睡前1-2小时限制屏幕时间
4. 考虑写日记来清理思绪`,
    'meditation-guide.md': `# 冥想指南

## 初学者技巧

* 从短时间会话开始
* 关注呼吸
* 找到安静舒适的空间
* 尝试引导冥想应用

## 建立习惯

1. 每天固定时间练习
2. 逐渐增加持续时间
3. 尝试不同类型的冥想
4. 记住没有"完美"的冥想`,
    'seasonal-allergies.md': `# 季节性过敏应对策略

## 日常管理技巧

* 关注当地花粉计数
* 保持窗户关闭
* 户外返回后淋浴并更换衣物
* 考虑使用空气净化器

## 治疗选择

1. 非处方抗组胺药
2. 盐水鼻腔冲洗
3. 非处方鼻腔喷雾
4. 严重情况咨询过敏专家`
};

const HealthTips = () => {
    const { t } = useLanguage();
    const theme = useTheme();
    const [tips, setTips] = useState([]);
    const [selectedTip, setSelectedTip] = useState(null);
    const [tipContent, setTipContent] = useState('');
    const [dialogOpen, setDialogOpen] = useState(false);
    const [loading, setLoading] = useState(true);
    const [tipLoading, setTipLoading] = useState(false);
    const [error, setError] = useState('');
    const [activeCategory, setActiveCategory] = useState('all');
    const [activeTag, setActiveTag] = useState(null);
    const [availableTags, setAvailableTags] = useState([]);
    const [showOnlyFeatured, setShowOnlyFeatured] = useState(false);
    const [availableCategories, setAvailableCategories] = useState([]);

    // 分类图标配置
    const categoryIcons = {
        'nutrition': <RestaurantIcon sx={{ color: '#2E7D32' }} />,  // 深绿色
        'fitness': <FitnessCenterIcon sx={{ color: '#1976D2' }} />, // 蓝色
        'wellness': <SpaIcon sx={{ color: '#0097A7' }} />,  // 青色
        'mental-health': <PsychologyIcon sx={{ color: '#7B1FA2' }} />, // 紫色
        'sleep': <SpaIcon sx={{ color: '#512DA8' }} />,  // 深紫色
        'all': <TipsAndUpdatesIcon sx={{ color: '#1565C0' }} />, // 深蓝色
        'general': <TipsAndUpdatesIcon sx={{ color: '#1565C0' }} /> // 深蓝色
    };

    // 分类名称映射
    const categoryNames = {
        'nutrition': '营养健康',
        'fitness': '运动健身',
        'wellness': '健康生活',
        'mental-health': '心理健康',
        'sleep': '睡眠',
        'general': '常见问题',
        'all': '全部'
    };

    // 分类标签背景色
    const categoryColors = {
        'nutrition': 'rgba(46, 125, 50, 0.1)', // 绿色
        'fitness': 'rgba(25, 118, 210, 0.1)',  // 蓝色
        'wellness': 'rgba(0, 151, 167, 0.1)',  // 青色
        'mental-health': 'rgba(123, 31, 162, 0.1)', // 紫色
        'sleep': 'rgba(81, 45, 168, 0.1)', // 深紫色
        'general': 'rgba(21, 101, 192, 0.1)', // 深蓝色
        'all': 'rgba(21, 101, 192, 0.1)' // 深蓝色
    };

    const categoryTextColors = {
        'nutrition': '#2E7D32', // 绿色
        'fitness': '#1976D2',  // 蓝色
        'wellness': '#0097A7',  // 青色
        'mental-health': '#7B1FA2', // 紫色
        'sleep': '#512DA8', // 深紫色
        'general': '#1565C0', // 深蓝色
        'all': '#1565C0' // 深蓝色
    };

    // 添加不同分类的默认摘要文本
    const getCategorySummary = (category) => {
        const summaries = {
            'nutrition': '均衡饮食是健康生活的基础，了解如何合理搭配食物，获取必要的营养素，保持身体健康活力。',
            'fitness': '科学的运动方式能提升体能、塑造体态并改善心理健康，掌握正确的健身知识，让运动成为生活的享受。',
            'wellness': '全面的健康生活方式需要均衡的饮食、适当的运动、充足的休息和积极的心态，掌握这些实用小技巧，构建健康可持续的生活方式。',
            'mental-health': '保持良好的心理健康与身体健康同样重要，了解情绪管理方法、压力应对技巧和心理调适策略，提高心理韧性和幸福感。',
            'sleep': '充足的高质量睡眠对身心健康至关重要，学习如何创造理想的睡眠环境，养成良好的睡前习惯，让您每天醒来精力充沛。',
            'general': '探索健康生活的小技巧，为您带来更健康的生活方式。'
        };
        return summaries[category] || summaries['general'];
    };

    // 获取分类列表
    useEffect(() => {
        const fetchCategories = async () => {
            try {
                console.log('Fetching categories from backend...');
                // Use the public endpoint that doesn't require authentication
                const response = await axios.get(`${API_URL || ''}/api/public/categories`);

                console.log('Categories API response:', response.data);

                if (response.data && Array.isArray(response.data)) {
                    // Add an "all" category
                    const allCategories = [
                        { slug: 'all', name: categoryNames['all'] || '全部' },
                        ...response.data
                    ];
                    console.log('Setting available categories:', allCategories);
                    setAvailableCategories(allCategories);
                } else {
                    console.error('API response is not an array:', response.data);
                    // Fallback to hardcoded categories
                    useDefaultCategories();
                }
            } catch (err) {
                console.error('获取分类失败:', err);
                // Fallback to hardcoded categories
                useDefaultCategories();
            }
        };

        const useDefaultCategories = () => {
            console.log('Using default hardcoded categories');
            const defaultCategories = [
                { slug: 'all', name: '全部' },
                { slug: 'nutrition', name: '营养健康' },
                { slug: 'fitness', name: '运动健身' },
                { slug: 'wellness', name: '健康生活' },
                { slug: 'mental-health', name: '心理健康' },
                { slug: 'sleep', name: '睡眠' },
                { slug: 'general', name: '常见问题' },
                { slug: 'preventive-care', name: '疾病预防' },
                { slug: 'disease-info', name: '疾病信息' }
            ];
            setAvailableCategories(defaultCategories);
        };

        fetchCategories();
    }, []);

    // 获取小贴士列表
    useEffect(() => {
        const fetchTips = async () => {
            setLoading(true);
            setError('');

            try {
                // 尝试从API获取数据
                const token = localStorage.getItem('token');
                let url;

                if (activeCategory === 'all') {
                    url = `${API_URL || ''}/api/tips`;
                } else {
                    url = `${API_URL || ''}/api/tips/category/${activeCategory}`;
                }

                try {
                    const response = await axios.get(url, {
                        headers: { Authorization: `Bearer ${token}` }
                    });

                    // Process response data
                    const tipData = response.data || [];

                    // Extract all unique tags for filter options
                    const allTags = [];
                    tipData.forEach(tip => {
                        if (tip.tags && Array.isArray(tip.tags)) {
                            tip.tags.forEach(tag => {
                                if (!allTags.includes(tag)) {
                                    allTags.push(tag);
                                }
                            });
                        }
                    });
                    // Filter out 'ai-generated' tag before sorting and setting
                    const filteredSortedTagsOnline = allTags.filter(tag => tag !== 'ai-generated').sort();
                    setAvailableTags(filteredSortedTagsOnline);

                    // Apply tag filtering if needed
                    let filteredTips = tipData;
                    if (activeTag) {
                        filteredTips = filteredTips.filter(tip =>
                            tip.tags && tip.tags.includes(activeTag)
                        );
                    }

                    // Apply featured filtering if needed
                    if (showOnlyFeatured) {
                        filteredTips = filteredTips.filter(tip => tip.featured);
                    }

                    setTips(filteredTips);
                    setLoading(false);
                } catch (apiError) {
                    console.error('API error, falling back to mock data:', apiError);
                    // If API fails, use mock data as fallback
                    useMockData();
                }
            } catch (err) {
                console.error('Error fetching health tips:', err);
                setError(err.response?.data?.message || 'Failed to load health tips');
                setLoading(false);
            }
        };

        // Fallback to mock data when API fails
        const useMockData = () => {
            console.log('Using mock data for health tips');

            // 使用预定义的静态数据，不需要延迟
            // Extract all unique tags
            const allTags = [];
            MOCK_TIPS.forEach(tip => {
                if (tip.tags && Array.isArray(tip.tags)) {
                    tip.tags.forEach(tag => {
                        if (!allTags.includes(tag)) {
                            allTags.push(tag);
                        }
                    });
                }
            });
            // Filter out 'ai-generated' tag before sorting and setting
            const filteredSortedTagsMock = allTags.filter(tag => tag !== 'ai-generated').sort();
            setAvailableTags(filteredSortedTagsMock);

            // Apply category filtering
            let filteredTips = activeCategory === 'all'
                ? MOCK_TIPS
                : MOCK_TIPS.filter(tip => tip.category === activeCategory);

            // Apply tag filtering if needed
            if (activeTag) {
                filteredTips = filteredTips.filter(tip =>
                    tip.tags && tip.tags.includes(activeTag)
                );
            }

            // Apply featured filtering if needed
            if (showOnlyFeatured) {
                filteredTips = filteredTips.filter(tip => tip.featured);
            }

            setTips(filteredTips);
            setLoading(false);
        };

        fetchTips();
    }, [activeCategory, activeTag, showOnlyFeatured]);

    // 获取特定小贴士的内容
    const fetchTipContent = async (filename) => {
        setTipLoading(true);

        try {
            // 尝试从API获取数据
            const token = localStorage.getItem('token');
            try {
                const response = await axios.get(`${API_URL || ''}/api/tips/${filename}`, {
                    headers: { Authorization: `Bearer ${token}` }
                });
                setTipContent(response.data.content);
                setTipLoading(false);
            } catch (apiError) {
                console.error(`API error, using mock content for ${filename}:`, apiError);
                useMockContent(filename);
            }
        } catch (err) {
            console.error(`Error fetching tip content for ${filename}:`, err);
            setTipContent('**内容不可用**');
            setTipLoading(false);
        }
    };

    // 使用模拟内容作为后备
    const useMockContent = (filename) => {
        // 立即使用预定义内容，不添加延迟
        const content = MOCK_CONTENTS[filename] || '**内容不可用**';
        setTipContent(content);
        setTipLoading(false);
    };

    const handleOpenTip = (tip) => {
        setSelectedTip(tip);
        setDialogOpen(true);

        // 如果是使用模拟数据并且内容已预定义，则直接设置内容
        if (MOCK_CONTENTS[tip.filename]) {
            setTipContent(MOCK_CONTENTS[tip.filename]);
            setTipLoading(false);
        } else {
            // 否则从API获取
            setTipContent('');
            setTipLoading(true);
            fetchTipContent(tip.filename);
        }
    };

    const handleCloseDialog = () => {
        setDialogOpen(false);
        setSelectedTip(null);
        setTipContent('');
    };

    // 切换小贴士分类
    const handleCategoryChange = (category) => {
        setActiveCategory(category);
        // 切换分类时重置标签筛选
        setActiveTag(null);
    };

    // 切换标签筛选
    const handleTagChange = (tag) => {
        setActiveTag(tag === activeTag ? null : tag);
    };

    // 切换精选筛选
    const handleFeaturedToggle = () => {
        setShowOnlyFeatured(!showOnlyFeatured);
    };

    // 获取分类标签颜色
    const getCategoryColor = (category) => {
        // If we have a specific color for this category, return it
        if (categoryColors[category]) {
            return categoryColors[category];
        }

        // Otherwise return a color based on category name if possible
        switch (category) {
            case 'preventive-care':
                return 'rgba(0, 151, 167, 0.1)'; // Similar to wellness
            case 'disease-info':
                return 'rgba(123, 31, 162, 0.1)'; // Similar to mental-health
            default:
                return categoryColors['general'];
        }
    };

    // 获取分类标签文字颜色
    const getCategoryTextColor = (category) => {
        // If we have a specific text color for this category, return it
        if (categoryTextColors[category]) {
            return categoryTextColors[category];
        }

        // Otherwise return a color based on category name if possible
        switch (category) {
            case 'preventive-care':
                return '#0097A7'; // Similar to wellness
            case 'disease-info':
                return '#7B1FA2'; // Similar to mental-health
            default:
                return categoryTextColors['general'];
        }
    };

    // 获取分类图标
    const getCategoryIcon = (category) => {
        // If we have a specific icon for this category, return it
        if (categoryIcons[category]) {
            return categoryIcons[category];
        }

        // Otherwise return a default icon based on category name if possible
        // For categories not in our predefined list
        switch (category) {
            case 'preventive-care':
                return <SpaIcon sx={{ color: '#0097A7' }} />;
            case 'disease-info':
                return <PsychologyIcon sx={{ color: '#7B1FA2' }} />;
            default:
                return <TipsAndUpdatesIcon sx={{ color: '#1565C0' }} />;
        }
    };

    // 获取分类名称
    const getCategoryLabel = (category) => {
        // If we have a specific label for this category in our predefined list, return it
        if (categoryNames[category]) {
            return categoryNames[category];
        }

        // Otherwise try to find it in the available categories from the backend
        const foundCategory = availableCategories.find(cat => cat.slug === category);
        if (foundCategory) {
            return foundCategory.name;
        }

        // If all else fails, make the category name more readable
        switch (category) {
            case 'preventive-care':
                return '疾病预防';
            case 'disease-info':
                return '疾病信息';
            default:
                // Capitalize and replace hyphens with spaces as a fallback
                return category.charAt(0).toUpperCase() +
                    category.slice(1).replace(/-/g, ' ');
        }
    };

    // 简单的Markdown渲染
    const renderMarkdown = (text) => {
        if (!text) return '';

        // 替换标题
        let formatted = text.replace(/^# (.*$)/gm, '<h1>$1</h1>');
        formatted = formatted.replace(/^## (.*$)/gm, '<h2>$1</h2>');
        formatted = formatted.replace(/^### (.*$)/gm, '<h3>$1</h3>');

        // 替换列表
        formatted = formatted.replace(/^\* (.*$)/gm, '<li>$1</li>');
        formatted = formatted.replace(/^(\d+)\. (.*$)/gm, '<li>$2</li>');
        formatted = formatted.replace(/<\/li>\n<li>/g, '</li><li>');
        formatted = formatted.replace(/(?:<li>.*<\/li>\n)+/gs, function (match) {
            return match.startsWith('<li>1') || match.startsWith('<li>2')
                ? '<ol>' + match + '</ol>'
                : '<ul>' + match + '</ul>';
        });

        // 替换粗体和斜体
        formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>');

        // 替换段落
        formatted = formatted.replace(/(?:^|\n)(?!\<h|\<ul|\<ol)(.+)/g, '<p>$1</p>');

        return formatted;
    };

    const renderTipContent = () => {
        if (tipLoading) {
            return (
                <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                    <CircularProgress color="primary" />
                    <Typography sx={{ ml: 2 }}>加载中...</Typography>
                </Box>
            );
        }

        return (
            <Box sx={{
                '& h1': {
                    mb: 2,
                    fontSize: '1.8rem',
                    color: theme.palette.mode === 'dark' ? '#90CAF9' : '#1565C0',
                    pb: 1,
                    borderBottom: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(144, 202, 249, 0.2)' : 'rgba(21, 101, 192, 0.2)'}`
                },
                '& h2': {
                    mt: 3,
                    mb: 2,
                    fontSize: '1.4rem',
                    color: theme.palette.mode === 'dark' ? '#64B5F6' : '#1976D2',
                    pb: 0.5,
                    borderBottom: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(100, 181, 246, 0.1)' : 'rgba(25, 118, 210, 0.1)'}`
                },
                '& p': { mb: 2 },
                '& ul, & ol': { mb: 2, pl: 2 }
            }}
                dangerouslySetInnerHTML={{ __html: renderMarkdown(tipContent) }}
            />
        );
    };

    // 渲染分类选择器
    const renderCategoryFilter = () => {
        // Check if we have categories from the backend
        if (availableCategories.length === 0) {
            return (
                <Box sx={{ mb: 3, display: 'flex', justifyContent: 'center' }}>
                    <CircularProgress size={24} />
                </Box>
            );
        }

        return (
            <Box sx={{ mb: 3 }}>
                <Box sx={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    gap: 0.7,
                    justifyContent: 'center',
                    backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(255, 255, 255, 0.7)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: '12px',
                    p: 1.5,
                    border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)'}`,
                    boxShadow: theme.palette.mode === 'dark' ? '0 4px 20px rgba(0, 0, 0, 0.2)' : '0 4px 20px rgba(0, 0, 0, 0.05)'
                }}>
                    {availableCategories.map(category => (
                        <Chip
                            key={category.slug}
                            label={category.name}
                            icon={getCategoryIcon(category.slug)}
                            onClick={() => handleCategoryChange(category.slug)}
                            color={activeCategory === category.slug ? "primary" : "default"}
                            variant={activeCategory === category.slug ? "filled" : "outlined"}
                            sx={{
                                m: 0.3,
                                fontSize: '0.85rem',
                                backgroundColor: activeCategory === category.slug
                                    ? theme.palette.mode === 'dark'
                                        ? 'rgba(25, 118, 210, 0.25)'
                                        : 'rgba(25, 118, 210, 0.85)'
                                    : theme.palette.mode === 'dark'
                                        ? 'rgba(255, 255, 255, 0.05)'
                                        : 'rgba(255, 255, 255, 0.65)',
                                backdropFilter: 'blur(8px)',
                                color: activeCategory === category.slug
                                    ? theme.palette.mode === 'dark' ? '#90CAF9' : '#fff'
                                    : theme.palette.mode === 'dark' ? '#fff' : 'text.primary',
                                border: '1px solid',
                                borderColor: activeCategory === category.slug
                                    ? theme.palette.mode === 'dark' ? 'rgba(144, 202, 249, 0.5)' : 'transparent'
                                    : theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.08)',
                                '&:hover': {
                                    backgroundColor: activeCategory === category.slug
                                        ? theme.palette.mode === 'dark' ? 'rgba(25, 118, 210, 0.35)' : 'rgba(25, 118, 210, 0.95)'
                                        : theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(255, 255, 255, 0.85)',
                                    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
                                    transform: 'translateY(-1px)'
                                },
                                transition: 'all 0.3s ease',
                                px: 1.2,
                                py: 2,
                                height: 'auto',
                                '& .MuiChip-label': {
                                    px: 0.5,
                                    fontWeight: 500
                                },
                                '& .MuiChip-icon': {
                                    color: activeCategory === category.slug
                                        ? theme.palette.mode === 'dark' ? '#90CAF9' : '#fff'
                                        : theme.palette.mode === 'dark' ? '#90CAF9' : 'inherit'
                                }
                            }}
                        />
                    ))}
                </Box>
            </Box>
        );
    };

    // 渲染标签选择器
    const renderTagFilter = () => {
        if (availableTags.length > 0) {
            return (
                <Box sx={{ mb: 3 }}>
                    <Typography variant="subtitle1" sx={{
                        mb: 1.5,
                        color: theme.palette.mode === 'dark' ? '#90CAF9' : '#1565C0',
                        fontWeight: 500,
                        display: 'flex',
                        alignItems: 'center'
                    }}>
                        <LocalOfferIcon sx={{ mr: 1, fontSize: '1rem' }} /> 标签筛选
                    </Typography>
                    <Box sx={{
                        display: 'flex',
                        flexWrap: 'wrap',
                        gap: 1,
                        backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.03)' : 'rgba(255, 255, 255, 0.5)',
                        backdropFilter: 'blur(8px)',
                        borderRadius: '10px',
                        p: 1,
                        border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.03)'}`,
                    }}>
                        {availableTags.map(tag => (
                            <Chip
                                key={tag}
                                label={tag}
                                size="small"
                                icon={<LocalOfferIcon />}
                                onClick={() => handleTagChange(tag)}
                                color={activeTag === tag ? "primary" : "default"}
                                variant={activeTag === tag ? "filled" : "outlined"}
                                sx={{
                                    m: 0.5,
                                    backgroundColor: activeTag === tag
                                        ? theme.palette.mode === 'dark'
                                            ? 'rgba(25, 118, 210, 0.25)'
                                            : 'rgba(25, 118, 210, 0.85)'
                                        : theme.palette.mode === 'dark'
                                            ? 'rgba(255, 255, 255, 0.05)'
                                            : 'rgba(255, 255, 255, 0.65)',
                                    backdropFilter: 'blur(8px)',
                                    color: activeTag === tag
                                        ? theme.palette.mode === 'dark' ? '#90CAF9' : '#fff'
                                        : theme.palette.mode === 'dark' ? '#fff' : 'text.primary',
                                    border: '1px solid',
                                    borderColor: activeTag === tag
                                        ? theme.palette.mode === 'dark' ? 'rgba(144, 202, 249, 0.5)' : 'transparent'
                                        : theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.08)',
                                    '&:hover': {
                                        backgroundColor: activeTag === tag
                                            ? theme.palette.mode === 'dark' ? 'rgba(25, 118, 210, 0.35)' : 'rgba(25, 118, 210, 0.95)'
                                            : theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(255, 255, 255, 0.85)',
                                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                                        transform: 'translateY(-1px)'
                                    },
                                    height: 'auto',
                                    py: 0.7,
                                    '& .MuiChip-label': {
                                        fontWeight: 500
                                    },
                                    '& .MuiChip-icon': {
                                        color: activeTag === tag
                                            ? theme.palette.mode === 'dark' ? '#90CAF9' : '#fff'
                                            : theme.palette.mode === 'dark' ? '#90CAF9' : 'inherit',
                                        fontSize: '0.8rem'
                                    }
                                }}
                            />
                        ))}
                    </Box>
                    {activeTag && (
                        <Button
                            size="small"
                            onClick={() => setActiveTag(null)}
                            startIcon={<CloseIcon fontSize="small" />}
                            sx={{
                                mt: 1,
                                color: theme.palette.mode === 'dark' ? '#90CAF9' : '#1565C0',
                                fontSize: '0.8rem',
                                textTransform: 'none',
                                '&:hover': {
                                    backgroundColor: theme.palette.mode === 'dark' ? 'rgba(144, 202, 249, 0.08)' : 'rgba(21, 101, 192, 0.08)'
                                }
                            }}
                        >
                            清除标签筛选
                        </Button>
                    )}
                </Box>
            );
        }
        return null;
    };

    // 添加精选内容筛选
    const renderFeaturedFilter = () => {
        return (
            <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" sx={{
                    mb: 1.5,
                    color: theme.palette.mode === 'dark' ? '#90CAF9' : '#1565C0',
                    fontWeight: 500,
                    display: 'flex',
                    alignItems: 'center'
                }}>
                    <StarIcon sx={{ mr: 1, fontSize: '1rem' }} /> 精选内容
                </Typography>
                <Chip
                    icon={<StarIcon />}
                    label="仅显示精选内容"
                    onClick={handleFeaturedToggle}
                    color={showOnlyFeatured ? "primary" : "default"}
                    variant={showOnlyFeatured ? "filled" : "outlined"}
                    sx={{
                        backgroundColor: showOnlyFeatured
                            ? theme.palette.mode === 'dark'
                                ? 'rgba(25, 118, 210, 0.25)'
                                : 'rgba(25, 118, 210, 0.85)'
                            : theme.palette.mode === 'dark'
                                ? 'rgba(255, 255, 255, 0.05)'
                                : 'rgba(255, 255, 255, 0.65)',
                        backdropFilter: 'blur(8px)',
                        color: showOnlyFeatured
                            ? theme.palette.mode === 'dark' ? '#90CAF9' : '#fff'
                            : theme.palette.mode === 'dark' ? '#fff' : 'text.primary',
                        border: '1px solid',
                        borderColor: showOnlyFeatured
                            ? theme.palette.mode === 'dark' ? 'rgba(144, 202, 249, 0.5)' : 'transparent'
                            : theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.08)',
                        '&:hover': {
                            backgroundColor: showOnlyFeatured
                                ? theme.palette.mode === 'dark' ? 'rgba(25, 118, 210, 0.35)' : 'rgba(25, 118, 210, 0.95)'
                                : theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(255, 255, 255, 0.85)',
                        },
                        height: 32,
                        '& .MuiChip-label': {
                            fontWeight: 500
                        },
                        '& .MuiChip-icon': {
                            color: showOnlyFeatured
                                ? theme.palette.mode === 'dark' ? '#90CAF9' : '#fff'
                                : theme.palette.mode === 'dark' ? '#FFC107' : '#FFC107'
                        }
                    }}
                />
            </Box>
        );
    };

    // 渲染小贴士列表
    const renderTipsList = () => {
        if (loading) {
            return (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
                    <CircularProgress color="primary" />
                </Box>
            );
        }

        if (error) {
            return (
                <Alert
                    severity="error"
                    sx={{
                        my: 3,
                        backgroundColor: theme.palette.mode === 'dark' ? 'rgba(211, 47, 47, 0.1)' : 'rgba(253, 237, 237, 0.9)',
                        backdropFilter: 'blur(10px)',
                        border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(211, 47, 47, 0.2)' : 'rgba(211, 47, 47, 0.1)'}`,
                        '& .MuiAlert-icon': {
                            color: theme.palette.mode === 'dark' ? '#ef5350' : '#d32f2f'
                        }
                    }}
                >
                    {error}
                </Alert>
            );
        }

        if (tips.length === 0) {
            return (
                <Box sx={{
                    my: 4,
                    textAlign: 'center',
                    p: 3,
                    backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(255, 255, 255, 0.7)',
                    backdropFilter: 'blur(10px)',
                    borderRadius: '12px',
                    border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)'}`,
                }}>
                    <Box sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        mb: 2,
                        color: theme.palette.mode === 'dark' ? '#64B5F6' : '#1976D2'
                    }}>
                        <TipsAndUpdatesIcon sx={{ fontSize: 40 }} />
                    </Box>
                    <Typography variant="h6" color="text.secondary">暂无健康小贴士信息</Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                        请稍后再试或尝试更改筛选条件
                    </Typography>
                </Box>
            );
        }

        return (
            <Box
                sx={{
                    display: 'grid',
                    gridTemplateColumns: {
                        xs: '1fr',
                        sm: 'repeat(2, 1fr)',
                        md: 'repeat(3, 1fr)'
                    },
                    gap: 3
                }}
            >
                {tips.map((tip) => {
                    const categoryColor = getCategoryColor(tip.category);
                    const categoryTextColor = getCategoryTextColor(tip.category);

                    return (
                        <Card
                            key={tip.id || tip.filename}
                            elevation={0}
                            onClick={() => handleOpenTip(tip)}
                            sx={{
                                height: '100%',
                                display: 'flex',
                                flexDirection: 'column',
                                transition: 'transform 0.3s, box-shadow 0.3s',
                                backgroundColor: theme.palette.mode === 'dark'
                                    ? 'rgba(66, 66, 66, 0.8)'
                                    : 'rgba(255, 255, 255, 0.8)',
                                backdropFilter: 'blur(20px)',
                                borderRadius: '16px',
                                border: `1px solid ${theme.palette.mode === 'dark'
                                    ? 'rgba(255, 255, 255, 0.08)'
                                    : 'rgba(0, 0, 0, 0.05)'}`,
                                boxShadow: theme.palette.mode === 'dark'
                                    ? '0 8px 32px rgba(0, 0, 0, 0.3)'
                                    : '0 8px 32px rgba(0, 0, 0, 0.08)',
                                overflow: 'hidden',
                                position: 'relative',
                                '&::before': {
                                    content: '""',
                                    position: 'absolute',
                                    top: 0,
                                    left: 0,
                                    width: '100%',
                                    height: '100%',
                                    background: `linear-gradient(160deg, ${categoryColor} 0%, transparent 100%)`,
                                    opacity: theme.palette.mode === 'dark' ? 0.1 : 0.2,
                                    zIndex: 0
                                },
                                '&:hover': {
                                    transform: 'translateY(-8px)',
                                    boxShadow: theme.palette.mode === 'dark'
                                        ? '0 12px 40px rgba(0, 0, 0, 0.4)'
                                        : '0 12px 40px rgba(0, 0, 0, 0.12)',
                                    cursor: 'pointer'
                                }
                            }}
                        >
                            <CardContent sx={{ flexGrow: 1, position: 'relative', zIndex: 1 }}>
                                <Box sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                                    <Box sx={{
                                        borderRadius: '50%',
                                        backgroundColor: categoryColor,
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        p: 1,
                                        mr: 1.5
                                    }}>
                                        {getCategoryIcon(tip.category)}
                                    </Box>
                                    <Box>
                                        <Chip
                                            label={getCategoryLabel(tip.category)}
                                            size="small"
                                            sx={{
                                                bgcolor: categoryColor,
                                                color: categoryTextColor,
                                                fontWeight: 600,
                                                fontSize: '0.7rem',
                                                height: 22,
                                                mb: 0.5,
                                                '& .MuiChip-label': {
                                                    px: 1
                                                }
                                            }}
                                        />
                                        <Typography
                                            variant="h6"
                                            component="h3"
                                            sx={{
                                                fontWeight: 600,
                                                color: theme.palette.mode === 'dark'
                                                    ? '#fff'
                                                    : '#333',
                                                lineHeight: 1.2
                                            }}
                                        >
                                            {tip.title}
                                        </Typography>
                                    </Box>
                                </Box>

                                <Typography
                                    variant="body2"
                                    color="text.secondary"
                                    sx={{
                                        mb: 3,
                                        lineHeight: 1.6,
                                        display: '-webkit-box',
                                        WebkitLineClamp: 3,
                                        WebkitBoxOrient: 'vertical',
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis',
                                        height: 60
                                    }}
                                >
                                    {tip.abstract || tip.summary || tip.description || getCategorySummary(tip.category)}
                                </Typography>

                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                    {tip.lastModified && (
                                        <Typography variant="caption" color="text.secondary">
                                            {new Date(tip.lastModified).toLocaleDateString('zh-CN')}
                                        </Typography>
                                    )}
                                    <Button
                                        variant="contained"
                                        size="small"
                                        sx={{
                                            backgroundColor: theme.palette.mode === 'dark'
                                                ? 'rgba(25, 118, 210, 0.8)'
                                                : '#1976D2',
                                            color: '#fff',
                                            borderRadius: '8px',
                                            textTransform: 'none',
                                            boxShadow: 'none',
                                            '&:hover': {
                                                backgroundColor: theme.palette.mode === 'dark'
                                                    ? 'rgba(25, 118, 210, 0.9)'
                                                    : '#1565C0',
                                                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)'
                                            }
                                        }}
                                        endIcon={<LocalOfferIcon />}
                                    >
                                        阅读详情
                                    </Button>
                                </Box>
                            </CardContent>
                        </Card>
                    )
                })}
            </Box>
        );
    };

    return (
        <>
            <Box sx={{
                my: 3,
                p: 3,
                position: 'relative',
                backgroundColor: theme.palette.mode === 'dark'
                    ? 'rgba(18, 18, 18, 0.7)'
                    : 'rgba(248, 250, 252, 0.8)',
                backdropFilter: 'blur(10px)',
                borderRadius: '24px',
                border: `1px solid ${theme.palette.mode === 'dark'
                    ? 'rgba(255, 255, 255, 0.05)'
                    : 'rgba(0, 0, 0, 0.02)'}`,
                boxShadow: theme.palette.mode === 'dark'
                    ? '0 10px 40px rgba(0, 0, 0, 0.3)'
                    : '0 10px 40px rgba(0, 0, 0, 0.05)',
            }}>
                <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    mb: 2,
                    pb: 2,
                    borderBottom: `1px solid ${theme.palette.mode === 'dark'
                        ? 'rgba(255, 255, 255, 0.05)'
                        : 'rgba(0, 0, 0, 0.05)'}`
                }}>
                    <TipsAndUpdatesIcon
                        sx={{
                            fontSize: 32,
                            mr: 2,
                            color: theme.palette.mode === 'dark' ? '#90CAF9' : '#1976D2'
                        }}
                    />
                    <Typography
                        variant="h4"
                        component="h1"
                        sx={{
                            fontWeight: 600,
                            color: theme.palette.mode === 'dark' ? '#fff' : '#333'
                        }}
                    >
                        健康小贴士
                    </Typography>
                </Box>
                <Typography variant="body1" color="text.secondary" paragraph>
                    探索由医疗专业人士提供的健康生活指南，了解如何保持最佳健康状态。
                </Typography>

                {renderCategoryFilter()}
                {renderTagFilter()}
                {renderFeaturedFilter()}
                {renderTipsList()}
            </Box>

            {/* 健康小贴士详情对话框 */}
            <Dialog
                open={dialogOpen}
                onClose={handleCloseDialog}
                fullWidth
                maxWidth="md"
                scroll="paper"
                sx={{
                    '& .MuiDialog-paper': {
                        backgroundColor: theme.palette.mode === 'dark'
                            ? 'rgba(66, 66, 66, 0.9)'
                            : 'rgba(255, 255, 255, 0.95)',
                        backdropFilter: 'blur(20px)',
                        borderRadius: '20px',
                        boxShadow: theme.palette.mode === 'dark'
                            ? '0 15px 60px rgba(0, 0, 0, 0.5)'
                            : '0 15px 60px rgba(0, 0, 0, 0.15)',
                        border: `1px solid ${theme.palette.mode === 'dark'
                            ? 'rgba(255, 255, 255, 0.05)'
                            : 'rgba(0, 0, 0, 0.02)'}`,
                        overflow: 'hidden'
                    }
                }}
            >
                <DialogTitle
                    sx={{
                        py: 2,
                        backgroundColor: theme.palette.mode === 'dark'
                            ? 'rgba(18, 18, 18, 0.5)'
                            : 'rgba(248, 250, 252, 0.8)',
                        backdropFilter: 'blur(10px)',
                        borderBottom: `1px solid ${theme.palette.mode === 'dark'
                            ? 'rgba(255, 255, 255, 0.05)'
                            : 'rgba(0, 0, 0, 0.05)'}`,
                    }}
                >
                    {selectedTip && (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Box sx={{
                                borderRadius: '50%',
                                backgroundColor: getCategoryColor(selectedTip.category),
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                p: 1,
                                mr: 1.5
                            }}>
                                {getCategoryIcon(selectedTip.category)}
                            </Box>
                            <Box>
                                <Chip
                                    label={getCategoryLabel(selectedTip.category)}
                                    size="small"
                                    sx={{
                                        bgcolor: getCategoryColor(selectedTip.category),
                                        color: getCategoryTextColor(selectedTip.category),
                                        fontWeight: 600,
                                        fontSize: '0.7rem',
                                        height: 22,
                                        mb: 0.5,
                                        '& .MuiChip-label': {
                                            px: 1
                                        }
                                    }}
                                />
                                <Typography
                                    variant="h6"
                                    sx={{
                                        fontWeight: 600,
                                        color: theme.palette.mode === 'dark' ? '#fff' : '#333',
                                    }}
                                >
                                    {selectedTip.title}
                                </Typography>
                            </Box>
                        </Box>
                    )}
                    <IconButton
                        aria-label="close"
                        onClick={handleCloseDialog}
                        sx={{
                            position: 'absolute',
                            right: 8,
                            top: 8,
                            color: theme.palette.text.secondary,
                        }}
                    >
                        <CloseIcon />
                    </IconButton>
                </DialogTitle>
                <DialogContent
                    dividers
                    sx={{
                        backgroundColor: theme.palette.mode === 'dark'
                            ? 'rgba(35, 35, 35, 0.5)'
                            : 'rgba(255, 255, 255, 0.8)',
                        backdropFilter: 'blur(5px)',
                        p: 3
                    }}
                >
                    {renderTipContent()}
                </DialogContent>
                <DialogActions
                    sx={{
                        backgroundColor: theme.palette.mode === 'dark'
                            ? 'rgba(18, 18, 18, 0.5)'
                            : 'rgba(248, 250, 252, 0.8)',
                        backdropFilter: 'blur(10px)',
                        p: 2
                    }}
                >
                    <Button
                        variant="contained"
                        onClick={handleCloseDialog}
                        sx={{
                            backgroundColor: theme.palette.mode === 'dark'
                                ? 'rgba(25, 118, 210, 0.8)'
                                : '#1976D2',
                            color: '#fff',
                            borderRadius: '8px',
                            textTransform: 'none',
                            '&:hover': {
                                backgroundColor: theme.palette.mode === 'dark'
                                    ? 'rgba(25, 118, 210, 0.9)'
                                    : '#1565C0',
                                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)'
                            }
                        }}
                    >
                        关闭
                    </Button>
                </DialogActions>
            </Dialog>
        </>
    );
};

export default HealthTips; 