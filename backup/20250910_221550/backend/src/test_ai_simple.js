const mysql = require('mysql2/promise');
require('dotenv').config();

async function testAIStats() {
    try {
        console.log('🔍 连接数据库...');
        const connection = await mysql.createConnection({
            host: process.env.DB_HOST || 'localhost',
            port: process.env.DB_PORT || 54322,
            user: process.env.DB_USER || 'postgres',
            password: process.env.DB_PASSWORD,
            database: process.env.DB_NAME || 'postgres'
        });
        
        console.log('📊 AI使用统计分析');
        console.log('='.repeat(50));
        
        // 总体统计
        const [totalStats] = await connection.query(`
            SELECT 
                COUNT(*) as total_calls,
                SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_calls,
                COUNT(DISTINCT provider) as unique_providers,
                COUNT(DISTINCT service_type) as unique_services
            FROM ai_usage_stats
        `);
        
        if (totalStats.length > 0) {
            const stats = totalStats[0];
            console.log(`📞 总调用次数: ${stats.total_calls}`);
            console.log(`✅ 成功调用: ${stats.successful_calls}`);
            console.log(`🏢 提供商数量: ${stats.unique_providers}`);
            console.log(`🔧 服务类型数量: ${stats.unique_services}`);
            
            if (stats.total_calls > 0) {
                const successRate = ((stats.successful_calls / stats.total_calls) * 100).toFixed(2);
                console.log(`📈 成功率: ${successRate}%`);
            }
        }
        
        // 按提供商统计
        console.log('\n按提供商统计:');
        const [providerStats] = await connection.query(`
            SELECT 
                provider,
                COUNT(*) as total_calls,
                SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_calls
            FROM ai_usage_stats 
            GROUP BY provider 
            ORDER BY total_calls DESC
        `);
        
        providerStats.forEach(provider => {
            const successRate = provider.total_calls > 0 ? 
                ((provider.successful_calls / provider.total_calls) * 100).toFixed(2) : '0.00';
            console.log(`🏢 ${provider.provider}: ${provider.total_calls}次调用 (${successRate}%成功)`);
        });
        
        // 按服务类型统计
        console.log('\n按服务类型统计:');
        const [serviceStats] = await connection.query(`
            SELECT 
                service_type,
                COUNT(*) as total_calls,
                SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_calls
            FROM ai_usage_stats 
            GROUP BY service_type 
            ORDER BY total_calls DESC
        `);
        
        serviceStats.forEach(service => {
            const successRate = service.total_calls > 0 ? 
                ((service.successful_calls / service.total_calls) * 100).toFixed(2) : '0.00';
            console.log(`🔧 ${service.service_type}: ${service.total_calls}次调用 (${successRate}%成功)`);
        });
        
        // 最近调用记录
        console.log('\n最近5次调用:');
        const [recentCalls] = await connection.query(`
            SELECT 
                provider,
                service_type,
                model_name,
                success,
                created_at
            FROM ai_usage_stats 
            ORDER BY created_at DESC 
            LIMIT 5
        `);
        
        recentCalls.forEach((call, index) => {
            const status = call.success ? '✅' : '❌';
            const time = new Date(call.created_at).toLocaleString();
            console.log(`${index + 1}. ${status} ${call.provider}/${call.service_type} (${call.model_name}) - ${time}`);
        });
        
        await connection.end();
        console.log('\n✅ 统计完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error('详细错误:', error);
    }
}

testAIStats(); 