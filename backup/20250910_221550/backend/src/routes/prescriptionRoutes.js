const express = require('express');
const router = express.Router();
const prescriptionController = require('../controllers/prescriptionController');
const authenticate = require('../middleware/authenticate'); // Using authenticate middleware
const { requireMember } = require('../middleware/userTypeAuth');

/**
 * @route   GET /api/prescriptions/:demographicNo
 * @desc    Get prescriptions for a specific patient (self or family) - Member only
 * @access  Private (Member)
 */
router.get('/:demographicNo', authenticate, requireMember, prescriptionController.getPatientPrescriptions);

// 获取处方的AI解释 - Member only
router.get('/:prescriptionId/explanation', authenticate, requireMember, prescriptionController.getPrescriptionExplanation);

module.exports = router; 