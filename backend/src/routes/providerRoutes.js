const express = require('express');
const router = express.Router();
const authenticate = require('../middleware/authenticate');
const providerController = require('../controllers/providerController');

/**
 * @route   GET /api/providers
 * @desc    Get all active providers (doctors and staff)
 * @access  Private
 */
router.get('/', authenticate, providerController.getAllProviders);

/**
 * @route   GET /api/providers/doctors
 * @desc    Get all active doctors
 * @access  Private
 */
router.get('/doctors', authenticate, providerController.getDoctors);

// Route to get availability for a specific provider
router.get('/:providerNo/availability', authenticate, providerController.getProviderAvailability);

// Route to get distinct, bookable consultation types (schedule template names)
router.get('/consultation-types', authenticate, providerController.getConsultationTypes);

// Route to get availability grouped by time for a specific consultation type (template name)
router.get('/availability/by-type', authenticate, providerController.getAvailabilityByType);

// Route to get available dates for a specific consultation type (template name)
router.get('/availability/by-type/dates', authenticate, providerController.getAvailableDatesByType);

// Get services offered by a specific provider
router.get('/:providerNo/services', authenticate, providerController.getProviderServices);

// Get available DATES for a specific provider and service type
router.get('/:providerNo/availability/dates', authenticate, providerController.getProviderAvailableDates);

module.exports = router; 