const pool = require('./config/database');

async function queryDocuments() {
  try {
    console.log('查询demographic_no=54897的文档...');
    
    // 查询文档
    const [documents] = await pool.query(`
      SELECT 
        d.document_no, 
        d.docdesc, 
        d.doctype, 
        d.contenttype,
        d.docfilename,
        d.updatedatetime, 
        d.observationdate
      FROM document d 
      JOIN ctl_document c ON d.document_no = c.document_no 
      WHERE c.module = 'demographic' 
      AND c.module_id = ? 
      ORDER BY d.updatedatetime DESC
    `, [54897]);
    
    console.log(`找到 ${documents.length} 条文档记录`);
    
    // 打印文档信息
    documents.forEach((doc, index) => {
      console.log(`\n文档 #${index + 1}:`);
      console.log(`  document_no: ${doc.document_no}`);
      console.log(`  docdesc: ${doc.docdesc}`);
      console.log(`  doctype: ${doc.doctype}`);
      console.log(`  contenttype: ${doc.contenttype}`);
      console.log(`  docfilename: ${doc.docfilename}`);
      console.log(`  updatedatetime: ${doc.updatedatetime}`);
      console.log(`  observationdate: ${doc.observationdate}`);
    });
    
    // 查询特定的文档类型统计
    const [doctypes] = await pool.query(`
      SELECT 
        doctype, 
        COUNT(*) as count
      FROM document d 
      JOIN ctl_document c ON d.document_no = c.document_no 
      WHERE c.module = 'demographic' 
      AND c.module_id = ? 
      GROUP BY doctype
      ORDER BY count DESC
    `, [54897]);
    
    console.log('\n文档类型统计:');
    doctypes.forEach(type => {
      console.log(`  ${type.doctype || 'NULL'}: ${type.count} 条记录`);
    });
    
    // 查找特定的XRAY文档
    const [xrayDocs] = await pool.query(`
      SELECT 
        d.document_no, 
        d.docdesc, 
        d.doctype, 
        d.docfilename
      FROM document d 
      JOIN ctl_document c ON d.document_no = c.document_no 
      WHERE c.module = 'demographic' 
      AND c.module_id = ? 
      AND (d.docfilename LIKE '%XRAY%' OR d.docfilename LIKE '%FM_%' OR d.docdesc LIKE '%X-Ray%')
    `, [54897]);
    
    console.log('\n包含XRAY或FM_的文档:');
    xrayDocs.forEach(doc => {
      console.log(`  document_no: ${doc.document_no}, doctype: ${doc.doctype}, docdesc: ${doc.docdesc}, docfilename: ${doc.docfilename}`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('查询出错:', error);
    process.exit(1);
  }
}

queryDocuments();
