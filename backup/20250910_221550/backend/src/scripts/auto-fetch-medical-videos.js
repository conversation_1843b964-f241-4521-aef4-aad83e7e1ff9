/**
 * 自动搜索YouTube医学科普视频并保存到数据库
 * 
 * 此脚本使用YouTube API搜索相关医学科普视频，
 * 通过Gemini AI筛选高质量内容，并自动保存到数据库
 * 
 * 重构版本 - 使用统一AI服务基础设施
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');
const mysql = require('mysql2/promise');
const aiService = require('../utils/aiService');
const MedicalPromptTemplates = require('../prompts/medicalPrompts');
require('dotenv').config({ path: path.join(__dirname, '../../../.env') });

// 配置API密钥
const YOUTUBE_API_KEY = process.env.YOUTUBE_API_KEY || 'AIzaSyDcEdc1SFu3RTM-rOkSLiY0S3AxMzk5Z1g';

// 数据库配置
const dbConfig = {
    host: process.env.DB_HOST || 'open-osp-db-1',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME || 'oscar',
    multipleStatements: true
};

// 搜索关键词列表
const SEARCH_KEYWORDS = [
    '中医养生保健科普',
    '医学知识科普视频',
    '健康知识科普讲解',
    '常见疾病预防科普',
    '医学专家健康讲座',
    '中医理疗技术讲解',
    '医学前沿研究科普',
    '健康生活方式科普',
    '中医传统疗法科普',
    '慢性病管理和预防'
];

/**
 * 使用YouTube API搜索视频
 * @param {string} query 搜索关键词
 * @param {number} maxResults 最大结果数
 * @returns {Promise<Array>} 视频列表
 */
async function searchYouTubeVideos(query, maxResults = 10) {
    console.log(`搜索关键词: "${query}"，最大结果数: ${maxResults}`);

    try {
        const response = await axios.get('https://www.googleapis.com/youtube/v3/search', {
            params: {
                part: 'snippet',
                q: query,
                type: 'video',
                maxResults: maxResults,
                order: 'relevance',
                videoEmbeddable: true,
                relevanceLanguage: 'zh',
                key: YOUTUBE_API_KEY
            }
        });

        // 提取视频信息
        const videos = response.data.items.map(item => ({
            id: item.id.videoId,
            title: item.snippet.title,
            description: item.snippet.description,
            thumbnailUrl: item.snippet.thumbnails.high?.url || item.snippet.thumbnails.default?.url,
            youtubeLink: `https://www.youtube.com/watch?v=${item.id.videoId}`,
            category: 'medical-education',
            published_at: item.snippet.publishedAt,
            channel_title: item.snippet.channelTitle
        }));

        console.log(`找到 ${videos.length} 个视频`);
        return videos;
    } catch (error) {
        console.error('YouTube API搜索失败:', error.message);
        if (error.response?.data) {
            console.error('API错误详情:', error.response.data.error?.message || JSON.stringify(error.response.data));
        }
        return [];
    }
}

/**
 * 使用统一AI服务评估视频质量 - 重构版本
 * @param {Array} videos 视频列表
 * @returns {Promise<Array>} 高质量视频列表
 */
async function evaluateVideosWithGemini(videos) {
    if (!videos || videos.length === 0) return [];

    console.log(`[AI Service] 使用统一AI服务评估 ${videos.length} 个视频的质量...`);

    const highQualityVideos = [];

    for (const video of videos) {
        try {
            console.log(`[AI Service] 评估视频: ${video.title}`);

            // 构建视频数据
            const videoData = {
                title: video.title,
                description: video.description,
                channel_title: video.channel_title
            };

            // 使用模板构建提示词
            const prompt = MedicalPromptTemplates.buildPrompt('videoEvaluation', 'zh', videoData);

            // 生成唯一缓存键
            const cacheKey = `video_eval_${video.id}`;

            // 调用统一AI服务
            const result = await aiService.generateContent(prompt, {
                service: 'videoEvaluation',
                cacheKey: cacheKey,
                config: { maxOutputTokens: 10, temperature: 0.2 },
                context: `videoEvaluation-${video.id}`
            });

            if (result.success) {
                const aiResponse = result.content.trim().toLowerCase();
                const isHighQuality = aiResponse.includes('是');

                if (isHighQuality) {
                    console.log(`✅ [AI Service] 高质量视频: ${video.title}`);
                    highQualityVideos.push(video);
                } else {
                    console.log(`❌ [AI Service] 低质量视频: ${video.title}`);
                }
            } else {
                console.error(`[AI Service] 评估失败: ${video.title} - ${result.error}`);
                // 如果AI评估失败，假设视频质量可接受
                console.log(`⚠️ [AI Service] 评估失败，默认接受: ${video.title}`);
                highQualityVideos.push(video);
            }
        } catch (error) {
            console.error(`[AI Service] 评估视频异常 "${video.title}":`, error.message);
            // 如果AI评估失败，假设视频质量可接受
            highQualityVideos.push(video);
        }
    }

    console.log(`[AI Service] 评估完成，找到 ${highQualityVideos.length} 个高质量视频`);
    return highQualityVideos;
}

/**
 * 检查视频是否已存在于数据库
 * @param {Object} connection 数据库连接
 * @param {string} youtubeLink YouTube链接
 * @returns {Promise<boolean>} 是否存在
 */
async function checkVideoExists(connection, youtubeLink) {
    try {
        const [rows] = await connection.query(
            'SELECT id FROM youtube_videos WHERE youtubeLink = ?',
            [youtubeLink]
        );
        return rows.length > 0;
    } catch (error) {
        console.error('检查视频是否存在失败:', error);
        return false;
    }
}

/**
 * 保存视频到数据库
 * @param {Array} videos 视频列表
 * @returns {Promise<number>} 保存成功的视频数量
 */
async function saveVideosToDatabase(videos) {
    if (!videos || videos.length === 0) return 0;

    console.log(`开始保存 ${videos.length} 个视频到数据库...`);

    let connection;
    let savedCount = 0;

    try {
        connection = await mysql.createConnection(dbConfig);

        for (const video of videos) {
            // 检查视频是否已存在
            const exists = await checkVideoExists(connection, video.youtubeLink);
            if (exists) {
                console.log(`视频已存在，跳过: ${video.title}`);
                continue;
            }

            // 插入新视频
            const [result] = await connection.query(
                'INSERT INTO youtube_videos (title, description, thumbnailUrl, youtubeLink, category, is_active, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())',
                [video.title, video.description, video.thumbnailUrl, video.youtubeLink, video.category || 'medical-education', 1]
            );

            if (result.insertId) {
                console.log(`✅ 保存成功: ${video.title}`);
                savedCount++;
            }
        }
    } catch (error) {
        console.error('保存视频到数据库失败:', error);
    } finally {
        if (connection) {
            await connection.end();
            console.log('数据库连接已关闭');
        }
    }

    return savedCount;
}

/**
 * 主函数 - 搜索并保存视频
 */
async function main() {
    console.log('===== 开始自动搜索医学科普YouTube视频 (使用统一AI服务) =====');

    // 随机选择3个搜索关键词
    const shuffledKeywords = SEARCH_KEYWORDS.sort(() => 0.5 - Math.random());
    const selectedKeywords = shuffledKeywords.slice(0, 3);

    let allVideos = [];

    // 搜索视频
    for (const keyword of selectedKeywords) {
        const videos = await searchYouTubeVideos(keyword, 5);
        allVideos = allVideos.concat(videos);
    }

    // 数组去重 (基于YouTube视频ID)
    allVideos = allVideos.filter((video, index, self) =>
        index === self.findIndex(v => v.id === video.id)
    );

    console.log(`共搜索到 ${allVideos.length} 个不重复视频`);

    // 使用统一AI服务评估视频质量
    const highQualityVideos = await evaluateVideosWithGemini(allVideos);

    // 保存高质量视频到数据库
    const savedCount = await saveVideosToDatabase(highQualityVideos);

    console.log(`===== 自动搜索完成，成功保存 ${savedCount} 个高质量医学科普视频 =====`);
    
    // 显示AI服务统计
    const metrics = aiService.getMetrics();
    console.log(`[AI Service] 本次运行统计: 总调用 ${metrics.totalCalls} 次, 成功率 ${metrics.successRate}, 缓存命中率 ${metrics.cacheHitRate}`);
}

// 执行主函数
main().catch(error => {
    console.error('程序执行出错:', error);
    process.exit(1);
}); 