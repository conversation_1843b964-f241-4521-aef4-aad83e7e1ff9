const express = require('express');
const router = express.Router();
const referralController = require('../controllers/referralController');
const authenticate = require('../middleware/authenticate');

// Get current user's referral code
router.get('/code', authenticate, referralController.getReferralCode);

// Get specific user's referral code (admin only)
router.get('/code/:demographicNo', authenticate, referralController.getReferralCode);

// Get current user's referrals
router.get('/list', authenticate, referralController.getUserReferrals);

// Get specific user's referrals (admin only)
router.get('/list/:demographicNo', authenticate, referralController.getUserReferrals);

// Validate a referral code (public endpoint)
router.get('/validate/:code', referralController.validateReferralCode);

module.exports = router; 