const pool = require('../config/database');

/**
 * Get all membership types
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} JSON response with membership types data
 */
exports.getMembershipTypes = async (req, res) => {
    try {
        // Define the specific list of membership codes to fetch
        const allowedCodes = [
            'AHM_EM', 'AHM_EM_FM', 'AHM_EM_KD', 'AHM_EM_YG', 'AHM_MT20',
            'AHM_SIG', 'AHM_SIG_FM', 'AHM_SIG_KD', 'AHM_SIG_YG',
            'AHM_VIP', 'AHM_VIP_FM', 'AHM_VIP_KD', 'AHM_VIP_YG'
        ];

        // Query the billingservice table for the specified service codes
        const query = `
            SELECT service_code, description, value, percentage, billingservice_date 
            FROM billingservice 
            WHERE service_code IN (?)
            ORDER BY service_code
        `;

        // The pool driver handles proper escaping for the IN clause array
        const [results] = await pool.query(query, [allowedCodes]);

        if (!results || results.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'No specified membership types found'
            });
        }

        // Return the found membership type data
        return res.json({
            success: true,
            types: results.map(type => ({
                service_code: type.service_code,
                description: type.description,
                value: type.value,
                percentage: type.percentage,
                date: type.billingservice_date
            }))
        });
    } catch (error) {
        console.error('Error fetching membership types:', error);
        return res.status(500).json({
            success: false,
            message: 'Server error fetching membership types'
        });
    }
};

/**
 * Get membership type details by code
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} JSON response with specific membership type data
 */
exports.getMembershipTypeByCode = async (req, res) => {
    try {
        const { code } = req.params;

        if (!code) {
            return res.status(400).json({
                success: false,
                message: 'Membership code is required'
            });
        }

        // 查询指定代码的会员类型信息
        const query = `
            SELECT service_code, description, value, percentage, billingservice_date 
            FROM billingservice 
            WHERE service_code = ?
        `;

        const [results] = await pool.query(query, [code]);

        if (!results || results.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Membership type not found'
            });
        }

        // 返回该会员类型的详细信息
        const typeInfo = results[0];
        return res.json({
            success: true,
            type: {
                service_code: typeInfo.service_code,
                description: typeInfo.description,
                value: typeInfo.value,
                percentage: typeInfo.percentage,
                date: typeInfo.billingservice_date
            }
        });
    } catch (error) {
        console.error(`Error fetching membership type ${req.params.code}:`, error);
        return res.status(500).json({
            success: false,
            message: 'Server error fetching membership type'
        });
    }
}; 