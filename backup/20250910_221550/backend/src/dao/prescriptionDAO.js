const pool = require('../config/database');
const { createLogger } = require('../utils/logger');

const logger = createLogger('PrescriptionDAO');

class PrescriptionDAO {
    static async getByDemographicNo(demographicNo) {
        const query = `
            SELECT
                p.script_no,
                p.provider_no,
                p.date_prescribed,
                p.textView,
                p.rx_comments,
                pr.first_name AS provider_first_name,
                pr.last_name AS provider_last_name
            FROM prescription p
            LEFT JOIN provider pr ON p.provider_no = pr.provider_no
            WHERE p.demographic_no = ?
            ORDER BY p.date_prescribed DESC;
        `;

        try {
            const startTime = Date.now();
            const [results] = await pool.query(query, [demographicNo]);
            const queryTime = Date.now() - startTime;

            if (queryTime > 1000) {
                logger.warn(`Slow query detected: ${queryTime}ms`, {
                    operation: 'getByDemographicNo',
                    demographicNo
                });
            }

            return results;
        } catch (error) {
            logger.error('Error in getByDemographicNo:', {
                error,
                demographicNo
            });
            throw error;
        }
    }

    static async checkFamilyRelationship(demoNo1, demoNo2) {
        const query = `
            SELECT 1 FROM relationships
            WHERE ((demographic_no = ? AND relation_demographic_no = ?) 
                OR (demographic_no = ? AND relation_demographic_no = ?))
            AND deleted != '1' LIMIT 1
        `;

        try {
            const [results] = await pool.query(query, [demoNo1, demoNo2, demoNo2, demoNo1]);
            return results.length > 0;
        } catch (error) {
            logger.error('Error in checkFamilyRelationship:', {
                error,
                demoNo1,
                demoNo2
            });
            throw error;
        }
    }

    /**
     * 根据处方ID获取处方
     * @param {number} prescriptionId - 处方ID
     * @returns {Promise<Object>} 处方对象
     */
    static async getById(prescriptionId) {
        try {
            const [rows] = await pool.query(
                `SELECT
                    p.script_no,
                    p.demographic_no,
                    p.provider_no,
                    p.date_prescribed,
                    p.textView,
                    p.rx_comments,
                    pr.first_name AS provider_first_name,
                    pr.last_name AS provider_last_name
                FROM prescription p
                LEFT JOIN provider pr ON p.provider_no = pr.provider_no
                WHERE p.script_no = ?`,
                [prescriptionId]
            );

            if (rows.length === 0) {
                return null;
            }

            // 返回第一个处方结果
            return rows[0];
        } catch (error) {
            logger.error('根据ID获取处方出错:', error);
            throw error;
        }
    }
}

module.exports = PrescriptionDAO; 