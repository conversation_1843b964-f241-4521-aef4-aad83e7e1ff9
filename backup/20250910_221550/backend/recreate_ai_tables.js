const mysql = require('mysql2/promise');

async function recreateAITables() {
    let connection;
    try {
        connection = await mysql.createConnection({
            host: process.env.MAIN_DB_HOST || '**************',
            user: process.env.MAIN_DB_USER || 'root',
            password: process.env.MAIN_DB_PASSWORD || 'Z2Rh6VGr7DE=',
            database: process.env.MAIN_DB_NAME || 'oscar'
        });

        console.log('Connected to database');

        // 1. 删除现有表
        console.log('Dropping existing ai_usage_stats table...');
        await connection.execute('DROP TABLE IF EXISTS ai_usage_stats');
        
        console.log('Dropping existing ai_error_logs table...');
        await connection.execute('DROP TABLE IF EXISTS ai_error_logs');

        // 2. 重新创建 ai_usage_stats 表
        console.log('Creating ai_usage_stats table...');
        const createUsageStatsTable = `
            CREATE TABLE ai_usage_stats (
                id INT AUTO_INCREMENT PRIMARY KEY,
                provider VARCHAR(50) NOT NULL COMMENT 'AI提供商: gemini, openrouter',
                service_type VARCHAR(50) NOT NULL COMMENT '服务类型: chatbot, medical_notes, tips_generation等',
                endpoint VARCHAR(100) COMMENT 'API端点',
                model_name VARCHAR(100) COMMENT '使用的模型名称',
                
                total_calls INT DEFAULT 0 COMMENT '总调用次数',
                successful_calls INT DEFAULT 0 COMMENT '成功调用次数',
                failed_calls INT DEFAULT 0 COMMENT '失败调用次数',
                
                input_tokens INT DEFAULT 0 COMMENT '输入令牌数',
                output_tokens INT DEFAULT 0 COMMENT '输出令牌数',
                total_tokens INT DEFAULT 0 COMMENT '总令牌数',
                
                cache_hits INT DEFAULT 0 COMMENT '缓存命中次数',
                cache_misses INT DEFAULT 0 COMMENT '缓存未命中次数',
                
                avg_response_time_ms DECIMAL(10,2) DEFAULT 0 COMMENT '平均响应时间(毫秒)',
                total_response_time_ms BIGINT DEFAULT 0 COMMENT '总响应时间(毫秒)',
                
                estimated_cost_usd DECIMAL(10,4) DEFAULT 0 COMMENT '预估成本(美元)',
                
                date_recorded DATE NOT NULL COMMENT '记录日期',
                hour_recorded TINYINT NOT NULL DEFAULT 0 COMMENT '记录小时(0-23)',
                
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                INDEX idx_provider_date (provider, date_recorded),
                INDEX idx_service_type_date (service_type, date_recorded),
                INDEX idx_date_hour (date_recorded, hour_recorded),
                INDEX idx_created_at (created_at),
                
                UNIQUE KEY uk_usage_stats (provider, service_type, model_name, date_recorded, hour_recorded)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI使用统计表'
        `;

        await connection.execute(createUsageStatsTable);

        // 3. 重新创建 ai_error_logs 表
        console.log('Creating ai_error_logs table...');
        const createErrorLogsTable = `
            CREATE TABLE ai_error_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                provider VARCHAR(50) NOT NULL COMMENT 'AI提供商',
                service_type VARCHAR(50) NOT NULL COMMENT '服务类型',
                error_type VARCHAR(100) COMMENT '错误类型',
                error_message TEXT COMMENT '错误信息',
                error_code VARCHAR(50) COMMENT '错误代码',
                
                request_summary TEXT COMMENT '请求摘要',
                response_summary TEXT COMMENT '响应摘要',
                
                occurred_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '发生时间',
                
                INDEX idx_provider_occurred (provider, occurred_at),
                INDEX idx_error_type (error_type),
                INDEX idx_occurred_at (occurred_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI错误日志表'
        `;

        await connection.execute(createErrorLogsTable);

        console.log('✅ AI tables recreated successfully with model_name field!');

    } catch (error) {
        console.error('❌ Error recreating AI tables:', error);
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

recreateAITables(); 