console.log('>>> Loading backend/src/services/consultationService.js...'); // <<< DEBUG LOG
const ConsultationDAO = require('../dao/consultationDAO');
const { createLogger } = require('../utils/logger');
const { ValidationError, AuthorizationError } = require('../utils/errors');
const cache = require('../utils/cache');

const logger = createLogger('ConsultationService');

class ConsultationService {
    // Reusing the same access validation logic as prescriptions
    // Adjust this logic if consultation access rules differ
    static async validateAccess(loggedInDemoNo, targetDemoNo) {
        if (!loggedInDemoNo || !targetDemoNo) {
            throw new ValidationError('Invalid demographic numbers provided for access validation');
        }

        if (loggedInDemoNo === targetDemoNo) {
            return true; // User can access their own records
        }

        // Check family relationship for access to others' records
        const hasAccess = await ConsultationDAO.checkFamilyRelationship(loggedInDemoNo, targetDemoNo);
        if (!hasAccess) {
            logger.warn('Authorization attempt failed', { loggedInDemoNo, targetDemoNo });
            throw new AuthorizationError('User does not have permission to access consultations for this patient');
        }

        return true;
    }

    static formatConsultation(consultation) {
        // Basic formatting, enhance as needed for frontend display
        return {
            id: consultation.requestId,
            referralDate: consultation.referalDate,
            appointmentDate: consultation.appointmentDate,
            appointmentTime: consultation.appointmentTime,
            reason: consultation.reason,
            clinicalInfo: consultation.clinicalInfo, // Consider sanitizing or truncating if needed
            status: consultation.status,
            statusText: consultation.statusText,
            urgency: consultation.urgency,
            lastUpdateDate: consultation.lastUpdateDate,
            specialistName: `${consultation.specialistFName || ''} ${consultation.specialistLName || ''}`.trim(),
            specialistType: consultation.specialistType,
            serviceDescription: consultation.serviceDescription,
        };
    }

    static async getPatientConsultations(loggedInUser, targetDemoNo) {
        if (!loggedInUser?.demographic_no) {
            throw new AuthorizationError('User not authenticated or missing demographic number');
        }

        const loggedInDemoNo = loggedInUser.demographic_no;

        try {
            // Validate if the logged-in user can access the target patient's consultations
            await this.validateAccess(loggedInDemoNo, targetDemoNo);

            const cacheKey = `consultations_${targetDemoNo}`;
            const cachedData = await cache.get(cacheKey);

            if (cachedData) {
                logger.info('Returning cached consultations', { targetDemoNo });
                return cachedData;
            }

            logger.info('Fetching consultations from DB', { targetDemoNo });
            const consultations = await ConsultationDAO.getByDemographicNo(targetDemoNo);
            const formattedConsultations = consultations.map(this.formatConsultation);

            // Cache the results (e.g., for 5 minutes)
            await cache.set(cacheKey, formattedConsultations, 300);

            return formattedConsultations;

        } catch (error) {
            // Log the error details before re-throwing or handling
            logger.error('Error in ConsultationService.getPatientConsultations:', {
                errorMessage: error.message,
                stack: error.stack,
                loggedInDemoNo,
                targetDemoNo
            });
            // Re-throw specific known errors or a generic one
            if (error instanceof ValidationError || error instanceof AuthorizationError) {
                throw error;
            }
            // Throw a generic error for unexpected issues
            throw new Error('Failed to retrieve patient consultations due to an internal error');
        }
    }
}

module.exports = ConsultationService; 