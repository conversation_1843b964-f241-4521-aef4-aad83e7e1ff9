const aiUsageTracker = require('./aiUsageTracker');

class AIServiceManager {
    constructor() {
        this.providers = {
            gemini: {
                name: 'gemini',
                baseURL: 'https://generativelanguage.googleapis.com/v1beta',
                apiKey: process.env.GEMINI_API_KEY,
                model: process.env.GEMINI_MODEL || 'gemini-2.0-flash',
                available: true,
                lastError: null,
                errorCount: 0,
                priority: 1, // 1 = 最高优先级
                quotaReset: null
            },
            openrouter: {
                name: 'openrouter',
                baseURL: 'https://openrouter.ai/api/v1',
                apiKey: process.env.OPENROUTER_API_KEY,
                model: process.env.MODEL_NAME || 'deepseek/deepseek-r1-0528:free',
                available: true,
                lastError: null,
                errorCount: 0,
                priority: 2, // 2 = 第二优先级
                quotaReset: null
            }
        };

        // 错误阈值配置
        this.config = {
            maxErrorCount: 3, // 连续错误次数上限
            errorResetTime: 300000, // 5分钟后重置错误计数
            retryDelay: 1000, // 重试延迟
            maxRetries: 2 // 最大重试次数
        };
    }

    /**
     * 获取可用的AI提供商（按优先级排序）
     */
    getAvailableProviders() {
        return Object.values(this.providers)
            .filter(provider => provider.available && provider.apiKey)
            .sort((a, b) => a.priority - b.priority);
    }

    /**
     * 标记提供商为不可用
     */
    markProviderUnavailable(providerName, error) {
        const provider = this.providers[providerName];
        if (provider) {
            provider.errorCount++;
            provider.lastError = error;
            
            if (provider.errorCount >= this.config.maxErrorCount) {
                provider.available = false;
                console.warn(`[AIServiceManager] Provider ${providerName} marked as unavailable after ${provider.errorCount} errors`);
                
                // 设置自动恢复
                setTimeout(() => {
                    provider.available = true;
                    provider.errorCount = 0;
                    provider.lastError = null;
                    console.log(`[AIServiceManager] Provider ${providerName} restored to available`);
                }, this.config.errorResetTime);
            }
        }
    }

    /**
     * 重置提供商状态
     */
    resetProviderStatus(providerName) {
        const provider = this.providers[providerName];
        if (provider) {
            provider.available = true;
            provider.errorCount = 0;
            provider.lastError = null;
        }
    }

    /**
     * 检查API配额错误
     */
    isQuotaError(error, responseText) {
        const quotaIndicators = [
            'quota exceeded',
            'rate limit',
            'too many requests',
            'insufficient quota',
            'billing required',
            '429',
            'quota_exceeded'
        ];
        
        const errorMessage = (error.message || '').toLowerCase();
        const responseTextLower = (responseText || '').toLowerCase();
        
        return quotaIndicators.some(indicator => 
            errorMessage.includes(indicator) || responseTextLower.includes(indicator)
        );
    }

    /**
     * 调用Gemini API
     */
    async callGeminiAPI(messages, options = {}) {
        const provider = this.providers.gemini;
        const startTime = Date.now();
        
        try {
            console.log(`[AIServiceManager] Calling Gemini API with model: ${provider.model}`);
            
            const systemPrompt = options.systemPrompt || '';
            let prompt;
            
            if (Array.isArray(messages)) {
                // 对话格式
                prompt = messages.map(msg => {
                    if (msg.role === 'system') return msg.content;
                    if (msg.role === 'user') return `User: ${msg.content}`;
                    if (msg.role === 'assistant') return `Assistant: ${msg.content}`;
                    return msg.content;
                }).join('\n');
            } else {
                // 单个提示
                prompt = messages;
            }
            
            if (systemPrompt) {
                prompt = systemPrompt + '\n\n' + prompt;
            }

            const response = await fetch(
                `${provider.baseURL}/models/${provider.model}:generateContent?key=${provider.apiKey}`,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{ text: prompt }]
                        }],
                        generationConfig: {
                            temperature: options.temperature || 0.7,
                            maxOutputTokens: options.maxTokens || 2048
                        }
                    }),
                    timeout: options.timeout || 30000
                }
            );

            const responseTime = Date.now() - startTime;
            const responseText = await response.text();

            if (!response.ok) {
                throw new Error(`Gemini API error: ${response.status} - ${responseText}`);
            }

            const data = JSON.parse(responseText);
            
            if (data.candidates && data.candidates[0]?.content?.parts?.[0]?.text) {
                const result = {
                    content: data.candidates[0].content.parts[0].text,
                    provider: 'gemini',
                    model: provider.model,
                    responseTime
                };

                // 记录成功的使用统计
                await aiUsageTracker.recordUsage({
                    provider: 'gemini',
                    serviceType: options.serviceType || 'ai_service',
                    endpoint: `${provider.baseURL}/models/${provider.model}:generateContent`,
                    modelName: provider.model,
                    success: true,
                    responseTimeMs: responseTime,
                    cacheHit: false,
                    estimatedCostUsd: 0
                });

                // 重置错误计数
                this.resetProviderStatus('gemini');
                
                return result;
            } else {
                throw new Error('Invalid response format from Gemini API');
            }

        } catch (error) {
            const responseTime = Date.now() - startTime;
            console.error(`[AIServiceManager] Gemini API error:`, error.message);

            // 记录失败的使用统计
            await aiUsageTracker.recordUsage({
                provider: 'gemini',
                serviceType: options.serviceType || 'ai_service',
                endpoint: `${provider.baseURL}/models/${provider.model}:generateContent`,
                modelName: provider.model,
                success: false,
                responseTimeMs: responseTime,
                cacheHit: false
            });

            // 记录错误详情
            await aiUsageTracker.recordError({
                provider: 'gemini',
                serviceType: options.serviceType || 'ai_service',
                errorType: 'API_ERROR',
                errorMessage: error.message,
                errorCode: error.status?.toString(),
                requestSummary: `Model: ${provider.model}, Messages: ${Array.isArray(messages) ? messages.length : 1}`,
                responseSummary: error.stack
            });

            // 检查是否是配额错误
            if (this.isQuotaError(error, error.message)) {
                this.markProviderUnavailable('gemini', error);
            }

            throw error;
        }
    }

    /**
     * 调用OpenRouter API
     */
    async callOpenRouterAPI(messages, options = {}) {
        const provider = this.providers.openrouter;
        const startTime = Date.now();
        
        try {
            console.log(`[AIServiceManager] Calling OpenRouter API with model: ${provider.model}`);
            
            let formattedMessages;
            if (Array.isArray(messages)) {
                formattedMessages = messages;
            } else {
                formattedMessages = [{ role: 'user', content: messages }];
            }
            
            if (options.systemPrompt) {
                formattedMessages.unshift({ role: 'system', content: options.systemPrompt });
            }

            const response = await fetch(`${provider.baseURL}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${provider.apiKey}`,
                    'Content-Type': 'application/json',
                    'HTTP-Referer': process.env.SITE_URL || 'http://localhost:3000',
                    'X-Title': process.env.SITE_NAME || 'MMC Wellness AI'
                },
                body: JSON.stringify({
                    model: provider.model,
                    messages: formattedMessages,
                    temperature: options.temperature || 0.7,
                    max_tokens: options.maxTokens || 2048
                }),
                timeout: options.timeout || 30000
            });

            const responseTime = Date.now() - startTime;
            const responseText = await response.text();

            if (!response.ok) {
                throw new Error(`OpenRouter API error: ${response.status} - ${responseText}`);
            }

            const data = JSON.parse(responseText);
            
            if (data.choices && data.choices[0]?.message?.content) {
                const result = {
                    content: data.choices[0].message.content,
                    provider: 'openrouter',
                    model: provider.model,
                    responseTime,
                    usage: data.usage
                };

                // 记录成功的使用统计
                await aiUsageTracker.recordUsage({
                    provider: 'openrouter',
                    serviceType: options.serviceType || 'ai_service',
                    endpoint: `${provider.baseURL}/chat/completions`,
                    modelName: provider.model,
                    success: true,
                    responseTimeMs: responseTime,
                    cacheHit: false,
                    estimatedCostUsd: 0
                });

                // 重置错误计数
                this.resetProviderStatus('openrouter');
                
                return result;
            } else {
                throw new Error('Invalid response format from OpenRouter API');
            }

        } catch (error) {
            const responseTime = Date.now() - startTime;
            console.error(`[AIServiceManager] OpenRouter API error:`, error.message);

            // 记录失败的使用统计
            await aiUsageTracker.recordUsage({
                provider: 'openrouter',
                serviceType: options.serviceType || 'ai_service',
                endpoint: `${provider.baseURL}/chat/completions`,
                modelName: provider.model,
                success: false,
                responseTimeMs: responseTime,
                cacheHit: false
            });

            // 记录错误详情
            await aiUsageTracker.recordError({
                provider: 'openrouter',
                serviceType: options.serviceType || 'ai_service',
                errorType: 'API_ERROR',
                errorMessage: error.message,
                errorCode: error.status?.toString(),
                requestSummary: `Model: ${provider.model}, Messages: ${Array.isArray(messages) ? messages.length : 1}`,
                responseSummary: error.stack
            });

            // 检查是否是配额错误
            if (this.isQuotaError(error, responseText)) {
                this.markProviderUnavailable('openrouter', error);
            }

            throw error;
        }
    }

    /**
     * 智能AI调用 - 自动选择最佳提供商并支持故障转移
     */
    async smartCall(messages, options = {}) {
        const availableProviders = this.getAvailableProviders();
        
        if (availableProviders.length === 0) {
            throw new Error('No AI providers available');
        }

        let lastError;
        
        for (const provider of availableProviders) {
            try {
                console.log(`[AIServiceManager] Trying provider: ${provider.name}`);
                
                if (provider.name === 'gemini') {
                    return await this.callGeminiAPI(messages, options);
                } else if (provider.name === 'openrouter') {
                    return await this.callOpenRouterAPI(messages, options);
                }
                
            } catch (error) {
                lastError = error;
                console.warn(`[AIServiceManager] Provider ${provider.name} failed:`, error.message);
                
                // 如果是配额错误，立即尝试下一个提供商
                if (this.isQuotaError(error, error.message)) {
                    console.log(`[AIServiceManager] Quota exceeded for ${provider.name}, trying next provider`);
                    continue;
                }
                
                // 其他错误也尝试下一个提供商
                continue;
            }
        }
        
        // 所有提供商都失败了
        throw new Error(`All AI providers failed. Last error: ${lastError?.message}`);
    }

    /**
     * 获取提供商状态
     */
    getProvidersStatus() {
        return Object.entries(this.providers).map(([name, provider]) => ({
            name,
            available: provider.available,
            errorCount: provider.errorCount,
            lastError: provider.lastError?.message,
            model: provider.model,
            priority: provider.priority
        }));
    }

    /**
     * 手动设置提供商可用性
     */
    setProviderAvailability(providerName, available) {
        const provider = this.providers[providerName];
        if (provider) {
            provider.available = available;
            if (available) {
                provider.errorCount = 0;
                provider.lastError = null;
            }
            console.log(`[AIServiceManager] Provider ${providerName} manually set to ${available ? 'available' : 'unavailable'}`);
        }
    }
}

// 导出单例实例
const aiServiceManager = new AIServiceManager();
module.exports = aiServiceManager; 