console.log('>>> demographicRoutes.js: Router loaded and being used by a request.'); // <<< DEBUG LOG
const express = require('express');
const demographicController = require('../controllers/demographicController');
const router = express.Router();

// Add debug middleware to catch all requests to this router
router.use((req, res, next) => {
    console.log('>>> demographicRoutes: Request received:', req.method, req.url, req.params);
    next();
});

// Placeholder for authentication middleware
// const { ensureAuthenticated } = require('../middleware/authMiddleware'); // Adjust path as needed

// Route to get demographic details - MUST come before avatar routes to avoid conflict
// Assuming ensureAuthenticated or similar middleware would be used
router.get(
    '/details/:demographic_no',
    // ensureAuthenticated,
    (req, res, next) => {
        console.log('>>> demographicRoutes: Details route hit with demographic_no:', req.params.demographic_no);
        next();
    },
    demographicController.getDemographicDetails
);

// Route to upload/update an avatar for a specific demographic record
// Assuming ensureAuthenticated or similar middleware would be used to protect this route
router.post(
    '/:demographic_no/avatar',
    // ensureAuthenticated, // Uncomment and use your actual auth middleware
    demographicController.uploadAvatar
);

// Route to get a pre-signed URL for an avatar
// May or may not require authentication depending on your app's privacy rules for avatars
router.get(
    '/:demographic_no/avatar',
    // ensureAuthenticated, // Or a different middleware for public/semi-public access
    demographicController.getAvatar
);

// Route to delete an avatar
// Assuming ensureAuthenticated or similar middleware would be used
router.delete(
    '/:demographic_no/avatar',
    // ensureAuthenticated,
    demographicController.deleteAvatar
);

module.exports = router; 