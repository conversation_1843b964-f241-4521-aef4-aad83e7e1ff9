#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # 无颜色

echo -e "${GREEN}开始设置每日健康小贴士生成系统...${NC}"

# 确保当前目录是scripts
cd "$(dirname "$0")"

# 安装必要的npm包
echo -e "${GREEN}安装必要的依赖...${NC}"
cd ../../
npm install axios dotenv

# 创建日志目录
echo -e "${GREEN}创建日志目录...${NC}"
mkdir -p ../logs

# 确保脚本有执行权限
echo -e "${GREEN}设置脚本执行权限...${NC}"
chmod +x src/scripts/generateDailyTip.js
chmod +x src/scripts/setupCronJob.js

# 运行一次生成脚本，测试是否正常工作
echo -e "${GREEN}测试生成一个健康小贴士...${NC}"
node src/scripts/generateDailyTip.js

# 如果成功，设置cron任务
if [ $? -eq 0 ]; then
  echo -e "${GREEN}测试成功，现在设置自动化定时任务...${NC}"
  node src/scripts/setupCronJob.js
else
  echo -e "${RED}测试失败，请检查错误并修复后再尝试设置定时任务${NC}"
  exit 1
fi

echo -e "${GREEN}设置完成！每日健康小贴士将在每天早上6点自动生成${NC}"
echo -e "${GREEN}生成的小贴士将保存在documents目录中${NC}"
echo -e "${GREEN}日志文件将保存在logs/daily-tip.log${NC}" 