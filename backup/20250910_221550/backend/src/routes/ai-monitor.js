const express = require('express');
const router = express.Router();

// Lazy load the AI service to handle initialization errors
let aiService = null;
let aiServiceError = null;

function getAIService() {
    if (aiService) return aiService;
    if (aiServiceError) throw aiServiceError;
    
    try {
        aiService = require('../utils/aiService');
        return aiService;
    } catch (error) {
        aiServiceError = error;
        throw error;
    }
}

/**
 * AI服务监控端点
 */
router.get('/metrics', (req, res) => {
    try {
        const ai = getAIService();
        const metrics = ai.getMetrics();
        
        res.json({
            success: true,
            timestamp: new Date().toISOString(),
            metrics: {
                ...metrics,
                uptime: process.uptime(),
                memoryUsage: process.memoryUsage(),
                nodeVersion: process.version
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message,
            message: 'AI service not available'
        });
    }
});

/**
 * AI服务健康检查
 */
router.get('/health', async (req, res) => {
    try {
        const ai = getAIService();
        
        // 测试简单的AI调用
        const testResult = await ai.generateContent('Hello, this is a health check test.', {
            service: 'default',
            config: { maxOutputTokens: 50 },
            context: 'healthCheck'
        });

        res.json({
            success: true,
            timestamp: new Date().toISOString(),
            status: testResult.success ? 'healthy' : 'degraded',
            details: {
                aiServiceStatus: testResult.success ? 'OK' : 'ERROR',
                response: testResult.success ? 'AI responded normally' : testResult.error,
                metrics: ai.getMetrics()
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            status: 'unhealthy',
            error: error.message,
            message: 'AI service initialization failed'
        });
    }
});

/**
 * 清理缓存端点
 */
router.post('/cache/cleanup', (req, res) => {
    try {
        const ai = getAIService();
        ai.cleanupCache();
        const metrics = ai.getMetrics();
        
        res.json({
            success: true,
            message: 'Cache cleanup completed',
            currentCacheSize: metrics.cacheSize
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message,
            message: 'AI service not available for cache cleanup'
        });
    }
});

/**
 * 重置指标端点
 */
router.post('/metrics/reset', (req, res) => {
    try {
        // 重置指标（如果AI服务支持的话）
        res.json({
            success: true,
            message: 'Metrics reset functionality would be implemented here',
            note: 'Current implementation preserves metrics for historical analysis'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

module.exports = router; 