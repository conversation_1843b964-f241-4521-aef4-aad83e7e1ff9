const pool = require('../config/database');
const { createLogger } = require('../utils/logger');

const logger = createLogger('PrescriptionExplanationDAO');

class PrescriptionExplanationDAO {
    /**
     * 根据处方ID获取处方解释
     * @param {number} scriptNo - 处方ID
     * @param {string} language - 语言 ('zh' 或 'en')
     * @returns {Promise<Object>} 处方解释对象
     */
    static async getByScriptNo(scriptNo, language = 'zh') {
        const query = `
            SELECT id, script_no, explanation, language, created_at, updated_at
            FROM prescription_explanations
            WHERE script_no = ? AND language = ?
            LIMIT 1;
        `;

        try {
            const startTime = Date.now();
            const [results] = await pool.query(query, [scriptNo, language]);
            const queryTime = Date.now() - startTime;

            if (queryTime > 1000) {
                logger.warn(`Slow query detected: ${queryTime}ms`, {
                    operation: 'getByScriptNo',
                    scriptNo,
                    language
                });
            }

            return results.length > 0 ? results[0] : null;
        } catch (error) {
            logger.error('Error in getByScriptNo:', {
                error,
                scriptNo,
                language
            });
            throw error;
        }
    }

    /**
     * 创建或更新处方解释
     * @param {number} scriptNo - 处方ID
     * @param {string} explanation - AI生成的解释文本
     * @param {string} language - 语言 ('zh' 或 'en')
     * @returns {Promise<Object>} 处理结果
     */
    static async createOrUpdate(scriptNo, explanation, language = 'zh') {
        const query = `
            INSERT INTO prescription_explanations (script_no, explanation, language)
            VALUES (?, ?, ?)
            ON DUPLICATE KEY UPDATE
                explanation = VALUES(explanation),
                updated_at = CURRENT_TIMESTAMP;
        `;

        try {
            const startTime = Date.now();
            const [result] = await pool.query(query, [scriptNo, explanation, language]);
            const queryTime = Date.now() - startTime;

            if (queryTime > 1000) {
                logger.warn(`Slow query detected: ${queryTime}ms`, {
                    operation: 'createOrUpdate',
                    scriptNo,
                    language
                });
            }

            return {
                success: true,
                id: result.insertId || null,
                affectedRows: result.affectedRows
            };
        } catch (error) {
            logger.error('Error in createOrUpdate:', {
                error,
                scriptNo,
                language
            });
            throw error;
        }
    }

    /**
     * 删除处方解释
     * @param {number} scriptNo - 处方ID
     * @param {string} language - 语言 ('zh' 或 'en')
     * @returns {Promise<Object>} 处理结果
     */
    static async delete(scriptNo, language = 'zh') {
        const query = `
            DELETE FROM prescription_explanations
            WHERE script_no = ? AND language = ?;
        `;

        try {
            const [result] = await pool.query(query, [scriptNo, language]);
            return {
                success: true,
                affectedRows: result.affectedRows
            };
        } catch (error) {
            logger.error('Error in delete:', {
                error,
                scriptNo,
                language
            });
            throw error;
        }
    }
}

module.exports = PrescriptionExplanationDAO; 