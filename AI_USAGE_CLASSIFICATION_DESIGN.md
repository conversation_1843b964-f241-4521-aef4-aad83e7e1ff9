# AI 使用统计分类设计方案

## 🎯 目标
细化AI使用类型分类，提供更有意义的统计分析和监控功能。

## 📊 详细分类设计

### 1. 主要服务类型 (service_category)
```javascript
const SERVICE_CATEGORIES = {
    // 用户交互类
    USER_INTERACTION: {
        name: '用户交互',
        types: ['chatbot', 'consultation_assistant', 'symptom_checker']
    },
    
    // 内容生成类
    CONTENT_GENERATION: {
        name: '内容生成',
        types: ['health_tips', 'health_news', 'medical_articles', 'patient_education']
    },
    
    // 医疗辅助类
    MEDICAL_ASSISTANCE: {
        name: '医疗辅助',
        types: ['medical_transcription', 'diagnosis_support', 'treatment_recommendation']
    },
    
    // 系统管理类
    SYSTEM_MANAGEMENT: {
        name: '系统管理',
        types: ['content_moderation', 'data_analysis', 'report_generation']
    }
};
```

### 2. 具体服务类型 (service_type)
```javascript
const DETAILED_SERVICE_TYPES = {
    // 聊天机器人相关
    'chatbot_general': '通用健康咨询',
    'chatbot_mmc_info': 'MMC服务介绍',
    'chatbot_appointment': '预约相关咨询',
    'chatbot_health_tips': '健康建议提供',
    
    // 内容生成相关
    'tips_daily_generation': '每日健康贴士生成',
    'tips_seasonal': '季节性健康提醒',
    'news_health_crawler': '健康新闻抓取',
    'news_content_summary': '新闻内容总结',
    'article_generation': '医疗文章生成',
    
    // 医疗辅助相关
    'medical_note_generation': '医疗记录生成',
    'diagnosis_assistance': '诊断辅助',
    'prescription_review': '处方审核',
    'symptom_analysis': '症状分析',
    
    // 系统功能相关
    'content_translation': '内容翻译',
    'data_extraction': '数据提取',
    'report_analytics': '报告分析',
    'quality_check': '质量检查'
};
```

### 3. 使用场景 (use_case)
```javascript
const USE_CASES = {
    'patient_inquiry': '患者咨询',
    'staff_assistance': '员工辅助',
    'content_creation': '内容创作',
    'automated_task': '自动化任务',
    'quality_assurance': '质量保证',
    'data_processing': '数据处理'
};
```

### 4. 用户类型 (user_type)
```javascript
const USER_TYPES = {
    'patient': '患者',
    'doctor': '医生',
    'nurse': '护士',
    'admin': '管理员',
    'system': '系统自动',
    'anonymous': '匿名用户'
};
```

## 🗄️ 数据库表结构优化

### ai_usage_stats 表增强
```sql
-- 添加新的分类字段
ALTER TABLE ai_usage_stats 
ADD COLUMN service_category VARCHAR(50) COMMENT '服务大类',
ADD COLUMN use_case VARCHAR(50) COMMENT '使用场景',
ADD COLUMN user_type VARCHAR(30) COMMENT '用户类型',
ADD COLUMN feature_used VARCHAR(100) COMMENT '具体功能',
ADD COLUMN session_id VARCHAR(100) COMMENT '会话ID',
ADD COLUMN user_id VARCHAR(50) COMMENT '用户ID（匿名化）',
ADD COLUMN content_type VARCHAR(50) COMMENT '内容类型',
ADD COLUMN language VARCHAR(10) DEFAULT 'zh' COMMENT '语言',
ADD COLUMN priority_level ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal' COMMENT '优先级';

-- 添加新的索引
ALTER TABLE ai_usage_stats 
ADD INDEX idx_service_category (service_category, date_recorded),
ADD INDEX idx_use_case (use_case, date_recorded),
ADD INDEX idx_user_type (user_type, date_recorded);
```

## 📈 统计维度扩展

### 1. 按服务大类统计
- 用户交互类使用频率
- 内容生成类成功率
- 医疗辅助类响应时间
- 系统管理类错误率

### 2. 按使用场景统计
- 患者咨询高峰时段
- 员工辅助使用模式
- 自动化任务执行效率

### 3. 按用户类型统计
- 不同用户群体的AI使用偏好
- 用户满意度指标
- 功能使用分布

### 4. 按功能特性统计
- 最受欢迎的AI功能
- 功能使用时长分析
- 功能组合使用模式

## 🎨 仪表板展示设计

### 1. 概览卡片
- 总调用次数
- 活跃用户数
- 平均响应时间
- 成功率

### 2. 服务分类饼图
- 按服务大类分布
- 按具体服务类型分布
- 按使用场景分布

### 3. 趋势线图
- 按小时/天/周的使用趋势
- 不同服务类型的增长趋势
- 响应时间趋势

### 4. 热力图
- 使用高峰时段
- 功能受欢迎程度
- 地理分布（如适用）

### 5. 详细分析表
- TOP功能排行
- 错误频率统计
- 性能指标对比

## 🔧 实施优先级

### Phase 1: 基础分类（本次实施）
1. 完善service_type分类
2. 添加service_category字段
3. 修复前端显示问题
4. 基础图表优化

### Phase 2: 增强分析
1. 添加use_case和user_type分类
2. 实施会话跟踪
3. 高级图表和分析

### Phase 3: 智能分析
1. AI使用模式识别
2. 预测性分析
3. 自动优化建议 