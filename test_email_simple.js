const nodemailer = require('nodemailer');

async function testEmailSending() {
    console.log('🧪 Testing Simple Email Sending...\n');

    try {
        // Email configuration (same as in the backend)
        const transporter = nodemailer.createTransporter({
            host: 'smtp.gmail.com',
            port: 587,
            secure: false,
            auth: {
                user: '<EMAIL>',
                pass: 'cmba qinv nrll rqqp',
            },
        });

        console.log('📧 Testing transporter configuration...');
        
        // Verify transporter
        await transporter.verify();
        console.log('✅ Transporter configuration is valid');

        // Test email
        const mailOptions = {
            from: 'MMC Wellness <<EMAIL>>',
            to: '<EMAIL>', // Send to ourselves for testing
            subject: 'Test Email - MMC Wellness System',
            html: `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <h2 style="color: #2c5aa0;">Test Email</h2>
                    <p>This is a test email to verify the email functionality is working correctly.</p>
                    <p>Timestamp: ${new Date().toISOString()}</p>
                </div>
            `,
        };

        console.log('📤 Sending test email...');
        const info = await transporter.sendMail(mailOptions);
        console.log('✅ Test email sent successfully!');
        console.log(`   Message ID: ${info.messageId}`);
        console.log(`   To: ${mailOptions.to}`);

    } catch (error) {
        console.error('❌ Email test failed:', error.message);
        console.error('   Full error:', error);
    }
}

// Run the test
testEmailSending();
