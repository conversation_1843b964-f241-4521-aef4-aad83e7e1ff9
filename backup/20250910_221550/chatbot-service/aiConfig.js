module.exports = {
    gemini: {
        apiKey: process.env.GEMINI_API_KEY,
        model: process.env.GEMINI_MODEL || 'gemini-2.0-flash',
        baseURL: 'https://generativelanguage.googleapis.com/v1beta',
        timeout: parseInt(process.env.AI_REQUEST_TIMEOUT) || 30000,
        
        // 默认生成配置
        defaultConfig: {
            temperature: 0.1,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 800
        },
        
        // 重试配置
        retryConfig: {
            maxRetries: 3,
            retryDelay: 1000,
            backoffMultiplier: 2,
            retryableErrors: ['ECONNRESET', 'ETIMEDOUT', 'EAI_AGAIN', 'ENOTFOUND']
        },
        
        // 不同服务的特定配置
        serviceConfigs: {
            medicalNotes: {
                maxOutputTokens: 800,
                temperature: 0.1
            },
            prescription: {
                maxOutputTokens: 600,
                temperature: 0.1
            },
            immunization: {
                maxOutputTokens: 800,
                temperature: 0.2
            },
            dietician: {
                maxOutputTokens: 800,
                temperature: 0.1
            },
            chatbot: {
                maxOutputTokens: 1000,
                temperature: 0.3
            },
            videoEvaluation: {
                maxOutputTokens: 10,
                temperature: 0.2
            },
            tipGeneration: {
                maxOutputTokens: 1200,
                temperature: 0.4
            }
        }
    },
    
    // OpenRouter配置（用于健康小贴士生成的备用）
    openRouter: {
        apiKey: process.env.OPENROUTER_API_KEY,
        baseURL: 'https://openrouter.ai/api/v1',
        model: process.env.MODEL_NAME || 'deepseek/deepseek-r1-0528:free'
    },
    
    // 缓存配置
    cache: {
        enabled: process.env.AI_CACHE_ENABLED !== 'false',
        ttl: {
            medicalNotes: 3600,      // 1小时
            prescription: 7200,      // 2小时
            immunization: 7200,      // 2小时
            dietician: 1800,         // 30分钟
            videoEvaluation: 86400,  // 24小时
            tipGeneration: 43200     // 12小时
        }
    },
    
    // 监控配置
    monitoring: {
        enabled: process.env.AI_MONITORING_ENABLED !== 'false',
        logLevel: process.env.AI_LOG_LEVEL || 'info'
    }
}; 