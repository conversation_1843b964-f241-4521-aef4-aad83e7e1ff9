#!/bin/bash

# PDF文档同步脚本 - 增强版
# 从远程Oscar服务器同步文档到本地

set -e  # 遇到错误时退出

# 配置变量
REMOTE_USER=mmc
REMOTE_HOST=**************
REMOTE_DIR=/home/<USER>/open-osp/volumes/OscarDocument/oscar
LOCAL_DIR=/home/<USER>/apps/mmcwebapp/local-documents/oscar

# 日志配置
LOG_DIR="/home/<USER>/apps/mmcwebapp/logs"
LOG_FILE="$LOG_DIR/sync-pdfs.log"
MAX_LOG_SIZE=10485760  # 10MB
MAX_LOG_FILES=5

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 确保日志目录存在
mkdir -p "$LOG_DIR"

# 日志函数
log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local log_entry="[$timestamp] [$level] $message"
    
    # 写入日志文件
    echo "$log_entry" >> "$LOG_FILE"
    
    # 根据级别显示彩色输出
    case $level in
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
        "SUCCESS")
            echo -e "${GREEN}[SUCCESS]${NC} $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        "DEBUG")
            echo -e "${PURPLE}[DEBUG]${NC} $message"
            ;;
        *)
            echo "$message"
            ;;
    esac
}

# 错误处理函数
handle_error() {
    local exit_code=$1
    log_message "ERROR" "脚本执行失败，退出码: $exit_code"
    log_message "ERROR" "同步操作未完成"
    exit $exit_code
}

# 日志轮转函数
rotate_logs() {
    if [[ -f "$LOG_FILE" ]] && [[ $(stat -f%z "$LOG_FILE" 2>/dev/null || stat -c%s "$LOG_FILE" 2>/dev/null) -gt $MAX_LOG_SIZE ]]; then
        log_message "INFO" "日志文件过大，开始轮转..."
        
        # 删除最旧的日志文件
        for i in $(seq $((MAX_LOG_FILES-1)) -1 1); do
            if [[ -f "${LOG_FILE}.$i" ]]; then
                mv "${LOG_FILE}.$i" "${LOG_FILE}.$((i+1))"
            fi
        done
        
        # 移动当前日志文件
        mv "$LOG_FILE" "${LOG_FILE}.1"
        
        log_message "INFO" "日志轮转完成"
    fi
}

# 检查连接函数
check_connection() {
    log_message "INFO" "检查与远程服务器的连接..."
    
    if ! ping -c 1 "$REMOTE_HOST" >/dev/null 2>&1; then
        log_message "ERROR" "无法ping通远程主机: $REMOTE_HOST"
        return 1
    fi
    
    if ! ssh -o ConnectTimeout=10 -o BatchMode=yes "$REMOTE_USER@$REMOTE_HOST" "exit" 2>/dev/null; then
        log_message "ERROR" "SSH连接失败: $REMOTE_USER@$REMOTE_HOST"
        log_message "INFO" "请检查SSH密钥配置或网络连接"
        return 1
    fi
    
    log_message "SUCCESS" "远程服务器连接正常"
    return 0
}

# 检查远程目录函数
check_remote_directory() {
    log_message "INFO" "检查远程目录: $REMOTE_DIR"
    
    if ! ssh "$REMOTE_USER@$REMOTE_HOST" "test -d '$REMOTE_DIR'" 2>/dev/null; then
        log_message "ERROR" "远程目录不存在: $REMOTE_DIR"
        return 1
    fi
    
    # 获取远程目录统计信息
    local remote_stats=$(ssh "$REMOTE_USER@$REMOTE_HOST" "find '$REMOTE_DIR' -type f | wc -l" 2>/dev/null)
    log_message "INFO" "远程目录文件数量: $remote_stats"
    
    return 0
}

# 同步前统计函数
pre_sync_stats() {
    log_message "INFO" "=== 同步前统计 ==="
    
    # 本地统计
    if [[ -d "$LOCAL_DIR" ]]; then
        local local_files=$(find "$LOCAL_DIR" -type f 2>/dev/null | wc -l)
        local local_size=$(du -sh "$LOCAL_DIR" 2>/dev/null | cut -f1)
        log_message "INFO" "本地文件数量: $local_files"
        log_message "INFO" "本地目录大小: $local_size"
    else
        log_message "INFO" "本地目录不存在，将创建: $LOCAL_DIR"
        mkdir -p "$LOCAL_DIR"
    fi
}

# 执行同步函数
perform_sync() {
    log_message "INFO" "=== 开始同步操作 ==="
    log_message "INFO" "源: $REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR/"
    log_message "INFO" "目标: $LOCAL_DIR/"
    
    # 创建临时文件记录rsync输出
    local rsync_log=$(mktemp)
    local rsync_error=$(mktemp)
    
    # 执行rsync同步
    if rsync -avz \
        --progress \
        --stats \
        --delete \
        --exclude='*.tmp' \
        --exclude='.*' \
        "$REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR/" \
        "$LOCAL_DIR/" \
        >"$rsync_log" 2>"$rsync_error"; then
        
        log_message "SUCCESS" "文件同步完成"
        
        # 解析rsync统计信息
        if [[ -s "$rsync_log" ]]; then
            local total_files=$(grep "Number of files:" "$rsync_log" | awk '{print $4}' | tr -d ',')
            local transferred=$(grep "Number of regular files transferred:" "$rsync_log" | awk '{print $6}' | tr -d ',')
            local total_size=$(grep "Total file size:" "$rsync_log" | awk '{print $4}')
            
            [[ -n "$total_files" ]] && log_message "INFO" "处理文件总数: $total_files"
            [[ -n "$transferred" ]] && log_message "INFO" "传输文件数量: $transferred"
            [[ -n "$total_size" ]] && log_message "INFO" "总文件大小: $total_size bytes"
        fi
        
    else
        local exit_code=$?
        log_message "ERROR" "rsync同步失败，退出码: $exit_code"
        
        # 记录错误详情
        if [[ -s "$rsync_error" ]]; then
            while IFS= read -r line; do
                log_message "ERROR" "rsync错误: $line"
            done < "$rsync_error"
        fi
        
        # 清理临时文件
        rm -f "$rsync_log" "$rsync_error"
        return $exit_code
    fi
    
    # 清理临时文件
    rm -f "$rsync_log" "$rsync_error"
    return 0
}

# 同步后统计函数
post_sync_stats() {
    log_message "INFO" "=== 同步后统计 ==="
    
    if [[ -d "$LOCAL_DIR" ]]; then
        local local_files=$(find "$LOCAL_DIR" -type f 2>/dev/null | wc -l)
        local local_size=$(du -sh "$LOCAL_DIR" 2>/dev/null | cut -f1)
        log_message "INFO" "本地文件数量: $local_files"
        log_message "INFO" "本地目录大小: $local_size"
    fi
}

# 权限修正函数
fix_permissions() {
    log_message "INFO" "=== 修正文件权限 ==="
    
    # 修正文件权限
    find "$LOCAL_DIR" -type f -exec chmod 644 {} \; 2>/dev/null || {
        log_message "WARNING" "部分文件权限修正失败"
    }
    
    # 修正目录权限
    find "$LOCAL_DIR" -type d -exec chmod 755 {} \; 2>/dev/null || {
        log_message "WARNING" "部分目录权限修正失败"
    }
    
    log_message "SUCCESS" "权限修正完成"
}

# 显示帮助信息
show_help() {
    echo "PDF文档同步脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -v, --verbose  详细输出模式"
    echo "  --dry-run      模拟运行，不实际同步"
    echo "  --no-delete    不删除本地多余文件"
    echo "  --stats-only   仅显示统计信息，不执行同步"
    echo ""
    echo "示例:"
    echo "  $0              # 正常同步"
    echo "  $0 --dry-run    # 模拟同步"
    echo "  $0 --stats-only # 仅统计"
}

# 解析命令行参数
DRY_RUN=false
NO_DELETE=false
STATS_ONLY=false
VERBOSE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --no-delete)
            NO_DELETE=true
            shift
            ;;
        --stats-only)
            STATS_ONLY=true
            shift
            ;;
        *)
            log_message "ERROR" "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 错误处理
trap 'handle_error $?' ERR

# 主执行流程
main() {
    # 开始日志
    log_message "INFO" "========================================="
    log_message "INFO" "PDF文档同步开始"
    log_message "INFO" "时间: $(date '+%Y-%m-%d %H:%M:%S')"
    log_message "INFO" "PID: $$"
    [[ "$DRY_RUN" == "true" ]] && log_message "INFO" "模式: 模拟运行"
    [[ "$STATS_ONLY" == "true" ]] && log_message "INFO" "模式: 仅统计"
    log_message "INFO" "========================================="
    
    # 轮转日志
    rotate_logs
    
    # 检查连接
    if ! check_connection; then
        handle_error 1
    fi
    
    # 检查远程目录
    if ! check_remote_directory; then
        handle_error 1
    fi
    
    # 同步前统计
    pre_sync_stats
    
    # 如果只是统计模式，跳过同步
    if [[ "$STATS_ONLY" == "true" ]]; then
        log_message "INFO" "仅统计模式，跳过同步操作"
    else
        # 执行同步
        if [[ "$DRY_RUN" == "true" ]]; then
            log_message "INFO" "模拟运行模式，实际不会传输文件"
            # 这里可以添加 rsync --dry-run 的逻辑
        else
            if ! perform_sync; then
                handle_error 1
            fi
            
            # 修正权限
            fix_permissions
            
            # 同步后统计
            post_sync_stats
        fi
    fi
    
    # 结束日志
    log_message "SUCCESS" "========================================="
    log_message "SUCCESS" "PDF文档同步完成"
    log_message "SUCCESS" "结束时间: $(date '+%Y-%m-%d %H:%M:%S')"
    log_message "SUCCESS" "========================================="
}

# 执行主函数
main "$@"

