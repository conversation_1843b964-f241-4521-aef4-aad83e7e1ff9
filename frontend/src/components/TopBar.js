import React, { useState, useContext, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
    AppBar,
    Toolbar,
    Typography,
    IconButton,
    Box,
    Avatar,
    Menu,
    MenuItem,
    ListItemIcon,
    Divider,
    Tooltip,
    Chip
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import PersonIcon from '@mui/icons-material/Person';
import SettingsIcon from '@mui/icons-material/Settings';
import LogoutIcon from '@mui/icons-material/Logout';
import { useView } from '../context/ViewContext';
import { useLanguage } from '../context/LanguageContext';
import { useUserType } from '../hooks/useUserType';
import { getAvatarUrl } from '../services/demographicService';

const TopBar = ({ handleDrawerToggle, drawerWidth }) => {
    const { loggedInUser } = useView();
    const { t } = useLanguage();
    const navigate = useNavigate();
    const [anchorElUser, setAnchorElUser] = useState(null);
    const [avatarUrl, setAvatarUrl] = useState(null);
    const { userType, isAdmin, isMember } = useUserType();

    // Determine display role based on user type
    let userRole = 'User';
    let chipColor = 'default';
    
    if (isAdmin) {
        userRole = 'Admin';
        chipColor = 'secondary';
    } else if (isMember) {
        userRole = 'Member';
        chipColor = 'primary';
    }

    const user = loggedInUser;

    // Fetch avatar URL when user changes
    useEffect(() => {
        const fetchAvatar = async () => {
            if (user?.demographic_no) {
                try {
                    const url = await getAvatarUrl(user.demographic_no);
                    setAvatarUrl(url);
                } catch (error) {
                    console.error('Error fetching avatar:', error);
                    setAvatarUrl(null);
                }
            }
        };

        fetchAvatar();
    }, [user?.demographic_no]);

    const handleOpenUserMenu = (event) => {
        setAnchorElUser(event.currentTarget);
    };

    const handleCloseUserMenu = () => {
        setAnchorElUser(null);
    };

    const handleLogout = () => {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        navigate('/login', { replace: true });
    };

    const handleMenuClick = (action) => {
        handleCloseUserMenu();
        if (action === 'logout') {
            handleLogout();
        } else if (action === 'profile') {
            navigate('/profile');
        } else if (action === 'settings') {
            navigate('/settings');
        }
    };

    return (
        <AppBar
            position="fixed"
            sx={{
                width: { sm: `calc(100% - ${drawerWidth}px)` },
                ml: { sm: `${drawerWidth}px` },
                zIndex: (theme) => theme.zIndex.drawer + 1,
            }}
            elevation={1}
        >
            <Toolbar>
                <IconButton
                    color="inherit"
                    aria-label="open drawer"
                    edge="start"
                    onClick={handleDrawerToggle}
                    sx={{ mr: 2, display: { sm: 'none' } }}
                >
                    <MenuIcon />
                </IconButton>

                <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
                    MMC Wellness
                </Typography>

                <Box sx={{ flexGrow: 0 }}>
                    <Tooltip title={t('user_menu_tooltip') || "Open user menu"}>
                        <IconButton onClick={handleOpenUserMenu} sx={{ p: 0 }}>
                            <Avatar
                                alt={user?.name || user?.email}
                                src={avatarUrl}
                                sx={{ width: 40, height: 40 }}
                            >
                                {!avatarUrl && (user?.demographic?.first_name ? user.demographic.first_name.charAt(0).toUpperCase() : (user?.email ? user.email.charAt(0).toUpperCase() : <PersonIcon />))}
                            </Avatar>
                            <Chip label={userRole} size="small" color={chipColor} sx={{ ml: 1 }} />
                        </IconButton>
                    </Tooltip>
                    <Menu
                        sx={{ mt: '45px' }}
                        id="menu-appbar"
                        anchorEl={anchorElUser}
                        anchorOrigin={{
                            vertical: 'top',
                            horizontal: 'right',
                        }}
                        keepMounted
                        transformOrigin={{
                            vertical: 'top',
                            horizontal: 'right',
                        }}
                        open={Boolean(anchorElUser)}
                        onClose={handleCloseUserMenu}
                        PaperProps={{
                            elevation: 2,
                            sx: {
                                overflow: 'visible',
                                filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.1))',
                                mt: 1.5,
                                '& .MuiAvatar-root': {
                                    width: 32,
                                    height: 32,
                                    ml: -0.5,
                                    mr: 1,
                                },
                                '&:before': {
                                    content: '""',
                                    display: 'block',
                                    position: 'absolute',
                                    top: 0,
                                    right: 14,
                                    width: 10,
                                    height: 10,
                                    bgcolor: 'background.paper',
                                    transform: 'translateY(-50%) rotate(45deg)',
                                    zIndex: 0,
                                },
                            },
                        }}
                    >
                        <MenuItem onClick={() => handleMenuClick('profile')}>
                            <ListItemIcon>
                                <PersonIcon fontSize="small" />
                            </ListItemIcon>
                            {t('profile') || 'Profile'}
                        </MenuItem>
                        <MenuItem onClick={() => handleMenuClick('settings')}>
                            <ListItemIcon>
                                <SettingsIcon fontSize="small" />
                            </ListItemIcon>
                            {t('settings') || 'Settings'}
                        </MenuItem>
                        <Divider />
                        <MenuItem onClick={() => handleMenuClick('logout')} sx={{ color: 'error.main' }}>
                            <ListItemIcon>
                                <LogoutIcon fontSize="small" color="error" />
                            </ListItemIcon>
                            {t('logout') || 'Logout'}
                        </MenuItem>
                    </Menu>
                </Box>
            </Toolbar>
        </AppBar>
    );
};

export default TopBar; 