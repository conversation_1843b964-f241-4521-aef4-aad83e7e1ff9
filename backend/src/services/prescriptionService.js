const PrescriptionDAO = require('../dao/prescriptionDAO');
const { createLogger } = require('../utils/logger');
const { ValidationError, AuthorizationError } = require('../utils/errors');
const cache = require('../utils/cache');

// Helper function to clean and format provider names
function formatProviderName(firstName, lastName, providerType = null) {
    // Clean the first name and last name by removing _number pattern
    const cleanFirstName = firstName ? firstName.replace(/_\d+/g, '').trim() : '';
    const cleanLastName = lastName ? lastName.replace(/_\d+/g, '').trim() : '';
    
    // Filter out Ken Sun completely
    if ((cleanFirstName.toLowerCase() === 'ken' && cleanLastName.toLowerCase() === 'sun') ||
        (cleanFirstName.toLowerCase().includes('ken') && cleanLastName.toLowerCase() === 'sun')) {
        return null; // Return null to indicate this provider should not be displayed
    }
    
    // Format the name
    let formattedName = `${cleanFirstName} ${cleanLastName}`.trim();
    
    // Add Dr. prefix if it's a doctor
    if (providerType === 'doctor' && formattedName) {
        formattedName = `Dr. ${formattedName}`;
    }
    
    return formattedName || 'Unknown Provider';
}

const logger = createLogger('PrescriptionService');

class PrescriptionService {
    static async validateAccess(loggedInDemoNo, targetDemoNo) {
        if (!loggedInDemoNo || !targetDemoNo) {
            throw new ValidationError('Invalid demographic numbers provided');
        }

        if (loggedInDemoNo === targetDemoNo) {
            return true;
        }

        const hasAccess = await PrescriptionDAO.checkFamilyRelationship(loggedInDemoNo, targetDemoNo);
        if (!hasAccess) {
            throw new AuthorizationError('No permission to access this prescription');
        }

        return true;
    }

    static formatPrescription(rx) {
        let medicationDetails = null;
        if (rx.textView && rx.textView.includes('\n\n')) {
            const parts = rx.textView.split('\n\n');
            if (parts.length > 1) {
                const extracted = parts.slice(1).join('\n\n').trim();
                if (extracted) {
                    medicationDetails = extracted;
                }
            }
        }

        return {
            id: rx.script_no,
            providerNo: rx.provider_no,
            providerName: formatProviderName(rx.provider_first_name, rx.provider_last_name, 'doctor') 
                || (rx.provider_no ? `Provider #${rx.provider_no}` : 'Unknown Provider'),
            datePrescribed: rx.date_prescribed,
            details: medicationDetails,
            comments: rx.rx_comments
        };
    }

    static async getPatientPrescriptions(loggedInUser, targetDemoNo) {
        if (!loggedInUser?.demographic_no) {
            throw new AuthorizationError('User not authenticated or not linked to a demographic');
        }

        await this.validateAccess(loggedInUser.demographic_no, targetDemoNo);

        const cacheKey = `prescriptions_${targetDemoNo}`;
        const cachedData = await cache.get(cacheKey);

        if (cachedData) {
            logger.info('Returning cached prescriptions', { targetDemoNo });
            return cachedData;
        }

        const prescriptions = await PrescriptionDAO.getByDemographicNo(targetDemoNo);
        const formattedPrescriptions = prescriptions.map(this.formatPrescription);

        // Cache for 5 minutes
        await cache.set(cacheKey, formattedPrescriptions, 300);

        return formattedPrescriptions;
    }

    /**
     * 根据处方ID获取处方
     * @param {Object} user - 已验证的用户对象
     * @param {number} prescriptionId - 处方ID
     * @returns {Promise<Object>} 处方对象
     */
    static async getPrescriptionById(user, prescriptionId) {
        try {
            if (!user || (!user.demographic_no && !user.demographicNo)) {
                throw new AuthorizationError('用户未经授权');
            }

            // 统一使用用户的demographic_no，兼容两种命名方式
            const userDemoNo = user.demographic_no || user.demographicNo;

            // 获取处方信息
            const prescription = await PrescriptionDAO.getById(prescriptionId);

            if (!prescription) {
                return null; // 如果找不到处方，返回null
            }

            // 确保使用正确的处方demographic_no属性名
            const prescriptionDemoNo = prescription.demographic_no || prescription.demographicNo;

            // 验证用户是否有权限访问此处方
            // 用户只能访问自己的处方或其家庭成员的处方
            const hasAccess =
                // 处方属于当前用户
                prescriptionDemoNo === userDemoNo ||
                // 或者当前用户与处方所有者存在家庭关系
                await PrescriptionDAO.checkFamilyRelationship(userDemoNo, prescriptionDemoNo);

            if (!hasAccess) {
                throw new AuthorizationError('您无权访问此处方');
            }

            // 格式化处方数据
            return this.formatPrescription(prescription);
        } catch (error) {
            logger.error('根据ID获取处方时出错:', error);
            throw error;
        }
    }
}

module.exports = PrescriptionService; 