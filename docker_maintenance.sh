#!/bin/bash

# Docker Compose 维护脚本
# 用于停止服务、清理镜像并重新构建启动

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "Docker Compose 维护脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -f, --file     指定docker-compose文件 (默认: docker-compose.yml)"
    echo "  -q, --quiet    静默模式，减少输出"
    echo "  --no-prune     跳过镜像清理步骤"
    echo "  --no-build     跳过重新构建，直接启动现有镜像"
    echo ""
    echo "示例:"
    echo "  $0                           # 使用默认设置"
    echo "  $0 -f docker-compose.prod.yml # 使用指定的compose文件"
    echo "  $0 --no-prune               # 跳过镜像清理"
    echo "  $0 --no-build               # 跳过重新构建"
}

# 默认参数
COMPOSE_FILE="docker-compose.yml"
QUIET_MODE=false
SKIP_PRUNE=false
SKIP_BUILD=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -f|--file)
            COMPOSE_FILE="$2"
            shift 2
            ;;
        -q|--quiet)
            QUIET_MODE=true
            shift
            ;;
        --no-prune)
            SKIP_PRUNE=true
            shift
            ;;
        --no-build)
            SKIP_BUILD=true
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查docker-compose文件是否存在
if [[ ! -f "$COMPOSE_FILE" ]]; then
    log_error "Docker Compose 文件不存在: $COMPOSE_FILE"
    exit 1
fi

# 检查Docker是否运行
if ! docker info >/dev/null 2>&1; then
    log_error "Docker 未运行或无权限访问"
    exit 1
fi

# 主要维护流程
main() {
    log_info "开始 Docker Compose 维护流程..."
    log_info "使用配置文件: $COMPOSE_FILE"
    
    # 步骤1: 停止并移除容器
    log_info "步骤 1/4: 停止并移除容器..."
    if [[ "$QUIET_MODE" == "true" ]]; then
        docker compose -f "$COMPOSE_FILE" down >/dev/null 2>&1
    else
        docker compose -f "$COMPOSE_FILE" down
    fi
    log_success "容器已停止并移除"
    
    # 步骤2: 清理镜像 (可选)
    if [[ "$SKIP_PRUNE" == "false" ]]; then
        log_info "步骤 2/4: 清理未使用的镜像..."
        if [[ "$QUIET_MODE" == "true" ]]; then
            docker image prune -a -f >/dev/null 2>&1
        else
            docker image prune -a -f
        fi
        log_success "镜像清理完成"
    else
        log_warning "步骤 2/4: 跳过镜像清理"
    fi
    
    # 步骤3: 显示系统状态
    log_info "步骤 3/4: 检查系统状态..."
    if [[ "$QUIET_MODE" == "false" ]]; then
        echo "磁盘使用情况:"
        df -h | grep -E "(Filesystem|/dev/)"
        echo ""
        echo "Docker 系统信息:"
        docker system df
        echo ""
    fi
    
    # 步骤4: 重新构建并启动
    if [[ "$SKIP_BUILD" == "false" ]]; then
        log_info "步骤 4/4: 重新构建并启动服务..."
        if [[ "$QUIET_MODE" == "true" ]]; then
            docker compose -f "$COMPOSE_FILE" up -d --build >/dev/null 2>&1
        else
            docker compose -f "$COMPOSE_FILE" up -d --build
        fi
        log_success "服务已重新构建并启动"
    else
        log_info "步骤 4/4: 启动服务 (不重新构建)..."
        if [[ "$QUIET_MODE" == "true" ]]; then
            docker compose -f "$COMPOSE_FILE" up -d >/dev/null 2>&1
        else
            docker compose -f "$COMPOSE_FILE" up -d
        fi
        log_success "服务已启动"
    fi
    
    # 显示最终状态
    log_info "检查服务状态..."
    docker compose -f "$COMPOSE_FILE" ps
    
    log_success "Docker Compose 维护完成！"
}

# 错误处理
trap 'log_error "脚本执行过程中发生错误，退出码: $?"' ERR

# 确认执行
if [[ "$QUIET_MODE" == "false" ]]; then
    echo "即将执行以下操作:"
    echo "1. 停止并移除所有容器"
    [[ "$SKIP_PRUNE" == "false" ]] && echo "2. 清理所有未使用的镜像"
    [[ "$SKIP_BUILD" == "false" ]] && echo "3. 重新构建并启动服务" || echo "3. 启动现有服务"
    echo ""
    read -p "确认继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        exit 0
    fi
fi

# 执行主流程
main

# 可选：显示日志
if [[ "$QUIET_MODE" == "false" ]]; then
    echo ""
    read -p "是否查看服务日志? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "显示服务日志 (按 Ctrl+C 退出)..."
        docker compose -f "$COMPOSE_FILE" logs -f
    fi
fi 