const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
const auth = require('../middleware/auth');
const { addUserTypeInfo } = require('../middleware/userTypeAuth');
const User = require('../models/user');
const pool = require('../config/database');

// 注册新用户
router.post('/register', authController.register);

// 用户登录
router.post('/login', authController.login);

// 获取用户类型信息
router.get('/user-type', auth, addUserTypeInfo, (req, res) => {
    try {
        res.json({
            success: true,
            userType: req.userTypeInfo.userType,
            permissions: {
                isUser: req.userTypeInfo.isUser,
                isMember: req.userTypeInfo.isMember,
                isAdmin: req.userTypeInfo.isAdmin
            },
            demographicNo: req.userTypeInfo.demographicNo
        });
    } catch (error) {
        console.error('Error getting user type:', error);
        res.status(500).json({
            success: false,
            message: 'Error retrieving user type information'
        });
    }
});

// 验证邮箱
router.get('/verify-email/:token', authController.verifyEmail);

// 重新发送验证邮件
router.post('/resend-verification', authController.resendVerificationEmail);

// 忘记密码，请求重置链接
router.post('/forgot-password', authController.forgotPassword);

// 验证重置令牌
router.get('/verify-reset-token/:token', authController.verifyResetToken);

// 重设密码
router.post('/reset-password', authController.resetPassword);

// 直接重设密码（通过验证邮箱后）
router.post('/reset-password-direct', authController.resetPasswordDirect);

// 检查邮箱是否存在
router.post('/check-email', authController.checkEmail);

// 发送邮箱验证码
router.post('/send-verification-code', authController.sendEmailVerificationCode);

// 验证邮箱验证码
router.post('/verify-email-code', authController.verifyEmailCode);

// 为 Oscar 现有患者创建登录账户
router.post('/link-oscar-patient', authController.linkOscarPatient);

// 关联现有患者账号
router.post('/link', authController.linkAccount);

// 获取用户个人资料
router.get('/profile', auth, authController.getProfile);

// 获取特定人口统计号码的资料（自己或家庭成员）
router.get('/profile/:demographicNo', auth, authController.getViewedProfile);

// 续费会员
router.post('/renew-membership', auth, authController.renewMembership);

// 获取用户及家庭成员会员信息
router.get('/membership/:demographicNo', auth, authController.getMembershipInfo);

// 检查管理员权限
router.get('/check-admin', auth, authController.checkAdmin);

// Admin: Flexible client search
router.get('/admin/clients', auth, authController.adminSearchClients);

module.exports = router; 