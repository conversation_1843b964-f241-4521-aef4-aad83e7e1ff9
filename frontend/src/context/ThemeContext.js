import React, { createContext, useState, useContext, useEffect } from 'react';
import { createTheme, ThemeProvider as MuiThemeProvider } from '@mui/material/styles';

// Create the theme context
const ThemeContext = createContext();

// Light theme configuration
const lightTheme = createTheme({
    palette: {
        mode: 'light',
        primary: {
            main: '#1976d2',
            light: '#42a5f5',
            dark: '#1565c0',
        },
        secondary: {
            main: '#dc004e',
            light: '#ff4081',
            dark: '#c51162',
        },
        background: {
            default: '#f5f5f7',
            paper: '#ffffff',
        },
    },
    // Other shared typography and component settings will be the same
    typography: {
        fontFamily: [
            '-apple-system',
            'BlinkMacSystemFont',
            '"Segoe UI"',
            'Roboto',
            '"Helvetica Neue"',
            'Arial',
            'sans-serif',
            '"Apple Color Emoji"',
            '"Segoe UI Emoji"',
            '"Segoe UI Symbol"',
        ].join(','),
        h4: {
            fontWeight: 600,
        },
        h5: {
            fontWeight: 600,
        },
        h6: {
            fontWeight: 600,
        },
        subtitle1: {
            fontWeight: 500,
        },
    },
    shape: {
        borderRadius: 12,
    },
    components: {
        MuiCard: {
            styleOverrides: {
                root: {
                    borderRadius: 16,
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
                    border: '1px solid rgba(0, 0, 0, 0.08)',
                    width: '100%',
                    transition: 'all 0.2s ease-in-out',
                    '&:hover': {
                        boxShadow: '0 6px 16px rgba(0, 0, 0, 0.1)',
                    },
                },
            },
        },
        MuiCardContent: {
            styleOverrides: {
                root: {
                    padding: 20,
                    '&:last-child': {
                        paddingBottom: 20,
                    },
                },
            },
        },
        MuiPaper: {
            styleOverrides: {
                root: {
                    borderRadius: 16,
                },
            },
        },
        MuiButton: {
            styleOverrides: {
                root: {
                    borderRadius: 8,
                    textTransform: 'none',
                    fontWeight: 500,
                },
            },
        },
    },
});

// Dark theme configuration
const darkTheme = createTheme({
    palette: {
        mode: 'dark',
        primary: {
            main: '#90caf9',
            light: '#e3f2fd',
            dark: '#42a5f5',
        },
        secondary: {
            main: '#f48fb1',
            light: '#f8bbd0',
            dark: '#ec407a',
        },
        background: {
            default: '#121212',
            paper: '#1e1e1e',
        },
    },
    // Shared typography settings
    typography: lightTheme.typography,
    shape: lightTheme.shape,
    components: {
        ...lightTheme.components,
        MuiCard: {
            styleOverrides: {
                root: {
                    borderRadius: 16,
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
                    border: '1px solid rgba(255, 255, 255, 0.05)',
                    width: '100%',
                    transition: 'all 0.2s ease-in-out',
                    '&:hover': {
                        boxShadow: '0 6px 16px rgba(0, 0, 0, 0.3)',
                    },
                },
            },
        },
    },
});

export const ThemeProvider = ({ children }) => {
    // Try to get theme preference from localStorage, default to light
    const [themeMode, setThemeMode] = useState(() => {
        const savedTheme = localStorage.getItem('themeMode');
        return savedTheme || 'light';
    });

    // Actual theme object based on mode
    const theme = themeMode === 'dark' ? darkTheme : lightTheme;

    // Save theme preference to localStorage when it changes
    useEffect(() => {
        localStorage.setItem('themeMode', themeMode);
    }, [themeMode]);

    // Function to toggle theme
    const toggleTheme = () => {
        setThemeMode(prevMode => prevMode === 'light' ? 'dark' : 'light');
    };

    // Context value
    const contextValue = {
        themeMode,
        setThemeMode,
        toggleTheme,
        isDarkMode: themeMode === 'dark'
    };

    return (
        <ThemeContext.Provider value={contextValue}>
            <MuiThemeProvider theme={theme}>
                {children}
            </MuiThemeProvider>
        </ThemeContext.Provider>
    );
};

// Custom hook to use the theme context
export const useThemeMode = () => {
    const context = useContext(ThemeContext);
    if (!context) {
        throw new Error('useThemeMode must be used within a ThemeProvider');
    }
    return context;
}; 