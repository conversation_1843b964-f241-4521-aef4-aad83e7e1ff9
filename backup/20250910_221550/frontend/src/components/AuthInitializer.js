import React, { useState, useEffect } from 'react';
import { API_URL } from '../utils/env';
import { Navigate } from 'react-router-dom';
import { Box, CircularProgress, Typography, Paper } from '@mui/material';
import { useLocation } from 'react-router-dom';

/**
 * This component acts as a gatekeeper for authenticated routes.
 * It verifies the JWT token and fetches the initial, essential user data.
 * It renders its children only after successful verification and data fetch.
 */
const AuthInitializer = ({ children }) => {
    const [isInitializing, setIsInitializing] = useState(true);
    const [authError, setAuthError] = useState(null);
    const [initialUser, setInitialUser] = useState(null);
    const [retryCount, setRetryCount] = useState(0);
    const MAX_RETRIES = 3;
    const location = useLocation();

    useEffect(() => {
        const initializeAuth = async () => {
            const token = localStorage.getItem('token');

            if (!token) {
                console.log('AuthInitializer: No token found.');
                setAuthError('No authentication token found.'); // Set error for potential display or redirect logic
                setIsInitializing(false);
                return;
            }

            try {
                console.log('AuthInitializer: Verifying token and fetching initial user data...');
                const response = await fetch(`${API_URL}/api/auth/profile`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                    },
                    signal: AbortSignal.timeout(20000) // 20 second timeout (increased from 10s)
                });

                if (response.status === 401 || response.status === 403) {
                    console.error(`AuthInitializer: Authentication error (${response.status}). Removing token.`);
                    localStorage.removeItem('token');
                    setAuthError('认证失败或会话已过期'); // Authentication failed or session expired
                    setIsInitializing(false);
                    return;
                }

                if (!response.ok) {
                    let errorMsg = `Auth Initializer failed. Status: ${response.status}`;
                    try {
                        const errorData = await response.json();
                        errorMsg = errorData.message || errorMsg;
                    } catch (e) { /* Ignore */ }
                    throw new Error(errorMsg);
                }

                const data = await response.json();

                if (data.success && data.user && data.user.id && data.user.demographic_no) {
                    console.log('AuthInitializer: Token verified, user data received.', data.user);
                    setInitialUser(data.user); // Store the fetched user data
                } else {
                    console.error('AuthInitializer: Invalid user data structure received.', data);
                    throw new Error('Received invalid user data from server.');
                }

            } catch (err) {
                console.error('AuthInitializer Error:', err);

                // Implement retry logic for network-related errors
                if ((err.name === 'AbortError' || err.name === 'TypeError' || err.message.includes('network'))
                    && retryCount < MAX_RETRIES) {
                    console.log(`AuthInitializer: Network error, retrying (${retryCount + 1}/${MAX_RETRIES})...`);
                    setRetryCount(prev => prev + 1);
                    // Don't set isInitializing to false to keep showing loading state
                    return;
                }

                // After max retries or for non-network errors
                if (err.name === 'AbortError') {
                    setAuthError('Server response timeout. Please check your connection.');
                } else {
                    setAuthError(err.message || 'An error occurred during initialization.');
                }

                // Don't remove token on network errors - only on auth errors
                if (!(err.name === 'AbortError' || err.name === 'TypeError' || err.message.includes('network'))) {
                    localStorage.removeItem('token');
                }
            }

            setIsInitializing(false);
        };

        initializeAuth();
    }, [retryCount, location.pathname]); // Dependency on retryCount and location.pathname

    // --- Conditional Rendering --- 

    if (isInitializing) {
        // Show full-screen loading indicator
        return (
            <Box
                sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: '100vh', // Full viewport height
                    width: '100vw',  // Full viewport width
                    flexDirection: 'column'
                }}
            >
                <CircularProgress size={60} />
                <Typography variant="h6" sx={{ mt: 3 }}>
                    Initializing Application...
                </Typography>
                {retryCount > 0 && (
                    <Typography variant="body2" sx={{ mt: 1, color: 'text.secondary' }}>
                        Retrying connection ({retryCount}/{MAX_RETRIES})...
                    </Typography>
                )}
            </Box>
        );
    }

    if (authError || !initialUser) {
        // Only redirect to login if there is an auth error or no user data
        // after initialization has completed
        console.log('AuthInitializer: Error or no user data after init, redirecting to login.');
        // But don't clear token just because of network issues
        return <Navigate to="/login" replace />;
    }

    // If initialization is complete, no error, and user data is available,
    // render the children, passing the initial user data down.
    console.log('AuthInitializer: Initialization successful, rendering application.');
    return React.cloneElement(children, { initialUser: initialUser });
};

export default AuthInitializer; 