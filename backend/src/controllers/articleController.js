const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

let useMockData = false;
let useLocalFiles = false;

// Local files configuration
const LOCAL_ARTICLES_PATH = process.env.LOCAL_ARTICLES_PATH || path.join(__dirname, '../../documents/articles');
const LOCAL_ASSETS_PATH = process.env.LOCAL_ASSETS_PATH || path.join(__dirname, '../../documents/assets');
const DOCUMENTS_BASE_PATH = path.join(__dirname, '../../documents');

// Check if local path exists
console.log(`Checking if documents base path exists: ${DOCUMENTS_BASE_PATH}`);
if (fs.existsSync(DOCUMENTS_BASE_PATH)) {
    console.log(`Using local files from ${DOCUMENTS_BASE_PATH}, local files found`);
    useLocalFiles = true;
} else {
    console.log(`Local path ${DOCUMENTS_BASE_PATH} not found, using mock data`);
    useMockData = true;
}

// 模拟数据
const mockArticles = [
    {
        filename: "functional-medicine.md",
        title: "Functional Medicine",
    },
    {
        filename: "preventive-health-tips.md",
        title: "Preventive Health Tips",
    },
    {
        filename: "wellness-nutrition.md",
        title: "Wellness Nutrition Guide",
    }
];

const mockArticleContent = {
    "functional-medicine.md": `# 功能医学：探索健康的整体方法

**发布日期：2023年11月15日**

## 什么是功能医学？

功能医学是一种以患者为中心的医疗方式，专注于确定和解决疾病的根本原因，而不仅仅是治疗症状。这种方法认识到身体是一个相互关联的系统，而不是独立工作的器官集合。

## 功能医学的关键原则

1. **生物个体化** - 每个人都是独特的，具有独特的基因、环境和生活方式因素
2. **循证医学** - 结合最新的科学研究与临床专业知识
3. **整体视角** - 考虑所有因素如何共同影响健康状况
4. **预防性** - 强调预防疾病，而不仅仅是在疾病发生后进行治疗

## 功能医学如何帮助您

功能医学在多种慢性健康问题的管理中特别有效，包括：

* 消化系统紊乱
* 自身免疫性疾病
* 荷尔蒙失衡
* 代谢问题
* 心脏健康
* 神经系统疾病

## MMC的功能医学方法

在MMC健康管理中心，我们的功能医学方案包括：

1. **详细的初始评估** - 全面了解您的健康史、生活方式和目标
2. **高级诊断测试** - 可能包括基因组学、微生物组分析和代谢测试
3. **个性化治疗计划** - 根据您独特的需求定制的干预方案
4. **持续支持** - 在您的健康之旅中提供持续的指导和调整

## 准备好开始您的功能医学之旅了吗？

如果您对我们的功能医学方案感兴趣，请咨询您的MMC医生或在下次访问时要求更多信息。通过采用这种综合方法，我们可以一起为您创造最佳的健康结果。

---

*本文仅供会员教育使用，不应替代专业医疗建议。请始终咨询您的医疗保健提供者进行个人健康决策。*`,
    "preventive-health-tips.md": `# 预防保健小贴士：保持健康的简单步骤

**发布日期：2023年12月10日**

## 预防胜于治疗

在MMC健康管理中心，我们坚信预防胜于治疗。以下是一些简单但有效的日常习惯，可以帮助您保持最佳健康状态。

## 健康生活的关键原则

1. **均衡饮食** - 摄入多种颜色的水果和蔬菜，限制加工食品
2. **规律运动** - 每周至少150分钟中等强度的有氧活动
3. **充足睡眠** - 每晚7-9小时的优质睡眠
4. **压力管理** - 通过冥想、呼吸练习或爱好来减轻压力
5. **定期体检** - 不要等到生病才看医生

## 季节性健康提示

### 春季
* 增加室外活动时间，享受阳光和新鲜空气
* 注意花粉过敏，必要时采取预防措施
* 春季是增加新鲜蔬菜摄入的好时机

### 夏季
* 防晒防中暑
* 保持充分水分摄入
* 享受季节性水果和蔬菜

### 秋季
* 开始加强免疫系统，预防感冒和流感
* 考虑接种流感疫苗
* 准备好应对较短的日照时间

### 冬季
* 保持适当的维生素D水平
* 继续室内运动计划
* 关注心理健康，防止冬季抑郁

## 健康目标设定

设定具体、可衡量、可实现、相关和有时限的健康目标。例如，不要说"我要减肥"，而是"我计划在三个月内通过每周运动四次和减少加工食品摄入来减掉5公斤"。

---

*请记住，这些提示仅供教育目的，不能替代个性化的医疗建议。*`,
    "wellness-nutrition.md": `# 健康营养指南：为最佳健康提供燃料

**发布日期：2024年1月5日**

## 营养与健康的关系

良好的营养是整体健康和疾病预防的基础。您的饮食选择每天都会影响您的健康、精力水平和整体幸福感。

## 基本营养原则

1. **多样性** - 食用各种食物以获取全面的营养
2. **适量** - 注意分量和总体摄入量
3. **营养密度** - 选择富含营养但热量相对较低的食物
4. **个体化** - 根据您的特定需求、健康状况和目标调整饮食

## 营养素基础知识

### 宏量营养素
* **蛋白质** - 肌肉修复和免疫功能的构建模块
* **碳水化合物** - 能量的主要来源
* **健康脂肪** - 荷尔蒙生产和营养吸收的必需品

### 微量营养素
* **维生素** - 调节众多身体过程
* **矿物质** - 支持骨骼健康、神经功能等
* **抗氧化剂** - 保护细胞免受损伤

## 健康饮食的实用技巧

1. **增加水果和蔬菜摄入** - 每天至少5份
2. **选择全谷物** - 如糙米、藜麦和全麦面包
3. **摄入优质蛋白质** - 瘦肉、豆类、鱼类、坚果和种子
4. **限制** - 加工食品、添加糖和反式脂肪
5. **关注水合作用** - 每天饮用足够的水

## MMC营养支持

作为MMC健康管理中心的会员，您可以访问我们的营养专家获得个性化的饮食建议。我们的团队可以帮助您制定符合您个人需求和健康目标的饮食计划。

## 食谱创意

### 健康早餐示例
* 燕麦片配浆果和坚果
* 全麦吐司配鳄梨和蛋
* 希腊酸奶配水果和蜂蜜

### 营养午餐选择
* 烤鸡沙拉配多种蔬菜
* 藜麦碗配烤蔬菜和鹰嘴豆
* 蔬菜汤配全麦面包

---

*请记住，良好的营养是一种生活方式，而不是短期饮食。做出可持续的改变，以支持长期健康。*`
};

// Category Translation Mapping (Shared or defined separately)
const categoryTranslation = {
    'general': '综合健康',
    'nutrition': '营养膳食',
    'fitness': '运动健身',
    'wellness': '身心健康',
    'mental-health': '心理健康',
    'preventive-care': '疾病预防',
    'disease-info': '疾病信息',
    'health': '健康资讯',
    'fm': '功能医学',
    'members': '会员专区',
    'tips': '健康小贴士',
    'articles': '健康文章' // Add mapping for articles if needed
    // Add more mappings as new categories appear
};

// Helper function to parse front matter (assuming it exists or is added to tipsController)
// If not shared, copy the parseFrontMatter function here from tipsController.js
const parseFrontMatter = (content) => {
    const frontMatterRegex = /^---\r?\n([\s\S]*?)\r?\n---\r?\n([\s\S]*)$/;
    const match = content.match(frontMatterRegex);
    if (!match) return { metadata: {}, content };
    const frontMatter = match[1];
    const actualContent = match[2];
    const metadata = {};
    frontMatter.split('\n').forEach(line => {
        const [key, ...valueParts] = line.split(':');
        if (key && valueParts.length) {
            const value = valueParts.join(':').trim();
            metadata[key.trim()] = value;
        }
    });
    return { metadata, content: actualContent };
};

// Helper function to extract title from filename (fallback)
const extractTitleFromFilename = (filename) => {
    return filename
        .replace(/\.md$/, '')
        .replace(/-/g, ' ')
        .replace(/_/g, ' ')
        .replace(/\b\w/g, l => l.toUpperCase());
};

// Helper function to determine content type
const determineContentType = (filename) => {
    const ext = path.extname(filename).toLowerCase();
    const contentTypes = {
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.gif': 'image/gif',
        '.pdf': 'application/pdf',
        '.doc': 'application/msword',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.xls': 'application/vnd.ms-excel',
        '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        '.ppt': 'application/vnd.ms-powerpoint',
        '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        '.txt': 'text/plain',
        '.md': 'text/markdown',
        '.csv': 'text/csv'
    };

    return contentTypes[ext] || 'application/octet-stream';
};

// Helper function to scan directories for markdown files (Recursive)
const scanDirectoriesForMarkdown = (basePath = DOCUMENTS_BASE_PATH) => {
    let allFiles = [];
    console.log(`[ArticleScanner] Scanning path: ${basePath}`);

    try {
        const entries = fs.readdirSync(basePath, { withFileTypes: true });

        // Process files in the current directory
        const filesInDir = entries
            .filter(entry => entry.isFile() && entry.name.endsWith('.md') && !entry.name.startsWith('.'))
            .map(entry => ({
                filename: entry.name,
                filePath: path.join(basePath, entry.name),
                category: path.basename(basePath) === 'documents' ? 'general' : path.basename(basePath)
            }));
        if (filesInDir.length > 0) {
            console.log(`[ArticleScanner] Found ${filesInDir.length} files in ${basePath}`);
            allFiles = [...allFiles, ...filesInDir];
        }

        // Recursively process subdirectories
        const directories = entries.filter(entry => entry.isDirectory() && !entry.name.startsWith('.') && entry.name !== 'assets'); // Exclude assets dir
        for (const dir of directories) {
            const subDirPath = path.join(basePath, dir.name);
            const filesInSubDir = scanDirectoriesForMarkdown(subDirPath);
            allFiles = [...allFiles, ...filesInSubDir];
        }

    } catch (err) {
        if (basePath === DOCUMENTS_BASE_PATH) {
            console.error(`[ArticleScanner] Error scanning base directory ${basePath}:`, err);
        } else {
            console.warn(`[ArticleScanner] Warning: Could not scan directory ${basePath}:`, err.code);
        }
    }

    if (basePath === DOCUMENTS_BASE_PATH) {
        console.log(`[ArticleScanner] Total files found: ${allFiles.length}`);
    }
    return allFiles;
};

// Helper function to sanitize filename (ensure safety)
const sanitizeFilename = (filename) => {
    // Basic sanitization, similar to tipsController if needed, or just path normalization
    const normalized = path.normalize(filename).replace(/^(\.\.(\/|\\|$))+/, '');
    // Stronger check for invalid chars or traversal attempts
    if (normalized.includes('..') || /[\\/:*?"<>|]/.test(normalized)) {
        console.error(`Sanitization Error: Invalid filename detected - ${filename}`);
        throw new Error('Invalid characters or path traversal attempt in filename');
    }
    return normalized;
};

const articleController = {
    // Get available categories with translation
    listCategories: async (req, res) => {
        try {
            if (useLocalFiles) {
                const allMarkdownFiles = scanDirectoriesForMarkdown(DOCUMENTS_BASE_PATH);
                const uniqueCategories = [...new Set(allMarkdownFiles.map(file => {
                    try {
                        const content = fs.readFileSync(file.filePath, 'utf8');
                        const { metadata } = parseFrontMatter(content);
                        return metadata.category || file.category || 'general';
                    } catch {
                        return file.category || 'general';
                    }
                }))];

                const translatedCategories = uniqueCategories.map(slug => ({
                    slug: slug,
                    name: categoryTranslation[slug] || slug
                }));

                res.json(translatedCategories);
            } else {
                // Mock categories
                res.json([
                    { slug: 'fm', name: '功能医学' },
                    { slug: 'health', name: '健康资讯' },
                    { slug: 'preventive-care', name: '疾病预防' }
                ]);
            }
        } catch (error) {
            console.error('Error fetching article categories:', error);
            res.status(500).json({ message: 'Failed to fetch article categories', error: error.message });
        }
    },

    // List all articles (Add abstract and categoryName)
    listArticles: async (req, res) => {
        try {
            if (useLocalFiles) {
                const allMarkdownFiles = scanDirectoriesForMarkdown(DOCUMENTS_BASE_PATH);
                const articles = allMarkdownFiles.map(({ filename, filePath, category: directoryCategory }) => {
                    try {
                        const content = fs.readFileSync(filePath, 'utf8');
                        const { metadata } = parseFrontMatter(content);
                        const stats = fs.statSync(filePath);
                        const finalCategorySlug = metadata.category || directoryCategory || 'general';

                        return {
                            filename,
                            title: metadata.title || extractTitleFromFilename(filename),
                            category: finalCategorySlug,
                            categoryName: categoryTranslation[finalCategorySlug] || finalCategorySlug,
                            tags: metadata.tags ? metadata.tags.split(',').map(tag => tag.trim()) : [],
                            abstract: metadata.abstract || '',
                            featured: metadata.featured === 'true',
                            lastModified: stats.mtime.toISOString(),
                        };
                    } catch (err) {
                        console.warn(`[listArticles] Error processing file ${filePath}:`, err);
                        return null;
                    }
                }).filter(Boolean);
                console.log(`[listArticles] Returning ${articles.length} articles.`);
                return res.json(articles);
            } else {
                // Mock data logic needs update
                const articles = mockArticles.map(article => {
                    const slug = article.category || 'general'; // Assuming mock has category
                    return {
                        ...article,
                        categoryName: categoryTranslation[slug] || slug,
                        abstract: 'Mock abstract for ' + article.title,
                        tags: [], featured: false, lastModified: new Date().toISOString()
                    }
                });
                return res.json(articles);
            }
        } catch (error) {
            console.error('Error fetching articles:', error);
            res.status(500).json({ message: 'Failed to fetch articles', error: error.message });
        }
    },

    // Get a specific article by filename (Add abstract and categoryName)
    getArticle: async (req, res) => {
        try {
            const { filename } = req.params;
            const sanitizedFilename = sanitizeFilename(filename);
            if (useLocalFiles) {
                const allMarkdownFiles = scanDirectoriesForMarkdown(DOCUMENTS_BASE_PATH);
                const fileInfo = allMarkdownFiles.find(file => file.filename === sanitizedFilename);
                if (!fileInfo) {
                    console.log(`[getArticle] Article not found: ${sanitizedFilename}`);
                    return res.status(404).json({ message: 'Article not found' });
                }
                const filePath = fileInfo.filePath;
                const directoryCategory = fileInfo.category;
                console.log(`[getArticle] Found article: ${sanitizedFilename} at path: ${filePath}`);
                const fileContent = fs.readFileSync(filePath, 'utf8');
                const { metadata, content } = parseFrontMatter(fileContent);
                const stats = fs.statSync(filePath);
                const finalCategorySlug = metadata.category || directoryCategory || 'general';

                res.json({
                    filename: sanitizedFilename,
                    title: metadata.title || extractTitleFromFilename(sanitizedFilename),
                    category: finalCategorySlug,
                    categoryName: categoryTranslation[finalCategorySlug] || finalCategorySlug,
                    tags: metadata.tags ? metadata.tags.split(',').map(tag => tag.trim()) : [],
                    abstract: metadata.abstract || '',
                    featured: metadata.featured === 'true',
                    lastModified: stats.mtime.toISOString(),
                    content
                });
            } else {
                // Mock data needs update
                const mockArticle = mockArticles.find(a => a.filename === sanitizedFilename);
                if (mockArticle) {
                    const slug = mockArticle.category || 'general';
                    return res.json({
                        filename: mockArticle.filename,
                        title: mockArticle.title,
                        category: slug,
                        categoryName: categoryTranslation[slug] || slug,
                        tags: [],
                        abstract: 'Mock abstract for ' + mockArticle.title,
                        featured: false,
                        lastModified: new Date().toISOString(),
                        content: mockArticleContent[sanitizedFilename] || `# Mock for ${sanitizedFilename}`
                    });
                } else {
                    return res.status(404).json({ message: 'Article not found' });
                }
            }
        } catch (error) {
            console.error(`Error fetching article ${req.params.filename}:`, error);
            res.status(500).json({ message: 'Failed to fetch article' });
        }
    },

    // Get asset related to articles (images, etc.)
    getAsset: async (req, res) => {
        try {
            if (useLocalFiles) {
                const assetSubPath = req.params[0];
                if (!assetSubPath) {
                    return res.status(400).send('Asset path required');
                }
                const safeSuffix = sanitizeFilename(assetSubPath); // Reuse sanitize function
                const fullAssetPath = path.join(LOCAL_ASSETS_PATH, safeSuffix);

                if (!fs.existsSync(fullAssetPath)) {
                    console.log(`[getAsset] Asset not found: ${fullAssetPath}`);
                    return res.status(404).json({ message: 'Asset not found' });
                }
                console.log(`[getAsset] Serving asset: ${fullAssetPath}`);
                const content = fs.readFileSync(fullAssetPath);
                const contentType = determineContentType(safeSuffix);
                res.setHeader('Content-Type', contentType);
                res.send(content);
            } else {
                return res.status(404).json({ message: 'Assets not available in mock mode' });
            }
        } catch (error) {
            console.error(`Error fetching asset ${req.params[0]}:`, error);
            res.status(500).json({ message: 'Failed to fetch asset' /*, error: error.message */ });
        }
    }
};

module.exports = articleController; 