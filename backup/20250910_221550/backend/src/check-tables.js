const pool = require('./config/database');

async function checkTables() {
  try {
    console.log('Checking database tables...');
    
    // 检查patient_summaries表是否存在
    try {
      const [tables] = await pool.query(`
        SHOW TABLES LIKE 'patient_summaries'
      `);
      
      if (tables.length > 0) {
        console.log('patient_summaries表存在');
        
        // 检查表结构
        const [columns] = await pool.query(`
          DESCRIBE patient_summaries
        `);
        
        console.log('patient_summaries表结构:');
        columns.forEach(column => {
          console.log(`- ${column.Field} (${column.Type}) ${column.Key ? 'Key: ' + column.Key : ''}`);
        });
        
        // 检查表中的数据
        const [count] = await pool.query(`
          SELECT COUNT(*) as total FROM patient_summaries
        `);
        
        console.log(`patient_summaries表中共有 ${count[0].total} 条记录`);
        
        // 检查特定demographic_no的数据
        const [records] = await pool.query(`
          SELECT demographic_no, language, summary_type, LEFT(generated_summary, 50) as summary_preview, last_updated
          FROM patient_summaries
          WHERE demographic_no = 54897
        `);
        
        if (records.length > 0) {
          console.log(`demographic_no=54897的记录数: ${records.length}`);
          console.log('记录预览:');
          records.forEach(record => {
            console.log(`- ${record.summary_type} (${record.language}): ${record.summary_preview}... [${record.last_updated}]`);
          });
        } else {
          console.log('没有找到demographic_no=54897的记录');
        }
      } else {
        console.log('patient_summaries表不存在');
      }
    } catch (error) {
      console.error('检查patient_summaries表时出错:', error);
    }
    
    // 检查appointment表中的数据
    try {
      const [appointmentCount] = await pool.query(`
        SELECT COUNT(*) as total FROM appointment WHERE demographic_no = 54897
      `);
      
      console.log(`demographic_no=54897在appointment表中共有 ${appointmentCount[0].total} 条记录`);
      
      if (appointmentCount[0].total > 0) {
        // 获取最早和最晚的预约日期
        const [dateRange] = await pool.query(`
          SELECT 
            MIN(appointment_date) as earliest_date,
            MAX(appointment_date) as latest_date
          FROM appointment
          WHERE demographic_no = 54897
        `);
        
        console.log(`预约日期范围: ${dateRange[0].earliest_date} 至 ${dateRange[0].latest_date}`);
        
        // 按年份统计预约数量
        const [yearStats] = await pool.query(`
          SELECT 
            YEAR(appointment_date) as year,
            COUNT(*) as count
          FROM appointment
          WHERE demographic_no = 54897
          GROUP BY YEAR(appointment_date)
          ORDER BY year DESC
        `);
        
        console.log('按年份统计预约数量:');
        yearStats.forEach(stat => {
          console.log(`- ${stat.year}年: ${stat.count}次预约`);
        });
      }
    } catch (error) {
      console.error('检查appointment表时出错:', error);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('检查数据库表时出错:', error);
    process.exit(1);
  }
}

checkTables();
