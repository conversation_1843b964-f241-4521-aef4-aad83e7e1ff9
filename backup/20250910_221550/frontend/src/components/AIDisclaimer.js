import React from 'react';
import { Alert, Box, Typography, Button } from '@mui/material';
import { Warning as WarningIcon, SmartToy as SmartToyIcon, CalendarToday as CalendarIcon } from '@mui/icons-material';
import { useLanguage } from '../context/LanguageContext';
import { useNavigate } from 'react-router-dom';

const AIDisclaimer = ({ 
    variant = 'outlined', 
    severity = 'warning', 
    sx = {},
    showIcon = true,
    compact = false 
}) => {
    const { t, language } = useLanguage();
    const navigate = useNavigate();

    const disclaimerText = {
        zh: {
            title: "⚠️ AI智能解读 - 仅供参考",
            content: "此内容由人工智能生成，旨在帮助您更好地理解医疗报告和记录。请注意：此解读仅供参考，不能替代专业医疗建议、诊断或治疗方案。如有任何疑问或需要专业医疗指导，请及时联系MMC健康管家或您的医疗服务提供者。",
            bookOnlineText: "如有任何疑问欢迎预约就诊"
        },
        en: {
            title: "⚠️ AI-Generated Content - For Reference Only",
            content: "This content is generated by artificial intelligence to help you better understand medical reports and records. Please note: This interpretation is for reference only and cannot replace professional medical advice, diagnosis, or treatment. For any questions or professional medical guidance, please contact MMC Health Navigator or your healthcare provider.",
            bookOnlineText: "Book Online for Any Questions"
        }
    };

    const currentLang = language === 'en' ? 'en' : 'zh';
    const text = disclaimerText[currentLang];

    const handleBookOnlineClick = () => {
        navigate('/booking');
    };

    if (compact) {
        return (
            <Alert 
                variant={variant}
                severity={severity}
                icon={showIcon ? <SmartToyIcon /> : false}
                sx={{
                    fontSize: '0.875rem',
                    '& .MuiAlert-message': {
                        padding: '4px 0',
                        width: '100%'
                    },
                    ...sx
                }}
                action={
                    <Button
                        size="small"
                        variant="outlined"
                        startIcon={<CalendarIcon />}
                        onClick={handleBookOnlineClick}
                        sx={{
                            fontSize: '0.75rem',
                            minWidth: 'auto',
                            px: 1,
                            py: 0.5
                        }}
                    >
                        {text.bookOnlineText}
                    </Button>
                }
            >
                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                    {text.title}
                </Typography>
            </Alert>
        );
    }

    return (
        <Alert 
            variant={variant}
            severity={severity}
            icon={showIcon ? <WarningIcon /> : false}
            sx={{
                mb: 2,
                '& .MuiAlert-message': {
                    width: '100%'
                },
                ...sx
            }}
        >
            <Box>
                <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                    {text.title}
                </Typography>
                <Typography variant="body2" sx={{ lineHeight: 1.5, mb: 2 }}>
                    {text.content}
                </Typography>
                <Button
                    variant="contained"
                    color="primary"
                    startIcon={<CalendarIcon />}
                    onClick={handleBookOnlineClick}
                    sx={{
                        fontSize: '0.875rem'
                    }}
                >
                    {text.bookOnlineText}
                </Button>
            </Box>
        </Alert>
    );
};

export default AIDisclaimer; 