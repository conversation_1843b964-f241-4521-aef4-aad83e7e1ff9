-- 检查youtube_videos表是否存在，如果不存在则创建
CREATE TABLE IF NOT EXISTS youtube_videos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    thumbnailUrl VARCHAR(255),
    youtubeLink VARCHAR(255) NOT NULL,
    category VARCHAR(50) NOT NULL,
    is_active TINYINT(1) DEFAULT 1,
    views INT DEFAULT 0,
    likes INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 检查是否存在category字段，添加medical-education分类
ALTER TABLE youtube_videos 
    MODIFY COLUMN category VARCHAR(50) NOT NULL;

-- 检查表中是否有medical-education分类，如果没有则添加一条示例数据
INSERT INTO youtube_videos (title, description, thumbnailUrl, youtubeLink, category)
SELECT '示例医学科普视频', '这是一个自动添加的示例医学科普视频', 'https://i.ytimg.com/vi/sample/maxresdefault.jpg', 'https://www.youtube.com/watch?v=sample', 'medical-education'
WHERE NOT EXISTS (
    SELECT 1 FROM youtube_videos WHERE category = 'medical-education' LIMIT 1
); 