import React, { useState, useEffect } from 'react';
import { API_URL } from '../utils/env';
import {
    Container,
    Paper,
    Typography,
    List,
    ListItem,
    ListItemText,
    Divider,
    Box,
    Alert,
    CircularProgress,
    Card,
    CardContent,
    Button,
    IconButton,
    Tooltip,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import DescriptionIcon from '@mui/icons-material/Description';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import MedicalInformationIcon from '@mui/icons-material/MedicalInformation';
import SwitchAccountIcon from '@mui/icons-material/SwitchAccount';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import RefreshIcon from '@mui/icons-material/Refresh';
import { format } from 'date-fns';
import { useView } from '../context/ViewContext';
import { useLanguage } from '../context/LanguageContext';
import AIDisclaimer from './AIDisclaimer';

const StyledPaper = styled(Paper)(({ theme }) => ({
    marginTop: theme.spacing(3),
    padding: theme.spacing(3),
}));

// 增强的Markdown渲染
const renderMarkdown = (text) => {
    if (!text) return '';

    // 清理markdown代码块标记
    let cleanText = text.replace(/```markdown\s+/g, '').replace(/```\s*$/g, '');

    // 替换标题
    let formatted = cleanText.replace(/^# (.*$)/gm, '<h1>$1</h1>');
    formatted = formatted.replace(/^## (.*$)/gm, '<h2>$1</h2>');
    formatted = formatted.replace(/^### (.*$)/gm, '<h3>$1</h3>');

    // 替换列表
    formatted = formatted.replace(/^\* (.*$)/gm, '<li>$1</li>');
    formatted = formatted.replace(/^\*\* (.*$)/gm, '<li>$1</li>'); // 处理双星号开头的列表项
    formatted = formatted.replace(/^(\d+)\. (.*$)/gm, '<li>$2</li>');
    formatted = formatted.replace(/<\/li>\n<li>/g, '</li><li>');
    formatted = formatted.replace(/(?:<li>.*<\/li>\n)+/gs, function (match) {
        return match.includes('<li>1') || match.includes('<li>2')
            ? '<ol>' + match + '</ol>'
            : '<ul>' + match + '</ul>';
    });

    // 替换粗体和斜体
    formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>');

    // 替换段落
    formatted = formatted.replace(/(?:^|\n)(?!\<h|\<ul|\<ol|\<li)(.+)/g, '<p>$1</p>');

    return formatted;
};

const InfoItem = ({ icon, primary, secondary }) => (
    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
        {icon && <Box sx={{ mr: 1.5, color: 'text.secondary' }}>{icon}</Box>}
        <Box>
            {primary && <Typography variant="body2" color="text.secondary">{primary}</Typography>}
            {secondary && <Typography variant="body1" sx={{ fontWeight: 500, whiteSpace: 'pre-wrap' }}>{secondary}</Typography>}
        </Box>
    </Box>
);

const Prescriptions = () => {
    const { t, language } = useLanguage();
    const {
        loggedInUser,
        viewingDemographicNo,
        setViewAs,
        isViewingOwnProfile,
        isLoading: contextLoading,
        error: contextError
    } = useView();

    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [prescriptions, setPrescriptions] = useState([]);
    const [explanations, setExplanations] = useState({});  // 存储所有处方的解释
    const [loadingExplanations, setLoadingExplanations] = useState({});
    const [fetchingAllExplanations, setFetchingAllExplanations] = useState(false);

    // Format date string
    const formatDate = (dateString) => {
        if (!dateString) return t('unknown_date');
        try {
            return format(new Date(dateString), 'yyyy-MM-dd');
        } catch (e) {
            console.error("Error formatting date:", e);
            return dateString; // Return original if formatting fails
        }
    };

    // 获取处方解释的函数 - 返回解释而不是更新状态
    const fetchExplanation = async (prescriptionId) => {
        const token = localStorage.getItem('token');
        if (!token) {
            console.error('No authentication token');
            return null;
        }

        try {
            setLoadingExplanations(prev => ({ ...prev, [prescriptionId]: true }));

            const response = await fetch(`${API_URL}/api/prescriptions/${prescriptionId}/explanation?lang=${language || 'zh'}`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });

            const data = await response.json();

            if (!response.ok) {
                console.error('Error fetching explanation:', data.message);
                return null;
            }

            if (data.success) {
                return data.explanation;
            } else {
                console.error('API error:', data.message);
                return null;
            }
        } catch (err) {
            console.error('Exception fetching explanation:', err);
            return null;
        } finally {
            setLoadingExplanations(prev => ({ ...prev, [prescriptionId]: false }));
        }
    };

    // 获取所有处方解释
    const fetchAllExplanations = async (rxList) => {
        if (!rxList || rxList.length === 0 || fetchingAllExplanations) return;

        setFetchingAllExplanations(true);

        try {
            // 创建处方ID到其数据对象的映射
            const prescriptionMap = rxList.reduce((map, rx) => {
                map[rx.id] = rx;
                return map;
            }, {});

            // 并行请求所有处方的解释
            const explanationPromises = rxList.map(rx => fetchExplanation(rx.id));
            const explanationResults = await Promise.all(explanationPromises);

            // 将结果整合到一个对象中
            const newExplanations = {};
            rxList.forEach((rx, index) => {
                if (explanationResults[index]) {
                    newExplanations[rx.id] = explanationResults[index];
                }
            });

            setExplanations(newExplanations);
        } catch (err) {
            console.error('Error fetching all explanations:', err);
        } finally {
            setFetchingAllExplanations(false);
        }
    };

    useEffect(() => {
        // Log the demographic number being fetched for
        console.log('[Prescriptions Component] Fetching for viewingDemographicNo:', viewingDemographicNo);

        const fetchPrescriptions = async () => {
            if (!viewingDemographicNo) {
                console.log('Prescriptions: Waiting for viewingDemographicNo...');
                setLoading(contextLoading); // Reflect context loading state
                return;
            }

            console.log(`Prescriptions: Fetching data for demographic_no: ${viewingDemographicNo}`);
            setLoading(true);
            setError('');
            setPrescriptions([]); // Clear previous results
            setExplanations({}); // 清空之前的解释
            setLoadingExplanations({});

            const token = localStorage.getItem('token');
            if (!token) {
                setError(t('not_authenticated'));
                setLoading(false);
                return;
            }

            try {
                const response = await fetch(`${API_URL}/api/prescriptions/${viewingDemographicNo}`, {
                    headers: {
                        Authorization: `Bearer ${token}`,
                    },
                });

                const data = await response.json();

                if (!response.ok) {
                    // Handle permission denied specifically
                    if (response.status === 403 && loggedInUser) {
                        setViewAs(loggedInUser.demographic_no); // Switch back to self
                    }
                    throw new Error(data.message || t('fetch_failed'));
                }

                if (data.success) {
                    console.log('Prescriptions data received:', data.prescriptions);
                    const rxList = data.prescriptions || [];
                    setPrescriptions(rxList);

                    // 先显示处方列表，然后立即开始获取解释
                    setLoading(false);

                    // 异步加载所有解释
                    fetchAllExplanations(rxList);
                } else {
                    throw new Error(data.message || t('api_error'));
                }
            } catch (err) {
                console.error('Error fetching prescriptions:', err);
                setError(err.message || t('fetch_error'));
                setLoading(false);
            }
        };

        fetchPrescriptions();
    }, [viewingDemographicNo, contextLoading, loggedInUser, setViewAs, t, language]); // 添加language到依赖数组

    const handleSwitchBack = () => {
        if (loggedInUser?.demographic_no) {
            setViewAs(loggedInUser.demographic_no);
        }
    }

    // 当解释不可用时重新获取
    const handleRefreshExplanation = async (prescriptionId) => {
        const explanation = await fetchExplanation(prescriptionId);
        if (explanation) {
            setExplanations(prev => ({
                ...prev,
                [prescriptionId]: explanation
            }));
        }
    };

    // Combine error states
    const displayError = error || contextError;

    return (
        <Container maxWidth="lg">
            {!isViewingOwnProfile && loggedInUser && (
                <Alert
                    severity="info"
                    sx={{ mt: 2, mb: 1 }}
                    action={
                        <Button
                            color="inherit"
                            size="small"
                            onClick={handleSwitchBack}
                            startIcon={<SwitchAccountIcon />}
                        >
                            {t('switch_back')}
                        </Button>
                    }
                >
                    {t('viewing_family_prescriptions')}
                </Alert>
            )}

            <StyledPaper elevation={3}>
                <Typography variant="h4" gutterBottom align="center">
                    {t('prescriptions')}
                </Typography>

                {contextLoading && (
                    <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
                        <CircularProgress />
                    </Box>
                )}

                {displayError && (
                    <Alert severity="error" sx={{ my: 2 }}>
                        {typeof displayError === 'string' ? displayError : JSON.stringify(displayError)}
                    </Alert>
                )}

                {!contextLoading && !displayError && (
                    <List disablePadding>
                        {loading ? (
                            <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
                                <CircularProgress />
                            </Box>
                        ) : prescriptions.length > 0 ? (
                            prescriptions.map((rx, index) => (
                                <React.Fragment key={rx.id}>
                                    <Card variant="outlined" sx={{ my: 1.5 }}>
                                        <CardContent>
                                            {/* 处方基本信息：日期和医生 */}
                                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                                                <InfoItem
                                                    icon={<CalendarMonthIcon fontSize="small" />}
                                                    secondary={formatDate(rx.datePrescribed)}
                                                />

                                                {rx.providerName && (
                                                    <InfoItem
                                                        icon={<MedicalInformationIcon fontSize="small" />}
                                                        primary={t('prescribed_by')}
                                                        secondary={rx.providerName}
                                                    />
                                                )}
                                            </Box>

                                            {/* AI 解释内容 */}
                                            <Box sx={{ minHeight: '150px' }}>
                                                {loadingExplanations[rx.id] ? (
                                                    <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
                                                        <CircularProgress size={24} />
                                                    </Box>
                                                ) : !explanations[rx.id] ? (
                                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                        <Alert
                                                            severity="info"
                                                            sx={{ width: '100%' }}
                                                            action={
                                                                <Tooltip title={t('refresh')}>
                                                                    <IconButton
                                                                        color="inherit"
                                                                        size="small"
                                                                        onClick={() => handleRefreshExplanation(rx.id)}
                                                                    >
                                                                        <RefreshIcon fontSize="small" />
                                                                    </IconButton>
                                                                </Tooltip>
                                                            }
                                                        >
                                                            {t('ai_summary_not_available')}
                                                        </Alert>
                                                    </Box>
                                                ) : (
                                                    <Box>
                                                        <AIDisclaimer compact={true} sx={{ mb: 2 }} />
                                                        <Box sx={{
                                                            whiteSpace: 'pre-wrap',
                                                            px: 1,
                                                            py: 1,
                                                            borderRadius: 1,
                                                            bgcolor: 'background.paper',
                                                            fontSize: '0.975rem',
                                                            lineHeight: 1.6,
                                                            '& h1': { fontSize: '1.4rem', mb: 1.5, fontWeight: 600 },
                                                            '& h2': { fontSize: '1.2rem', mb: 1, mt: 1.5, fontWeight: 600 },
                                                            '& h3': { fontSize: '1.1rem', mb: 0.8, mt: 1.2, fontWeight: 600 },
                                                            '& p': { mb: 1 },
                                                            '& ul, & ol': { mb: 1.5, pl: 2 },
                                                            '& li': { mb: 0.5 },
                                                            '& strong': { fontWeight: 600 },
                                                            '& em': { fontStyle: 'italic' }
                                                        }}>
                                                            <Typography
                                                                variant="subtitle1"
                                                                sx={{ fontWeight: 'medium', mb: 1, display: 'flex', alignItems: 'center' }}
                                                            >
                                                                <SmartToyIcon sx={{ mr: 1, fontSize: '1.1rem' }} />
                                                                {t('ai_generated_summary')}
                                                            </Typography>
                                                            <Typography
                                                                variant="body1"
                                                                component="div"
                                                                dangerouslySetInnerHTML={{ __html: renderMarkdown(explanations[rx.id]) }}
                                                            />
                                                        </Box>
                                                    </Box>
                                                )}
                                            </Box>
                                        </CardContent>
                                    </Card>
                                    {index < prescriptions.length - 1 && <Divider />}
                                </React.Fragment>
                            ))
                        ) : (
                            <Box sx={{ textAlign: 'center', py: 3 }}>
                                <Typography variant="body1" color="text.secondary">
                                    {t('no_prescriptions')}
                                </Typography>
                            </Box>
                        )}
                    </List>
                )}
            </StyledPaper>
        </Container>
    );
};

export default Prescriptions; 