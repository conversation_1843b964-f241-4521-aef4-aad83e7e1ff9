# MMC健康商城设置指南

## 概述

MMC健康商城是一个集成的电商平台，支持销售会员服务、功能医学、减重管理等健康相关产品和服务。

## 功能特点

### 🛍️ 商品管理
- **多分类支持**：会员服务、功能医学、减重管理、健康检查、营养补充、在线咨询
- **多语言支持**：中英文商品信息
- **灵活定价**：支持一次性购买和订阅服务
- **商品搜索**：按分类和关键词搜索

### 💳 支付集成
- **Stripe支付**：支持信用卡、借记卡、Apple Pay、Google Pay
- **加拿大本土化**：CAD货币，支持加拿大税率
- **安全可靠**：PCI DSS合规，Webhook验证

### 📦 订单管理
- **完整订单流程**：购物车 → 结账 → 支付 → 订单跟踪
- **订单状态管理**：待处理、已确认、处理中、已完成、已取消
- **用户订单历史**：用户可查看所有订单记录

### 👥 用户体验
- **响应式设计**：支持桌面和移动设备
- **购物车功能**：添加、删除、修改商品数量
- **商品详情**：详细的商品描述和功能特点
- **多语言界面**：中英文切换

## 技术架构

### 后端技术栈
- **Node.js + Express**：API服务器
- **MySQL**：数据存储
- **Stripe API**：支付处理
- **JWT**：用户认证

### 前端技术栈
- **React**：用户界面
- **Material-UI**：UI组件库
- **Axios**：HTTP客户端

### 数据库设计
```sql
product_categories  # 商品分类
products           # 商品信息
orders             # 订单
order_items        # 订单项
cart_items         # 购物车
user_services      # 用户购买的服务记录
```

## 安装设置

### 1. 环境变量配置

在 `.env` 文件中添加以下配置：

```bash
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Store Settings
STORE_NAME=MMC健康商城
STORE_CURRENCY=CAD
```

### 2. 安装依赖

```bash
# 后端依赖
cd backend
npm install stripe

# 前端依赖
cd frontend
npm install @stripe/stripe-js @stripe/react-stripe-js
```

### 3. 数据库设置

运行数据库迁移脚本：

```bash
cd backend
node src/scripts/setupStore.js
```

### 4. Stripe配置

1. 注册 [Stripe账户](https://stripe.com)
2. 获取API密钥（测试环境）
3. 设置Webhook端点：`https://yourdomain.com/api/store/webhook/stripe`
4. 配置Webhook事件：
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`

## 支付方式对比

| 支付方式 | 手续费 | 优势 | 劣势 |
|---------|--------|------|------|
| **Stripe** | 2.9% + $0.30 | 集成简单，支持多种支付方式 | 手续费较高 |
| **PayPal** | 2.9% + $0.30 | 用户接受度高 | 需要PayPal账户 |
| **Moneris** | 2.65% + $0.28 | 加拿大本土，费率较低 | 集成复杂 |
| **Square** | 2.65% + $0.10 | 适合小企业 | 功能相对简单 |

**推荐**：Stripe（开发阶段）→ Moneris（生产环境）

## API端点

### 商品相关
```
GET  /api/store/products          # 获取商品列表
GET  /api/store/products/:id      # 获取商品详情
GET  /api/store/categories        # 获取分类列表
```

### 订单相关
```
POST /api/store/orders            # 创建订单
GET  /api/store/orders            # 获取用户订单
GET  /api/store/orders/:id        # 获取订单详情
```

### 支付相关
```
POST /api/store/payment/create-intent  # 创建支付意图
POST /api/store/webhook/stripe         # Stripe Webhook
```

## 商品分类说明

### 1. 会员服务 (Membership Services)
- 年度会员、高级会员等
- 订阅制服务
- 包含多项权益

### 2. 功能医学 (Functional Medicine)
- 个性化检测
- 功能医学评估
- 治疗方案制定

### 3. 减重管理 (Weight Management)
- 减重计划
- 营养指导
- 运动方案

### 4. 健康检查 (Health Checkups)
- 体检套餐
- 专项检查
- 健康报告

### 5. 营养补充 (Nutrition Supplements)
- 保健品
- 营养品
- 功能性食品

### 6. 在线咨询 (Online Consultations)
- 医生咨询
- 营养师咨询
- 专家问诊

## 部署说明

### Docker部署

1. 更新 `docker-compose.yml`：
```yaml
services:
  backend:
    environment:
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - STRIPE_WEBHOOK_SECRET=${STRIPE_WEBHOOK_SECRET}
```

2. 重新构建容器：
```bash
docker-compose down
docker-compose up --build
```

### 生产环境配置

1. **SSL证书**：确保HTTPS访问
2. **Webhook安全**：验证Stripe签名
3. **数据备份**：定期备份订单数据
4. **监控告警**：支付失败、订单异常监控

## 测试

### 测试用例

1. **商品浏览**：分类筛选、搜索功能
2. **购物车**：添加、删除、修改数量
3. **订单流程**：创建订单 → 支付 → 确认
4. **支付测试**：使用Stripe测试卡号

### Stripe测试卡号
```
成功支付：4242 4242 4242 4242
失败支付：4000 0000 0000 0002
需要验证：4000 0025 0000 3155
```

## 常见问题

### Q: 如何添加新的商品分类？
A: 在数据库 `product_categories` 表中添加记录，或通过管理界面添加。

### Q: 如何修改税率？
A: 在环境变量中设置 `STORE_TAX_RATE`，或在订单计算逻辑中修改。

### Q: 支付失败如何处理？
A: 系统会自动标记订单为失败状态，用户可以重新尝试支付。

### Q: 如何设置促销活动？
A: 可以在商品表中添加折扣字段，或创建优惠券系统。

## 后续扩展

### 计划功能
- [ ] 优惠券系统
- [ ] 库存管理
- [ ] 物流跟踪
- [ ] 用户评价
- [ ] 推荐系统
- [ ] 移动应用

### 集成建议
- **CRM系统**：客户关系管理
- **ERP系统**：企业资源规划
- **邮件营销**：自动化营销
- **数据分析**：销售数据分析

## 联系支持

如有技术问题，请联系开发团队或查看相关文档。 