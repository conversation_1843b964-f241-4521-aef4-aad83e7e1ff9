import React from 'react';
import { Box, Stack, useTheme, useMediaQuery } from '@mui/material';
import PropTypes from 'prop-types';

/**
 * CardContainer component ensures consistent width for all cards across the application
 * with responsive spacing and padding for mobile devices
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to render
 * @param {number} props.spacing - Spacing between items (default: 2)
 * @param {Object} props.sx - Additional style properties
 * @param {boolean} props.padded - Whether to add extra padding on mobile (default: true)
 * @returns {React.ReactElement} CardContainer component
 */
const CardContainer = ({ children, spacing = 2, sx = {}, padded = true }) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

    // Increase spacing and add padding for mobile devices
    const mobileSpacing = isMobile ? (spacing + 1) : spacing;
    // 不再添加默认的水平内边距，避免占用全屏宽度
    const mobilePadding = isMobile && padded ? { px: 0 } : {};

    return (
        <Box
            sx={{
                width: '100%',
                maxWidth: '100%',
                mb: 3,
                ...(isMobile ? {
                    // 移除边框，增加内容宽度
                    '& .MuiCard-root': {
                        width: '95%', // 增加到95%而不是calc(100% - 16px)
                        mx: 'auto',
                        borderRadius: '8px',
                        boxShadow: theme.shadows[1],
                        border: 'none', // 移除边框
                        mb: 2 // 增加底部边距
                    },
                    '& .MuiCardContent-root': {
                        p: 2, // 减少内边距以增加内容空间
                    }
                } : {}),
                ...mobilePadding,
                ...sx
            }}
        >
            <Stack spacing={mobileSpacing}>
                {children}
            </Stack>
        </Box>
    );
};

CardContainer.propTypes = {
    children: PropTypes.node.isRequired,
    spacing: PropTypes.number,
    sx: PropTypes.object,
    padded: PropTypes.bool
};

export default CardContainer; 