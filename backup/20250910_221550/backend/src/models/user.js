console.log('>>> Loading backend/src/models/user.js...'); // <<< DEBUG LOG
const pool = require('../config/database');
const bcrypt = require('bcryptjs');

class User {
    static async findById(id) {
        try {
            const [rows] = await pool.query(
                'SELECT * FROM user_auth WHERE id = ?',
                [id]
            );
            return rows[0];
        } catch (error) {
            throw error;
        }
    }

    static async findByEmail(email) {
        try {
            const [rows] = await pool.query(
                'SELECT id, email, password_hash, demographic_no, role FROM user_auth WHERE email = ?',
                [email]
            );
            return rows[0];
        } catch (error) {
            throw error;
        }
    }

    static async findByDemographicNo(demographicNo) {
        try {
            const [rows] = await pool.query(
                'SELECT * FROM user_auth WHERE demographic_no = ?',
                [demographicNo]
            );
            return rows[0];
        } catch (error) {
            throw error;
        }
    }

    static async hashPassword(password) {
        return await bcrypt.hash(password, 10);
    }

    static async create(userData) {
        const { 
            email, 
            password, 
            firstName, 
            lastName, 
            phoneNumber,  // Fixed field name
            dateOfBirth, 
            sex, 
            role, 
            referralCode,
            // Add the missing fields
            title,
            address,
            city,
            province,
            postalCode,
            healthInsuranceNumber
        } = userData;
        try {
            const hashedPassword = await this.hashPassword(password);
            const verificationToken = this.generateVerificationToken();
            
            // Start transaction
            const connection = await pool.getConnection();
            await connection.beginTransaction();
            
            try {
                // Create user in user_auth table - new users default to 'user' type
                const [result] = await connection.query(
                    'INSERT INTO user_auth (email, password_hash, verification_token, role) VALUES (?, ?, ?, ?)',
                    [email, hashedPassword, verificationToken, role || 'user']
                );
                
                const userId = result.insertId;
                
                // Create demographic record with all the fields
                const demographicData = {
                    title: title,
                    firstName: firstName,
                    lastName: lastName,
                    email,
                    phoneNumber: phoneNumber,  // Fixed field name
                    dateOfBirth: dateOfBirth,
                    sex: sex,
                    // Add the missing fields
                    address: address,
                    city: city,
                    province: province,
                    postalCode: postalCode,
                    healthInsuranceNumber: healthInsuranceNumber
                };
                
                const demographicNo = await this.createDemographic(demographicData, connection);
                
                // Link user to demographic
                await connection.query(
                    'UPDATE user_auth SET demographic_no = ? WHERE id = ?',
                    [demographicNo, userId]
                );
                
                // Setup initial patient program/membership
                await this.setupPatientProgram(demographicNo, connection);
                
                // Handle referral code if provided
                if (referralCode) {
                    // Validate referral code and get referrer demographic number
                    const [referrerResults] = await connection.query(
                        'SELECT demographic_no FROM referral_codes WHERE referral_code = ? AND is_active = TRUE',
                        [referralCode]
                    );
                    
                    if (referrerResults.length > 0) {
                        const referrerDemographicNo = referrerResults[0].demographic_no;
                        
                        // Record referral
                        await connection.query(
                            'INSERT INTO referrals (referrer_demographic_no, referred_demographic_no) VALUES (?, ?)',
                            [referrerDemographicNo, demographicNo]
                        );
                        
                        // Add reward points to referrer (100 points for successful referral)
                        await connection.query(
                            'INSERT INTO reward_points (demographic_no, points, description) VALUES (?, ?, ?)',
                            [
                                referrerDemographicNo, 
                                100, 
                                `Reward for referring ${firstName} ${lastName} (ID: ${demographicNo})`
                            ]
                        );

                        // Update referral status to completed
                        await connection.query(
                            'UPDATE referrals SET status = "completed" WHERE referrer_demographic_no = ? AND referred_demographic_no = ?',
                            [referrerDemographicNo, demographicNo]
                        );
                    }
                }
                
                // Commit transaction
                await connection.commit();
                
                return {
                    userId,
                    demographicNo,
                    email,
                    verificationToken
                };
            } catch (error) {
                // Roll back the transaction in case of error
                await connection.rollback();
                throw error;
            } finally {
                connection.release();
            }
        } catch (error) {
            throw error;
        }
    }

    static async verifyPassword(password, hashedPassword) {
        return await bcrypt.compare(password, hashedPassword);
    }

    static async getDemographicInfo(demographicNo) {
        try {
            const [rows] = await pool.query(
                'SELECT * FROM demographic WHERE demographic_no = ?',
                [demographicNo]
            );
            return rows[0];
        } catch (error) {
            throw error;
        }
    }

    static async linkDemographic(userId, demographicNo) {
        try {
            const [result] = await pool.query(
                'UPDATE user_auth SET demographic_no = ? WHERE id = ?',
                [demographicNo, userId]
            );
            return result.affectedRows > 0;
        } catch (error) {
            throw error;
        }
    }

    static async setupPatientProgram(demographicNo, existingConnection = null) {
        try {
            // 设置默认项目ID和医生
            const programId = 10034;
            const providerId = '999998';

            // 创建admission记录
            const [admissionResult] = await (existingConnection || pool).query(
                `INSERT INTO admission (
                    client_id, program_id, provider_no, admission_date,
                    admission_status, admission_from_transfer, automatic_discharge,
                    temporary_admission_flag, radioDischargeReason, lastUpdateDate
                ) VALUES (?, ?, ?, NOW(), 'current', 0, 0, 0, 0, NOW())`,
                [demographicNo, programId, providerId]
            );

            // 创建program_client_restriction记录
            await (existingConnection || pool).query(
                `INSERT INTO program_client_restriction (
                    program_id, demographic_no, provider_no, start_date, end_date
                ) VALUES (?, ?, ?, NOW(), DATE_ADD(NOW(), INTERVAL 5 YEAR))`,
                [programId, demographicNo, providerId]
            );

            return true;
        } catch (error) {
            console.error('Error setting up patient program:', error);
            throw error;
        }
    }

    static async createDemographic(demographicData, existingConnection = null) {
        try {
            const connection = existingConnection || await pool.getConnection();
            await connection.beginTransaction();

            try {
                // 创建demographic记录，确保所有必要字段都正确设置
                const [result] = await connection.query(
                    `INSERT INTO demographic (
                        title, last_name, first_name, address, city, province, 
                        postal, phone, year_of_birth, month_of_birth, date_of_birth, 
                        hin, sex, email, provider_no, official_lang, spoken_lang,
                        patient_status, date_joined, roster_status,
                        lastUpdateDate
                    ) VALUES (
                        ?, ?, ?, ?, ?, ?,
                        ?, ?, ?, ?, ?,
                        ?, ?, ?, '999998', 'English', 'English',
                        'AC', NOW(), 'AC',
                        NOW()
                    )`,
                    [
                        demographicData.title || 'Mr',
                        demographicData.lastName,
                        demographicData.firstName,
                        demographicData.address,
                        demographicData.city,
                        demographicData.province,
                        demographicData.postalCode,
                        demographicData.phoneNumber,
                        demographicData.dateOfBirth ? new Date(demographicData.dateOfBirth).getFullYear() : new Date().getFullYear() - 30,
                        demographicData.dateOfBirth ? new Date(demographicData.dateOfBirth).getMonth() + 1 : 1,
                        demographicData.dateOfBirth ? new Date(demographicData.dateOfBirth).getDate() : 1,
                        demographicData.healthInsuranceNumber || null,
                        demographicData.sex || 'U', // Default to 'U' (Unspecified) if not provided
                        demographicData.email
                    ]
                );

                const demographicNo = result.insertId;

                // 创建admission记录，确保所有必要字段都正确设置
                await connection.query(
                    `INSERT INTO admission (
                        client_id, program_id, provider_no, admission_date,
                        admission_status, admission_from_transfer, automatic_discharge,
                        temporary_admission_flag, radioDischargeReason, lastUpdateDate
                    ) VALUES (
                        ?, 10034, '999998', NOW(),
                        'current', 0, 0,
                        0, 0, NOW()
                    )`,
                    [demographicNo]
                );

                await connection.commit();
                return demographicNo;
            } catch (error) {
                await connection.rollback();
                throw error;
            } finally {
                connection.release();
            }
        } catch (error) {
            throw error;
        }
    }

    static async getDemographicByHinAndDOB(hin, dateOfBirth) {
        try {
            // Parse the date string
            const date = new Date(dateOfBirth);
            const year = date.getFullYear();
            const month = date.getMonth() + 1; // JavaScript months are 0-based
            const day = date.getDate();

            const [rows] = await pool.query(
                'SELECT * FROM demographic WHERE hin = ? AND year_of_birth = ? AND month_of_birth = ? AND date_of_birth = ?',
                [hin, year, month, day]
            );
            return rows[0];
        } catch (error) {
            throw error;
        }
    }

    static async getMembershipInfo(demographicNo) {
        try {
            // 获取最新的会员记录，使用 service_date 作为开始日期
            const [currentMembershipBase] = await pool.query(
                `SELECT bm.demographic_no,
                        concat(d.last_name, ', ', d.first_name) client,
                        bm.billing_code,
                        bm.bill_amount,
                        bm.service_date,
                        bm.birth_date,
                        bs.description
                 FROM billingmaster bm
                 LEFT JOIN billingservice bs ON bs.service_code = bm.billing_code
                 INNER JOIN demographic d ON d.demographic_no = bm.demographic_no
                 WHERE bm.demographic_no = ?
                   AND bm.billingstatus = 'A'
                   AND bm.billing_code LIKE 'AHM%'
                 ORDER BY service_date DESC 
                 LIMIT 1`,
                [demographicNo]
            );

            if (!currentMembershipBase[0]) {
                return null;
            }

            const currentCode = currentMembershipBase[0].billing_code;
            const currentServiceDate = currentMembershipBase[0].service_date;
            const currentActualAmount = currentMembershipBase[0].bill_amount;

            // 计算会员有效期（service_date + 1年）
            // service_date 格式是 yyyymmdd，需要转换为标准日期格式
            let startDate, endDate, isExpired;
            try {
                // 将 yyyymmdd 格式转换为 Date 对象
                const serviceDateStr = currentServiceDate.toString();
                if (serviceDateStr.length === 8) {
                    const year = parseInt(serviceDateStr.substring(0, 4));
                    const month = parseInt(serviceDateStr.substring(4, 6)) - 1; // JavaScript月份从0开始
                    const day = parseInt(serviceDateStr.substring(6, 8));
                    startDate = new Date(year, month, day);
                } else {
                    // 如果格式不对，尝试直接解析
                    startDate = new Date(currentServiceDate);
                }

                // Check if the startDate is valid before proceeding
                if (isNaN(startDate.getTime())) {
                    console.warn(`Invalid service_date found for demographic_no ${demographicNo}: ${currentServiceDate}`);
                    startDate = new Date(); // Default to current date
                }

                endDate = new Date(startDate);
                endDate.setFullYear(endDate.getFullYear() + 1);

                // 检查会员是否过期 - 使用当地时间进行比较
                const now = new Date();
                // 将结束日期设置为当天的23:59:59，确保在到期日当天仍然有效
                const endOfDay = new Date(endDate);
                endOfDay.setHours(23, 59, 59, 999);
                isExpired = now > endOfDay;
                
                // 特殊测试：如果是 XIN ZHANG 的数据，强制检查
                if (demographicNo.toString().includes('23706') || currentServiceDate === '20231228') {
                    console.log(`[SPECIAL DEBUG] XIN ZHANG membership check:`);
                    console.log(`[SPECIAL DEBUG] Service date: ${currentServiceDate}`);
                    console.log(`[SPECIAL DEBUG] Start date: ${startDate}`);
                    console.log(`[SPECIAL DEBUG] End date: ${endDate}`);
                    console.log(`[SPECIAL DEBUG] End of day: ${endOfDay}`);
                    console.log(`[SPECIAL DEBUG] Current time: ${now}`);
                    console.log(`[SPECIAL DEBUG] Should be expired: ${now > endOfDay}`);
                }
                
                // 添加调试信息
                console.log(`[DEBUG] Membership expiry check for demographic_no ${demographicNo}:`);
                console.log(`[DEBUG] Service date: ${currentServiceDate} -> Start date: ${startDate.toISOString()}`);
                console.log(`[DEBUG] End date: ${endDate.toISOString()} -> End of day: ${endOfDay.toISOString()}`);
                console.log(`[DEBUG] Current date: ${now.toISOString()} (${now.toLocaleDateString()})`);
                console.log(`[DEBUG] Is expired: ${isExpired} (now > endOfDay: ${now.getTime()} > ${endOfDay.getTime()})`);
                console.log(`[DEBUG] Time difference: ${(now.getTime() - endOfDay.getTime()) / (1000 * 60 * 60 * 24)} days`);
            } catch (dateError) {
                console.error('Error processing membership dates:', dateError);
                startDate = new Date();
                endDate = new Date();
                endDate.setFullYear(endDate.getFullYear() + 1);
                isExpired = false;
            }

            // 获取会员历史记录
            const [history] = await pool.query(
                `SELECT bm.service_date, bm.billing_code, bm.bill_amount, bm.billingstatus
                FROM billingmaster bm 
                WHERE bm.demographic_no = ? 
                AND bm.billing_code LIKE 'AHM%' 
                AND bm.billingstatus = 'A'
                ORDER BY bm.service_date DESC`,
                [demographicNo]
            );

            // 最终检查：确保过期状态正确
            const finalResult = {
                current: {
                    service_date: startDate.toISOString(), // Use calculated start date
                    billing_code: currentCode,
                    bill_amount: currentActualAmount, // Use the actual bill_amount from billingmaster
                    endDate: endDate.toISOString(),
                    isExpired
                },
                history // History still shows the actual billed amount
            };
            
            console.log(`[FINAL DEBUG] Returning membership data for ${demographicNo}:`, finalResult);
            return finalResult;
        } catch (error) {
            console.error('Error in getMembershipInfo:', error);
            // Return a default empty structure instead of throwing
            return {
                current: null,
                history: []
            };
        }
    }

    static async findDemographicByEmail(email) {
        try {
            const [rows] = await pool.query(
                'SELECT * FROM demographic WHERE email = ?',
                [email]
            );
            return rows[0] || null;
        } catch (error) {
            throw error;
        }
    }

    static async findAllDemographicsByEmail(email) {
        try {
            const [rows] = await pool.query(
                'SELECT demographic_no, title, first_name, last_name, address, city, province, postal, year_of_birth, month_of_birth, date_of_birth, sex, email FROM demographic WHERE email = ?',
                [email]
            );
            return rows;
        } catch (error) {
            throw error;
        }
    }

    static async checkEmailExists(email) {
        try {
            // 检查 user_auth 表
            const userAuth = await this.findByEmail(email);

            // 检查 demographic 表中的所有匹配记录
            const demographics = await this.findAllDemographicsByEmail(email);

            return {
                exists: !!userAuth,
                existsInOscar: demographics.length > 0,
                demographicCount: demographics.length,
                demographics: demographics
            };
        } catch (error) {
            throw error;
        }
    }

    static async linkOscarPatient(email, password, demographicNo) {
        try {
            // 如果提供了特定的 demographic_no，则使用它
            let demographic;

            if (demographicNo) {
                const [rows] = await pool.query(
                    'SELECT * FROM demographic WHERE demographic_no = ? AND email = ?',
                    [demographicNo, email]
                );
                demographic = rows[0];
                if (!demographic) {
                    throw new Error('No matching patient record found with this ID and email');
                }
            } else {
                // 否则尝试查找email对应的记录
                demographic = await this.findDemographicByEmail(email);
                if (!demographic) {
                    throw new Error('No matching patient record found with this email');
                }
            }

            // 检查该 demographic 是否已关联账户
            const existingUser = await this.findByDemographicNo(demographic.demographic_no);

            if (existingUser) {
                throw new Error('This patient record is already linked to another account');
            }

            // 创建新用户并关联 demographic
            const hashedPassword = await this.hashPassword(password);
            const [result] = await pool.query(
                'INSERT INTO user_auth (email, password_hash, demographic_no) VALUES (?, ?, ?)',
                [email, hashedPassword, demographic.demographic_no]
            );

            return {
                userId: result.insertId,
                demographicNo: demographic.demographic_no,
                demographicInfo: demographic
            };
        } catch (error) {
            console.error('Error in linkOscarPatient:', error);
            throw error;
        }
    }

    // 生成随机验证令牌
    static generateVerificationToken() {
        return require('crypto').randomBytes(32).toString('hex');
    }

    // 验证用户邮箱
    static async verifyEmail(token) {
        try {
            const [result] = await pool.query(
                'UPDATE user_auth SET is_verified = true, verification_token = NULL WHERE verification_token = ?',
                [token]
            );

            if (result.affectedRows === 0) {
                return { success: false, message: '无效的验证令牌或账户已经被验证' };
            }

            return { success: true, message: '邮箱验证成功' };
        } catch (error) {
            throw error;
        }
    }

    // 重新发送验证邮件
    static async regenerateVerificationToken(userId) {
        try {
            const verificationToken = this.generateVerificationToken();

            const [result] = await pool.query(
                'UPDATE user_auth SET verification_token = ? WHERE id = ? AND is_verified = false',
                [verificationToken, userId]
            );

            if (result.affectedRows === 0) {
                return { success: false, message: '用户不存在或已经验证' };
            }

            return { success: true, verificationToken };
        } catch (error) {
            throw error;
        }
    }

    // 检查用户是否已验证
    static async isVerified(userId) {
        try {
            const [rows] = await pool.query(
                'SELECT is_verified FROM user_auth WHERE id = ?',
                [userId]
            );

            if (rows.length === 0) {
                return false;
            }

            return rows[0].is_verified === 1;
        } catch (error) {
            throw error;
        }
    }

    // 创建密码重置令牌
    static async createPasswordResetToken(email) {
        try {
            // 查找用户
            const user = await this.findByEmail(email);
            if (!user) {
                return { success: false, message: '没有找到该邮箱对应的用户' };
            }

            // 生成重置令牌
            const resetToken = require('crypto').randomBytes(32).toString('hex');

            // 设置令牌过期时间（1小时后）
            const resetTokenExpires = new Date();
            resetTokenExpires.setHours(resetTokenExpires.getHours() + 1);

            // 更新用户记录
            const [result] = await pool.query(
                'UPDATE user_auth SET reset_token = ?, reset_token_expires = ? WHERE id = ?',
                [resetToken, resetTokenExpires, user.id]
            );

            if (result.affectedRows === 0) {
                return { success: false, message: '更新用户记录失败' };
            }

            return {
                success: true,
                resetToken,
                email: user.email
            };
        } catch (error) {
            throw error;
        }
    }

    // 验证密码重置令牌
    static async verifyPasswordResetToken(token) {
        try {
            const [rows] = await pool.query(
                'SELECT id FROM user_auth WHERE reset_token = ? AND reset_token_expires > NOW()',
                [token]
            );

            if (rows.length === 0) {
                return { success: false, message: '无效或已过期的重置令牌' };
            }

            return {
                success: true,
                userId: rows[0].id
            };
        } catch (error) {
            throw error;
        }
    }

    // 重设密码
    static async resetPassword(userId, newPassword) {
        try {
            const hashedPassword = await this.hashPassword(newPassword);

            const [result] = await pool.query(
                'UPDATE user_auth SET password_hash = ?, reset_token = NULL, reset_token_expires = NULL WHERE id = ?',
                [hashedPassword, userId]
            );

            if (result.affectedRows === 0) {
                return { success: false, message: '重设密码失败' };
            }

            return { success: true };
        } catch (error) {
            throw error;
        }
    }

    static async getFamilyMembers(demographicNo, requesterUserId) {
        try {
            const accessWindowMinutes = 30;
            // Define query function inside to reuse
            const fetchRelations = async (whereClause, params) => {
                const [relations] = await pool.query(
                    `SELECT 
                        r.relation as relationship_type, 
                        r.relation_demographic_no as relative_demographic_no,
                        d.first_name AS relative_first_name,
                        d.last_name AS relative_last_name,
                        d.email AS relative_email, 
                        d.sex,
                        d.year_of_birth
                     FROM relationships r
                     JOIN demographic d ON r.relation_demographic_no = d.demographic_no
                     ${whereClause}
                     AND r.deleted != '1'
                     ORDER BY r.relation, d.last_name, d.first_name`,
                    params
                );
                return relations;
            };

            // Query forward relationships
            const forwardRelations = await fetchRelations(
                'WHERE r.demographic_no = ?',
                [demographicNo]
            );

            // Query reverse relationships (adjust join and where clause)
            const [reverseRelations] = await pool.query(
                `SELECT 
                        r.relation as relationship_type, 
                        r.demographic_no as relative_demographic_no, -- This is the relative now
                        d.first_name AS relative_first_name,
                        d.last_name AS relative_last_name,
                        d.email AS relative_email, 
                        d.sex,
                        d.year_of_birth
                     FROM relationships r
                     JOIN demographic d ON r.demographic_no = d.demographic_no -- Join on the other side
                     WHERE r.relation_demographic_no = ? -- Find where the target is the logged-in user
                     AND r.deleted != '1'
                     ORDER BY r.relation, d.last_name, d.first_name`,
                [demographicNo]
            );


            // Merge results and add access check
            const allRelations = [];
            const processedRelativeNos = new Set();

            const processRelation = async (relation) => {
                if (processedRelativeNos.has(relation.relative_demographic_no)) {
                    return; // Skip duplicate
                }
                processedRelativeNos.add(relation.relative_demographic_no);

                // Check for recent verified access
                let hasRecentAccess = false;
                if (requesterUserId) { // Only check if requesterUserId is provided
                    const checkQuery = `
                        SELECT 1
                        FROM view_verification_codes
                        WHERE requester_user_id = ?
                          AND target_demographic_no = ?
                          AND is_verified = TRUE
                          AND verified_at >= NOW() - INTERVAL ? MINUTE
                        LIMIT 1
                    `;
                    const [accessRows] = await pool.query(checkQuery, [
                        requesterUserId,
                        relation.relative_demographic_no,
                        accessWindowMinutes
                    ]);
                    hasRecentAccess = accessRows.length > 0;
                }

                allRelations.push({ ...relation, hasRecentAccess });
            };

            // Process both sets of relations
            for (const relation of forwardRelations) {
                await processRelation(relation);
            }
            for (const relation of reverseRelations) {
                // Ensure the relative_demographic_no isn't the user themselves in reverse cases
                if (relation.relative_demographic_no !== demographicNo) {
                    await processRelation(relation);
                }
            }

            return allRelations;
        } catch (error) {
            console.error('Error fetching family members with access check:', error);
            return [];
        }
    }

    // Flexible search for demographics (admin use)
    static async searchDemographics({ name, email, phone, hin, page = 1, limit = 20 }) {
        try {
            let whereClauses = [];
            let params = [];

            if (name) {
                // Enhanced name search to support multiple formats
                const searchTerm = name.trim();
                
                // Check if it contains a comma (Last, First format)
                if (searchTerm.includes(',')) {
                    const [lastName, firstName] = searchTerm.split(',').map(s => s.trim());
                    whereClauses.push('(last_name LIKE ? AND first_name LIKE ?)');
                    params.push(`%${lastName}%`, `%${firstName}%`);
                }
                // Check if it contains a space (First Last format)
                else if (searchTerm.includes(' ')) {
                    const parts = searchTerm.split(' ').map(s => s.trim()).filter(s => s);
                    if (parts.length >= 2) {
                        const firstName = parts[0];
                        const lastName = parts.slice(1).join(' '); // Handle multi-part last names
                        whereClauses.push('((first_name LIKE ? AND last_name LIKE ?) OR (last_name LIKE ? AND first_name LIKE ?))');
                        params.push(`%${firstName}%`, `%${lastName}%`, `%${firstName}%`, `%${lastName}%`);
                    } else {
                        // Single word after space cleanup
                whereClauses.push('(first_name LIKE ? OR last_name LIKE ?)');
                        params.push(`%${searchTerm}%`, `%${searchTerm}%`);
                    }
                }
                // Single word - search both first and last name
                else {
                    whereClauses.push('(first_name LIKE ? OR last_name LIKE ?)');
                    params.push(`%${searchTerm}%`, `%${searchTerm}%`);
            }
            }
            
            if (email) {
                whereClauses.push('email LIKE ?');
                params.push(`%${email}%`);
            }
            
            if (phone) {
                // Enhanced phone search - remove all non-digits for comparison
                const phoneDigits = phone.replace(/\D/g, '');
                if (phoneDigits.length >= 3) {
                    // Search in phone field, removing non-digits from database values too
                    whereClauses.push('REPLACE(REPLACE(REPLACE(REPLACE(phone, " ", ""), "-", ""), "(", ""), ")", "") LIKE ?');
                    params.push(`%${phoneDigits}%`);
            }
            }
            
            if (hin) {
                // Enhanced HIN search - support 9-digit format
                const hinDigits = hin.replace(/\D/g, '');
                if (hinDigits.length >= 3) {
                whereClauses.push('hin LIKE ?');
                    params.push(`%${hinDigits}%`);
                }
            }

            const where = whereClauses.length > 0 ? 'WHERE ' + whereClauses.join(' AND ') : '';
            const offset = (page - 1) * limit;

            // Main query - include date_joined from user_auth if available
            const [rows] = await pool.query(
                `SELECT 
                    d.demographic_no, 
                    d.first_name, 
                    d.last_name, 
                    d.email, 
                    d.phone, 
                    d.hin, 
                    d.year_of_birth, 
                    d.month_of_birth, 
                    d.date_of_birth, 
                    d.sex,
                    ua.created_at as date_joined
                 FROM demographic d
                 LEFT JOIN user_auth ua ON d.demographic_no = ua.demographic_no
                 ${where}
                 ORDER BY d.last_name, d.first_name
                 LIMIT ? OFFSET ?`,
                [...params, limit, offset]
            );

            // Count query for pagination
            const [countRows] = await pool.query(
                `SELECT COUNT(*) as total FROM demographic d ${where}`,
                params
            );
            const total = countRows[0]?.total || 0;

            return { results: rows, total };
        } catch (error) {
            throw error;
        }
    }

    /**
     * Validate a referral code
     * @param {string} code - The referral code to validate
     * @returns {Promise<boolean>} - Whether the code is valid
     */
    static async validateReferralCode(code) {
        try {
            // Direct database query instead of using ReferralService
            const [results] = await pool.query(
                'SELECT demographic_no FROM referral_codes WHERE referral_code = ? AND is_active = TRUE',
                [code]
            );
            return results.length > 0;
        } catch (error) {
            console.error('Error validating referral code:', error);
            return false;
        }
    }
}

// Export the User class directly
module.exports = User; 