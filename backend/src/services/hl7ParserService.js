const MedicalTermTranslator = require('./medicalTermTranslator');

/**
 * HL7 Message Parser Service
 * Parses HL7 messages and converts them to human-readable format
 */

class HL7ParserService {
    /**
     * Parse HL7 message and extract relevant information
     * @param {string} hl7Message - Raw HL7 message
     * @returns {Object} Parsed HL7 data
     */
    static parseHL7Message(hl7Message) {
        if (!hl7Message || typeof hl7Message !== 'string') {
            return {
                error: 'Invalid HL7 message format',
                parsed: false
            };
        }

        try {
            // Try to decode Base64 if the message appears to be encoded
            let decodedMessage = hl7Message;
            if (this.isBase64(hl7Message)) {
                try {
                    decodedMessage = Buffer.from(hl7Message, 'base64').toString('utf8');
                    console.log('Successfully decoded Base64 HL7 message');
                } catch (decodeError) {
                    console.error('Failed to decode Base64 message:', decodeError);
                    // Continue with original message if decode fails
                }
            }

            // Split message into segments - try different delimiters
            let segments = decodedMessage.split('\r').filter(segment => segment.trim());
            if (segments.length <= 1) {
                // Try other common delimiters
                segments = decodedMessage.split('\n').filter(segment => segment.trim());
            }
            if (segments.length <= 1) {
                // Try pipe-separated format
                segments = decodedMessage.split('|').filter(segment => segment.trim());
            }
            
            console.log(`Found ${segments.length} HL7 segments`);
            console.log('First few segments:', segments.slice(0, 3));
            
            // Log segment types for debugging
            const segmentTypes = segments.map(seg => seg.substring(0, 3)).filter(type => type.length === 3);
            console.log('HL7 segment types found:', segmentTypes);
            
            const parsedData = {
                parsed: true,
                messageType: '',
                patientInfo: {},
                orderingProvider: {},
                results: [],
                observations: [],
                rawSegments: {}
            };

            segments.forEach(segment => {
                const segmentType = segment.substring(0, 3);
                const fields = segment.split('|');
                
                parsedData.rawSegments[segmentType] = fields;

                switch (segmentType) {
                    case 'MSH':
                        parsedData.messageType = this.parseMSH(fields);
                        break;
                    case 'PID':
                        parsedData.patientInfo = this.parsePID(fields);
                        break;
                    case 'ORC':
                        parsedData.orderingProvider = this.parseORC(fields);
                        break;
                    case 'OBR':
                        parsedData.observations.push(this.parseOBR(fields));
                        break;
                    case 'OBX':
                        parsedData.results.push(this.parseOBX(fields));
                        break;
                }
            });

            return parsedData;
        } catch (error) {
            console.error('Error parsing HL7 message:', error);
            return {
                error: 'Failed to parse HL7 message',
                parsed: false,
                details: error.message
            };
        }
    }

    /**
     * Parse MSH (Message Header) segment
     */
    static parseMSH(fields) {
        if (fields.length < 9) return {};
        
        return {
            messageType: fields[9] || '',
            messageControlId: fields[10] || '',
            processingId: fields[11] || '',
            versionId: fields[12] || '',
            dateTime: fields[7] || ''
        };
    }

    /**
     * Parse PID (Patient Identification) segment
     */
    static parsePID(fields) {
        if (fields.length < 20) return {};
        
        const patientId = fields[3] ? fields[3].split('^') : [];
        
        return {
            patientId: patientId[0] || '',
            patientIdType: patientId[1] || '',
            lastName: fields[5] ? fields[5].split('^')[0] : '',
            firstName: fields[5] ? fields[5].split('^')[1] : '',
            middleName: fields[5] ? fields[5].split('^')[2] : '',
            dateOfBirth: fields[7] || '',
            gender: fields[8] || '',
            address: fields[11] || '',
            phone: fields[13] || ''
        };
    }

    /**
     * Parse ORC (Common Order) segment
     */
    static parseORC(fields) {
        if (fields.length < 12) return {};
        
        const orderingProvider = fields[12] ? fields[12].split('^') : [];
        
        return {
            orderControl: fields[1] || '',
            orderId: fields[2] || '',
            orderingProvider: {
                id: orderingProvider[0] || '',
                lastName: orderingProvider[1] || '',
                firstName: orderingProvider[2] || '',
                middleName: orderingProvider[3] || ''
            },
            orderDateTime: fields[7] || ''
        };
    }

    /**
     * Parse OBR (Observation Request) segment
     */
    static parseOBR(fields) {
        if (fields.length < 20) return {};
        
        return {
            orderId: fields[2] || '',
            universalServiceId: fields[4] || '',
            observationDateTime: fields[7] || '',
            specimenReceivedDateTime: fields[14] || '',
            orderingProvider: fields[16] || '',
            diagnosticService: fields[24] || ''
        };
    }

    /**
     * Parse OBX (Observation Result) segment
     */
    static parseOBX(fields) {
        if (fields.length < 6) return {};
        
        const observationId = fields[3] ? fields[3].split('^') : [];
        const valueType = fields[2] || '';
        let value = fields[5] || '';
        let unit = '';
        let referenceRange = '';
        let abnormalFlag = '';
        let status = '';

        // Parse value and unit for different value types
        if (valueType === 'NM') {
            // Numeric value - check if it contains unit information
            if (value.includes('^')) {
                const valueParts = value.split('^');
                value = valueParts[0] || '';
                unit = valueParts[1] || '';
            }
        } else if (valueType === 'ST' || valueType === 'TX') {
            // String or text value - clean HTML tags
            value = this.cleanHtmlTags(value);
        }

        // Get reference range (field 7)
        if (fields[7]) {
            referenceRange = fields[7];
        }
        
        // Get abnormal flag (field 8)
        if (fields[8]) {
            abnormalFlag = fields[8];
        }
        
        // Get status (field 11)
        if (fields[11]) {
            status = fields[11];
        }

        // Get unit from field 6 if not already set
        if (!unit && fields[6]) {
            unit = fields[6];
        }

        return {
            setId: fields[1] || '',
            valueType: valueType,
            observationId: observationId[0] || '',
            observationName: observationId[1] || '',
            value: value,
            unit: unit,
            referenceRange: referenceRange,
            abnormalFlag: abnormalFlag,
            status: status,
            observationDateTime: fields[14] || '',
            resultStatus: fields[11] || ''
        };
    }

    /**
     * Convert parsed HL7 data to human-readable format
     */
    static toHumanReadable(parsedData) {
        if (!parsedData.parsed) {
            return {
                error: parsedData.error || 'Unable to parse HL7 message',
                readable: false
            };
        }

        const readable = {
            readable: true,
            summary: {
                messageType: parsedData.messageType.messageType || 'Unknown',
                patientName: `${parsedData.patientInfo.firstName || ''} ${parsedData.patientInfo.lastName || ''}`.trim(),
                dateOfBirth: this.formatDate(parsedData.patientInfo.dateOfBirth),
                gender: this.formatGender(parsedData.patientInfo.gender),
                orderDateTime: this.formatDateTime(parsedData.orderingProvider.orderDateTime)
            },
            results: [],
            observations: []
        };

        // Format lab results
        parsedData.results.forEach(result => {
            // Include results even if some fields are missing
            if (result.observationName || result.observationId) {
                const testName = result.observationName || result.observationId || 'Unknown Test';
                const translation = MedicalTermTranslator.translate(testName);
                
                readable.results.push({
                    testName: testName,
                    testNameChinese: translation ? translation.chinese : testName,
                    explanation: translation ? translation.explanation : '',
                    category: translation ? translation.category : '其他',
                    value: result.value || 'N/A',
                    unit: result.unit || '',
                    referenceRange: result.referenceRange || 'N/A',
                    abnormalFlag: this.formatAbnormalFlag(result.abnormalFlag),
                    status: result.status || 'Final'
                });
            }
        });

        // Format observations
        parsedData.observations.forEach(obs => {
            if (obs.universalServiceId) {
                readable.observations.push({
                    testType: obs.universalServiceId,
                    orderDateTime: this.formatDateTime(obs.observationDateTime),
                    specimenReceived: this.formatDateTime(obs.specimenReceivedDateTime),
                    diagnosticService: obs.diagnosticService
                });
            }
        });

        return readable;
    }

    /**
     * Format date from HL7 format (YYYYMMDD) to readable format
     */
    static formatDate(hl7Date) {
        if (!hl7Date || hl7Date.length < 8) return 'Unknown';
        
        try {
            const year = hl7Date.substring(0, 4);
            const month = hl7Date.substring(4, 6);
            const day = hl7Date.substring(6, 8);
            return `${year}-${month}-${day}`;
        } catch (error) {
            return hl7Date;
        }
    }

    /**
     * Format date/time from HL7 format
     */
    static formatDateTime(hl7DateTime) {
        if (!hl7DateTime || hl7DateTime.length < 14) return 'Unknown';
        
        try {
            const year = hl7DateTime.substring(0, 4);
            const month = hl7DateTime.substring(4, 6);
            const day = hl7DateTime.substring(6, 8);
            const hour = hl7DateTime.substring(8, 10);
            const minute = hl7DateTime.substring(10, 12);
            return `${year}-${month}-${day} ${hour}:${minute}`;
        } catch (error) {
            return hl7DateTime;
        }
    }

    /**
     * Format gender code
     */
    static formatGender(genderCode) {
        const genderMap = {
            'M': 'Male',
            'F': 'Female',
            'U': 'Unknown'
        };
        return genderMap[genderCode] || genderCode;
    }

    /**
     * Format abnormal flag
     */
    static formatAbnormalFlag(flag) {
        if (!flag) return 'Normal';
        
        const flagStr = flag.toString().toLowerCase();
        
        if (flagStr.includes('high') || flagStr.includes('h') || flagStr.includes('+')) {
            return 'High';
        } else if (flagStr.includes('low') || flagStr.includes('l') || flagStr.includes('-')) {
            return 'Low';
        } else if (flagStr.includes('abnormal') || flagStr.includes('a') || flagStr.includes('pos')) {
            return 'Abnormal';
        } else if (flagStr.includes('normal') || flagStr.includes('n') || flagStr.includes('neg')) {
            return 'Normal';
        }
        
        return 'Normal';
    }

    /**
     * Clean HTML tags from text
     */
    static cleanHtmlTags(text) {
        if (!text) return '';
        return text
            .replace(/\\\.br\\/g, '\n')  // Replace \.br\ with newlines
            .replace(/<br\s*\/?>/gi, '\n')  // Replace <br> tags
            .replace(/<[^>]*>/g, '')  // Remove other HTML tags
            .trim();
    }

    /**
     * Check if a string is Base64 encoded
     */
    static isBase64(str) {
        try {
            // Check if string contains only Base64 characters
            const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
            if (!base64Regex.test(str)) {
                return false;
            }
            
            // Try to decode and re-encode to verify
            const decoded = Buffer.from(str, 'base64');
            const reEncoded = Buffer.from(decoded).toString('base64');
            return str === reEncoded;
        } catch (error) {
            return false;
        }
    }

    /**
     * Generate sample lab results for demonstration
     */
    static generateSampleResults() {
        return {
            readable: true,
            summary: {
                messageType: 'ORU^R01',
                patientName: 'YAO SONG',
                dateOfBirth: '1983-12-16',
                gender: 'Male',
                orderDateTime: '2022-05-18 09:30:00'
            },
            results: [
                {
                    testName: 'WBC',
                    testNameChinese: '白细胞',
                    explanation: '白细胞是人体免疫系统的重要组成部分，负责抵抗感染和疾病。正常值表示免疫系统功能正常。',
                    category: '血常规',
                    value: '5.8',
                    unit: '10⁹/L',
                    referenceRange: '4.0-10.0',
                    abnormalFlag: 'Normal',
                    status: 'Final'
                },
                {
                    testName: 'RBC',
                    testNameChinese: '红细胞',
                    explanation: '红细胞负责运输氧气到全身各个组织。正常值表示血液携氧能力良好。',
                    category: '血常规',
                    value: '4.74',
                    unit: '10¹²/L',
                    referenceRange: '4.20-5.40',
                    abnormalFlag: 'Normal',
                    status: 'Final'
                },
                {
                    testName: 'Hemoglobin (血红蛋白)',
                    value: '137',
                    unit: 'g/L',
                    referenceRange: '135-170',
                    abnormalFlag: 'Normal',
                    status: 'Final'
                },
                {
                    testName: 'Hematocrit (红细胞压积)',
                    value: '0.41',
                    unit: 'L/L',
                    referenceRange: '0.40-0.50',
                    abnormalFlag: 'Normal',
                    status: 'Final'
                },
                {
                    testName: 'MCV (平均红细胞体积)',
                    value: '86',
                    unit: 'fL',
                    referenceRange: '82-98',
                    abnormalFlag: 'Normal',
                    status: 'Final'
                },
                {
                    testName: 'MCH (平均血红蛋白量)',
                    value: '28.9',
                    unit: 'pg',
                    referenceRange: '27.5-33.5',
                    abnormalFlag: 'Normal',
                    status: 'Final'
                },
                {
                    testName: 'MCHC (平均血红蛋白浓度)',
                    value: '336',
                    unit: 'g/L',
                    referenceRange: '300-370',
                    abnormalFlag: 'Normal',
                    status: 'Final'
                },
                {
                    testName: 'RDW (红细胞分布宽度)',
                    value: '12.3',
                    unit: '%',
                    referenceRange: '11.5-14.5',
                    abnormalFlag: 'Normal',
                    status: 'Final'
                },
                {
                    testName: 'Platelet Count (血小板计数)',
                    value: '315',
                    unit: '10⁹/L',
                    referenceRange: '150-400',
                    abnormalFlag: 'Normal',
                    status: 'Final'
                },
                {
                    testName: 'Neutrophils (中性粒细胞)',
                    value: '3.1',
                    unit: '10⁹/L',
                    referenceRange: '2.0-7.5',
                    abnormalFlag: 'Normal',
                    status: 'Final'
                },
                {
                    testName: 'Lymphocytes (淋巴细胞)',
                    value: '2.1',
                    unit: '10⁹/L',
                    referenceRange: '1.0-4.0',
                    abnormalFlag: 'Normal',
                    status: 'Final'
                },
                {
                    testName: 'Monocytes (单核细胞)',
                    value: '0.4',
                    unit: '10⁹/L',
                    referenceRange: '0.1-0.8',
                    abnormalFlag: 'Normal',
                    status: 'Final'
                },
                {
                    testName: 'Eosinophils (嗜酸性粒细胞)',
                    value: '0.1',
                    unit: '10⁹/L',
                    referenceRange: '0.0-0.7',
                    abnormalFlag: 'Normal',
                    status: 'Final'
                },
                {
                    testName: 'Basophils (嗜碱性粒细胞)',
                    value: '0.0',
                    unit: '10⁹/L',
                    referenceRange: '0.0-0.2',
                    abnormalFlag: 'Normal',
                    status: 'Final'
                },
                {
                    testName: 'Pathological Casts',
                    value: 'Neg',
                    unit: '/HPF',
                    referenceRange: 'Negative',
                    abnormalFlag: 'Normal',
                    status: 'Final'
                },
                {
                    testName: 'Crystals',
                    value: 'Neg',
                    unit: '/HPF',
                    referenceRange: 'N/A',
                    abnormalFlag: 'Abnormal',
                    status: 'Final'
                },
                {
                    testName: 'Urinalysis Comment',
                    value: 'New method as of 18 October 2021.\nReporting categories and reference\nintervals correspondingly updated.\n\nPlease see https://www.lifelabs.com/\nhealthcare-providers/reports/\n?myProvince=bc for test interferences\nand alternate reporting units.',
                    unit: '',
                    referenceRange: 'N/A',
                    abnormalFlag: 'Abnormal',
                    status: 'Final'
                }
            ],
            observations: [
                {
                    testType: 'Complete Blood Count',
                    orderDateTime: '2022-05-18 09:30:00',
                    specimenReceived: '2022-05-18 09:00:00',
                    diagnosticService: 'HEMATOLOGY'
                }
            ]
        };
    }
}

module.exports = HL7ParserService; 